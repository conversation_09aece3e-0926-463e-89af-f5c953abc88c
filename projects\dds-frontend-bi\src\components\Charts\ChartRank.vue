<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"

import { getTextWidth, toThousands } from "@/utils"

import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "value"
    },
    // Y轴字段
    yField: {
      type: String,
      default: "name"
    },
    // 系列名称
    seriesName: {
      type: String,
      default: ""
    },
    // 颜色
    color: {
      type: Array,
      default: () => ["#2361DB"]
    },
    // 柱状图宽
    barWidth: {
      type: Number,
      default: 12
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: "auto"
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: false
    },
    sortType: {
      type: String,
      default: "asc"
    },
    isDrill: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      maxWidth: 0,
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.chart.on("click", params => {
        console.log(params, "2222222222222")
        this.$emit("chartClick", params)
      })

      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      let max = 0
      this.chartData.forEach(item => {
        max = Math.max(max, item[this.xField])
        this.maxWidth = Math.max(
          getTextWidth(item[this.yField], 12),
          this.maxWidth
        )
      })
      console.log(getTextWidth("58,324,556.17元", 12), "22222222222222")
      let right = getTextWidth(toThousands(max) + this.unit, 12)

      console.log(right)
      const series = []
      let dataset = null
      series.push(
        this.createSeriesObject({
          name: this.seriesName ?? this.yField,

          color: this.color[0]
        })
      )
      ;(dataset = [
        { dimensions: [this.yField, this.xField], source: this.chartData },
        {
          transform: {
            type: "sort",
            config: { dimension: this.xField, order: this.sortType }
          }
        }
      ]),
        this.chart.setOption({
          dataset,
          tooltip: {
            // trigger: "axis",

            // triggerOn: "click",
            trigger: this.isDrill ? "item" : "axis",

            axisPointer: {
              show: false,
              type: "shadow",
              label: { show: false, backgroundColor: "transparent" },
              shadowStyle: {
                color: "rgba(35,97,219,0.05)"
              }
            },
            formatter: params => {
              if (this.isDrill) {
                return `<div style="color:#fff;font-size:14px;font-weight: 500">点击查看详情</div>`
              }

              return `<div>
              <p class="tooltip-title">${params[0].seriesName}</p>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
                   params[0].color
                 }" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${toThousands(
                  params[0].value[
                    params[0].dimensionNames[params[0].encode.x[0]]
                  ]
                )}${this.unit}
                </span>
              </div>
            </div>`
            },
            className: this.isDrill
              ? "echarts-tooltip-drill"
              : "echarts-tooltip-diy"
          },

          dataZoom: this.showDataZoom&&this.chartData.length> this.showNum
            ? [
                {
                  type: "slider",
                  yAxisIndex: 0,
                  zoomLock: true,
                  width: 6,
                  right: 0,

                  showDetail: false,
                  startValue: this.chartData.length,
                  endValue: this.chartData.length - this.showNum + 1,
                  showDataShadow: false,
                  fillerColor: "#DBDBDB", // 滑块的颜色

                  backgroundColor: "transparent", // 滑块轨道的颜色
                  borderColor: "transparent", // 滑块轨道边框的颜色
                  moveHandleIcon: "none",

                  zoomOnMouseWheel: false,
                  brushSelect: false,

                  handleIcon:
                    "M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z",
                  handleSize: "100%",
                  handleStyle: {
                    color: "#DBDBDB",
                    borderColor: "transparent"
                  }
                },
                {
                  type: "inside",
                  id: "insideY",
                  yAxisIndex: 0,
                  zoomOnMouseWheel: false,
                  moveOnMouseMove: true,
                  moveOnMouseWheel: true
                }
              ]
            : [],
          grid: {
            top: 0,
            bottom: 0,
            left: 0,
            right,
            containLabel: true
          },
          legend: {
            show: false
          },
          xAxis: {
            type: "value",
            max: function () {
              return max * 1.1
            },
            boundaryGap: ["0%", "5%"],
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },

          yAxis: [
            {
              type: "category",
              triggerEvent: true,

              axisLabel: {
                fontSize: 12,
                color: "#323233",
                fontWeight: "400",
                formatter: name => {
                  return name.length > 6 ? name.substr(0, 6) + "..." : name
                }
                // tooltip: {
                //   show: true,
                //   trigger: "item" //鼠标移动上去展示全称
                // }
              },

              splitLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            }
          ],
          series
        })

      this.calculationLocation(this.maxWidth)
      // extension(this.chart, "yAxis")
    },
    // 创建系列对象
    createSeriesObject({ name, color, index = 1, yField = this.yField }) {
      return {
        type: "bar",
        barWidth: this.barWidth,
        name: name,
        itemStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },
        datasetIndex: index,
        label: {
          show: true,
          color: "#323233",
          fontSize: 12,
          position: "left",
          offset: [0, 0],
          formatter: params => {
            return (
              toThousands(
                params.value[params.dimensionNames[params.encode.x[0]]]
              ) + this.unit
            )
          }
        }
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.x[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    },
    // 计算柱状图数值应该显示的位置
    calculationLocation(maxWidth) {
      console.log(maxWidth, "maxWidth")
      // 整个图表的宽度
      let chartWidth = this.chart.getWidth()
      console.log(maxWidth, chartWidth)
      let maxWidth1 = maxWidth > 84 ? 84 : maxWidth
      // 计算完毕的配置项
      // let option = this.chart.getOption()
      // //grid
      // let grid = option.grid[0]
      // //grid的left、right
      // let gLeft = grid.left
      // let gRight = grid.right
      // //计算left和right的距离
      // gLeft = (chartWidth * parseFloat(gLeft)) / 100
      // gRight = (chartWidth * parseFloat(gRight)) / 100
      // //label横向偏移的距离
      // let x = chartWidth - gLeft - gRight
      // 更新lable的配置之后init配置项，然后重渲染图表
      this.chart.setOption({
        series: [
          {
            label: {
              offset: [chartWidth - maxWidth1 - 10, 0]
            }
          }
        ]
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-drill {
  background: rgba(48, 49, 51, 1) !important;
}
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    min-width: 220px;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 9px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
