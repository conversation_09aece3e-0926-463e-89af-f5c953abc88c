<template>
  <DT-View :show="showPage">
    <el-row>
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="addWidget"
      >
        新增
      </el-button>
    </el-row>
    <el-table :data="data" style="width: 100%" >
      <el-table-column
        prop="name"
        show-overflow-tooltip
        label="图表"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="viewName"
        show-overflow-tooltip
        label="数据集"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="viewCode"
        show-overflow-tooltip
        label="数据集编码"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="cs"
        show-overflow-tooltip
        label="应用次数"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="description"
        show-overflow-tooltip
        label="描述"
        align="center"
      >
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="copyWidget(scope.row)"
          >
            复制
          </el-button>
          <el-button
            @click="editWidget(scope.row.id)"
            type="text"
            size="small"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="delWidget(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="sizeChange"
      @currentChange="currentChange"
    />
    <!-- 复制dialog -->
    <el-dialog
      title="复制图表"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="copyWidgetConfig"
        :rules="rules"
        ref="ruleForm"
        label-width="50px"
        class="demo-ruleForm"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="copyWidgetConfig.name"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            type="textarea"
            :rows="2"
            v-model="copyWidgetConfig.description"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveWidget">确 定</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from "@/service"

export default {
  components: {},
  props: {},
  data() {
    return {
      showPage:false,
      dialogVisible: false,
      pagination: {
        total: 1,
        pageSize: 10,
        currentPage: 1,
      },
      data: [],
      copyWidgetConfig: {
        name: "",
        description: "",
      },
      rules: {
        name: [ { required: true, message: "请输入图表名称", trigger: "blur" } ],
      },
    }
  },
  computed: {},
  created() {
    this.getWidgetList()
  },
  mounted() {},
  watch: {},
  methods: {
    // 编辑
    editWidget(id) {
      this.$router.push(`Workbench?isFullPage=true&id=${id}`)
    },
    // 新增
    addWidget() {
      this.$router.push("Workbench?isFullPage=true")
    },
    // 复制
    copyWidget(item) {
      this.dialogVisible = true
      this.copyWidgetConfig = { ...item, name: item.name + "_copy" }
    },
    // 复制保存
    saveWidget() {
      this.$refs.ruleForm.validate(async(valid) => {
        if (valid) {
          delete this.copyWidgetConfig.id
          const { code } = await Request.widget.create(this.copyWidgetConfig)
          if (code === 200) {
            this.$message({
              type: "success",
              message: "复制成功!",
            })
            this.getWidgetList()
            this.dialogVisible = false
          }
        } else {
          return false
        }
      })
    },
    // 删除
    delWidget(id) {
      this.$confirm("此操作将删除该图表, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async() => {
        const { code,message } = await Request.widget.delete({ id })
        if (code === 200) {
          this.$message({
            type: "success",
            message: message,
          })
          this.getWidgetList()
        }
      })
    },
    // 获取列表
    async getWidgetList() {
      this.$dt_loading.show()
      const {
        data: { list, totalCount, currentPage },
      } = await Request.widget.getPage({
        name: "",
        currentPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
      })
      this.data = list
      this.pagination.total = totalCount
      this.pagination.currentPage = currentPage
      this.$dt_loading.hide()
      this.showPage = true
    },
    // 分页
    sizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.getWidgetList()
    },
    // 分页
    currentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.getWidgetList()
    },
  },
}
</script>

<style scoped lang="scss"></style>
