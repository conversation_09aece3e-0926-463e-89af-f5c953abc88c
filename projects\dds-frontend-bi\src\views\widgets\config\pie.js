import ChartTypes from './ChartTypes'
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  CHART_PIE_LABEL_POSITIONS
} from '@/globalConstants'

const pie = {
  id: ChartTypes.Pie,
  name: 'pie',
  title: '饼图',
  icon: 'chart_pie',
  coordinate: 'cartesian',
  rules: [ { dimension: [ 0, 9999 ], metric: 1 }, { dimension: 0, metric: [ 2, 9999 ] } ],
  dimetionAxis: 'col',
  data: [
    {
      title: '维度',
      type: 'category',
      name: "cols"

    },
    {
      title: '指标',
      type: 'value',
      name: "metrics"
    },
    {
      title: '颜色',
      type: 'category',
      name: "color"
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '筛选',
    //   type: 'all',
    //   name: "filters"

    // }


  ],
  style: {
    label: {
      showLabel: false,
      pieLabelPosition: CHART_PIE_LABEL_POSITIONS[0].value,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: '12',
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
      labelParts: [ 'dimensionValue', 'indicatorValue', 'percentage' ]
    },
    legend: {
      showLegend: true,
      legendPosition: 'right',
      right: 20,
      top: 20,
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR,
      orient: 'vertical',
      itemWidth: 7,
      itemHeight: 7,
      icon: 'circle'
    },
    spec: {
      roseType: false,
      circle: true,
      x: 25,
      y: 50,
      outerRadius: 72,
      innerRadius: 60,
      radius: 70,
      borderWidth: 2,

    },
    title: {
      show: true,
      text: 0,
      subtext: '总计',
      x: 24,
      y: 43,
      textStyle: {
        fontSize: 26,
      },
      subtextStyle: {
        fontSize: 12,
      },

    },

    // toolbox: {
    //   showToolbox: false
    // }
  }
}

export default pie
