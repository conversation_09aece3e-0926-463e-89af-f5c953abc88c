// 引入Mock.js
const Mock = require('mockjs')

export default {
  '/teacherEducationalDisplay/leftOne': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|6': [
        {
          'val|10-100': 0.0,
          'name': '@ctitle(4,10)',
        },
      ],
    }),
  '/teacherEducationalDisplay/leftTwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|16': [
        {
          'val|10-100': 0.0,
          'name': '@ctitle(4,10)',
        },
      ],
    }),
  '/teacherEducationalDisplay/leftThree': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|5': [
        {
          'val|100-999': 0,
          'name|+1': [
            '专业必修课',
            '专业选修课',
            '公共必修课',
            '公共选修课',
            '其他',
          ],
        },
      ],
    }),
  '/teacherEducationalDisplay/leftThreeOther': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|25': [
        {
          'val|10-100': 0.0,
          'name': '@ctitle(4,10)',
        },
      ],
    }),

  '/teacherEducationalDisplay/centerOne': () =>
    Mock.mock({
      code: 200,
      message: 'OK',
      data: [
        { id: 1, name: '专任教师', val: 1234 },
        { id: 2, name: '开课课程', val: 1324 },
        { id: 3, name: '培养方案', val: 21 },
        { id: 4, name: '教学任务', val: 1123 },
        { id: 5, name: '异动学籍', val: 121 },
        { id: 6, name: '排课教室', val: 1124 },
        { id: 7, name: '调停课', val: 234 },
        { id: 8, name: '专任教师', val: 20 },
      ],
    }),
  '/teacherEducationalDisplay/centerOneDetail': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|0-25': [
        {
          col1: '@ctitle(4,10)',
          col2: '@ctitle(4,10)',
          col3: '@ctitle(4,10)',
          col4: '@ctitle(4,10)',
          col5: '@ctitle(4,10)',
        },
      ],
    }),
  '/teacherEducationalDisplay/centerTwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4': [
        {
          'val1|100-999': 0,
          'val2|100-999': 0,
          'name|+1': [
            '2021-2022学年第一学期',
            '2022-2023学年第一学期',
            '2024-2025学年第一学期',
            '2025-2026学年第一学期',
          ],
        },
      ],
    }),

  '/teacherEducationalDisplay/rightOne': () =>
    Mock.mock({
      code: 200,
      message: 'OK',
      data: {
        'data': { 'name': '25', 'val|0-99': 0 },
        'list|10': [
          {
            'monday|100-999': 0,
            'tuesday|100-999': 0,
            'wednesday|100-999': 0,
            'thursday|100-999': 0,
            'friday|100-999': 0,
            'saturday|100-999': 0,
            'sunday|100-999': 0,
          },
        ],
      },
    }),
  '/teacherEducationalDisplay/rightTwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4000': [
        {
          'unit|0-20': 0,
          'name': '@ctitle(10,20)',
          'tercher_name': '@ctitle(10,20)',
          'headcount|10000-99999': 0,
        },
      ],
    }),
}
