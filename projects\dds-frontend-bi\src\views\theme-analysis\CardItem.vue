<template>
  <div :class="'card' + type" @click="goPage(info)">
    <div class="icon">
      <div class="icon-floder cover">
        <img
          :src="info.thumbnail + ''"
          :class="[info.thumbnail ? 'cover' : '']"
          @error="
            e => {
              e.target.src = type == 1 ? defaultScreenImage : defaultPortalImage
            }
          "
        />
      </div>
    </div>
    <div class="title">
      {{ info.name }}
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: 1 // 大屏1  看板2
    }
  },
  data() {
    return {
      defaultScreenImage: require("@/assets/imgs/bi/screen-default.png"),
      defaultPortalImage: require("@/assets/imgs/bi/portal-default.png")
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    goPage(info) {
      // 大屏
      if (this.type === 1) {
        // 外链
        if (info.type === 3) {
          const token = this.$utils.auth.getAdminheader()["X-Token"]
          const url = info.router + `?token=${token}&system=dds`
          window.open(url, "_blank")
        } else if (info.type === 1) {
          // bi大屏
          window.open(
            `/ddsBi/displayPreview?isFullPage=true&displayId=${info.id}`,
            "_blank"
          )
        } else {
          window.open(info.router, "_blank")
        }
      } else {
        console.log(window.location)
        if (info.dashbordType === 0) {
          const theme = this.$route.query.theme
          window.open(
            `/ddsBi/viewDashboards/Portalview/${info.id}?isFullPage=true&theme=${theme}&redirect=${window.location.pathname}${window.location.search}`,
            "_blank"
          )
        } else if (info.dashbordType === 1) {
          window.open(info.router, "_blank")
        } else {
          window.open(info.router, "_blank")
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.card1 {
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8edf2;

  .icon {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    background: #f5f5f5;
    .icon-floder.cover {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      img {
        object-fit: cover;
        border-radius: 4px;

        width: 100%;
        height: 100%;
        &.cover {
          object-position: left top;
          object-fit: cover;
          width: 100%;
          height: 100%;
          animation: showAnimate-48018687 0.1s linear 0.2s;
          animation-fill-mode: backwards;
        }
      }
    }
  }
  .title {
    padding-left: 16px;
    height: 49px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #1d2129;
    line-height: 49px;
  }
  &:hover {
    box-shadow: 0px 0px 12px 1px rgba(0, 25, 102, 0.1);
  }
}

.card2 {
  background-color: #fff;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8edf2;

  .icon {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    background: #ebedf0;
    .icon-floder.cover {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      padding: 16px;
      box-sizing: border-box;
      img {
        object-fit: cover;
        border-radius: 4px;

        width: 100%;
        height: 100%;
        &.cover {
          object-position: left top;
          object-fit: cover;
          width: 100%;
          height: 100%;
          animation: showAnimate-48018687 0.1s linear 0.2s;
          animation-fill-mode: backwards;
        }
      }
    }
  }
  .title {
    padding-left: 16px;
    height: 49px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #1d2129;
    line-height: 49px;
  }
  &:hover {
    box-shadow: 0px 0px 12px 1px rgba(0, 25, 102, 0.1);
  }
}
</style>
