<template>
  <div class="display-slide-background" >
    <div class="screen-wrapper" :style="style">
      <slot />
    </div>
  </div>
</template>
<script>
import debounce from "lodash/debounce"
export default {
  name: "",
  props: {
    slide: {
      type: Object,
      default: () => {},
    },
  },
  computed: {},
  data() {
    return {
      newScale: [ 1, 1 ],
      style: {
        width: `${this.slide.config.slideParams.width}px`,
        height: `${this.slide.config.slideParams.height}px`,
        transform: `scale(1) translate(-50%, 0%)`,
        backgroundColor: this.slide.config.slideParams.backgroundColor,
        backgroundImage:process.env.NODE_ENV === "development"?`url("http://***************${this.slide.config.slideParams.backgroundImage}")` :  `url("${window.location.origin}${this.slide.config.slideParams.backgroundImage}")`,
        backgroundSize: "cover",
        left: "0%",
        top: "0%",
        fontSize: `${this.slide.config.slideParams.newScale}px`,
      },
    }
  },
  mounted() {
    this.getScale()
    window.addEventListener('resize', debounce(this.getScale, 1000))
  },
  watch: {
    slide: {
      handler() {
        this.getScale()
      },
      deep: true,
    },
  },
  methods: {
    getScale() {
      const containerWidth = window.innerWidth
      const containerHeight = window.innerHeight
      const {
        width: slideWidth,
        height: slideHeight,
        scaleMode,
      } = this.slide.config.slideParams
      const landscapeScale = containerWidth / slideWidth
      const portraitScale = containerHeight / slideHeight
      switch (scaleMode) {
      case "scaleWidth":
        this.style.top = "0%"
        this.style.left = "0%"
        this.style.transform = `scale(${landscapeScale}) translate(0%, 0%)`
        break
      case "scaleHeight":
        this.style.left = "50%"
        this.style.top = "0%"
        this.style.transform = `scale(${portraitScale}) translate(-50%, 0%)`
        break
      case "scaleFull":
        this.style.left = "0%"
        this.style.top = "0%"
        this.style.transform = `scale(${landscapeScale},${portraitScale}) translate(0%, 0%)`
        break
      }
    },
  },
  destroyed() {
    window.removeEventListener('resize', debounce(this.getScale, 1000))
  },
}
</script>
<style lang="scss" scoped>
.display-slide-background {
  position: relative;
  overflow: auto;
  width: 100vw;
  height: 100vh;
}
.screen-wrapper {
  transform-origin: 0 0;
  box-sizing: border-box;
  color: #fff;
  position: absolute;
  transition: 0.3s;
  overflow: auto;
}
</style>
