<template>
  <el-select
    v-model="selectedValue"
    filterable
    remote
    multiple
    :remote-method="handleRemote"
    :loading="loading"
    :loading-text="'加载中...'"
    :no-match-text="'无匹配数据'"
    :value-key="valueKey"
    collapse-tags
    @change="handleChange"
    ref="my-select"
    @scroll="handleScroll"
    style="width: 100%"
    v-bind="$attrs"
    :placeholder="placeholder"
    v-load-more="handleScroll"
    @remove-tag="removeTag"
    @visible-change="reverseArrow"
    v-suffix-click="handleSuffixClick"
    v-select-tag-tooltip="100"

  >
    <el-option
      label="全部"
      :value="ALL_DIMVALUE_CODE"
      @click.native.stop="selectAll"
    ></el-option>
    <el-option
      v-for="item in options"
      :key="item[props.value]"
      :label="item[props.label]"
      :value="valueKey ? item : item[props.value]"
    ></el-option>
    <el-option
      v-if="loading"
      disabled
      value="__loading"
      label="加载中..."
    ></el-option>
    <el-option
      v-else-if="noMore && options.length"
      disabled
      value="__no_more"
      label="没有更多了"
    ></el-option>
    <template #suffix>123123</template>
  </el-select>
</template>

<script>
import config from "@/service/config"

export default {
  name: "ElSelectWithPagination",
  directives: {
    loadMore: {
      bind: (el, binding) => {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        )
        SELECTWRAP_DOM.addEventListener("scroll", function () {
          const threshold = 2 // 容差像素

          const CONDITION =
            this.scrollHeight - this.scrollTop - this.clientHeight <= threshold
          if (CONDITION) {
            binding.value()
          }
        })
      }
    },
    // 自定义指令
    suffixClick: {
      bind: (el, binding) => {
        const suffixElement = el.querySelector(".el-input__suffix")
        if (suffixElement) {
          suffixElement.addEventListener("click", binding.value)
        }
      },
      unbind: (el, binding) => {
        const suffixElement = el.querySelector(".el-input__suffix")
        if (suffixElement) {
          suffixElement.removeEventListener("click", binding.value)
        }
      }
    }
  },
  props: {
    value: {
      type: [String, Number, Object],
      default: null
    },
    size: {
      type: Number,
      default: 20
    },
    placeholder: {
      type: String,
      default: "请选择"
    },
    fetchDataMethod: {
      type: Function,
      required: true
    },
    lxbm: {
      type: String,
      required: true
    },
    dimCol: {
      type: String,
      required: true
    },
    indCode: {
      type: String,
      required: true
    },
    valueKey: {
      type: String,
      default: ""
    },
    props: {
      type: Object,
      default() {
        return {
          label: "label",
          value: "value"
        }
      }
    }
  },
  data() {
    return {
      selectedValue: this.value, // 双向绑定的值
      options: [], // 存储下拉框选项
      loading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: "",
      total: 0,
      isDropdownOpen: false, // 下拉框是否打开
      ALL_DIMVALUE_CODE: config.ALL_DIMVALUE_CODE,
      noMore: false // 是否没有更多了
    }
  },
  watch: {
    wdzd: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.noMore = false // 切换时重置
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      }
    },
    // 监听父组件传入的value，保持双向绑定
    value: {
      handler(newVal) {
        this.selectedValue = newVal
        // if (
        //   !this.selectedValue.includes(config.ALL_DIMVALUE_CODE) &&
        //   this.selectedValue.length &&
        //   this.selectedValue.length === this.options.length
        // ) {
        //   this.selectedValue.unshift(config.ALL_DIMVALUE_CODE)
        // }
      },
      immediate: true
    },
    dimCol: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.noMore = false // 切换时重置
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      }
    }
  },
  created() {
    console.log("????????", this.dimCol)
    this.fetchData()
  },
  mounted() {
    let rulesDom = this.$refs["my-select"].$el.querySelector(
      ".el-input .el-input__suffix .el-input__suffix-inner .el-input__icon"
    ) // 找到dom
    rulesDom.classList.add("el-icon-arrow-up") // 对d
  },
  methods: {
    // 处理远程搜索的方法
    handleRemote(query) {
      this.options = [] // 清空列表
      this.current = 1 // 重置分页页码
      this.loading = true
      this.noMore = false // 搜索时重置
      this.query = query
      this.fetchData() // 调用父组件传入的fetchDataMethod方法
    },

    // 加载分页数据
    async fetchData() {
      if (this.noMore) return// 已经到底不再请求
      const { data } =
        await this.$httpBi.compositeIndicator.getSelectDimensionValue({
          indType: this.lxbm,
          dimCol: this.dimCol,
          indCode: this.indCode,
          pageSize: this.size,
          wdz: this.query,
          currentPage: this.current
        })
      if (data.list) {
        const list = data.list.map(item => ({
          dimCol: item.dimCol,
          dimVal: item.dimValue,
          dimValue: item.dimValue
        }))
        this.options.push(...list)
        this.total = data.totalCount
        // 判断是否到底
        if (this.options.length >= this.total) {
          this.noMore = true
        }
      } else {
        this.noMore = true
      }
      console.log(this.options, "this.options")
      this.loading = false
    },

    // 处理滚动事件
    handleScroll() {
      if (!this.loading && !this.noMore && this.options.length < this.total) {
        this.current++
        this.fetchData()
      }
    },
    selectAll() {
      // console.log(this.selectedValue,'this.selectedValue')

      // if (this.selectedValue.length < this.total) {
      //   this.selectedValue = [config.ALL_DIMVALUE_CODE]
      // } else {
      //   // 取消全选
      //   this.selectedValue = []
      // }
      if (!this.selectedValue.length) {
        return (this.selectedValue = [])
      }

      if (
        this.selectedValue.includes(config.ALL_DIMVALUE_CODE) &&
        this.selectedValue.length > 1
      ) {
        // 取消全选
        this.selectedValue = this.selectedValue.filter(
          item => item !== config.ALL_DIMVALUE_CODE
        )
      } else {
        // 全选
        this.selectedValue = [config.ALL_DIMVALUE_CODE]
      }
      this.$emit("input", this.selectedValue)
      this.$emit("change", this.selectedValue)
      // console.log(this.selectedValue, "val")

      // this.$emit("input", this.selectedValue)
      // this.$emit("change", this.selectedValue)
    },
    removeTag(val) {
      if (val === config.ALL_DIMVALUE_CODE) {
        this.selectedValue = []
        this.$emit("input", this.selectedValue)
        this.$emit("change", this.selectedValue)
      }
    },
    handleChange(val) {
      console.log(val, this.options.length, "////////////")
      if (val.includes(config.ALL_DIMVALUE_CODE)) {
        if (val.length > 1) {
          // 选了全部后又选其他选项，取消全部
          this.selectedValue = this.selectedValue.filter(
            item => item !== config.ALL_DIMVALUE_CODE
          )
        }
      } else {
        if (this.$refs["my-select"].dropdownVisible) {
          // 选了其他选项后选全部，取消其他选项只留全部
          this.selectedValue = [config.ALL_DIMVALUE_CODE]
        }
      }
      // if (val.includes(config.ALL_DIMVALUE_CODE)&& val.length - 1 < this.total) {
      //   this.selectedValue = this.selectedValue.filter(item => {
      //     return item !== config.ALL_DIMVALUE_CODE
      //   })
      // }
      console.log(this.selectedValue, "this.selectedValue")

      this.$emit("input", this.selectedValue)
      this.$emit("change", this.selectedValue)
    },
    searchStudnt() {
      // getStudentList({ query: this.addForm.query }).then(res => {
      //   const [student] = res.data
      //   this.$set(this.addForm, 'toAddDate', student.toAddDate)
      //   this.$set(this.addForm, 'modeId', student.modeId || '')
      //   this.$set(this.addForm, 'counselorName', student.counselorName)
      //   this.$set(this.addForm, 'applyDetail', student.applyDetail)
      //   this.$set(this.addForm, 'query', student.simpleStudentDetailVO.query)
      //   this.$set(this.addForm, 'studentName', student.simpleStudentDetailVO.studentName)
      //   this.$set(this.addForm, 'sex', student.simpleStudentDetailVO.sex == 1 ? '男' : '女')
      //   this.$set(this.addForm, 'className', student.simpleStudentDetailVO.className)
      //   this.$set(this.addForm, 'phone', student.simpleStudentDetailVO.phone)
      //   this.$set(this.addForm, 'deptName', student.simpleStudentDetailVO.deptName)
      //   this.$set(this.addForm, 'tutorsName', student.simpleStudentDetailVO.tutorsName)
      // })
    },
    reverseArrow(flag) {
      this.isDropdownOpen = flag
      let rulesDom = this.$refs["my-select"].$el.querySelector(
        ".el-input .el-input__suffix .el-input__suffix-inner .el-input__icon"
      ) // 找到dom
      if (flag) {
        rulesDom.classList.add("is-reverse") // 对dom新增class
      } else {
        rulesDom.classList.remove("is-reverse") // 对dom新增class
      }
    },
    handleSuffixClick() {
      if (this.isDropdownOpen) {
        // this.$refs["my-select"].close() // 关闭下拉框
        console.log("//")
      } else {
        this.$refs["my-select"].toggleMenu() // 打开下拉框
      }
    }
  }
}
</script>

<style scoped lang="scss">
/* 可根据需求自定义样式 */

::v-deep .el-select__tags {
  display: flex;
  flex-wrap: nowrap;
}
</style>
