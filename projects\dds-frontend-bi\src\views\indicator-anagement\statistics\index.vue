<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative'
    }"
  >
    <!-- 头部 -->
    <div class="layout-header">
      <div class="indicator-title">{{ title }}</div>
      <div class="tabs">
        <div
          class="tab-item"
          :class="{
            active: currentTab === item.id
          }"
          @click="currentTab = item.id"
          v-for="item in tabs"
          :key="item.id"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- 概率分布情况 -->
    <template v-if="currentTab === '1'">
      <div class="obviously-container">
        <div class="obviously-title">显著性水平最高的统计学特征</div>
        <div class="type-title">正态分布</div>
        <div class="warm-reminder">
          <svg-icon icon-class="warning" style="margin-top: 5px" />
          <div class="warm-reminder-content">
            <p>
              偏度概念：偏度衡量随机变量概率分布的不对称性，是相对于平均值不对称程度的度量，通过对偏度系数的测量，我们能够判定数据分布的不对称程度以及方向。
            </p>
            <p>
              峰度概念：是研究数据分布陡峭或者平滑的统计量，通过对峰度系数的测量，我们能够判定数据相对于正态分布而言是更陡峭还是更平缓。
            </p>
            <p @click="dialogVisible = true">正态算法模型详情了解>></p>
          </div>
        </div>
        <!-- 正态分布 -->
        <div class="normal-chart">
          <ChartNormal
            :chart-data="chartData"
            x-field="label"
            y-field="value"
            series-name=""
            y-axis-name=""
            unit=""
          />
        </div>
        <div class="distributes">
          <div class="distribute-item">
            <div class="distribute-item-num">0.5</div>
            <div class="distribute-item-title">相对于正态分布的偏度</div>
          </div>
          <div class="distribute-item">
            <div class="distribute-item-num">1.5</div>
            <div class="distribute-item-title">相对于正态分布的峰度</div>
          </div>
        </div>
      </div>
      <!-- 是否显示更多按钮 -->
      <div
        style="padding: 40px 0; text-align: center"
        v-show="!isShowMore"
        @click="isShowMore = true"
      >
        <el-button size="mini">查看更多概率分布</el-button>
      </div>
      <el-collapse-transition>
        <div class="other-container" v-show="isShowMore">
          <div class="other-title">其他统计学特征</div>
          <!-- 均匀分布 -->
          <div class="type-title">均匀分布</div>
          <div class="warm-reminder">
            <svg-icon icon-class="warning" style="margin-top: 5px" />
            <div class="warm-reminder-content">
              <p>
                在概率论和统计学中，均匀分布也叫矩形分布，它是对称概率分布，在相同长度间隔的分布概率是等可能的。
                均匀分布由两个参数a和b定义，它们是数轴上的最小值和最大值，通常缩写为U（a，b）。
              </p>
              <p @click="dialogVisible = true">均匀分布模型详情了解>></p>
            </div>
          </div>
          <div class="avg-chart">
            <ChartAvg
              :chart-data="chartData1"
              x-field="label"
              :y-field="['value', 'value1']"
              series-name=""
              y-axis-name=""
              unit=""
            />
          </div>
          <div class="formula">
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">最大值（a）</div>
            </div>
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">最大值（a）</div>
            </div>
            <div class="formula-img"></div>
          </div>
          <!-- 指数分布 -->
          <div class="type-title">指数分布</div>
          <div class="warm-reminder">
            <svg-icon icon-class="warning" style="margin-top: 5px" />
            <div class="warm-reminder-content">
              <p>
                指数分布擅长描述独立同质随机事件之间的时间间隔。它由一个单一参数λ定义，该参数称为率参数或强度参数，直观体现了单位时间内事件发生的平均速率。。
              </p>
              <p @click="dialogVisible = true">指数分布模型详情了解>></p>
            </div>
          </div>
          <div class="exponent-chart">
            <ChartExponent
              :chart-data="chartData2"
              x-field="label"
              :y-field="['value', 'value1', 'value2']"
              :series-name="['λ=0.5', 'λ=1.0', 'λ=1.5']"
              y-axis-name=""
              unit=""
            />
          </div>
          <div class="formula">
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">期望λ（单位时间内事件发生的平均速率）</div>
            </div>
            <div class="formula-img exponent"></div>
          </div>
          <!-- 拉普拉斯 -->
          <div class="type-title">拉普拉斯分布</div>
          <div class="warm-reminder">
            <svg-icon icon-class="warning" style="margin-top: 5px" />
            <div class="warm-reminder-content">
              <p>
                拉普拉斯分布又名双指数分布，以其独特的对称性和尖峰厚尾特性区别于其他常见分布，如正态分布。该分布由两个参数定义：位置参数μ（均值）和尺度参数γ（有时也称作分散参数），其概率密度函数呈现出中心对称的形态。
              </p>
              <p @click="dialogVisible = true">拉普拉斯分布模型详情了解>></p>
            </div>
          </div>
          <div class="lapras-chart">
            <ChartLapras
              :chart-data="chartData2"
              x-field="label"
              :y-field="['value', 'value1', 'value2']"
              :series-name="['λ=0.5', 'λ=1.0', 'λ=1.5']"
              y-axis-name=""
              unit=""
            />
          </div>
          <div class="formula">
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">位置参数（μ）为</div>
            </div>
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">尺度参数（b）为</div>
            </div>
            <div class="formula-img lapras"></div>
          </div>

          <div class="type-title">伽马分布</div>
          <div class="warm-reminder">
            <svg-icon icon-class="warning" style="margin-top: 5px" />
            <div class="warm-reminder-content">
              <p>
                伽马分布适用于描述非负实数变量的概率分布情况。它由两个正实数参数定义：形状参数α（alpha）和尺度参数β（beta），记作Γ(α,
                β)。伽马分布最早由Adolphe
                Quetelet在研究人类生存时间时提出，并在后续的统计理论和应用中扮演了重要角色。
              </p>
              <p @click="dialogVisible = true">伽马分布模型详情了解>></p>
            </div>
          </div>
          <div class="gamma-chart">
            <ChartGamma
              :chart-data="chartData2"
              x-field="label"
              :y-field="['value', 'value1', 'value2']"
              :series-name="['λ=0.5', 'λ=1.0', 'λ=1.5']"
              y-axis-name=""
              unit=""
            />
          </div>
          <div class="formula">
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">形状参数α（alpha）为</div>
            </div>
            <div class="num-item">
              <div class="num">1.2</div>
              <div class="title">尺度参数β（beta）为</div>
            </div>
            <div class="formula-img gamma"></div>
          </div>
        </div>
      </el-collapse-transition>
    </template>
    <!-- 数据异常情况 -->
    <div class="data-exception" v-else>
      <div class="data-exception-title">数据异常情况</div>
      <div class="type-title">箱型图</div>
      <div class="warm-reminder">
        <svg-icon icon-class="warning" style="margin-top: 5px" />
        <div class="warm-reminder-content">
          <p>
            箱型图（又称盒须图、箱线图）是一种用于展示一组数据分布情况的统计图表，尤其擅长于识别数据中的异常值（outliers）和极端值（extreme
            values）。
          </p>
        </div>
      </div>
      <div class="boxplot-chart">
        <ChartBoxplot
          :chart-data="chartData1"
          x-field="label"
          :y-field="['value', 'value1']"
          series-name=""
          y-axis-name=""
          unit=""
        />
      </div>
      <div class="type-title">箱型图构成</div>
      <p>
        1. 下四分位数（Q1，第一四分位数）：数据中所有数值中最小的75%数值的上限。
      </p>
      <p>
        2. 上四分位数（Q3，第三四分位数）：数据中所有数值中最小的25%数值的下限。
      </p>
      <p>3. 中位数（Median）：数据集中间的数值，即Q2，位于Q1和Q3之间。</p>
      <p>4. 箱子（Box）：由Q1到Q3的区间构成，代表数据的中心50%分布范围。</p>
      <p>
        5.须（Whiskers）：延伸自箱子两端，通常定义为箱子外侧到数据范围的1.5倍四分位距（Interquartile
        Range, IQR = Q3 - Q1）处。超出此范围的点视为异常值。
      </p>
      <p>6. 异常点（Outliers）：落在须之外的数据点，被认为是异常值。</p>
      <p>
        7. 极端值（Far
        Outliers）：某些定义中，比异常值更远离箱子的点，有时标记为星号或其他特殊符号。
      </p>
      <div class="formula-img"></div>
      <div class="type-title">识别异常数据的步骤</div>
      <p>1. 计算四分位数：首先计算数据集的Q1、Q3以及中位数。</p>
      <p>2. 确定四分位距（IQR）：IQR = Q3 - Q1。</p>
      <p>3. 绘制箱型图：根据上述统计量绘制箱型图，包括箱子、须和中位数。</p>
      <p>
        4. 确定异常值范围：异常值通常定义为低于Q1 - 1.5 * IQR或高于Q3 + 1.5 *
        IQR的数据点。
      </p>
      <p>
        5.
        识别异常值：在箱型图中，超出须范围的点被视为异常值。这些点可能单独标记出来，或者以不同的颜色或符号显示。
      </p>
    </div>
  </DT-View>
</template>

<script>
import ChartNormal from "./charts/ChartNormal.vue"
import ChartAvg from "./charts/ChartAvg.vue"
import ChartExponent from "./charts/ChartExponent.vue"
import ChartLapras from "./charts/ChartLapras.vue"
import ChartGamma from "./charts/ChartGamma.vue"
import ChartBoxplot from "./charts/ChartBoxplot.vue"
export default {
  components: {
    ChartNormal,
    ChartAvg,
    ChartExponent,
    ChartLapras,
    ChartGamma,
    ChartBoxplot
  },
  props: {},
  data() {
    return {
      title: "各院系在校生人数分布",
      currentTab: "1",
      isShowMore: true, // 是否显示更多
      tabs: [
        {
          name: "概率分布情况",
          id: "1"
        },
        {
          name: "数据异常情况",
          id: "2"
        }
      ],
      chartData: [
        {
          label: "μ-3σ",
          value: 0
        },
        {
          label: "μ-2σ",
          value: 0.1
        },
        {
          label: "μ-1σ",
          value: 0.2
        },
        {
          label: "μ",
          value: 0.25
        },
        {
          label: "μ+1σ",
          value: 0.2
        },
        {
          label: "μ+2σ",
          value: 0.1
        },
        {
          label: "μ+3σ",
          value: 0
        }
      ],
      chartData1: [
        {
          label: "a",
          value: 3,
          value1: 0
        },
        {
          label: "b",
          value: 3,
          value1: 0
        }
      ],
      chartData2: [
        {
          label: "0",
          value: 1.5,
          value1: 1.2,
          value2: 1.3
        },
        {
          label: "1",
          value: 0.9,
          value1: 1.0,
          value2: 0.9
        },
        {
          label: "2",
          value: 0.3,
          value1: 0.5,
          value2: 0.6
        },
        {
          label: "3",
          value: 0.3,
          value1: 0.3,
          value2: 0.4
        },
        {
          label: "4",
          value: 0.1,
          value1: 0.2,
          value2: 0.3
        },
        {
          label: "5",
          value: 0,
          value1: 0,
          value2: 0
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {
    // 准备数据
    var data = this.generateData()
    console.log(data)
  },
  watch: {},
  methods: {
    func(x) {
      x /= 10
      return Math.sin(x) * Math.cos(x * 2 + 1) * Math.sin(x * 3 + 2) * 50
    },
    generateData() {
      let data = []
      for (let i = 0; i <= 10; i += 0.1) {
        data.push([i, this.func(i)])
      }
      return data
    }
  }
}
</script>

<style scoped lang="scss">
.layout-header {
  width: 100%;
  height: 56px;
  box-shadow: inset 0px -1px 0px 0px #ebedf0;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
  .indicator-title {
    line-height: 56px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    text-align: left;
    font-style: normal;
    box-sizing: border-box;
  }
  .tabs {
    display: flex;
    align-items: center;
    height: 56px;
    .tab-item {
      padding: 0px 12px;
      height: 30px;
      border-radius: 15px;
      border: 1px solid #dcdfe6;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #606266;
      line-height: 30px;
      text-align: center;
      margin-left: 8px;
      cursor: pointer;
      &.active {
        background: rgba(21, 99, 255, 0.1);
        color: #1563ff;
        font-weight: 500;
      }
    }
  }
}
.obviously-container {
  padding: 32px 20px 0px;
  box-sizing: border-box;
  .obviously-title {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2f3338;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .type-title {
    position: relative;
    margin-top: 24px;
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    padding-left: 8px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 14px;
      background: #1563ff;
    }
  }
  .warm-reminder {
    position: relative;
    width: 100%;
    height: 98px;
    background: #fffbe6;
    border-radius: 2px;
    border: 1px solid #fff1b8;
    margin: 16px auto 24px;
    display: flex;
    padding: 12px 17px 0;
    box-sizing: border-box;
    .warm-reminder-content {
      margin-left: 8px;
      p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
        height: 22px;
      }
      p:last-child {
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1563ff;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-decoration-line: underline;
        margin-top: 8px;
        cursor: pointer;
      }
    }
    .el-icon-close {
      position: absolute;
      right: 12px;
      top: 12px;
      cursor: pointer;
    }
  }
  .normal-chart {
    width: 480px;
    margin: 46px auto;
    height: 307px;
    // margin: ;
  }
  .distributes {
    display: flex;
    margin-top: 40px;
    .distribute-item {
      width: calc(50% - 12px);
      padding: 40px;
      box-sizing: border-box;
      height: 157px;
      border-radius: 8px;
      margin-right: 24px;
      background: #f5f7fa url("~@/assets/images/fd.png") no-repeat top 32px
        right 39px;

      .distribute-item-num {
        height: 40px;
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 40px;
        color: #389cff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .distribute-item-title {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #2f3338;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 14px;
      }
      &:last-child {
        margin-right: 0;
        background: #f5f7fa url("~@/assets/images/pd.png") no-repeat top 32px
          right 39px;
      }
    }
  }
}

.other-container {
  padding: 64px 20px 0px;
  box-sizing: border-box;
  .other-title {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2f3338;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .type-title {
    position: relative;
    margin-top: 24px;
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    padding-left: 8px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 14px;
      background: #1563ff;
    }
  }
  .warm-reminder {
    position: relative;
    width: 100%;
    background: #fffbe6;
    border-radius: 2px;
    border: 1px solid #fff1b8;
    margin: 16px auto 24px;
    display: flex;
    padding: 12px 17px 12px;
    box-sizing: border-box;
    .warm-reminder-content {
      margin-left: 8px;
      p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
      }
      p:last-child {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1563ff;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-decoration-line: underline;
        margin-top: 8px;
        cursor: pointer;
      }
    }
    .el-icon-close {
      position: absolute;
      right: 12px;
      top: 12px;
      cursor: pointer;
    }
  }
  .avg-chart {
    width: 394px;
    margin: 55px auto;
    height: 290px;
  }
  .exponent-chart {
    width: 381px;
    margin: 67px auto;
    height: 266px;
  }

  .lapras-chart {
    width: 444px;
    margin: 53px auto;
    height: 294px;
  }

  .gamma-chart {
    width: 444px;
    margin: 53px auto;
    height: 292px;
  }
  .formula {
    width: 100%;
    height: 157px;
    background: #f5f7fa;
    border-radius: 8px;
    margin-top: 55px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px;
    box-sizing: border-box;
    margin-bottom: 40px;
    .num-item {
      padding-left: 40px;
      .num {
        height: 40px;
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 40px;
        color: #389cff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .title {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #2f3338;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 14px;
      }
    }

    .formula-img {
      width: 264px;
      height: 93px;
      background: url("~@/assets/images/jyfb.png") no-repeat;
      &.exponent {
        background: url("~@/assets/images/zsfb.png") no-repeat;
      }
      &.lapras {
        background: url("~@/assets/images/lpls.png") no-repeat;
      }
      &.gamma {
        background: url("~@/assets/images/gamma.png") no-repeat;
      }
    }
  }
}
.data-exception {
  padding: 32px 20px 48px;
  box-sizing: border-box;
  .data-exception-title {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2f3338;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .type-title {
    position: relative;
    margin-top: 24px;
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    padding-left: 8px;
    margin-bottom: 16px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 14px;
      background: #1563ff;
    }
  }
  .warm-reminder {
    position: relative;
    width: 100%;
    background: #fffbe6;
    border-radius: 2px;
    border: 1px solid #fff1b8;
    margin: 16px auto 24px;
    display: flex;
    padding: 12px 17px 12px;
    box-sizing: border-box;
    .warm-reminder-content {
      margin-left: 8px;
      p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #323233;
        line-height: 22px;
      }
    }
    .el-icon-close {
      position: absolute;
      right: 12px;
      top: 12px;
      cursor: pointer;
    }
  }
  .boxplot-chart {
    width: 601px;
    height: 300px;
    margin: 50px auto;
  }
  p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #646566;
    line-height: 28px;
  }
  .formula-img {
    margin: 16px 0px 40px;
    width: 1128px;
    height: 606px;
    background: url("~@/assets/images/xxt.png") no-repeat;
    background-size: contain;
  }
}
</style>
