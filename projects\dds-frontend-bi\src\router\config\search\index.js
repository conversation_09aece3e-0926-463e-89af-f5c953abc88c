import { prefix } from "../../prefix"
import Search from "@/views/search"

export default [
  {
    name: "search",
    component: Search,
    path: prefix + "/search",
    meta: {
      title: "综合查询"
    }
  },
  {
    name: "SearchList",
    component: () => import("@/views/search/SearchList"),
    path: prefix + "/searchList",
    meta: {
      title: "综合查询"
    }
  },
  {
    name: "appDetail",
    component: () => import("@/views/search/ViewData/Detail"),
    path: prefix + "/appDetail1",
    meta: {
      title: "应用指标详情"
    }
  },
  {
    name: "appDetail",
    component: () => import("@/views/search/app-indicators/indicator-detail"),
    path: prefix + "/appDetail",
    meta: {
      title: "应用指标详情"
    }
  },
  // 原子指标
  {
    name: "atomDetail",
    component: () => import("@/views/search/Source/Detail"),
    path: prefix + "/atomDetail",
    meta: {
      title: "原子指标详情"
    }
  },
  {
    name: "searchDetail",
    component: () => import("@/views/search/detail"),
    path: prefix + "/searchDetail",

    meta: {
      title: "搜索详情"
    }
  }
]
