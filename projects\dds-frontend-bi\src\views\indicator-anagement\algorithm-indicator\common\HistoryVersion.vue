<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px'
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">历史版本</div>
      <i
        class="el-icon-close"
        style="margin-left: auto"
        @click="$router.go(-1)"
      ></i>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="editor-pane">
        <MonacoEditor
          ref="MonacoEditor"
          :init-value.sync="form.sql"
          @mouseup="getSelectCode"
          :hint-data="hintData"
          height="100%"
          language="python"
        />
      </div>
      <div class="config-pane">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="date" label="编辑时间"></el-table-column>
          <el-table-column prop="name" label="创建人"></el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '2'">预览中</span>
              <template v-else>
                <el-button type="text" size="small">还原</el-button>

                <el-button
                  type="text"
                  size="small"
                  @click="handlePreview(scope.row)"
                >
                  预览
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer-btn">
        <el-button >
          退出并恢复为进入前状态
        </el-button>
        <el-button type="success" @click="handleRunSql">试计算</el-button>
        <el-button type="primary" @click="nextStep">保存当前算法</el-button>
      </div>
    </div>
  </DT-View>
</template>

<script>
import Request from "@/service"
// import encryptSql from "@/utils/encryptSql.js"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import MonacoEditor from "@/components/MonacoEditor"
import { exportExcel } from "@/utils"

export default {
  components: { MonacoEditor },

  props: {},
  data() {
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = this.form.tableData1.filter(
        item => item.fieldNameModified === value
      )
      if (arr.length > 1) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
      const { data } = await Request.api.paramPost(
        "SqlIndicator/existByNameAndType",
        {
          name: value,
          type: arr[0].tagType,

          dimensions: this.form.tableData1
            .filter(e => e.tagType === "派生维度")
            .map(item => item.fieldNameModified)
        }
      )

      if (data) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
    }
    return {
      isSelectSQL: false, // 是否选中sql
      activeId: 1,
      sourcesList: [
        {
          updateTime: null,
          id: 39,
          name: "112.60-dds_platform_dev",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 38,
          name: "ods",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 37,
          name: "演示数据源",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 30,
          name: "演示配置流程库",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 18,
          name: "112.60-dds_platform_3.0.0",
          description: "112.60-dds_platform_3.0.0",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 6,
          name: "113.8库",
          description: "达芬奇",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 5,
          name: "112.60库/dds-platform",
          description: "112.60库dds-platform",
          type: "jdbc"
        },
        {
          updateTime: "2022-02-25 16:32:25",
          id: 2,
          name: "nbda",
          description: "oracleNbda数据库",
          type: "jdbc"
        },
        {
          updateTime: "2022-01-26 10:18:07",
          id: 1,
          name: "dds-platform",
          description: "数字桌面演示数据库",
          type: "jdbc"
        }
      ], // 数据源列表
      form: {
        sourceId: 27, // 数据源id
        sql: `from datetime import datetime

def days_between_dates(date1, date2):
    date_format = "%Y-%m-%d"
    d1 = datetime.strptime(date1, date_format)
    d2 = datetime.strptime(date2, date_format)
    = d2 - d1
    return.days

# 示例用法
date1 = "2025-05-01"
date2 = "2025-05-14"
print(f"Days between: {days_between_dates(date1, date2)} days")`,
        variables: [],
        tableData1: []
      },
      rules: {
        fieldNameModified: [
          {
            validator: changeFieldNameModified,
            trigger: "blur",
            required: true
          }
        ]
      },
      dbs: [],
      tableData: [
        {
          date: "2025-05-11 21:23:30",
          name: "admin",
          status: "1", // 1还原//2预览中//3预览
          code: `import csv

def analyze_csv(file_name):
    with open(file_name, mode='r') as file:
        csv_reader = csv.reader(file)
        headers = next(csv_reader)
        print(f"Headers: {headers}")
        data = [row for row in csv_reader]
        print(f"Total rows: {len(data)}")
        return data

# 示例用法
file_name = 'data.csv'
data = analyze_csv(file_name)`
        },
        {
          date: "2025-05-06 21:23:30",
          name: "admin",
          status: "2",
          code: `from datetime import datetime

def days_between_dates(date1, date2):
    date_format = "%Y-%m-%d"
    d1 = datetime.strptime(date1, date_format)
    d2 = datetime.strptime(date2, date_format)
    = d2 - d1
    return.days

# 示例用法
date1 = "2025-05-01"
date2 = "2025-05-14"
print(f"Days between: {days_between_dates(date1, date2)} days")`
        },
        {
          date: "2025-05-05 21:23:30",
          name: "admin",
          status: "3",
          code: `class Student:
    def __init__(self, name, age, grade):
        self.name = name
        self.age = age
        self.grade = grade

class StudentManager:
    def __init__(self):
        self.students = []

    def add_student(self, name, age, grade):
        new_student = Student(name, age, grade)
        self.students.append(new_student)

    def display_students(self):
        for student in self.students:
            print(f"Name: {student.name}, Age: {student.age}, Grade: {student.grade}")`
        },
        {
          date: "2025-05-03 21:23:30",
          name: "admin",
          status: "3",
          code: `def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# 示例用法
numbers = [64, 25, 12, 22, 11]
sorted_numbers = bubble_sort(numbers)
print(f"Sorted list: {sorted_numbers}")`
        },
        {
          date: "2025-05-01 21:23:30",
          name: "admin",
          status: "3",
          code: `def factorial(n):
    if n == 0 or n == 1:
        return 1
    return n * factorial(n-1)

# 示例用法
num = 5
result = factorial(num)
print(f"The factorial of {num} is: {result}")`
        }
      ],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableColumns: [],
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf"
      },
      tables: [],
      columns: [],
      isFull: false, // 是否全屏
      copySql: "", // 备份sql
      sqlInfo: {}, // 保存的sql信息
      indCode: "",
      numericFields: [], // 数值字段

      code: '# 默认示例代码\nprint("Hello World")',
      cmOptions: {
        tabSize: 4,
        mode: "text/x-python",
        theme: "material-darker",
        lineNumbers: true,
        line: true,
        readOnly: false
      },
      algorithm: {
        name: "",
        description: ""
      },
      parameters: []
    }
  },
  computed: {
    hintData() {
      return [
        ...this.tables.map(item => item.name),
        ...this.columns.map(item => item.name)
      ]
    },
    isNewSql() {
      console.log(this.copySql, "this.copySql")
      console.log(this.form.sql, "this.form.sql")

      return this.indCode
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handlePreview(row) {
      this.form.sql = row.code
      this.tableData.forEach(element => {
        element.status = "1"
      })
      row.status = "2"

      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue()
      })
      console.log(this.form.sql, "this.form.sql")
    },
    // 获取sql详情
    async getSqlDetail() {
      const { data } = await Request.api.paramPostQuery(
        "/SqlIndicator/getSqlIndicatorByIndCode",
        {
          indCode: this.indCode
        }
      )
      this.form.sql = data.sqlStatement
      this.copySql = data.sqlStatement
      this.sqlInfo = data
      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue()
        this.handleRunSql()
      })
    },

    // 全部导出
    handleAllExport() {
      // 导出
      exportExcel("/api/dds-server-bi/SqlIndicator/exportAllData", {
        sql: this.form.sql
      })
    },
    async handleSave() {
      let params = this.form.tableData1.map(item => {
        return {
          ...item,
          unitName: item.unitName === "其他" ? item.diydw : item.unitName
        }
      })
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/SqlIndicator/save",
            params
          )
          this.$message.success(data)
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {}
          })
        }
      })
    },
    // 初始化数据源
    async initSource() {
      const { data } = await Request.view.getSources()
      this.sourcesList = data
    },
    // 数据源库
    async sourceDb() {
      this.dbs = []
      if (this.form.sourceId) {
        Request.view
          .getDatabases({ id: this.form.sourceId })
          .then(res => {
            for (let i = 0; i < res.data.length; i++) {
              this.dbs.push({ name: res.data[i], type: "DB" })
            }
          })
          .catch(() => {})
      }
      console.log(this.dbs)
    },
    // 树节点加载
    loadNode(node, resolve) {
      // 第一层库
      if (node.level === 0) {
        return resolve(this.dbs)
      }
      // 第二层表
      if (node.level === 1) {
        Request.view
          .getTables({ id: this.form.sourceId, dbName: node.data.name })
          .then(res => {
            this.tables = this.tables.concat(res.data.tables)
            return resolve(res.data.tables)
          })
          .catch(() => {})
      }
      if (node.level === 2) {
        Request.view
          .getColumns({
            id: this.form.sourceId,
            dbName: node.parent.data.name,
            tableName: node.data.name
          })
          .then(res => {
            this.columns = this.columns.concat(res.data.columns)
            return resolve(
              res.data.columns.map(item => ({
                ...item,
                leaf: true
              }))
            )
          })
          .catch(() => {})
      }
      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    getSelectCode() {
      if (this.$refs.MonacoEditor.getSelectionVal()) {
        this.isSelectSQL = true
      } else {
        this.isSelectSQL = false
      }
    },
    // 执行slq
    async handleRunSql(callback) {
      if (this.form.sql.trim() === "") {
        this.$message.warning("请输入sql")
        return
      }
      if (!this.form.sourceId) {
        this.$message.warning("请选择数据源")
        return
      }
      const params = {
        sql: this.form.sql,
        page: this.page.currentPage,
        pageSize: this.page.pageSize
      }

      // if (this.isSelectSQL) {
      //   params.sql = this.$refs.MonacoEditor.getSelectionVal()
      // }
      // 加密sql
      // params.sql = encryptSql.encrypt(params.sql)
      const { data } = await Request.api.paramPost(
        "/SqlIndicator/tryExecute",
        params
      )
      if (data.error) {
        this.tableColumns = []
        this.tableData = []
        this.page.total = 0
        return this.$message.error(data.error)
      }

      if (data.data.length) {
        const res = await Request.api.paramPost(
          "/SqlIndicator/classifyFields",
          data.data
        )
        // 获取数值字段
        this.numericFields = res.data.numericFields || []

        // 设置表头
        this.tableColumns = Object.keys(data.data[0]).map(item => ({
          label: item,
          prop: item,
          visible: true,
          sortable: false
        }))
      } else {
        this.tableColumns = []
      }
      console.log(this.tableColumns, "thistableColumns")
      this.tableData = data.data || []
      this.page.total = data.total
      console.log(callback, "callback")
      if (typeof callback === "function") {
        callback()
      }
    },
    handleExportExcel(selection) {
      console.log(selection, "selection")

      jsonToSheetXlsx({
        data: selection,
        filename: "表数据" + new Date().getTime()
      })
    },
    // 下一步
    nextStep() {
      this.handleRunSql(() => {
        console.log("执行回到")
        this.activeId = 2
        if (this.indCode) {
          this.form.tableData1 = [this.sqlInfo]
        } else {
          this.form.tableData1 = this.tableColumns.map(item => ({
            fieldName: item.prop,
            fieldNameModified: item.prop,
            tagType: "指标",
            period: "1",
            scopeId: 0,
            precision: null,
            rounding: "否",
            thresholdMin: null,
            thresholdMax: null,
            unitName: null,
            description: "",
            tagsName: [],
            createdById: null,
            indCode: null,
            sqlStatementOrigin: this.form.sql
          }))
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  i{
    font-size: 24px;
    cursor: pointer;
    &:hover{
      color: #1563ff;
    }
  }
  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }
  .steps {
    width: 150px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;
    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;
      &.active {
        background: #1563ff;
      }
    }
    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;
      &.active {
        color: #1563ff;
      }
      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}
.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.editor-content {
  width: 100%;
  display: flex;

  background-color: #f0f2f5;
  box-sizing: border-box;

  position: relative;
  padding: 20px;
  min-height: calc(100vh - 230px);

  background: #fff;
  padding-bottom: 52px;
  display: flex;

  .editor-pane {
    width: calc(100% - 540px);
    padding: 20px;
    background: #2d2d2d;
  }

  .config-pane {
    flex: 0 0 520px;
    overflow-y: auto;
    margin-left: 20px;
  }

  .footer-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 52px;
    padding-right: 24px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
  }
}
.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}
#project_frame {
  .el-tree-node:not(.is-expanded)
    > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }
  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}
#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
