<template>
  <DT-View :inner-style="{ background: '#f0f2f5', padding: 0 }">
    <template v-if="mainShow">
      <ModuleOne />
      <ModuleTwo />
      <ModuleThree @handleDetail="handleDetail" />
    </template>
    <DetailView
      v-else
      @handleBack="mainShow = true"
      :title="detailInfo.indName"
      :ind-code="detailInfo.indCode"
    />
  </DT-View>
</template>

<script>
import Request from "@/service"
import ModuleOne from "./components/ModuleOne.vue"
import ModuleTwo from "./components/ModuleTwo.vue"
import ModuleThree from "./components/ModuleThree.vue"
import DetailView from "./DetailView.vue"
export default {
  components: {
    ModuleOne,
    ModuleTwo,
    ModuleThree,
    DetailView
  },
  data() {
    return {
      mainShow: true,
      data: {
        unresolvedIndicatorNum: 0,
        unresolvedExceptionNum: 0,
        recentOneMonthIndicatorNum: 0,
        recentOneMonthExceptionNum: 0,
        recentOneMonthResolvedNum: 0,
        recentSixMonthExceptionTrend: []
      },
      detailInfo: {}
    }
  },
  provide() {
    return {
      parent: this
    }
  },
  created() {
    this.getData()
  },
  methods: {
    async getData() {
      const { data } = await Request.api.paramPost("zeroWarn/getPlatformReport")
      console.log(data)
      this.data = data
    },
    handleDetail(row) {
      this.detailInfo = row
      this.mainShow = false
    }
  }
}
</script>

<style scoped lang="scss"></style>
