import renderBubbleMap from "./mapBubble"
export default function (chartData) {
  const {
    metrics,
    cols,
    data,
    chartStyles: { label, spec, visualMap }
  } = chartData
  // 获取data数组对象中value属性最大的值
  let maxValue = 0
  data.forEach(item => {
    if (item[`${metrics[0].agg}(${metrics[0].displayName})`] > maxValue) {
      maxValue = item[`${metrics[0].agg}(${metrics[0].displayName})`]
    }
  })
  maxValue = Math.floor(maxValue)
  // 气泡图
  if (spec.layerType === "scatter" || spec.layerType === "heatmap") {
    console.log(renderBubbleMap(chartData, spec.layerType, cols), "方法")
    return renderBubbleMap(chartData, spec.layerType, cols)
  } else {
    return {
      dataset: {
        dimensions: [
          cols[0].displayName,
          `${metrics[0].agg}(${metrics[0].displayName})`
        ],
        source: data.map(item => ({
          ...item,
          [cols[0].displayName]: getProvinceName(item[cols[0].displayName])
        }))
      },
      tooltip: {
        // ...TOOLTIP_STYLE,
        // trigger: "item",
        formatter: params => {
          const { name, value, color } = params
          const tooltipLabels = []
          if (color) {
            let circle = `<span  style="background:${params.color};width:10px;height:10px;border-radius:50%;display:inline-block;margin-right: 5px;"></span>`
            tooltipLabels.push(circle)
          }
          tooltipLabels.push(name)
          tooltipLabels.push(value)
          return tooltipLabels.join("")
        }
      },
      visualMap: {
        show: visualMap.showVisualMap,
        min: 0,
        max: maxValue,
        realtime: false,
        calculable: true,
        ...getPosition(visualMap.visualMapPosition),
        inRange: {
          color: [visualMap.startColor, visualMap.endColor]
        },
        itemWidth: visualMap.visualMapWidth,
        itemHeight: visualMap.visualMapHeight,
        textStyle: {
          color: visualMap.fontColor,
          fontFamily: visualMap.fontFamily,
          fontSize: visualMap.fontSize
        },
        orient: visualMap.visualMapDirection
      },
      series: {
        type: "map",
        zoom: 1.1,
        selectedMode: false,
        roam: spec.roam,
        map: spec.areaCode,
        // label: {
        //   show: true
        // },
        label: {
          show: label.showLabel,
          color: label.labelColor,
          fontFamily: label.labelFontFamily,
          fontSize: label.labelFontSize
        },
        silent: !spec.isHighlight,

        itemStyle: {
          areaColor: spec.areaColor,
          borderColor: spec.borderColor,
          borderWidth: spec.isShowBorder ? 1 : 0, // 设置外层边框
          emphasis: {
            areaColor: spec.highlightareaColor,
            borderColor: spec.highlightborderColor,
            label: {
              color: spec.highlightLabelColor
            }
          }
        }
      }
    }
  }
}
function getPosition(position) {
  let positionValue
  switch (position) {
    case "leftBottom":
      positionValue = {
        left: "left",
        top: "bottom"
      }
      break
    case "leftTop":
      positionValue = {
        left: "left",
        top: "top"
      }
      break
    case "rightTop":
      positionValue = {
        left: "right",
        top: "top"
      }
      break
    case "rightBottom":
      positionValue = {
        left: "right",
        top: "bottom"
      }
      break
  }
  return positionValue
}
const provinceSuffix = [
  "省",
  "市",
  "维吾尔自治区",
  "回族自治区",
  "壮族自治区",
  "特别行政区",
  "自治区"
]
function getProvinceName(name) {
  provinceSuffix.forEach(ps => {
    if (name.includes(ps)) {
      name = name.replace(ps, "")
    }
  })
  return name
}
