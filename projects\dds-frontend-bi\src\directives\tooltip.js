import Vue from "vue"

Vue.directive("tooltip-content", {
  bind(el, binding) {
    const maxWidth = binding.value
      ? typeof binding.value === "number"
        ? binding.value + "px"
        : binding.value
      : "120px"
    el.style.display = "inline-block"
    el.style.maxWidth = maxWidth
    el.style.overflow = "hidden"
    el.style.textOverflow = "ellipsis"
    el.style.whiteSpace = "nowrap"

    function showTooltip() {
      if (el.scrollWidth > el.offsetWidth) {
        const tooltip = document.createElement("div")
        tooltip.className = "el-tooltip__popper is-dark"
        tooltip.style.position = "fixed"
        tooltip.style.zIndex = 9999
        tooltip.style.padding = "8px 12px"
        tooltip.style.background = "#303133"
        tooltip.style.color = "#fff"
        tooltip.style.borderRadius = "4px"
        tooltip.style.fontSize = "12px"
        tooltip.style.boxShadow = "0 2px 12px 0 rgba(0,0,0,0.1)"
        tooltip.style.pointerEvents = "none"
        tooltip.innerText = el.innerText
        document.body.appendChild(tooltip)
        const rect = el.getBoundingClientRect()
        let left = rect.left
        if (left + tooltip.offsetWidth > window.innerWidth) {
          left = window.innerWidth - tooltip.offsetWidth - 8
        }
        tooltip.style.top = rect.bottom + 4 + "px"
        tooltip.style.left = left + "px"
        el._tooltip = tooltip
      }
    }
    function hideTooltip() {
      if (el._tooltip) {
        document.body.removeChild(el._tooltip)
        el._tooltip = null
      }
    }
    el.addEventListener("mouseenter", showTooltip)
    el.addEventListener("mouseleave", hideTooltip)
    el._hideTooltip = hideTooltip
  },
  unbind(el) {
    el.removeEventListener("mouseenter", el._showTooltip)
    el.removeEventListener("mouseleave", el._hideTooltip)
    if (el._tooltip) {
      document.body.removeChild(el._tooltip)
      el._tooltip = null
    }
  }
})
Vue.directive("select-tag-tooltip", {
  inserted(el, binding) {
    const maxWidth = binding.value
      ? typeof binding.value === "number"
        ? binding.value + "px"
        : binding.value
      : "120px"
    // 监听tag变化
    const observer = new MutationObserver(() => {
      const tags = el.querySelectorAll(".el-select__tags-text, .custom-tag")
      tags.forEach(tag => {
        tag.style.maxWidth = maxWidth
        tag.style.overflow = "hidden"
        tag.style.textOverflow = "ellipsis"
        tag.style.whiteSpace = "nowrap"
        // 避免重复绑定
        if (!tag._hasTooltip) {
          tag._hasTooltip = true
          tag.addEventListener("mouseenter", function () {
            // 只在内容溢出时显示tooltip
            if (tag.scrollWidth > tag.offsetWidth) {
              const tooltip = document.createElement("div")
              tooltip.className = "el-tooltip__popper is-dark"
              tooltip.style.position = "fixed"
              tooltip.style.zIndex = 9999
              tooltip.style.padding = "8px 12px"
              tooltip.style.background = "#303133"
              tooltip.style.color = "#fff"
              tooltip.style.borderRadius = "4px"
              tooltip.style.fontSize = "12px"
              tooltip.style.boxShadow = "0 2px 12px 0 rgba(0,0,0,0.1)"
              tooltip.style.pointerEvents = "none"
              tooltip.innerText = tag.innerText
              document.body.appendChild(tooltip)
              const rect = tag.getBoundingClientRect()
              // 优化：防止tooltip超出屏幕
              let left = rect.left
              if (left + tooltip.offsetWidth > window.innerWidth) {
                left = window.innerWidth - tooltip.offsetWidth - 8
              }
              tooltip.style.top = rect.bottom + 4 + "px"
              tooltip.style.left = left + "px"
              tag._tooltip = tooltip
            }
          })
          tag.addEventListener("mouseleave", function () {
            if (tag._tooltip) {
              document.body.removeChild(tag._tooltip)
              tag._tooltip = null
            }
          })
        }
      })
    })
    observer.observe(el, { childList: true, subtree: true })
    el._tagTooltipObserver = observer
  },
  unbind(el) {
    if (el._tagTooltipObserver) {
      el._tagTooltipObserver.disconnect()
      el._tagTooltipObserver = null
    }
    // 清理所有tooltip
    const tags = el.querySelectorAll(".el-select__tags-text, .custom-tag")
    tags.forEach(tag => {
      if (tag._tooltip) {
        document.body.removeChild(tag._tooltip)
        tag._tooltip = null
      }
    })
  }
})
