<template>
  <div class="data-config-section">
    <h4>
      {{ title }}
    </h4>
    <div class="chart-draggable-container">
      <draggable
        :group="{
          name: 'itxst',
          put: (e) => draggablePull(type), //是否允许拖入当前组
        }"
        animation="600"
        class="draggable-wrapper"
        v-model="fieldList"
      >
        <transition-group class="draggable-group">
          <template v-for="(field, index) in fieldList">
            <ChartDraggableElement
              @onItemRemove="onItemRemove"
              :draggable-element="field"
              :key="index"
              v-bind="$attrs"
            />
          </template>
        </transition-group>
      </draggable>

      <div class="empty" v-if="fieldList.length === 0">
        <span
        >请拖拽<i>{{ formatType(type) }}</i
        >字段到此处</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import ChartDraggableElement from "../ChartDraggable/ChartDraggableElement.vue"
export default {
  components: { draggable, ChartDraggableElement },
  props: {
    title: {
      type: String,
      default: "",
    },
    
    // 图表配置参数的key
    type: {
      type: String,
      default: "",
    },
    // 图表配置参数的label
    label: {
      type: String,
      default: "",
    },
    // 图表配置参数
    widgetParams: {
      type: Object,
      default: () => ({}),
    },
    // 当前拖拽字段类型
    currentDraggableType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {}
  },
  computed: {
    // 拖拽的数据
    fieldList: {
      get() {
        if (!this.label || !this.widgetParams[this.label]) return []
        return [ ...this.widgetParams[this.label] ]
      },
      set(newVal) {
        console.log(
          "%cBaseDataConfigSection.vue line:76 object",
          "color: #007acc;",
          newVal
        )
        this.$emit("draggableChange", this.label, newVal)
      },
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 是否允许拖入当前组
    draggablePull(type) {
      if (type === "all") return true
      if (this.currentDraggableType === type) {
        return true
      } else {
        return false
      }
    },
    // 字段名称
    formatType(type) {
      if (type === "all") return "任意"
      if (type === "category") return "分类"
      if (type === "value") return "数值"
    },
    // 删除字段
    onItemRemove(item) {
      this.fieldList = this.fieldList.filter((field) => field.id !== item.id)
    },
  },
}
</script>

<style scoped lang="scss">
.data-config-section {
  margin-top: 10px;
  h4 {
    color: rgba(64, 64, 64, 0.6509803921568628);
    line-height: 18px;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }
  .chart-draggable-container {
    position: relative;
    width: 100%;
    min-height: 60px;
    border: 1px dashed #ccc;
    padding: 10px;
    box-sizing: border-box;
    display: block;
    .draggable-wrapper {
      width: 100%;
      height: 100%;
      min-height: 60px;
    }
    .draggable-group {
      display: block;
      width: 100%;
      min-height: 60px;
      height: 100%;
    }
  }
  .empty {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ccc;
    i {
      color: skyblue;
    }
  }
}
</style>
