import { DEFAULT_SPLITER } from "@/globalConstants"
import { decodeMetricName } from "../component/util.js"
import * as echarts from "echarts"
import store from "@/store"
import {
  getDimetionAxisOption,
  getMetricAxisOption,
  getLabelOption,
  getLegendOption,
  
  makeGrouped,
  getGroupedXaxis,
  
  getCartesianChartReferenceOptions,
} from "./util"
const defaultThemeColors = store.getters.currentTheme.color
export default function(chartProps) {
  const { data, cols, chartStyles, color, metrics, references } =
    chartProps
  const { spec, xAxis, yAxis, splitLine, label, legend, grid } = chartStyles
  const {
    showVerticalLine,
    verticalLineColor,
    verticalLineSize,
    verticalLineStyle,
    showHorizontalLine,
    horizontalLineColor,
    horizontalLineSize,
    horizontalLineStyle,
  } = splitLine

  const labelOption = {
    label: getLabelOption("line", label, metrics),
  }
  const xAxisColumnName = cols[0].displayName
  let xAxisData = []
  let grouped = {}

  if (color.length) {
    xAxisData = getGroupedXaxis(data, xAxisColumnName, metrics)
    grouped = makeGrouped(
      data,
      color.map((c) => c.displayName),
      xAxisColumnName,
      metrics,
      xAxisData
    )
  } else {
    xAxisData = data.map((d) => d[xAxisColumnName] || "")
  }
  const series = []
  const seriesData = []
  const referenceOptions = getCartesianChartReferenceOptions(
    references,
    "line",
    metrics,
    data
  )
  metrics.forEach((m, i) => {
    // const decodedMetricName = decodeMetricName(m.displayName)
    if (color.length) {
      const groupedEntries = Object.entries(grouped)
      groupedEntries.forEach(([ k, v ], gIndex) => {
        const serieObj = {
          id: `${m.name}${DEFAULT_SPLITER}${DEFAULT_SPLITER}${k}`,
          name: `${k}${metrics.length > 1
            ? m.field.alias
              ? ` ${m.field.alias}`
              : `${m.displayName}`
            : ""
          }`,

          type: "line",
          sampling: "average",
          data: v.map((g,) => g[`${m.agg}(${m.displayName})`]),
          itemStyle: {
            // color: color[0].values[gIndex].color,
          },
          areaStyle: {
            opacity: spec.isAreaStyle ? 1 : 0,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: defaultThemeColors[i % 7] + "33",
              },
              {
                offset: 1,
                color: defaultThemeColors[i % 7] + "00",
              },
            ]),
          },
          smooth: spec.smooth,
          step: spec.step,
          ...labelOption,
          ...(gIndex === groupedEntries.length - 1 &&
            i === metrics.length - 1 &&
            referenceOptions),
        }
        series.push(serieObj)
        seriesData.push(grouped[k])
      })
    } else {
      const serieObj = {
        id: m.name,
        type: "line",
        smooth: spec.smooth,
        step: spec.step,
        name: m.field.alias ?? m.displayName,
        itemStyle: {
          // color: m.color ?? defaultThemeColors[i],
        },
        areaStyle: {
          opacity: spec.isAreaStyle ? 1 : 0,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: defaultThemeColors[i % 7] + "33",
            },
            {
              offset: 1,
              color: defaultThemeColors[i % 7] + "00",
            },
          ]),
        },
        ...labelOption,
        data: data.map((g) => g[`${m.agg}(${m.displayName})`]),
        ...(i === metrics.length - 1 && referenceOptions),
      }
      series.push(serieObj)
      seriesData.push([ ...data ])
    }
  })
  const seriesNames = series.map((s) => s.name)

  const xAxisSplitLineConfig = {
    showLine: showVerticalLine,
    lineColor: verticalLineColor,
    lineSize: verticalLineSize,
    lineStyle: verticalLineStyle,
  }

  const yAxisSplitLineConfig = {
    showLine: showHorizontalLine,
    lineColor: horizontalLineColor,
    lineSize: horizontalLineSize,
    lineStyle: horizontalLineStyle,
  }
  const options = {
    xAxis: getDimetionAxisOption(xAxis, xAxisSplitLineConfig, xAxisData),
    yAxis: getMetricAxisOption(
      yAxis,
      yAxisSplitLineConfig,
      metrics.map((m) => decodeMetricName(m.name)).join(` / `)
    ),
    series,
    legend: getLegendOption(legend, seriesNames),
    // grid: spec.showDataZoom
    //   ? {
    //     ...getGridPositions(
    //       legend,
    //       seriesNames,
    //       "",
    //       false,
    //       yAxis,
    //       xAxis,
    //       xAxisData
    //     ),
    //     bottom: 90,
    //   }
    //   : getGridPositions(
    //     legend,
    //     seriesNames,
    //     "",
    //     false,
    //     yAxis,
    //     xAxis,
    //     xAxisData
    //   ),
    grid,
    dataZoom: [
      {
        show: spec.showDataZoom ? true : false,
        endValue: spec.endValue && spec.showDataZoom ? spec.endValue : null, // 初始化滚动条
        // backgroundColor: "#F0F2F5",
        // type: "slider",
        // handleIcon:
        //   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAxFJREFUSEu1lV9IU1Ecx3/n7o9ru4TzBjLxz2Z3GivHWGLsQdIehIQZQkNkBfXay4ReLLEIs/kSbC+91kNDZAONgYFQLnwYyRhT15R5cxNlQ9hlEnftj7obx1yozWi13cdzf+d8fn/O+X4RnP2hxcVFlUwmGyAIohshpEEIXcDhPM8neJ4P5fP5+VQqNdXR0RHBy8WOQkUW0cLCgoqiqBdisfg2SZICmUwGVVVVIBQKD8P39/chm81CKpUCjuMOcrmci2XZx52dnb+BTgOIQCBglkqlr2pqaki5XA4EQfyhSIB8Pg/JZBJYluXS6fQDnU7nAIB8YdNxgGB5efkhSZITdXV1CGdcyocrisViPMdxw1qt9iUAHOD9BQDh9/vvVFdXv2loaECFVpQCKLRua2uL393dvafX69/iSjAAud1uurW11d/Y2EiWmvnpJHAlm5ubXDgc1huNRgYDRCsrKw6FQmGiKKrUpIvGsywL8Xjc2dbWZkYul+uSTqcLqlQqwVkD9Xq9YDAYThxWbK0QgAcfiUQOAoHAFeTz+Z7U19c/q62tPTN7j8cDXV1dJ/4XWzsesLOzA9vb209RMBj80NTUdIMkybICOI7Ds/iIQqFQjKZphUgkKitgb28PGIaJo7W1tWxLS4sYoWKP+ifzX1rE8zyEw+Fc5QEVb9HS0pKnubn5eiWGvLGx8Qkr53O1Wj1SiWu6vr4+jqxW61WTyfS5Eg/N6XRew1dH7vP5XiuVylvllIpoNPquvb39PgZIR0dHL5vN5nmlUikrh9hFo9GUw+HoHhsb+3IodgBAzszM3NVqtbZyyHUwGLT09fVhueYO5RoAsLucn5ubs9A0/eh/DIdhGGtPT48dAL4BQPaX4QCABEOmp6cHNBrNOEVRslIsM5FIpFZXV0f6+/unjg7PFAynoEGCo0rIoaEh9eDg4DBFUTf/xvRZln0/OTk5YbPZ1nFbcOanLbMAwQ4vBoBzePgWi+Vib2+vkaIog0QioYVCofzIGpOZTIZhWdY7OzvrttvtXwHgOwCkASB33PR/AK8MqDBvjq1bAAAAAElFTkSuQmCC",
        // moveHandleSize: 0,
        // showDataShadow: false,
        // fillerColor: "#CED4D9",
        // handleStyle: {
        //   color: "#63659F",
        // },
        // height: 18,
        // xAxisIndex: [0],
        // startValue: 0,
        // bottom: 0,
        // borderColor: "transparent",
        // brushSelect: false,
        // textStyle: {
        //   color: "rgba(0, 0, 0, 0.65)",
        //   fontWeight: "400",
        //   fontSize: 12,
        //   fontFamily: "PingFangSC-Regular, PingFang SC",
        //   lineHeight: 18,
        // },
      },
    ],
    tooltip: {
      trigger: "axis",
      confine: true,

      // ...TOOLTIP_STYLE,
      // formatter: getChartTooltipLabel("line", seriesData, {
      //   cols,
      //   metrics,
      //   color,
      // }),
    },
  }

  return options
}
