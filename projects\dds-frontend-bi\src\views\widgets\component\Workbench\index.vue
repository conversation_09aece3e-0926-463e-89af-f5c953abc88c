<template>
  <div>
    <EditorHeader
      @saveWidget="saveWidget"
      @onResetWidgets="onResetWidgets"
      :description.sync="description"
      :name.sync="name"
    />
    <div class="container">
      <OperatingPanel
        :flag.sync="flag"
        :views="views"
        :view-id="viewId"
        :limit="widgetProps.limit"
        :widget-props.sync="widgetProps"
        :category-drag-items="categoryDragItems"
        :value-drag-items="valueDragItems"
        @onViewSelect="onViewSelect"
        @getWidgetData="getWidgetData"
        @limitChange="onLimitChange"
        :view-config-data="viewConfigData"
      />
      <div class="viewPanel" :style="{width,height}">
        <Widget
          :widget-props="widgetProps"
          @onPageChange="onPageChange"
          :loading="loading"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Request from "@/service"
import EditorHeader from "./EditorHeader.vue"
import OperatingPanel from "./OperatingPanel.vue"
import Widget from "../Widget/index.vue"

import { widgetParamsFormat, encodeMetricName } from "../util"
import widgetlibs from "../../config/index"
import ChartTypes from "../../config/ChartTypes"

import defaultTheme from "@/assets/json/echartsThemes/default.project.json"
const defaultThemeColors = defaultTheme.theme.color

export default {
  components: { EditorHeader, OperatingPanel, Widget },
  props: {},
  data() {
    return {
      description: "",
      name: "",
      views: [],
      viewId: "",
      loading: false,
      categoryDragItems: [], // 分类
      valueDragItems: [], // 数值
      updatedPagination: {
        pageNo: 0,
        pageSize: 0,
      },
      limit: null,
      viewConfigData: {},
      widgetProps: {
        data: [],
        pagination: {
          pageNo: 0,
          pageSize: 0,
          totalCount: 0,
          withPaging: false,
        },
        cols: [],
        color: [],
        rows: [],
        metrics: [],
        secondaryMetrics: [],
        filters: [],
        chartStyles: widgetlibs[0].style,
        selectedChartId: 1,
        selectedChart: widgetlibs[0],
        controls: [],
        orders: [],
        name: "table",
        mode: "chart",
        model: {},
        flag: false,
        references: [],
        limit: null,
        cache: false,
        expired: 300,
        autoLoadData: true,
      },
      width:'100%',
      height:'100%'
    }
  },
  computed: {},
  created() {
    this.getViews()
    const { id,width,height } = this.$route.query
    // 编辑
    if (id) {
      this.widgetId = id
      this.getWidgetDeploy(id)
    
    }
    if (width&height){
      this.width=width+'px'
      this.height = height+'px'
    }
  },
  mounted() {},
  watch: {
    // 维度
    "widgetProps.cols": {
      deep: true,
      handler() {
        this.flag && this.getWidgetData()
      },
    },
    // 指标
    "widgetProps.metrics": {
      deep: true,
      handler() {
        this.flag && this.getWidgetData()
      },
    },
    "widgetProps.color": {
      deep: true,
      handler() {
        this.flag && this.getWidgetData()
      },
    },
    // 右指标
    "widgetProps.secondaryMetrics": {
      deep: true,
      handler() {
        this.flag && this.getWidgetData()
      },
    },
    // 是否使用原始数据
    "widgetProps.chartStyles.table.withNoAggregators": {
      handler(val, old) {
        if (val === old) return
        this.flag && this.getWidgetData()
      },
    },
    "widgetProps.filters": {
      deep: true,
      handler() {
        // this.flag && this.getWidgetData()
      },
    },
  },
  methods: {
    // 保存图表
    async saveWidget(name, description) {
      if (!name.trim()) {
        this.$message.error("图表名称不能为空")
        return
      }

      // if (!this.widgetProps.data.length) {
      //   this.$message.error("请选择一个数据维度/指标");
      //   return;
      // }
      const widget = {
        name,
        description,
        viewId: this.viewId,
        type: 1,
        config: JSON.stringify(this.widgetProps),
        publish: true,
      }
      if (this.widgetId) {
        // 编辑
        const { code } = await Request.widget.update({
          ...widget,
          id: this.widgetId,
        })
        if (code === 200) {
          this.$message.success("修改成功")
          this.$router.go(-1)
        }
      } else {
        // 新增
        const { code } = await Request.widget.create(widget)
        if (code === 200) {
          this.$message.success("添加成功")
          this.$router.go(-1)
        }
      }
    },
    // 获取图表配置
    async getWidgetDeploy(id) {
      const {
        data: { viewId, config, name, description },
      } = await Request.widget.getOne({ id })
      this.viewId = viewId
      this.widgetProps = JSON.parse(config)
      this.name = name
      this.description = description
      this.onLoadViewDetail(viewId)
      this.getWidgetData()
    },
    onViewSelect(id) {
      if (this.widgetProps.data.length) {
        this.$confirm("切换 View 会清空所有配置项，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.viewId = id
          this.flag = false
          this.onResetWidgets()
          this.onLoadViewDetail(id)
        })
      } else {
        this.viewId = id
        this.onLoadViewDetail(id)
      }
    },
    // 获取数据集详情
    async onLoadViewDetail(id) {
      this.viewId = id
      this.categoryDragItems = []
      this.valueDragItems = []
      const { roleIdList } = this.$store.state.user
      const { data } = await Request.view.getOne({ id })

      const roles = data.roles.length ? data.roles : []
      // 无权限字段集合
      let currentRolesColumn = []
      if (roles.length && roleIdList) {
        // 将roles数组中roleIdList数组包含roleId的columnAuth数组合并去重
        roleIdList.forEach((roleId) => {
          const columnAuth = JSON.parse(
            roles.find((item) => item.roleId === roleId).columnAuth
          )
          currentRolesColumn = Array.from(
            new Set(currentRolesColumn.concat(columnAuth))
          )
        })
      }
      console.log(currentRolesColumn, "currentRolesColumn")
      this.viewConfigData = data
      this.widgetProps.model = data.model
      const model = JSON.parse(data.model)
      var colorIndex = 0
      Object.entries(model).forEach(([ key, m ]) => {
        if (!currentRolesColumn.includes(key)) {
          if (m.modelType === "category") {
            this.categoryDragItems.push({
              name: encodeMetricName(key),
              displayName: key,
              type: "category",
              visualType: m.visualType,
              sqlType: m.sqlType,
              checked: false,
              config: true,
              field: { alias: null, desc: "", useExpression: false }, // 别名
              format: { formatType: "default" }, // 格式
              sort: { sortType: "none" }, // 排序
              title: undefined,
              values: [],
            })
          } else {
            this.valueDragItems.push({
              name: encodeMetricName(key),
              displayName: key,
              type: "value",
              visualType: m.visualType,
              sqlType: m.sqlType,
              checked: false,
              agg: "sum",
              chart: {},
              config: true,
              field: { alias: null, desc: "", useExpression: false },
              format: { formatType: "default" },
              title: undefined,
              sort: { sortType: "none" },
              color: defaultThemeColors[colorIndex++],
            })
          }
        }
      })
    },
    // 获取数据集
    async getViews() {
      // const { data } = await Request.view.getAll();
      const { data } = await Request.view.getGroupAndView()
      this.views = data
    },
    // 数据图表数据
    async getWidgetData() {
      this.loading = true
      const { data } = await Request.view.getdata(
        widgetParamsFormat(
          this.widgetProps,
          this.viewId,
          this.updatedPagination
        )
      )
      this.widgetProps.data = data.resultList
      if (this.widgetProps.selectedChartId === ChartTypes.Table) {
        this.widgetProps.pagination = {
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          totalCount: data.totalCount,
          withPaging: this.widgetProps.chartStyles.table.withPaging,
        }
      }
      this.loading = false
    },
    // 展示数据量
    onLimitChange(limit) {
      this.widgetProps.limit = limit
      this.getWidgetData()
    },
    // 触发分页
    onPageChange(page) {
      this.updatedPagination.pageNo = page.currentPage
      this.updatedPagination.pageSize = page.pageSize
      this.getWidgetData()
    },
    // 重置Widgets
    onResetWidgets() {
      Object.assign(this.$data.widgetProps, this.$options.data().widgetProps)
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  z-index: 1;
  width: 100vw;
  height: calc(100vh - 62px);
  display: flex;
  .viewPanel {
    // flex: 1;
    margin: auto;
    padding: 15px;
    box-sizing: border-box;
    background-color: #fff;
    width: 200px;
  }
}
</style>
