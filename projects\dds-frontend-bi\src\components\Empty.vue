<template>
  <div class="empty-container" :style="{ '--image-width': size+'px', '--image-height': size+'px' }">
    <img 
      :src="imageUrl" 
      alt="empty placeholder" 
      class="empty-image"
    >
    <p class="empty-text">{{ description }}</p>
  </div>
</template>

<script>
export default {
  name: 'Empty',
  props: {
    imageUrl: {
      type: String,
      default: () => require('@/assets/images/empty1.png')
    },
    description: {
      type: String,
      default: '暂无数据'
    },
    size: {
      type: [String, Number],
      default: '128'
    },
  
  }
}
</script>

<style scoped lang="scss">
.empty-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  padding: 10px  4px; /* 对应py-10 px-4 */
}

.empty-image {
  width: var(--image-width);
  height: var(--image-height);
  margin-bottom: 4px; /* 对应mb-4 */
  object-fit: contain;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.empty-container:hover .empty-image {
  opacity: 1;
}

.empty-text {
  font-size: 14px;
  line-height: 1.5;
  color: #6b7280; /* 对应text-gray-500 */
  text-align: center;
}
</style>