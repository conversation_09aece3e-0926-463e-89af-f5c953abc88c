<template>
  <header>
    <el-page-header @back="goBack" :content="title"> </el-page-header>
  </header>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>

<style scoped lang="scss">
header {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  height: 50px;
  background: #ffffff;
  display: flex;
  align-items: center;
}
</style>
