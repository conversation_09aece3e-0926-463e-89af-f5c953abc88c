<template>
  <div class="operatingPanel">
    <!-- 左侧数据集 -->
    <el-form label-position="top" class="model" style="text-align: left">
      <div style="margin-left: 15px">
        <el-form-item label="数据指标:">
          <el-cascader
            :value="viewId"
            :options="views"
            @change="viewSelect"
            :show-all-levels="false"
            filterable
            :props="{
              value: 'id',
              label: 'name',
              emitPath: false,
            }"
          ></el-cascader>
          <!-- <el-select
            :value="viewId"
            placeholder="选择一个数据指标"
            @change="viewSelect"
            filterable
          >
            <el-option
              v-for="item in views"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select> -->
          <el-tooltip
            class="item"
            effect="dark"
            content="编辑SQL"
            placement="top"
          >
            <i class="el-icon-edit" @click="goEditSql"></i>
          </el-tooltip>
        </el-form-item>
      </div>
      <div class="category itemBox">
        <el-form-item label="分类型:">
          <draggable
            v-model="categoryDragItems"
            animation="300"
            drag-class="dragClass"
            ghost-class="ghostClass"
            chosen-class="chosenClass"
            @start="onStart('category')"
            :move="handleMove"
            :group="{ name: 'itxst', pull: 'clone', put: false }"
          >
            <transition-group>
              <p class="item" v-for="item in categoryDragItems" :key="item">
                <svg-icon class="icon" icon-class="string" />
                {{ item.displayName }}
              </p>
            </transition-group>
          </draggable>
        </el-form-item>
      </div>
      <div class="val itemBox">
        <el-form-item label="数值型:">
          <draggable
            v-model="valueDragItems"
            animation="300"
            drag-class="dragClass"
            ghost-class="ghostClass"
            chosen-class="chosenClass"
            @start="onStart('value')"
            @end="onEnd"
            :group="{ name: 'itxst', pull: 'clone', put: false }"
          >
            <transition-group>
              <p class="item1" v-for="item in valueDragItems" :key="item">
                <svg-icon
                  class="icon"
                  :icon-class="
                    item.visualType == 'string' ? 'string' : 'number'
                  "
                />
                {{ item.displayName }}
              </p>
            </transition-group>
          </draggable>
        </el-form-item>
      </div>
    </el-form>
    <div>
      <!-- 图表类型 -->
      <div class="chart-type-panel">
        <div class="title">图表类型:</div>
        <div class="chart-type-list">
          <template v-for="c in widgetlibs">
            <ChartIndicator
              :key="c.id"
              :chart-info="c"
              :selected-chart="chartConfig.selectedChart"
              @chartSelect="chartSelect"
              :dimetions-and-metrics-count="getDimetionsAndMetricsCount"
            />
          </template>
        </div>
      </div>
      <div class="DeployPanel">
        <el-tabs v-model="activeName" stretch @tab-click="handleClick">
          <el-tab-pane label="数据" name="data">
            <!-- 指标/维度 -->
            <div
              class="dimension"
              :key="index"
              v-for="(items, index) in chartConfig.selectedChart.data"
            >
              <h4>
                {{ items.title }}
                <!-- <i
                  class="el-icon-s-tools"
                  v-if="items.name == 'color'"
                  @click="setMetricsColor"
                  >设置</i
                > -->
              </h4>
              <draggable
                :group="{
                  name: 'itxst',
                  put: (e) => draggablePull(items), //是否允许拖入当前组
                }"
                class="draggable-wrapper"
                v-model="chartConfig[items.name]"
                @change="onDraggableEnd(items.name, arguments)"
              >
                <DragItem
                  v-for="(col, index) in chartConfig[items.name]"
                  :key="index"
                  :item="col"
                  :list="chartConfig[items.name]"
                  :type="items.name"
                  @onItemRemove="removeDropboxItem"
                  @onSetAlias="dropboxItemChangeFieldConfig"
                  @onSort="getDropboxItemSortDirection"
                  @onCollect="getDropboxItemCollect"
                  @onSetFormat="getDropboxItemFormat"
                  @onSetColor="getDropboxItemColor"
                  @onSetFilters="getDropboxItemFilters(col)"
                />
              </draggable>
            </div>
          </el-tab-pane>
          <el-tab-pane label="样式" name="style">
            <ConfigSections
              @switchChange="switchChange"
              @changeStyle="changeStyle"
              :chart-data="chartConfig"
              :name="chartConfig.name"
            />
          </el-tab-pane>
          <el-tab-pane label="配置" name="setting">
            <div class="paramsPane">
              <div class="paneBlock">
                <h4>
                  <span>控制器</span>
                  <span class="addVariable" @click="handleConfig">
                    点击配置
                  </span>
                </h4>
              </div>
              <div
                class="paneBlock"
                v-if="['line', 'bar', 'doubleYAxis'].includes(chartConfig.name)"
              >
                <h4>
                  <span>参考线</span>
                  <span class="addVariable" @click="handleReference">
                    点击配置
                  </span>
                </h4>
              </div>
              <div class="paneBlock">
                <h4>展示数据量</h4>
                <div class="blockBody">
                  <el-row
                    gutter="8"
                    type="flex"
                    align="middle"
                    class="blockRow"
                  >
                    <el-col span="24">
                      <el-input-number
                        v-model="limit"
                        controls-position="right"
                        @change="limitChange"
                      ></el-input-number>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
            <LocalControlConfig
              v-if="controlConfigVisible"
              :controls="chartConfig.controls"
              @saveControls="saveControls"
              :value-drag-items="valueDragItems"
              :view="viewConfigData"
              :control-config-visible.sync="controlConfigVisible"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 别名 -->
    <FieldModal ref="FieldModal" @onSaveField="onSaveField" />
    <!-- 格式 -->
    <FormatModal ref="FormatModal" @onSaveFormat="onSaveFormat" />
    <!-- 颜色 -->
    <ColortModal
      ref="ColortModal"
      @onSaveColor="onSaveColor"
      @onCancelColor="onCancelColor"
    />

    <el-dialog title="筛选配置" :visible.sync="filterModalVisible">
      <FilterSettingForm
        :distinct-column-values="distinctColumnValues"
        :mode.sync="mode"
        :filters-name="filtersName"
        :sql-type="sqlType"
        :target="target"
        :field-type="fieldType"
        @onCancelFilter="onCancelFilter"
        @onSaveFilter="onSaveFilter"
      />
    </el-dialog>
    <!-- 参考线 -->
    <Reference
      ref="reference"
      :reference-visible.sync="ReferenceVisible"
      :references.sync="chartConfig.references"
      :metrics="chartConfig.metrics"
    />
  </div>
</template>

<script>
import draggable from "vuedraggable"
import ChartIndicator from "./ChartIndicator.vue"
import DragItem from "./Dropbox/DragItem.vue"
import ConfigSections from "./ConfigSections/index.vue"
import FieldModal from "../Config/Field"
import FilterSettingForm from "../Config/FilterSettingForm/FilterSettingForm.vue"
import FormatModal from "../Config/Format"
import ColortModal from "../Config/Color"
import LocalControlConfig from "@/components/Control/widget"
import Reference from "./Reference/index"
import Request from "@/service"
import { getTable, checkChartEnable } from "../util"
import widgetlibs from "../../config/index"
const defaultTheme = require("@/assets/json/echartsThemes/default.project.json")
const defaultThemeColors = defaultTheme.theme.color
export default {
  components: {
    draggable,
    FilterSettingForm,
    ChartIndicator,
    DragItem,
    ConfigSections,
    FieldModal,
    FormatModal,
    ColortModal,
    LocalControlConfig,
    Reference,
  },
  props: {
    views: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: String,
      default: "",
    },
    categoryDragItems: {
      type: Array,
      default: () => [],
    },
    valueDragItems: {
      type: Array,
      default: () => [],
    },
    widgetProps: {
      type: Array,
      default: () => [],
    },
    viewId: {
      type: String,
      default: "",
    },
    flag: {
      type: Boolean,
      default: false,
    },
    viewConfigData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dragType: null,
      colorItemTag: {},
      existWarning: null,
      controlConfigVisible: false,
      ReferenceVisible: false,
      filterModalVisible: false,
      activeName: "data",
      defaultThemeColors,
      currentEditingItem: {},
      currentEditingList: [],
      nameColor: "",
      widgetlibs,
      colorType: "",
      filtersName: "",
      sqlType: "",
      distinctColumnValues: [],
      target: [],
      fieldType: "",
      mode: "value",
    }
  },
  computed: {
    chartConfig() {
      return this.widgetProps
    },
    // 获取维度和指标数量
    getDimetionsAndMetricsCount() {
      const { cols, metrics, secondaryMetrics } = this.chartConfig
      const dcount = cols.length
      const mcount = secondaryMetrics
        ? secondaryMetrics.length + metrics.length
        : metrics.length
      const isConform = checkChartEnable(
        dcount,
        mcount,
        this.chartConfig.selectedChart
      )
      if (!isConform) {
        this.updateChartConfig()
        return null
      }
      return [ dcount, mcount ]
    },
  },
  created() {
    console.log(
      "%cOperatingPanel.vue line:392 widgetlibs",
      "color: #26bfa5;",
      widgetlibs
    )
  },

  mounted() {},
  watch: {
    chartConfig: {
      deep: true,
      handler(newVal) {
        this.$emit("upadte:widgetProps", newVal)
      },
    },
  },
  methods: {
    updateChartConfig() {
      this.chartConfig.selectedChart = getTable()
      this.chartConfig.chartStyles = getTable().style
      this.chartConfig.selectedChartId = getTable().id
      this.chartConfig.name = getTable().name
    },
    // 判断维度是否存在
    handleMove(evt) {
      console.log(evt)
      // if (
      //   this.chartConfig.cols.find(
      //     (item) => item.displayName===evt.draggedContext.element.displayName
      //   )
      // ) {
      //   if (!this.existWarning) {
      //     this.existWarning=this.$message({
      //       type: "warning",
      //       message: "字段已经存在",
      //       onClose: () => {
      //         this.existWarning=null
      //       },
      //     })
      //   }
      //   return false
      // }
    },
    handleConfig() {
      if (!this.viewId) return this.$message.error("请先选择一个数据指标")
      this.controlConfigVisible = true
    },
    handleReference() {
      this.$refs.reference.editingReferences = this._.cloneDeep(
        this.chartConfig.references
      )
      if (this.chartConfig.references.length) {
        this.$refs.reference.selected = this._.cloneDeep(
          this.chartConfig.references[0]
        )
      }
      this.ReferenceVisible = true
    },
    // 编辑sql跳转
    goEditSql() {
      this.$router.push(`EditView?isFullPage=true&id=${this.viewId}`)
    },
    // 选择数据集
    viewSelect(viewId) {
      console.log(this.views)
      this.$emit("onViewSelect", viewId)
    },
    // 删除指标/维度
    removeDropboxItem(list, item) {
      this.onStart()
      list.splice(list.indexOf(item), 1)
      this.$emit("getWidgetData")
    },
    // 排序
    getDropboxItemSortDirection(list, item, sortType) {
      const index = list.indexOf(item)
      list[index].sort = { sortType }
      this.$emit("getWidgetData")
    },
    // 别名
    dropboxItemChangeFieldConfig(list, item) {
      this.currentEditingList = list
      this.currentEditingItem = item
      this.$refs.FieldModal.itemForm = { ...item.field }
      this.$refs.FieldModal.fieldModalVisible = true
    },
    // 提交别名
    onSaveField({ alias }) {
      const index = this.currentEditingList.indexOf(this.currentEditingItem)
      this.currentEditingList[index].field.alias = alias
      this.$refs.FieldModal.fieldModalVisible = false
    },
    // 汇总方式
    getDropboxItemCollect(list, item, type) {
      const index = list.indexOf(item)
      list[index].agg = type
    },
    // 设置数值格式
    getDropboxItemFormat(list, item) {
      this.currentEditingItem = item
      this.currentEditingList = list
      this.$refs.FormatModal.format = this._.cloneDeep(
        this.currentEditingItem.format
      )
      this.$refs.FormatModal.foramtVisible = true
    },
    // 格式保存
    onSaveFormat(format) {
      const index = this.currentEditingList.indexOf(this.currentEditingItem)
      this.currentEditingList[index].format = format
      this.$refs.FormatModal.foramtVisible = false
    },
    // 设置颜色
    setMetricsColor() {
      this.colorType = "metrics"
      this.$refs.ColortModal.colorModalVisible = true
      this.$refs.ColortModal.distinctvalue = this._.cloneDeep(
        this.chartConfig.metrics
      )
    },
    // 配置颜色
    getDropboxItemColor() {
      this.colorType = "distinctvalue"
      this.$refs.ColortModal.colorModalVisible = true
      this.$refs.ColortModal.distinctvalue = this._.cloneDeep(
        this.chartConfig.color[0].values
      )
    },
    // 拖拽结束
    async onDraggableEnd(name, val) {
      if (name === "color") {
        this.colorType = "distinctvalue"
        this.colorItemTag = val[0].added.element
        this.nameColor = val[0].added.element.displayName
        const { data } = await Request.view.getdistinctvalue({
          columns: [ this.nameColor ],
          cache: false,
          expired: 0,
          viewId: this.viewId,
        })
        this.$refs.ColortModal.distinctvalue = data.map((item, index) => ({
          name: item[this.nameColor],
          color: defaultThemeColors[index],
        }))
        this.onSaveColor(this.$refs.ColortModal.distinctvalue)
        // this.$refs.ColortModal.colorModalVisible = true;
      }
    },
    // 取消颜色
    onCancelColor() {
      const index = this.chartConfig.color.findIndex(
        (item) => item.displayName === this.nameColor
      )
      this.chartConfig.color.splice(index, 1)
      this.$refs.ColortModal.colorModalVisible = false
    },
    // 保存颜色
    onSaveColor(distinctvalue) {
      if (this.colorType === "metrics") {
        this.chartConfig.metrics = distinctvalue
      } else {
        this.chartConfig.color = [ this.colorItemTag ]
        this.chartConfig.color[0].values = this._.cloneDeep(distinctvalue)
      }
      this.$emit("getWidgetData")
      this.$refs.ColortModal.colorModalVisible = false
    },
    // 取消筛选
    onCancelFilter() {
      const index = this.chartConfig.filters.findIndex(
        (item) => item.displayName === this.filtersName
      )
      this.chartConfig.filters.splice(index, 1)
      this.filterModalVisible = false
    },
    // 筛选
    async getDropboxItemFilters(val) {
      this.filtersName = val.displayName

      if (val.type === "value") {
        this.mode = 'conditional'  // 自定义筛选
      } else {
        this.mode = 'value'  // 值筛选
        const { data } = await Request.view.getdistinctvalue({
          columns: [ this.filtersName ],
          cache: false,
          expired: 0,
          viewId: this.viewId,
        })
        this.distinctColumnValues = data.map((item) => item[this.filtersName])
      }
      this.fieldType = val.type
      this.sqlType = val.sqlType
      
      this.target = val.values
      this.filterModalVisible = true
    },
    // 保存筛选
    onSaveFilter({
      filterSource,
      sqlModel,
    }) {
      console.log('%cOperatingPanel.vue line:552 object', 'color: #007acc;',    this.chartConfig)
      this.chartConfig.filters.find(
        (item) => item.displayName === this.filtersName
      ).config = {
        filterSource,
        sqlModel,
      }
      this.$emit("getWidgetData")
      this.filterModalVisible = false
    },
    // 图表选择
    chartSelect(enable, config) {
      if (!enable) return
      this.$emit("update:flag", true)
      if (
        config.name !== "doubleYAxis" &&
        this.chartConfig.secondaryMetrics.length
      ) {
        this.chartConfig.secondaryMetrics = []
      }
      if (
        [
          "table",
          "doubleYAxis",
          "scorecard",
          "radar",
          "wordCloud",
          "bubble",
        ].includes(config.name) &&
        this.chartConfig.color.length
      ) {
        this.chartConfig.color = []
      }
      this.chartConfig.selectedChart = config
      this.chartConfig.chartStyles = this._.cloneDeep(config.style)
      this.chartConfig.selectedChartId = config.id
      this.chartConfig.name = config.name
    },
    // 样式选择
    changeStyle(type, from) {
      this.chartConfig.chartStyles[type] = from
    },
    // 分页  原始数据  pagesize
    switchChange(key, value) {
      this.chartConfig.chartStyles.table[key] = value
      this.$emit("getWidgetData")
    },
    limitChange(limit) {
      this.$emit("limitChange", limit)
    },
    saveControls(controls) {
      this.chartConfig.controls = controls
    },
    onStart(type) {
      this.dragType = type
      this.$emit("update:flag", true)
    },
    draggablePull(item) {
      if (item.type === "all") return true
      if (this.dragType === item.type) {
        return true
      } else {
        return false
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import "./Workbench.scss";

.operatingPanel {
  display: flex;
  height: 100%;
  margin-right: 15px;
  .model {
    display: flex;
    width: 250px;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    height: 100%;
    .itemBox {
      flex: 1;
      padding-left: 20px;
      border-top: 1px solid #ccc;
      overflow: auto;
    }
    .divider {
      width: 100%;
      height: 1px;
      background-color: #ccc;
    }
    .dragClass {
      background-color: #d5e9bd !important;
      opacity: 1 !important;
      box-shadow: none !important;
      outline: none !important;
      background-image: none !important;
    }

    .col {
      width: 40%;
      flex: 1;
      border: solid 1px #eee;
      border-radius: 5px;
      float: left;
    }
    .col + .col {
      margin-left: 10px;
    }

    .item,
    .item1 {
      margin: 0px 10px;
      border: solid 1px #eee;
      background-color: #f1f1f1;
      font-size: 14px;
      height: 24px;
      line-height: 24px;
    }
    .item:hover {
      background-color: #a0d5f4;
      cursor: pointer;
    }
    .item1:hover {
      background-color: #d5e9bd;
      cursor: pointer;
    }
    .item + .item {
      border-top: none;
    }
    .dragClass {
      background-color: #d5e9bd !important;
      opacity: 1 !important;
      box-shadow: none !important;
      outline: none !important;
      background-image: none !important;
    }
  }
  .chart-type-panel {
    flex-shrink: 0;
    padding: 10px;
    background-color: #fff;
    font-size: 12px;
    .chart-type-list {
      width: 100%;
      display: grid;
      justify-items: center;
      grid-template-columns: repeat(5, 1fr);
      grid-auto-rows: 1fr;
      grid-gap: 10px;
      span {
        line-height: initial;
        height: 100%;
        font-size: 22px;
        border-radius: 5px;
        cursor: pointer;
        text-align: center;
        width: 100%;
        position: relative;
        .icon {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
        }
      }
      span:hover {
        background-color: #eaedf1;
      }
      span::before {
        content: "";
        width: 100%;
        padding-bottom: 100%;
        display: block;
      }
      .disabledIcon {
        cursor: not-allowed;
      }
      .activedIcon {
        background-color: #eaedf1;
      }
    }
  }
  .DeployPanel {
    background-color: #fff;
    height: calc(100% - 166px);

    width: 250px;
    .dimension {
      width: 100%;
      padding: 0 15px;
      h4 {
        margin-top: 10px;
        color: #404040a6;
        line-height: 18px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        cursor: pointer;
      }

      .draggable-wrapper {
        width: 100%;
        min-height: 60px;
        border: 1px dashed #ccc;
        padding: 10px;
        box-sizing: border-box;
        display: block;
      }
    }

    ::v-deep .el-tabs__item {
      padding: 0;
    }
    ::v-deep .el-tabs {
      height: 100%;
    }

    ::v-deep .el-tabs__header {
      margin: 0;
    }
    ::v-deep .el-tabs__content {
      height: calc(100% - 40px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.2) 25%,
          transparent 25%,
          transparent 50%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0.2) 75%,
          transparent 75%,
          transparent
        );
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #ededed;
        border-radius: 10px;
      }
    }
  }
}

::v-deep .el-tabs__item {
  padding: 0;
}
::v-deep .el-tabs {
  height: 100%;
}

::v-deep .el-tabs__header {
  margin: 0;
}
::v-deep .el-tabs__content {
  height: calc(100% - 40px);
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: skyblue;
    background-image: -webkit-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      transparent 75%,
      transparent
    );
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
  }
}
::v-deep .el-form-item__label {
  font-size: 12px;
  font-weight: 600;
}
</style>
