<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="780px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="dimDefinition.version">
        <el-input
          v-model="form.dimDefinition.version"
          placeholder="请输入版本名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input
          v-model="form.dimDefinition.tags"
          placeholder="请输入标签"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入描述"
          v-model="form.dimDefinition.description"
        ></el-input>
      </el-form-item>
      <el-form-item label="维度值">
        <el-radio-group
          v-model="form.createType"
          :disabled="form.dimDefinition.definitionCode"
        >
          <el-radio :label="0">基于数据源创建</el-radio>
          <el-radio :label="1">手动添加</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.createType == 0">
        <el-form-item label="更新频率">
          <el-select
            v-model="form.dimDefinition.updateFrequency"
            placeholder="请选择维度类型"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="更新方式">
          <el-radio-group v-model="form.dimDefinition.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </template>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button
        type="primary"
        v-if="form.dimDefinition.definitionCode"
        @click="saveEditDimension"
      >
        确定
      </el-button>
      <el-button type="primary" v-else @click="createDimension">
        创建维度
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {},
  data() {
    return {
      dialogTitle: "新建图表",
      dialogVisible: false,
      form: {
        dimDefinition: {
          categoryCode: "",
          dimName: "",
          description: "",
          version: "",
          tags: "",
          updateFrequency: 1,
          updateType: 0
        },
        createType: 1,
        dimLevels: []
      },
      rules: {
        "dimDefinition.version": [
          { required: true, message: "请输入版本名称", trigger: "blur" }
        ]
      },
      updateFrequencys: [
        {
          label: "按日",
          value: 1
        },
        {
          label: "按周",
          value: 2
        },
        {
          label: "按月",
          value: 3
        },
        {
          label: "按学期",
          value: 4
        },
        {
          label: "按学年",
          value: 5
        },
        {
          label: "按年",
          value: 6
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    openDialog(item) {
      if (item.definitionCode) {
        console.log(item, "item")
        this.dialogTitle = "编辑维度"
        this.form = {
          dimDefinition: {
            categoryCode: "",
            dimName: "",
            description: "",
            version: "",
            tags: "",
            updateFrequency: 1,
            updateType: 0,
            ...item
          },
          createType: item.createType,
          dimLevels: []
        }
      } else {
        this.dialogTitle = "创建版本"
        this.form = {
          dimDefinition: {
            categoryCode: "",
            dimName: "",
            description: "",
            version: "",
            tags: "",
            updateFrequency: 1,
            updateType: 0,
            ...item
          },
          createType: 1,
          dimLevels: []
        }
      }
      this.dialogVisible = true
    },
    allowDrop() {
      return false
    },
    // tree节点能否被拖拽
    allowDrag(draggingNode) {
      console.log(draggingNode, "拖拽节点")
      if (draggingNode.level === 2) {
        return true
      }
    },
    // tree拖拽开始
    handleTreeDragStart(node, event) {
      console.log(node)
      if (node.level === 2) {
        this.isDragging = true
        // 在拖拽开始时设置拖拽节点的数据
        event.dataTransfer.setData("draggingItem", JSON.stringify(node.data))
      } else {
        return false
      }
    },
    handleDragOver(e) {
      e.preventDefault()
    },
    handleTargetDrop(e) {
      if (e.target === this.$refs.targetContainer) {
        // 阻止默认行为}
        e.preventDefault()
        const data = JSON.parse(e.dataTransfer.getData("draggingItem"))
        console.log(data, "拖拽节点")
        this.form.dimLevels.push(data)
      }
    },
    // 创建维度
    createDimension() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          console.log(this.form, "this.form")
          const { data } = await Request.api.paramPost("/DimManage/addDim", {
            ...this.form,
            dimDefinition: {
              ...this.form.dimDefinition
            },
            dimLevels: [],
            configs: []
          })
          this.$message.success("创建成功")
          this.dialogVisible = false
          // this.$emit("refresh")
          this.$emit("createDimension", data)
        } else {
          return false
        }
      })
    },
    saveEditDimension() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          console.log(this.form, "this.form")
          const { data } = await Request.api.paramPost(
            "/DimManage/editVersion",
            {
              ...this.form.dimDefinition
            }
          )
          this.$message.success("编辑成功")
          this.dialogVisible = false
          // this.$emit("refresh")
          this.$emit("createDimension", data)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.step3 {
  .dimension-table {
    .dimension-head {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .dimension-head-text {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        margin-bottom: 16px;
      }
    }

    .dimension-body {
      display: flex;
      height: 200px;
      background: #f5f7fa;
      border-radius: 8px;
      overflow: auto;
      .el-tree {
        width: 50%;
        background: #f5f7fa;
      }

      .dimension-value-preview {
        border-left: 1px solid #edeff0;
        width: 50%;
        display: flex;
        flex-direction: column;
        padding-top: 10px;
        padding-left: 10px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        span {
          margin-top: 10px;
        }
      }
    }
  }

  .drop-zone {
    width: 100%;

    margin: 20px 0;
    h3 {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      margin-bottom: 16px;
    }
    .target-container {
      background: #f5f7fa;
      border-radius: 8px;
      height: 200px;
      padding: 20px;
      box-sizing: border-box;
      .dim-item {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        margin-bottom: 10px;
        display: flex;
        .mover {
          width: 12px;
          height: 12px;
          background: url("~@/assets/images/drag.png") no-repeat;
          cursor: move;
        }
        .dim-cname {
          margin-left: auto;
        }
      }
    }
  }
  .table-relation {
    h3 {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      margin-bottom: 16px;
    }
    .relations {
      width: 100%;
      height: 72px;
      background: #f5f7fa;
      border-radius: 8px;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      overflow-x: auto;
      .relation-item {
        display: flex;
        align-items: center;
        .left-table,
        .right-table {
          position: relative;
          width: 166px;
          height: 32px;
          background: #ffffff;
          border-radius: 6px;
          border: 1px solid #1563ff;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #222222;
          line-height: 32px;
          text-align: center;
          font-style: normal;
        }
        .left-table {
          position: relative;
          z-index: 99;
          &::after {
            position: absolute;
            top: 15px;
            right: -24px;
            content: "";
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: "";
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 0 4px 4px 0;
            top: 12px;
            right: -4px;
            // transform: translate(100%);
          }
        }
        .left-icon {
          position: relative;
          z-index: 99;
          height: 32px;
          &::after {
            position: absolute;
            top: 15px;
            right: -24px;
            content: "";
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: "";
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 0 4px 4px 0;
            top: 12px;
            right: -4px;
            // transform: translate(100%);
          }
        }
        .right-table {
          position: relative;
          z-index: 99;
          &::after {
            position: absolute;
            top: 15px;
            left: -24px;
            content: "";
            width: 24px;
            height: 1px;
            border: 1px solid #1563ff;
          }
          &::before {
            content: "";
            width: 4px;
            height: 8px;
            background: #1563ff;
            position: absolute;
            border-radius: 4px 0 0 4px; /* 左上 + 左下为圆角 */
            top: 12px;
            left: -4px;
            // transform: translate(100%);
          }
        }
        .relation-icon {
          width: 40px;
          height: 24px;
          margin: 0 24px;
          &.LEFT {
            background: url("~@/assets/images/LEFT.png") no-repeat;
          }
          &.RIGHT {
            background: url("~@/assets/images/RIGHT.png") no-repeat;
          }
          &.INNER {
            background: url("~@/assets/images/INNER.png") no-repeat;
          }
        }
      }
    }
  }

  .drop-zone.drag-over {
    border-color: #409eff;
    background: #f0f7ff;
  }
}
.custom-node {
  display: flex;
  width: calc(100% - 80px);
}
// .dialog-footer {
// }

::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.table-list {
  width: 640px;
  margin: 20px auto;

  .checkbox-warp {
    height: calc(100vh - 420px);
    overflow: auto;
    margin-top: 24px;
  }

  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  ::v-deep .el-checkbox {
    margin-bottom: 24px;
  }

  //滚动条样式
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #cbced1;
  }

  ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent !important;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

.el-tree-node.dragging > .el-tree-node__content {
  opacity: 0.2;
}

.el-tree-node > .el-tree-node__content {
  &.is-current {
    background: #f5f7fa !important;
  }

  &.is-focusable {
    background: #f5f7fa !important;
  }

  &:hover {
    background: #f5f7fa !important;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-btns {
      opacity: 1;
    }
  }
}

#project_frame .el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa !important;

  .el-tree-node__label {
    background-color: transparent;
  }

  .custom-tree-btns {
    opacity: 1;
  }
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__label {
  background-color: transparent !important;
}

.custom-tree-node {
  padding: 0 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .custom-tree-btns {
    opacity: 0;
  }
}
</style>
