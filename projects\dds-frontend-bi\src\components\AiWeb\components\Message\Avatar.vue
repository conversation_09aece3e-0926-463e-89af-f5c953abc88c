<template>
  <div>
    <template v-if="image">
      <el-avatar :src="defaultAvatar" fit="fill" size="40"></el-avatar>
    </template>
    <span v-else class="dark:text-white ai-avatar">
    </span>
  </div>
</template>

<script>
import { isString } from "@/utils/is"

export default {
  components: {},
  props: {
    image: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultAvatar: require("@/assets/images/ai/avatar.png"),
      aiAvatar: require("@/assets/images/ai/avatar.png")
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    isString
  }
}
</script>

<style scoped lang="scss">
.ai-avatar{
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-image: url('~@/assets/images/ai/avatar.png'),
    // 跟背景色保持一致，根据实际情况修改
    linear-gradient(
        180deg,
        rgba(67, 192, 255, 1),
        rgba(120, 140, 255, 1)
      ); // 取border-image的渐变色，按实际来修改
  background-size: cover;
border-top: 1px solid  transparent;
  background-origin: border-box;
  background-clip: content-box, border-box;
  padding: 2px;

  
}
</style>
