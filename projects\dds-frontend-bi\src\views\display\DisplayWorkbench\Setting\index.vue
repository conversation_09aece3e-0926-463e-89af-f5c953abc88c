<template>
  <div class="layer-setting">
    <el-tabs
      v-if="curCode !== 'screen'"
      v-model="tabType"
      @tab-click="handleClick"
    >
      <el-tab-pane label="样式" name="style">
        <DynamicForm
          :options="styleOptions"
          v-bind="$attrs"
          v-on="$listeners"
        />
      </el-tab-pane>
      <el-tab-pane label="事件" name="event">
        <DynamicForm
          :options="eventOptions"
          v-bind="$attrs"
          v-on="$listeners"
        />
      </el-tab-pane>
    </el-tabs>
    <DynamicForm
      v-else
      :options="styleOptions"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import DynamicForm from "./components/DynamicForm"

export default {
  components: { DynamicForm },
  props: {
    styleOptions: {
      type: Array,
      default: () => []
    },
    eventOptions: {
      type: Array,
      default: () => []
    },
    curCode: {
      type: String,
      default: "screen"
    }
  },
  data() {
    return {
      form: {},
      tabType: "style"
    }
  }
}
</script>

<style scoped lang="scss">
.layer-setting {
  width: 300px;
  padding: 10px 15px;
  background-color: #fff;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
