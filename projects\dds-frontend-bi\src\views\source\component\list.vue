<template>
  <dt-single-page-view
    class="cardList"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <!-- <DT-Form
      v-model="data"
      type="search"
      :show-button="false"
      :render="render"
    /> -->
    <el-button type="primary" icon="el-icon-plus" @click="handleOnAdd">
      新建
    </el-button>
    <el-table :data="filterTableData" style="width: 100%">
      <el-table-column
        prop="name"
        label="数据源名称"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="type"
        label="数据源类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateTime"
        label="最近同步时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="描述"
        align="left"
      ></el-table-column>
      <!-- <el-table-column prop="description" label="描述" align="center">
      </el-table-column> -->
      <el-table-column prop="operate" label="操作" align="right">
        <template slot-scope="{ row }">
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="infoLine(row.id, row.name)"
          >
            查看
          </el-link>
          <div class="el-divider el-divider--vertical" /> -->

          <el-link
            :underline="false"
            type="primary"
            @click="uploadLine(row.id)"
            v-if="row.type === 'csv'"
          >
            上传
          </el-link>
          <div
            class="el-divider el-divider--vertical"
            v-if="row.type === 'csv'"
          />
          <el-link :underline="false" type="primary" @click="updLine(row.id)">
            修改
          </el-link>
          <div class="el-divider el-divider--vertical" />
          <el-link :underline="false" type="primary" @click="syncMetaData(row)">
            同步数据源
          </el-link>
          <div class="el-divider el-divider--vertical" />
          <el-link :underline="false" type="primary" @click="delLine(row.id)">
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="上传 CSV 文件"
      :visible.sync="importVisible"
      width="576px"
    >
      <el-form :model="formDialog" label-width="120px">
        <el-form-item label="表名" prop="tableName">
          <el-input
            v-model="formDialog.tableName"
            placeholder="请输入"
            class="my_with"
          ></el-input>
        </el-form-item>
        <el-form-item label="主键" prop="primaryKeys">
          <el-input
            v-model="formDialog.primaryKeys"
            placeholder="请输入"
            class="my_with"
          ></el-input>
        </el-form-item>
        <el-form-item label="索引值" prop="indexKeys">
          <el-input
            v-model="formDialog.indexKeys"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="导入方式" prop="mode">
          <el-radio-group v-model="formDialog.mode">
            <el-radio
              :label="item.value"
              v-for="(item, key) in importList"
              :key="key"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
          <el-popover placement="left" title="" width="360" trigger="hover">
            <div class="content">
              <div class="item">新增：首次上传文件到新表</div>
              <div class="item">
                替换：保持原有表结构不变，清空原有表数据后上传
              </div>
              <div class="item">
                追加：保持原有表结构不变，保持原有表数据并追加
              </div>
              <div class="item">覆盖：重建表结构并替换数据</div>
            </div>
            <el-link
              slot="reference"
              class="el-icon-warning-outline"
              style="font-size: 16px; margin-left: 12px"
            ></el-link>
          </el-popover>
        </el-form-item>

        <el-form-item label="上传" prop="file">
          <el-upload
            action="#"
            :show-file-list="false"
            style="display: contents"
            accept=".csv"
            :before-upload="beforeUpload"
          >
            <el-button
              type="
          primary"
              icon="el-icon-upload2"
            >
              {{ formDialog.file ? "重新长传" : "点击上传" }}
            </el-button>
            <el-popover placement="left" title="" width="260" trigger="hover">
              <div class="content">
                <div class="item">
                  csv文件第一行必须含有表头作为字段，第二行为表头字段的数据类型格式，目前只支持jdbc数据库导入
                </div>
              </div>
              <el-link
                slot="reference"
                class="el-icon-warning-outline"
                style="font-size: 16px; margin-left: 12px"
              ></el-link>
            </el-popover>
            <div class="fileInfo el-icon-document" v-if="formDialog.file">
              {{ formDialog.file.name }}
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCSV()">保存</el-button>
      </div>
    </el-dialog>
  </dt-single-page-view>
</template>
<script>
import Request from "@/service"
import Axios from "axios"
export default {
  name: "data-source-list",
  data() {
    return {
      show: false,
      loading: true,
      tableData: [],
      data: {},
      render: [
        {
          type: "input",
          key: "search",
          label: "库名称",
          props: {
            placeholder: "请输入库名称"
          }
        }
      ],
      importVisible: false,
      formDialog: {
        sourceId: "",
        mode: 0,
        tableName: "",
        primaryKeys: "",
        indexKeys: "",
        file: ""
      },
      importList: [
        {
          label: "新增",
          value: 0
        },
        {
          label: "替换",
          value: 1
        },
        {
          label: "追加",
          value: 2
        },
        {
          label: "覆盖",
          value: 3
        }
      ]
    }
  },
  created() {
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
    this.initData()
  },
  computed: {
    filterTableData() {
      return this.tableData.filter(
        data =>
          !this.data.search ||
          data.name.toLowerCase().includes(this.data.search.toLowerCase())
      )
    }
  },
  methods: {
    handleOnAdd() {
      this.$emit("click", { com: "edit", opt: "add", data: { id: "" } })
    },
    infoLine(id, name) {
      // 修改
      this.$emit("click", {
        com: "info",
        opt: "info",
        data: { id: id, name: name }
      })
    },
    updLine(id) {
      // 修改
      this.$emit("click", { com: "edit", opt: "edit", data: { id: id } })
    },
    delLine(id) {
      this.$confirm("此操作将删除选中数据, 是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true
        Request.source
          .delete({ id: id })
          .then(res => {
            this.$message.success(res.data)
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
          .finally(() => {
            this.initData()
          })
      })
    },
    async syncMetaData(row) {
      this.$confirm("此操作将同步数据源, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async() => {
          await Request.source.syncMetaData({
            id: row.id
          })
          this.$message.success("同步成功")
        })
        .catch(() => {
        
        })
    },
    uploadLine(id) {
      this.formDialog.sourceId = id
      this.formDialog.mode = 0
      this.formDialog.tableName = ""
      this.formDialog.primaryKeys = ""
      this.formDialog.indexKeys = ""
      this.formDialog.file = ""
      this.importVisible = true
    },
    initData() {
      this.loading = true
      Request.source
        .getAll()
        .then(res => {
          this.tableData = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    beforeUpload(file) {
      console.log("file:", file)
      this.formDialog.file = file
    },
    submitCSV() {
      let fd = new FormData()
      fd.append("file", this.formDialog.file)
      fd.append("sourceId", this.formDialog.sourceId)
      fd.append("mode", this.formDialog.mode)
      fd.append("tableName", this.formDialog.tableName)
      fd.append("primaryKeys", this.formDialog.primaryKeys)
      fd.append("indexKeys", this.formDialog.indexKeys)

      Axios({
        method: "POST",
        url:
          "/api/dds-server-bi/bi/source/" +
          this.formDialog.sourceId +
          "/uploadcsv",
        headers: { ...this.$utils.auth.getAdminheader() },
        data: fd
      }).then(res => {
        if (res.data.code === 200) {
          this.$message.success(res.data.data ? res.data.data : "上传成功")
          setTimeout(() => {
            this.importVisible = false
          }, 1500)
        } else {
          this.$message.error(
            res.data.message ? res.data.message : "上传失败，请稍后重试"
          )
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cardList {
  .title {
    margin-top: 100px;
  }

  .buttons {
    margin-top: 50px;
  }
  .fileInfo {
    display: block;
    margin-top: 10px;
    height: 26px;
    line-height: 26px;
  }
}
</style>
