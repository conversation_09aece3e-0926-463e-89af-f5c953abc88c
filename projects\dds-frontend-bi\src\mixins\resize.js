import debounce from "lodash/debounce" // 防抖函数
// import * as echarts from 'echarts';
export default {
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener("resize", debounce(this.resize, 500))
  },
  methods: {
    resize() {
      if (this.chart) {
        this.chart.resize()
        if (this.calculationLocation) {
          this.calculationLocation(this.maxWidth)
        }
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          (el, i) => `<div class="content-panel">
        <p>
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${el.value[el.dimensionNames[el.encode.y[0]]]}${i > 2 ? "%" : "人"}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart?.dispose()
    this.chart = null
  }
}
