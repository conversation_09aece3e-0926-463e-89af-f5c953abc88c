<template>
  <div :class="{ filterItem: !filterTree.root&&filterTree.type }">
    <div v-if="filterTree.type == 'link'">
      <div class="filterBlock">
        <div class="filterRel">
          <div class="filterRadio">
            <el-radio-group v-model="filterTree.rel" size="mini">
              <el-radio-button label="and">And</el-radio-button>
              <el-radio-button label="or">Or</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="filterList">
          <render-filters
            v-for="c in filterTree.children"
            :key="c.id"
            v-on="$listeners"
            :filter-tree="c"
          />
        </div>
      </div>
    </div>
    <div v-else-if="filterTree.type == 'node'">
      <div class=" noPadding" :class="{ filterItem: !filterTree.root }">
        <div class="filterFormItem">
          <p>{{ filterTree.name }}</p>
        </div>
        <div class="filterFormItem">
          <el-select v-model="filterTree.operator" placeholder="">
            <el-option
              v-for="item in options"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div class="filterFormItem">
          <el-input v-model="filterTree.value" placeholder=""></el-input>
        </div>
        <el-button
          circle
          icon="el-icon-plus"
          type="primary"
          @click="addParallelNode(filterTree.id)"
        />
        <el-button
          v-if="!filterTree.root"
          circle
          icon="el-icon-s-operation"
          type="primary"
          @click="forkTreeNode(filterTree.id)"
        />

        <el-button
          circle
          icon="el-icon-minus"
          @click="deleteNode(filterTree.id)"
        />
      </div>
    </div>
    <div v-else class="empty" @click="onAddRoot">
      <h3><i class="el-icon-plus"></i>点击添加</h3>
    </div>
  </div>
</template>

<script>
export default {
  name: "render-filters",
  components: {},
  props: {
    filterTree: {
      type: Object,
      default: () => ({}),
    },
    filtersName:{
      type: String,
      default: "",
    },
    sqlType:{
      type: String,
      default: "",
    },
  },
  data() {
    return {
      options:[ '=', '>', '<', '>=', '<=', '!=' ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    onAddRoot() {
      this.$emit("onAddRoot")
      console.log("xxxx")
    },
    addParallelNode(nodeId) {
      this.$emit("onAddTreeNode", nodeId)
    },
    deleteNode(nodeId) {
      this.$emit("onDeleteTreeNode", nodeId)
    },
    forkTreeNode(nodeId) {
      this.$emit("onForkTreeNode", nodeId)
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-select .el-input__inner {
  width: 60px !important;
}
::v-deep .el-input--small .el-input__inner {
  width: 100px;
}
::v-deep .el-radio-button__inner {
  width: 40px;
  text-align: left;
  display: flex;
  justify-content: center;
  align-items: center;
}
.empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.filterBlock {
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.filterRel {
  width: 88px;
  margin: 0 5px;
  flex-shrink: 0;
  position: relative;

  &:after {
    width: 10px;
    height: 0;
    border-top: 1px solid skyblue;
    position: absolute;
    top: 50%;
    right: -10px;
    content: "";
  }
}
.filterRadio {
  width: 120px;
  margin-right: 5px;
}
.filterList {
  padding-left: 5px;
  margin-left: 5px;
  border-left: 1px solid skyblue;
  display: flex;
  flex-direction: column;
}
.noPadding {
  padding: 4px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.filterItem {
  padding: 4px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;

  & > *:not(.filterBlock) {
    margin: 0 4px;
  }

  &:before {
    width: 10px;
    height: 0;
    border-top: 1px solid skyblue;
    position: absolute;
    top: 50%;
    left: -5px;
    content: "";
  }

  &.root:before {
    width: 0;
    display: none;
  }

  &.noPadding {
    padding: 0;
  }

  &:first-child,
  &:last-child {
    &:after {
      width: 3px;
      background-color: #ffffff;
      position: absolute;
      left: -7px;
      content: "";
    }
  }

  &:first-child {
    &:after {
      height: 50%;
      top: 0;
    }
  }

  &:last-child {
    &:after {
      height: 50%;
      bottom: -1px;
    }
  }
}

.filterFormItem {
  margin-bottom: 0;

  .inputNumber {
    width: 100%;
  }
}

.filterFormKey {
  padding-left: 8px;
  font-weight: bold;
}

.filterFormOperator {
  min-width: 60px;
}

.filterFormInput {
  width: 100px;
}
</style>
