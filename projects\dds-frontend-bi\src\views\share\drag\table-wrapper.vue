<template>
  <div class="table-wrapper">
    <!-- 如果是根源于不渲染tip -->
    <div
      v-if="!root"
      class="table-link-tip"
      :style="{
        top: getTop(index, 43, parTop) + 'px',
        left: item.level * 180 + (item.level - 1) * 90 + 'px',
      }"
    >
      <!-- 图标 -->
      <div class="table-link-icon-wrapper">
        <i class="el-icon-set-up"></i>
      </div>
      <!-- 横线 -->
      <div
        class="table-link-line horizontal"
        :style="{
          width: index ? '72px' : '90px',
          top: '14px',
        }"
      ></div>
      <!-- 竖线 -->
      <div
        class="table-link-line vertical"
        :style="{
          height: getTop(index, 43, 0) + 'px',
          bottom: '14px',
        }"
      ></div>
    </div>
    <!-- 表信息 -->
    <div
      class="table-info"
      :style="{
        top: getTop(index, 42, parTop) + 'px',
        left: item.level * 270 + 'px',
      }"
    >
      {{ item.target }}
    </div>
    <template v-if="index == tree.length - 1 && !root">
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        show-vertical
        :level="item.level"
        :source="item.source"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop) + 42"
        :tip-top="getTop(index, 43, parTop) + 43"
        :tip-height="getTop(index, 43, 0) + 43"
      />
    </template>
    <!-- 如果有子元素递归渲染 -->
    <template v-if="item.children && item.children.length">
      <table-wrapper
        v-for="(e, i) in item.children"
        :item="e"
        :key="i"
        :tree="item.children"
        :dargging="dargging"
        :index="i"
        v-on="$listeners"
        :par-top="getTop(index, 42, parTop)"
      />
    </template>
    <template v-else>
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        :source="item.target"
        :level="item.level + 1"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop)"
        :tip-top="getTop(index, 43, parTop)"
      />
    </template>
  </div>
</template>

<script>
import DropBox from "./drop-box"
export default {
  name: "table-wrapper",
  components: { DropBox },
  props: {
    item: {
      type: Object,
    },
    index: {
      type: Number,
    },
    tree: {
      type: Array,
    },
    parTop: {
      type: Number,
      default: 0,
    },
    root: {
      type: Boolean,
      default: false,
    },
    dargging: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    getTop(index, num, top = 0) {
      if (index) {
        return (this.tree[index - 1].children.length + index) * num + top
      } else {
        return index * num + top
      }
    },
  },
}
</script>

<style scoped lang="scss">
.table-wrapper {
  position: absolute;
  .table-link-tip {
    width: 90px;
    height: 28px;
    position: absolute;
    align-items: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
    .table-link-line {
      position: absolute;
      z-index: 2;
      background: #c1c1c1;
    }
    .table-link-line.vertical {
      width: 1px;
      left: 18px;
    }
    .table-link-line.horizontal {
      right: 0;
      height: 1px;
    }
  }
  .table-link-tip:hover .table-link-line {
    background: #2153d4;
  }
  .table-info {
    height: 28px;
    width: 180px;
    background: #fff;
    position: absolute;
    color: #000;
    cursor: pointer;
    padding: 1px 1px 1px 0;
    line-height: 28px;
    border-left: 2px solid #2153d4;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
