<template>
  <el-dialog
    title="大屏设置"
    :visible.sync="visible"
    append-to-body
    width="60%"
    @close="this.close"
  >
    <el-form
      label-width="140px"
      :model="displayParams"
      ref="form"
      :rules="rules"
    >
      <el-row gutter="8">
        <el-col span="12">
          <el-card class="box-card" header="全局播放设置">
            <el-form-item label="自动播放:">
              <el-checkbox v-model="displayParams.autoPlay"></el-checkbox>
            </el-form-item>
            <el-form-item label="每页停留时间(秒):" prop="autoSlide">
              <el-input-number
                controls-position="right"
                :min="3"
                v-model="displayParams.autoSlide"
              ></el-input-number>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col span="12">
          <el-card class="box-card" header="全局动画设置">
            <el-form-item label="过渡动画:" label-width="100px">
              <el-select
                placeholder="请选择"
                v-model="displayParams.transitionStyle"
              >
                <el-option
                  v-for="item in transitionStyleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="动画速度(秒):"
              label-width="100px"
            
            >
              <el-select v-model="displayParams.transitionSpeed" placeholder="请选择">
                <el-option label="默认" value="default"> </el-option>
                <el-option label="快" value="fast"> </el-option>
                <el-option label="慢" value="slow"> </el-option>
              </el-select>
            </el-form-item>
          </el-card>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="this.close">取 消</el-button>
      <el-button type="primary" @click="saveDisplay">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex"
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      displayParams: {
        autoPlay: false,
        autoSlide: 10,
        transitionStyle: "none",
        transitionSpeed: "default",
      },
      rules: {
        autoSlide: [ { required: true, message: "请输入数字", trigger: "blur" } ],
      },
      transitionStyleOptions: [
        {
          label: "无",
          value: "none",
        },
        {
          label: "淡入淡出",
          value: "fade",
        },
        {
          label: "滑动",
          value: "slide",
        },
        {
          label: "凸镜",
          value: "convex",
        },
        {
          label: "凹镜",
          value: "concave",
        },
        {
          label: "缩放",
          value: "zoom",
        },
      ],
    }
  },
  computed: {
    ...mapGetters({
      displayInfo: "displayInfo",
    }),
  },
  created() {},
  mounted() {},
  watch: {
    displayInfo(val) {
      if (val) {
        this.displayParams = val.config.displayParams
      }
    },
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit("update:visible", false)
    },
    // 保存大屏设置
    saveDisplay() {
      console.log('%cDisplaySettingModal.vue line:145 object', 'color: #007acc;', this.displayInfo)
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          const { code } = await this.$httpBi.display.updDisplay({
            ...this.displayInfo,
            config: JSON.stringify({
              displayParams: this.displayParams,
            })
          })
          if (code === 200) {
            this.$message.success("保存成功")
            this.close()
            this.$store.commit("display/SET_DISPLAY_INFO", {
              ...this.displayInfo,
              config: {
                displayParams: this.displayParams,
              },
            })
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="less">
</style>
