<template>
  <div ref="content" class="content">
    <div class="tip" v-if="showSelection">
      <span class="tip-name">
        当前表格已选择
        <span class="tip-count">{{ selection.length }}</span>
        项
      </span>
      <div class="btn" @click="toggleSelection">清空</div>
      <div class="btn" v-if="showBatchTag" @click="handleMakeTag">
        批量打标签
      </div>
      <div class="btn" @click="handleExport" disabled>批量导出</div>
      <slot name="btn" :selection="selection"></slot>
      <div class="right-btn">
        <el-tooltip
          class="item"
          effect="dark"
          content="全部导出"
          placement="top"
        >
          <svg-icon
            class="icon"
            icon-class="export1"
            @click="handleAllExport"
          />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="列显隐" placement="top">
          <svg-icon class="icon" @click="drawer = true" icon-class="setting" />
        </el-tooltip>
      </div>
    </div>
    <el-table
      :data="tableData"
      ref="multipleTable"
      :key="resizeKey"
      v-loading="loading"
      :row-key="getRowKey"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      v-on="$listeners"
      v-bind="$attrs"
      style="width: 100%"
    >
      <template v-if="show">
        <el-table-column
          v-if="showSelection"
          type="selection"
          width="55"
          reserve-selection
        ></el-table-column>
        <template v-for="column in tableColumns">
          <el-table-column
            reserve-selection
            v-if="column.visible"
            :prop="column.prop"
            :key="column.prop"
            :label="column.label"
            :min-width="
              column.width
                ? column.width
                : flexColumnWidth(column.prop, column.fixed)
            "
            :sortable="column.sortable"
            :fixed="column.fixed"
          >
            <template v-if="column.slot" #default="scope">
              <slot :name="column.prop + 'Slot'" v-bind="scope"></slot>
            </template>

            <template v-else #default="scope">
              <template v-if="column.type === 'index'">
                {{ (page.currentPage - 1) * page.pageSize + scope.$index + 1 }}
              </template>
              <span
                v-else-if="column.formatter"
                v-html="column.formatter(scope.row[column.prop])"
              ></span>
              <template v-else>
                {{ scope.row[column.prop] || "-" }}
              </template>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <DT-Pagination
      :hidden="page.total === 0"
      :total="page.total"
      :page-size="page.pageSize"
      :current-page="page.currentPage"
      @sizeChange="handleSizeChange"
      :pager-count="pagerCount"
      @currentChange="handleCurrentChange"
    />
    <el-drawer title="列显隐" :visible.sync="drawer" @close="drawer = false">
      <el-table :data="tableColumns" border style="width: 100%">
        <el-table-column prop="label" label="列名"></el-table-column>
        <el-table-column prop="visible" label="隐藏">
          <template #default="{ row }">
            <el-checkbox
              v-model="row.visible"
              @change="handleVisible"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="visible" label="排序">
          <template #default="{ row }">
            <el-checkbox
              v-model="row.sortable"
              true-label="custom"
              :false-label="false"
            ></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script>
import { getTextWidth } from "@/utils"
import debounce from "lodash/debounce" // 防抖函数
export default {
  components: {},
  props: {
    // 分页配置
    page: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    },
    // 表格loading
    loading: {
      type: Boolean,
      default: false
    },
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 表格列配置
    tableColumns: {
      type: Array,
      default: () => []
    },
    // 是否显示批量打标签
    showBatchTag: {
      type: Boolean,
      default: false
    },
    // 是否显示多选框
    showSelection: {
      type: Boolean,
      default: true
    },
    // 是否显示批量移除
    showDetachg: {
      type: Boolean,
      default: false
    },
    pagerCount: {
      type: String || Number,
      default: 7
    },
    id: {
      type: String,
      default: "id"
    },
    id1: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      resizeKey: "key",
      selection: [],
      drawer: false,
      show: false,
      columnsWidthMap: new Map(),
      totalWidth: 0
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.show = true
    })

    window.addEventListener("resize", debounce(this.resize, 500))
  },
  watch: {
    tableData() {
      this.columnsWidthMap.clear()
    },
    tableColumns() {
      this.columnsWidthMap.clear()
    }
  },
  methods: {
    resize() {
      this.$nextTick(() => {
        this.resizeKey = Math.random()
        this.$nextTick(() => {
          this.selection.forEach(row => {
            this.$refs.multipleTable.toggleRowSelection(row)
          })
        })
      })
    },
    clearSort() {
      this.$refs.multipleTable.clearSort()
    },
    flexColumnWidth(prop) {
      const { tableData, tableColumns } = this
      // 如果没有数据直接返回auto
      if (!tableData || !tableData.length) return "auto"

      // 获取当前table元素dom宽度
      let tableElWidth = this.showSelection
        ? this.$refs.content.offsetWidth - 55
        : this.$refs.content.offsetWidth

      // 当前字段的宽度
      let currentMaxWidth = 0

      // 所有字段的宽度

      // 平均宽度
      let avgWidth = tableElWidth / tableColumns.length - 20

      if (this.columnsWidthMap.has(prop)) {
        currentMaxWidth = this.columnsWidthMap.get(prop)
      } else {
        this.totalWidth = this.showSelection ? 55 : 0
        tableColumns.forEach(c => {
          let maxTextWidth =
            getTextWidth(
              c.label,
              14,
              "PingFangSC-Medium, PingFang SC",
              "bold"
            ) +
            21 +
            (c.sortable ? 24 : 0)
          console.log(c.label, "c.label", maxTextWidth, avgWidth)
          // 遍历取最长字段
          tableData.forEach(element => {
            maxTextWidth = Math.max(
              maxTextWidth,
              getTextWidth(element[c.prop], 14) + 21
            )
          })
          // 记录当前字段最大宽度
          if (c.prop === prop) {
            currentMaxWidth = maxTextWidth
          }
          // 如果当前字段有固定宽度
          if (c.width) {
            maxTextWidth = c.width
          }
          // 当前字段不显示
          if (!c.visible) {
            maxTextWidth = 0
          }
          this.columnsWidthMap.set(c.prop, maxTextWidth)
          this.totalWidth = this.totalWidth + maxTextWidth
        })
      }
      return currentMaxWidth
    },
    getRowKey(row) {
      if (this.id1) {
        return row[this.id] + row[this.id1]
      }
      return row[this.id]
    },
    // 选择
    handleSelectionChange(val) {
      this.selection = val
    },
    // 清空
    toggleSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    // 分页
    handleCurrentChange(val) {
      this.$emit("update:page", {
        ...this.page,
        currentPage: val.currentPage
      })
      this.$emit("onload", this.page)
    },
    // 每页显示条数
    handleSizeChange(val) {
      this.$emit("update:page", {
        ...this.page,
        currentPage: 1,
        pageSize: val.pageSize
      })
      this.$emit("onload", this.page)
    },
    // 批量移除
    handleRemove() {
      if (this.selection.length === 0) {
        this.$message.error("请选择要移除的数据")
        return
      }
      this.$emit("handleRemove", this.selection)
    },
    // 批量打标签
    handleMakeTag() {
      if (this.selection.length === 0) {
        this.$message.error("请选择要打标签的数据")
        return
      }
      this.$emit("handleMakeTag", this.selection)
    },
    // 批量导出
    handleExport() {
      if (this.selection.length === 0) {
        this.$message.error("请选择要导出的数据")
        return
      }
      this.$emit("handleExport", this.selection)
    },
    handleUpdate() {
      if (this.selection.length === 0) {
        this.$message.error("请选择要更新的数据")
        return
      }
      this.$emit("handleUpdate", this.selection)
    },
    handleAllExport() {
      this.$notify.info({
        title: "温馨提示",
        message: "如果数据庞大会导致下载缓慢哦，请您耐心等待！"
      })
      this.$emit("handleExport", [])
    },
    handleSortChange({ column, prop, order }) {
      this.$emit("handleSortChange", {
        column,
        prop,
        order
      })
    },
    handleVisible() {
      console.log(this.tableColumns)
      this.$emit("update:tableColumns", this.tableColumns)
    },
    getTextWidth
  }
}
</script>

<style scoped lang="scss">
.content {
  width: 100%;
}
::v-deep .el-drawer .el-drawer__body {
  overflow: auto;
}
.tip {
  padding: 0 16px;
  box-sizing: border-box;
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background: rgba(91, 143, 249, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(91, 143, 249, 0.2);
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #222222;
  .tip-count {
    color: #5b8ff9;
    margin: 0 4px;
  }
  .btn {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #5b8ff9;
    margin-left: 24px;
    cursor: pointer;
  }
  .icon {
    //右对齐

    margin-left: 16px;
    cursor: pointer;
  }
  .right-btn {
    //右对齐

    margin-left: auto;
    .icon {
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
/* 为了确保自定义样式能覆盖默认样式，你可能需要使用更具体的选择器 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; /* 设置滚动条宽度 */
  height: 6px; /* 设置滚动条高度 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 3px; /* 滚动条圆角 */
  background: rgba(0, 0, 0, 0.2); /* 滚动条颜色 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1); /* 滚动条轨道阴影 */
  background-color: #f0f0f0; /* 滚动条轨道颜色 */
  border-radius: 3px; /* 滚动条圆角 */
}
</style>
