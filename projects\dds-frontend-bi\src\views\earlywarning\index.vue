<template>
  <dt-single-page-view>
    <DT-Form
      v-model="searchData"
      type="search"
      :show-button="true"
      :render="render"
      @confirm="handleSearch"
    />
    <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
      新增预警
    </el-button> -->
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="warnName" label="预警名称"> </el-table-column>
      <el-table-column prop="dashboardName" label="看板名称"> </el-table-column>
      <el-table-column prop="widgetName" label="图表名称"> </el-table-column>
      <el-table-column prop="status" label="预警状态">
        <template slot-scope="{ row }">
          <el-tag
            :type="row.status === '1' ? 'success' : 'danger'"
            disable-transitions
          >
            {{ row.status === '1' ? '开启' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="创建人"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间"> </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="{ row }">
          <el-button
            @click.native.prevent="startRow(row)"
            type="text"
            size="small"
          >
            {{ row.status === '0' ? '开启' : '禁用' }}
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <!-- <el-button
            @click.native.prevent="editRow(row)"
            type="text"
            size="small"
          >
            编辑
          </el-button>
          <el-divider direction="vertical"></el-divider> -->
          <el-button
            @click.native.prevent="deleteRow(row.id)"
            type="text"
            size="small"
          >
            删除
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button
            @click.native.prevent="recordRow(row.id)"
            type="text"
            size="small"
          >
            预警记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="searchData.currentPage"
      :page-size="searchData.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </dt-single-page-view>
</template>

<script>
import Request from '@/service'
export default {
  components: {},
  props: {},
  data() {
    return {
      show: false,
      loading: true,
      total: 0,
      tableData: [],
      searchData: {
        currentPage: 1,
        pageSize: 10,
        type: '0',
        name: ''
      },
      render: [
        {
          type: 'input',
          key: 'name',
          label: '预警名称',
          props: {
            placeholder: '请输入预警名称'
          }
        }
      ]
    }
  },
  computed: {},
  created() {
    this.getData()
  },
  mounted() {},
  watch: {},
  methods: {
    // table-点击“禁用/启用”
    startRow(row) {
      row.triggerStatus = row.status === '0' ? '1' : '0'
      Request.warning
        .updStatusWarning({
          id: row.id,
          triggerStatus: row.triggerStatus
        })
        .then(() => {
          this.$message.success(
            (row.triggerStatus === '1' ? '启用' : '禁用') + '成功'
          )
          this.getData()
        })
        .catch(() => {
          this.$message.error('操作失败')
        })
        .finally(() => {
          this.loading = false
          this.dialogVisible = false
        })
    },
    async getData() {
      this.loading = true
      Request.warning
        .getPage({
          currentPage: this.searchData.currentPage, // 页码
          pageSize: this.searchData.pageSize, // 页面大小
          name: this.searchData.name // 配置名称
        })
        .then(res => {
          this.tableData = res.data.list
          this.total = res.data.totalCount
        })
        .catch(() => {
          this.$message.error('操作失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    // table-点击“删除”
    async deleteRow(id) {
      this.$confirm('此操作将删除选中数据, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        Request.warning
          .delWarning({ id: id })
          .then(() => {
            this.$message.success('删除成功')
          })
          .catch(() => {
            this.$message.error('操作失败')
          })
          .finally(() => {
            this.getData()
          })
      })
    },
    // table-点击“预警记录”
    recordRow(id) {
      this.$router.push({
        name: 'EarlywarningRecord',
        query: {
          id
        }
      })
    },
    // 添加或更新数据
    async onSaveWarning(row, text) {
      this.loading = true
      if (row.id) {
        Request.warning
          .updateWarning({
            ...row,
            triggerRules: JSON.stringify(row.triggerRules),
            noticeType: JSON.stringify(row.noticeType)
          })
          .then(() => {
            this.$message.success(text ? text + '成功' : '更新成功')
            this.getData()
          })
          .catch(() => {
            this.$message.error('操作失败')
          })
          .finally(() => {
            this.loading = false
            this.dialogVisible = false
          })
      } else {
        // await this.$httpBi.warning.addWarning({
        //   ...this.ruleForm,
        //   receiver: this.$store.state.user.userId,
        //   dashboardId: this.currentWidget.dashboardId,
        //   viewId: this.currentWidget.viewId,
        //   widgetId: this.currentWidget.widgetId,
        //   triggerRules: JSON.stringify(this.ruleForm.triggerRules),
        //   noticeType: JSON.stringify(this.ruleForm.noticeType)
        // })
        // this.init()
        // this.dialogVisible = false
      }
    },
    // 切换table页码
    handleCurrentChange(val) {
      this.searchData.currentPage = val
      this.getData()
    },
    // 点击顶部“搜索”
    handleSearch() {
      this.searchData.currentPage = 1
      this.getData()
    }
  }
}
</script>

<style scoped lang="less"></style>
