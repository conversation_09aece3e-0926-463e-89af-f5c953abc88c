<template>
  <DT-View :outer-style="{ padding: 0 }">
    <DT-Header @back="$emit('closeAdd')" title="返回" content="指标对比分析" />
    <SearchForm
      @search="getAllIndicators"
      :columns="columns"
      :is-card="false"
      :search-param.sync="form"
      style="margin: 24px 0"
    />
    <div class="container">
      <!-- 相关指标 -->
      <div class="left">
        <div class="title">检索到{{ allIndicatorsNumber }}项相关指标</div>
        <div class="content">
          <div
            class="target-item"
            v-for="(item, index) in allIndicators"
            :key="index"
          >
            <div class="target-item-title">
              {{ item.name }}({{ item.children.length }})
            </div>
            <div
              class="target-item-content"
              v-for="(e, i) in item.children"
              :key="i"
            >
              <div class="target-type">
                <div
                  class="target-type-item"
                  id="draggable"
                  :draggable="!selectIndicators.length || isDrag(e)"
                  :style="{
                    cursor:
                      !selectIndicators.length || isDrag(e)
                        ? 'move'
                        : 'not-allowed'
                  }"
                  @dragstart="onDragStart(e, $event)"
                  @dragend="onDragEnd"
                >
                  <span class="zbmc">{{ e.zbmc }}</span>
                  <span class="zblx">{{ e.zblx }}</span>
                </div>
              </div>
              <div class="tags">
                <!-- gray: !isSameDimensionsGray(wd)  -->
                <div
                  class="tag-item gray"
                  :class="{
                    deep: isSameDimensions(wd)
                  }"
                  v-for="wd in e.xsc.slice(0, 5)"
                  :key="wd.id"
                >
                  {{ wd.zdmc }}
                </div>
                <span v-if="e.xsc.length > 5">等5项维度</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <template v-if="selectIndicators.length">
          <!-- 已经选择指标 -->
          <div class="target">
            <div class="target-title">已选择指标</div>
            <div class="target-wrap">
              <el-tag
                v-for="tag in selectIndicators"
                style="margin-right: 12px"
                :key="tag"
                closable
                effect="plain"
                @close="deleteSelectIndicators(tag)"
              >
                <!-- @close="selectIndicators.splice(index, 1)" -->

                {{ tag.zbmc }}
              </el-tag>
              <div
                class="target-content"
                id="droppable"
                :class="isDragover ? 'dragover' : ''"
                @dragover.prevent="isDragover = true"
                @dragleave="isDragover = false"
                @drop="onDrop"
              >
                请将指标拖动到此处
              </div>
            </div>
          </div>
          <!-- 已选择维度 -->
          <div class="dimensionality">
            <div class="dimensionality-title">已选择维度</div>
            <div class="dimensionality-content">
              <el-form :inline="true" class="inline-form">
                <el-form-item
                  v-for="(item, i) in selectDimensionList"
                  :label="item.wdmc"
                  :key="i"
                >
                  <el-select
                    v-model="item.vals"
                    multiple
                    placeholder="请选择"
                    @change="getTableData"
                  >
                    <el-option
                      v-for="item in item.wdz"
                      :key="item.value"
                      :label="item.name"
                      :value="item.bm"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                    style="margin-left: 16px"
                    @click="addDimensionality"
                    :loading="commonDimension"
                  >
                    添加维度
                  </el-button>
                </el-form-item>
              </el-form>
              <el-button
                style="margin-left: 16px"
                @click="dialogShow = true"
                type="primary"
                v-if="tableData.length"
              >
                保存为分析主题
              </el-button>
            </div>
          </div>
          <!-- 汇总表 -->
          <div class="summary">
            <div class="summary-title">
              {{ selectIndicators.map(item => item.zbmc).join("、") }}汇总表
              <div class="btn" v-if="tableData.length">
                <el-button @click="chartDialogVisible = true">
                  生成图表
                </el-button>
                <el-button type="primary" @click="handleExport">导出</el-button>
              </div>
            </div>
            <el-table
              v-if="tableData.length"
              :data="tableData"
              style="width: 100%"
              border
              height="300"
            >
              <el-table-column
                v-for="(column, index) in headKeys"
                :prop="column.zd"
                :label="column.zdmc"
                :key="index"
              ></el-table-column>
            </el-table>
          </div>
        </template>
        <div
          v-else
          class="empty"
          :class="isDragover ? 'dragover' : ''"
          @dragover.prevent="isDragover = true"
          @dragleave="isDragover = false"
          @drop="onDrop"
        >
          <span>请将指标拖动到此处</span>
          <span style="margin: 10px">或</span>
          <el-button type="primary" @click="themeDialogVisible = true">
            加载分析主题
          </el-button>
        </div>
      </div>
    </div>
    <el-dialog
      title="请选择要添加的维度"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <div>
        <el-checkbox-group v-model="selectDimensionIds" @change="getTableData">
          <el-checkbox
            v-for="(item, index) in sameDimension"
            :label="item.similarlyId"
            :key="index"
          >
            {{ item.wdmc }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </el-dialog>
    <el-dialog
      title="选择分析主题"
      :visible.sync="themeDialogVisible"
      width="786"
    >
      <div style="display: flex; align-items: center">
        <div class="label" style="margin-right: 10px; white-space: nowrap">
          快速检索:
        </div>
        <el-input
          v-model="input"
          style="width: 220px"
          placeholder="请输入主题名称、指标名称"
        ></el-input>
      </div>
      <CommonTable
        :page.sync="page"
        :show-selection="false"
        :table-data="tableData1"
        :show-batch-tag="false"
        :table-columns.sync="tableColumns"
        @onload="getTableData"
        height="240px"
      >
        <template #actionSlot>
          <el-button type="text" style="margin-right: 10px" @click="handleLoad">
            加载
          </el-button>
          <el-popconfirm title="确定删除该主题吗？" @onConfirm="handleDelTheme">
            <el-button type="text" slot="reference">删除</el-button>
          </el-popconfirm>
        </template>
      </CommonTable>
    </el-dialog>

    <el-dialog
      title=""
      :visible.sync="chartDialogVisible"
      width="80%"
      :before-close="handleCloseChart"
    >
      <div style="display: flex; align-items: center" slot="title">
        <div class="label" style="margin-right: 10px; white-space: nowrap">
          图表类型:
        </div>
        <el-select v-model="chartType" placeholder="请选择">
          <el-option label="折线图" value="ChartLine"></el-option>
          <el-option label="柱状图" value="ChartColumn"></el-option>
          <el-option label="饼图" value="ChartPie"></el-option>
        </el-select>
        <el-button style="margin-left: 10px" @click="exportImg">
          导出图片
        </el-button>
        <el-button style="margin-left: 10px" @click="exportPdf">
          导出PDF
        </el-button>
        <el-button style="margin-left: 10px" @click="handleExport">
          导出Excel
        </el-button>
      </div>
      <div class="search-item">
        <div class="name">维度值:</div>
        <div class="tabs">
          <div
            class="tab"
            :class="chartXField === item.wdmc ? 'active' : ''"
            @click="chartXField = item.wdmc"
            v-for="(item, i) in selectDimensionList"
            :key="i"
          >
            {{ item.wdmc }}
          </div>
        </div>
      </div>
      <div class="chart-box" ref="chartBox">
        <div class="pie-chart" v-if="chartType === 'ChartPie'">
          <div class="pie-item" v-for="(item, index) in demoWd" :key="index">
            <ChartPie
              :chart-data="chartData"
              :series-name="item.zdmc"
              color-field="w1"
              :angle-field="item.zd"
              y-axis-name="item"
              unit=""
              :subtitle="item.zdmc"
              is-formatter-x-axis
              ref="chartAll"
            />
          </div>
        </div>
        <components
          v-else
          :is="chartType"
          :chart-data="chartData"
          x-field="w1"
          :y-field="yField"
          :series-name="seriesName"
          y-axis-name=""
          :bar-width="16"
          show-data-zoom
          :show-num="8"
          unit=""
          is-formatter-x-axis
          ref="chartAll"
        />
      </div>
    </el-dialog>

    <el-dialog
      title="保存为分析主题"
      :border="true"
      :visible.sync="dialogShow"
      @close="handleCancel"
    >
      <el-form
        :model="themeInfo"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="主题名称" prop="name">
          <el-input
            v-model="themeInfo.name"
            placeholder="请输入主题名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="仅我可见">
          <el-radio-group v-model="themeInfo.isOnlySee">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>
<script>
import options from "../mixins/options"
import html2canvas from "html2canvas"
import SearchForm from "@/components/SearchForm/index.vue"
import { jsonToSheetXlsx } from "@/utils/ExportExcel"
import CommonTable from "@/components/CommonTable"
import ChartLine from "@/components/Charts/ChartLine"
import ChartColumn from "@/components/Charts/ChartColumn"
import ChartPie from "@/components/Charts/ChartPie"

export default {
  components: { CommonTable, ChartLine, ChartColumn, ChartPie, SearchForm },
  mixins: [options],

  props: {},
  data() {
    return {
      commonDimension: false,
      chartXField: "",
      demoWd: [],

      themeInfo: {
        name: "",
        isOnlySee: 1
      },
      dialogShow: false,
      chartType: "ChartLine",
      // 已选择指标
      selectIndicators: [],
      // 已选择维度id
      selectDimensionIds: [],

      // 维度子级
      selectdimensionalityChild: {},
      // 全部指标
      allIndicators: [],
      // 相同维度
      sameDimension: [],
      isDragover: false,
      dialogVisible: false, // 选择维度弹窗
      themeDialogVisible: false, // 分析主题弹窗
      chartDialogVisible: false, // 图表
      params: {
        zb: [],
        wd: []
      },
      tempParams: {
        zb: [],
        wd: []
      },
      form: {
        zbmc: "",
        lxbm: "",
        pswd: [],
        jsfs: "",
        zcyj: ""
      },

      columns: [
        {
          label: "指标名称",
          prop: "zbmc",
          search: {
            el: "input"
          }
        },
        {
          label: "指标类型",
          prop: "zblx",
          type: "select",
          search: {
            el: "select"
          },
          enum: this.getIndicatorType
        },
        {
          label: "包含维度",
          prop: "pswd",
          search: {
            el: "select",
            props: {
              multiple: true
            }
          },
          fieldNames: {
            label: "wdmc",
            value: "id"
          },
          enum: this.getWdTableList
        },
        {
          label: "计算方式",
          prop: "jsfs",
          search: {
            el: "select"
          },
          enum: []
        },
        {
          label: "支持预警",
          prop: "zcyj",
          search: {
            el: "select"
          },
          enum: [
            {
              label: "是",
              value: "1"
            },
            {
              label: "否",
              value: "0"
            }
          ]
        }
      ],
      tableData: [],
      headKeys: [],
      page: {
        total: 1,
        currentPage: 1,
        pageSize: 10
      },
      tableData1: [
        {
          val1: "在校生基本情况分析",
          val2: "全日制在校生数、普通本专科全日制在校生数、硕士研究生全日制在校生数、博士研究生全日制在校生数",
          val3: "院系 /  性别",
          val4: "admin"
        }
      ], // 分析主题数据
      tableColumns: [
        {
          label: "主题名称",
          prop: "val1",
          visible: true,
          sortable: false
        },
        {
          label: "包含指标",
          prop: "val2",
          visible: true,
          width: 200,
          sortable: false
        },
        {
          label: "检索维度",
          prop: "val3",
          visible: true,
          sortable: false
        },
        {
          label: "创建人",
          prop: "val4",
          visible: true,
          sortable: false
        },
        {
          label: "操作",
          prop: "action",
          visible: true,
          slot: true,
          sortable: false
        }
      ],

      chartFormatData: {
        // 指标
        indicators: [
          {
            name: "全日制在校生数",
            value: "z1"
          },
          {
            name: "普通本专科全日制在校生数",
            value: "z2"
          }
        ],
        // 维度
        dimension: [
          {
            name: "院系",
            value: "w1"
          },
          {
            name: "性别",
            value: "w2"
          }
        ]
      }
    }
  },
  computed: {
    // 全部指标数量
    allIndicatorsNumber() {
      console.log(this.allIndicators)
      if (!this.allIndicators) return 0
      return this.allIndicators.reduce((total, item) => {
        if (!item.children) return total + 0
        return total + item.children.length
      }, 0)
    },
    // 获取所有单元格合并数据
    spanArr() {
      if (!this.tableColumn.length) return []
      const mergeCols = [this.headKeys[0]] // 需要合并的列（字段）
      return this.getMergeCells(
        this.tableData.slice(1),
        this.tableColumn,
        mergeCols
      )
    },
    tableColumn() {
      return this.headKeys.map(item => {
        return {
          prop: item,
          label: this.tableData[0][item]
        }
      })
    },
    selectDimensionList() {
      return this.sameDimension.filter(item => {
        if (this.selectDimensionIds.includes(item.similarlyId)) {
          return {
            ...item,
            vals: [""]
          }
        }
      })
    },
    yField() {
      return this.demoWd.map(item => item.zd)
    },
    seriesName() {
      return this.demoWd.map(item => item.zdmc)
    },
    chartData() {
      if (this.chartDialogVisible) {
        return this.tableData.filter(item => item.t1 === this.chartXField)
      } else {
        return []
      }
    }
  },
  created() {
    this.columns[3].enum = this.jsfsList
    this.getAllIndicators()
  },
  mounted() {},
  watch: {
    selectDimensionList() {
      if (this.selectDimensionList.length) {
        this.chartXField = this.selectDimensionList[0].wdmc
      }
    }
  },
  methods: {
    handleCloseChart() {
      this.chartDialogVisible = false
    },
    exportImg() {
      const content = this.$refs.chartBox
      console.log(content, "content")
      html2canvas(content, {
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = `${
          this.selectIndicators.map(item => item.zbmc).join("、") + "汇总表"
        }.jpg` // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    },
    exportPdf() {
      this.ExportSavePdf(
        this.selectIndicators.map(item => item.zbmc).join("、") + "汇总表",
        this.$refs.chartBox,
        false
      )
    },
    // 加载
    handleLoad() {
      this.themeDialogVisible = false

      this.selectIndicators = [
        {
          lxbm: "ps",
          zbmc: "全日制在校生数",
          ms: "",
          xsc: [
            {
              id: 326,
              aid: 209,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "学籍状态",
              wdzd: "XJZTMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 327,
              aid: 209,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "政治面貌",
              wdzd: "ZZMMMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 328,
              aid: 209,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "性别",
              wdzd: "XBMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 329,
              aid: 209,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "院系名称",
              wdzd: "YXMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            }
          ],
          cjr: "admin",
          id: 209,
          zblx: "派生指标",
          type: 1,
          parentId: 91,
          bq: ""
        },
        {
          lxbm: "ps",
          zbmc: "全日制本科在校生数",
          ms: "",
          xsc: [
            {
              id: 330,
              aid: 210,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "学籍状态",
              wdzd: "XJZTMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 331,
              aid: 210,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "就读学历",
              wdzd: "JDXL",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 332,
              aid: 210,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "院系名称",
              wdzd: "YXMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 333,
              aid: 210,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "性别",
              wdzd: "XBMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            }
          ],
          cjr: "admin",
          id: 210,
          zblx: "派生指标",
          type: 1,
          parentId: 91,
          bq: ""
        },
        {
          lxbm: "ps",
          zbmc: "全日制专科在校生数",
          ms: "",
          xsc: [
            {
              id: 334,
              aid: 211,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "学籍状态",
              wdzd: "XJZTMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 335,
              aid: 211,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "就读学历",
              wdzd: "JDXL",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 336,
              aid: 211,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "院系名称",
              wdzd: "YXMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 337,
              aid: 211,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "性别",
              wdzd: "XBMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            }
          ],
          cjr: "admin",
          id: 211,
          zblx: "派生指标",
          type: 1,
          parentId: 91,
          bq: ""
        },
        {
          lxbm: "ps",
          zbmc: "全日制专升本在校生数",
          ms: "",
          xsc: [
            {
              id: 338,
              aid: 212,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "学籍状态",
              wdzd: "XJZTMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 339,
              aid: 212,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "就读学历",
              wdzd: "JDXL",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 340,
              aid: 212,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "院系名称",
              wdzd: "YXMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 341,
              aid: 212,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "性别",
              wdzd: "XBMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            },
            {
              id: 342,
              aid: 212,
              wdid: 0,
              tabid: "ods_gxxs_bzksjbxx_x",
              zdmc: "政治面貌",
              wdzd: "ZZMMMC",
              wdlx: "ps",
              lxbm: "ps",
              wdz: "",
              wdzlx: null,
              wdzval: null
            }
          ],
          cjr: "admin",
          id: 212,
          zblx: "派生指标",
          type: 1,
          parentId: 91,
          bq: ""
        }
      ]

      this.sameDimension = [
        {
          lvl: 0,
          zb: [
            {
              id: 209,
              lxbm: "ps",
              wdzd: "XJZTMC",
              wdbm: "#326ps",
              zdmc: "学籍状态"
            },
            {
              id: 210,
              lxbm: "ps",
              wdzd: "XJZTMC",
              wdbm: "#330ps",
              zdmc: "学籍状态"
            },
            {
              id: 211,
              lxbm: "ps",
              wdzd: "XJZTMC",
              wdbm: "#334ps",
              zdmc: "学籍状态"
            },
            {
              id: 212,
              lxbm: "ps",
              wdzd: "XJZTMC",
              wdbm: "#338ps",
              zdmc: "学籍状态"
            }
          ],
          wdmc: "学籍状态",
          wdz: [
            {
              bm: "无学籍",
              name: "无学籍"
            },
            {
              bm: "有学籍",
              name: "有学籍"
            }
          ],
          similarlyId: "#326ps#330ps#334ps#338ps"
        },
        {
          lvl: 0,
          zb: [
            {
              id: 209,
              lxbm: "ps",
              wdzd: "XBMC",
              wdbm: "#328ps",
              zdmc: "性别"
            },
            {
              id: 210,
              lxbm: "ps",
              wdzd: "XBMC",
              wdbm: "#333ps",
              zdmc: "性别"
            },
            {
              id: 211,
              lxbm: "ps",
              wdzd: "XBMC",
              wdbm: "#337ps",
              zdmc: "性别"
            },
            {
              id: 212,
              lxbm: "ps",
              wdzd: "XBMC",
              wdbm: "#341ps",
              zdmc: "性别"
            }
          ],
          wdmc: "性别",
          wdz: [
            {
              bm: "女",
              name: "女"
            },
            {
              bm: "男",
              name: "男"
            }
          ],
          similarlyId: "#328ps#333ps#337ps#341ps",
          vals: []
        },
        {
          lvl: 0,
          zb: [
            {
              id: 209,
              lxbm: "ps",
              wdzd: "YXMC",
              wdbm: "#329ps",
              zdmc: "院系名称"
            },
            {
              id: 210,
              lxbm: "ps",
              wdzd: "YXMC",
              wdbm: "#332ps",
              zdmc: "院系名称"
            },
            {
              id: 211,
              lxbm: "ps",
              wdzd: "YXMC",
              wdbm: "#336ps",
              zdmc: "院系名称"
            },
            {
              id: 212,
              lxbm: "ps",
              wdzd: "YXMC",
              wdbm: "#340ps",
              zdmc: "院系名称"
            }
          ],
          wdmc: "院系名称",
          wdz: [
            {
              bm: "化学与材料科学学院",
              name: "化学与材料科学学院"
            },
            {
              bm: "动物医学院",
              name: "动物医学院"
            },
            {
              bm: "机电工程学院",
              name: "机电工程学院"
            },
            {
              bm: "风景园林与艺术设计学院",
              name: "风景园林与艺术设计学院"
            },
            {
              bm: "体育与健康学院",
              name: "体育与健康学院"
            },
            {
              bm: "湖南机电职业技术学院",
              name: "湖南机电职业技术学院"
            },
            {
              bm: "环境与生态学院",
              name: "环境与生态学院"
            },
            {
              bm: "地球科学学院",
              name: "地球科学学院"
            },
            {
              bm: "食品科学技术学院",
              name: "食品科学技术学院"
            },
            {
              bm: "人文与外语学院",
              name: "人文与外语学院"
            },
            {
              bm: "信息与智能科学技术学院",
              name: "信息与智能科学技术学院"
            },
            {
              bm: "教育学院",
              name: "教育学院"
            },
            {
              bm: "体育学院",
              name: "体育学院"
            },
            {
              bm: "新闻与传播学院",
              name: "新闻与传播学院"
            },
            {
              bm: "湖南工业职业技术学院",
              name: "湖南工业职业技术学院"
            },
            {
              bm: "长沙商贸旅游职业技术学院",
              name: "长沙商贸旅游职业技术学院"
            },
            {
              bm: "法学与社会学院",
              name: "法学与社会学院"
            },
            {
              bm: "公共管理与法学学院",
              name: "公共管理与法学学院"
            },
            {
              bm: "资源学院",
              name: "资源学院"
            },
            {
              bm: "水利与土木工程学院",
              name: "水利与土木工程学院"
            },
            {
              bm: "经济学院",
              name: "经济学院"
            },
            {
              bm: "音乐与舞蹈学院",
              name: "音乐与舞蹈学院"
            },
            {
              bm: "园艺学院",
              name: "园艺学院"
            },
            {
              bm: "商学院",
              name: "商学院"
            },
            {
              bm: "马克思主义学院",
              name: "马克思主义学院"
            },
            {
              bm: "湖南生物机电职业技术学院",
              name: "湖南生物机电职业技术学院"
            }
          ],
          similarlyId: "#329ps#332ps#336ps#340ps",
          vals: []
        }
      ]
      this.selectDimensionIds = [
        "#328ps#333ps#337ps#341ps",
        "#329ps#332ps#336ps#340ps"
      ]

      this.getTableData()
    },
    handleSave() {
      this.dialogShow = false
      this.$message.success("分析主题保存成功")
    },
    // 导出excel
    handleExport() {
      jsonToSheetXlsx({
        data: this.tableData,
        filename: "汇总表"
        // header: Object.keys(this.tableData[0]).reduce((obj, item) => {
        //   obj[item] = this.headKeys.find(e => e.zd === item).zdmc
        //   return obj
        // }, {})
      })
    },
    // 删除主题
    handleDelTheme() {
      this.$message.success("删除成功")
    },
    async getTableData() {
      let zb = this.selectIndicators.map(item => ({
        id: item.id,
        lxbm: item.lxbm
      }))
      console.log(this.selectIndicators, "this.selectIndicators")
      console.log(this.sameDimension, "this.sameDimension")
      console.log(this.selectDimensionIds, "this.selectDimensionIds")
      console.log(this.selectDimensionList, "this.selectDimensionList")

      let wd = this.selectDimensionList.map(item => ({
        vals: item.vals,
        zb: item.zb,
        wdmc: item.wdmc
      }))
      if (!wd.length) {
        return (this.tableData = []), (this.headKeys = [])
      }
      const { data } =
        await this.$httpBi.indicatorAnagement.getIndicatorAnalysis({
          zb,
          wd
        })
      this.tableData = data.data || []
      this.headKeys = data.head || []
      this.demoWd = data.head.filter(item => Number(item.type) === 1)
    },

    // 拖拽开始
    onDragStart(data) {
      console.log(data, "data")
      // 是否已存在当前指标

      this.dargging = true
      this.dragged = data
    },

    // 拖拽结束
    onDragEnd() {
      if (this.dragged) {
        this.dragged = null
      }
      this.dargging = false
    },
    // 拖拽放下
    onDrop(e) {
      if (!this.dargging) return
      e.preventDefault()
      const isHas = this.selectIndicators.some(
        item => item.id === this.dragged.id
      )
      if (isHas) {
        this.$message.warning("该指标已存在")
        if (this.selectDimensionList.length) {
          this.getTableData()
        }
        this.dargging = false
      } else {
        this.selectIndicators.push(this.dragged)
      }
      console.log(this.selectIndicators, " this.selectIndicators")
      // this.params.zb = this.selectIndicators.map(item => ({
      //   zbid: item.id,
      //   lxbm: item.lxbm
      // }))
      this.getSameDimension()
      // if (this.params.wd.length) {
      //   this.getTableData()
      // }
      this.isDragover = false
      this.tableData = []
      this.headKeys = []
    },
    // 获取相同维度
    async getSameDimension() {
      this.commonDimension = true
      let ids = this.selectIndicators.map(item => ({
        id: item.id,
        lxbm: item.lxbm
      }))
      const { data } = await this.$httpBi.indicatorAnagement.getSameDimension({
        lvl: 30, // 最低相似度  默认30以上
        data: ids
      })
      this.sameDimension = data

      this.commonDimension = false

      // this.getTableData()
    },
    // 获取维度选项
    // async getDimensionOptions(bm) {
    //   const { data } = await this.$httpBi.indicatorAnagement.getDimensionOptions({
    //     bm
    //   })
    //   this.$set(this.selectdimensionalityChild, bm, data)
    //   console.log(this.selectdimensionalityChild)
    // },
    async getDimensionOptions(bm, wdmc, isExist, promises) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionOptions({
          bm
        })

      this.$set(this.selectdimensionalityChild, bm, data)
      console.log(this.selectdimensionalityChild)

      if (!isExist) {
        this.params.wd.push({
          wdbm: bm,
          vals: [],
          wdmc
        })
      } else {
        const matchingItem = this.tempParams.wd.find(e => e.wdbm === bm)
        this.params.wd.push(matchingItem)
      }

      promises.push(Promise.resolve())
    },
    // 获取维度列表
    async getWdTableList() {
      return await this.$httpBi.indicatorAnagement.getWdTableList({})
    },
    // 获取所有指标--树性结构
    async getAllIndicators() {
      const { data } = await this.$httpBi.indicatorAnagement.getZbfx(this.form)
      this.allIndicators = data
    },
    //
    deleteSelectIndicators(item) {
      this.selectIndicators = this.selectIndicators.filter(
        e => e.id !== item.id
      )
      this.params.zb = this.selectIndicators.map(item => ({
        zbid: item.id,
        lxbm: item.lxbm
      }))
      if (this.selectIndicators.length === 0) {
        this.params.wd = []
        this.selectDimensionIds = []
        this.sameDimension = []
        this.selectdimensionalityChild = {}
        this.tableData = []
      } else {
        this.getSameDimension()
      }
      if (this.selectIndicators.length && this.selectDimensionList.length) {
        this.getTableData()
      }
    },

    // 添加维度
    addDimensionality() {
      this.dialogVisible = true
      // 备份参数
      this.tempParams = JSON.parse(JSON.stringify(this.params))
    },
    // 是否可以拖拽
    isDrag(item) {
      return item.xsc.some(e => {
        return this.sameDimension.some(ele => ele.wdmc === e.zdmc)
      })
    },
    // 是否选中有相同维度
    isSameDimensions(wd) {
      return this.sameDimension.some(e => e.wdmc === wd.zdmc)
    },
    // 指标相同为度中是否
    isSameDimensionsGray(item) {
      return this.sameDimension.some(e => e.wdmc === item.wdbm)
    },
    getMergeCells(tableData = [], tableColumn = [], mergeCols = []) {
      const fields = tableColumn?.map(v => v.prop)
      const array = []
      if (!tableData?.length || !tableColumn?.length || !mergeCols?.length)
        return
      // 倒叙遍历行（方便统计合并列单元格数至最上方，避免表格塌陷）
      for (let row = tableData.length - 1; row >= 0; row--) {
        array[row] = []
        for (let col = 0; col < fields.length; col++) {
          // 1.最后一行单元格不合并（初始无可对比数据）
          // 2.不在指定列（mergeCols）的单元格不合并
          // 3.空值不合并
          if (
            row === tableData.length - 1 ||
            !mergeCols.includes(fields[col]) ||
            !tableData[row][fields[col]]
          ) {
            array[row][col] = [1, 1]
            continue
          }
          // 4.数据相同但所属父级不一致的单元格不合并
          const parentFields = mergeCols.slice(0, col) // 在指定合并列中找出所有父级
          if (
            mergeCols.includes(fields[col]) &&
            parentFields?.includes(fields[col - 1])
          ) {
            const currentParents = parentFields.map(
              field => tableData[row][field]
            ) // 当前单元格所有父级
            const nextRowParents = parentFields.map(
              field => tableData[row + 1][field]
            ) // 下一行单元格所有父级
            if (currentParents?.toString() !== nextRowParents?.toString()) {
              array[row][col] = [1, 1]
              continue
            }
          }
          // 5.合并相同数据的单元格
          if (tableData[row][fields[col]] === tableData[row + 1][fields[col]]) {
            const beforeCell = array[row + 1][col]
            array[row][col] = [1 + beforeCell[0], 1]
            beforeCell[0] = 0
            beforeCell[1] = 0
          } else {
            array[row][col] = [1, 1] // 否则不合并
          }
        }
      }
      // console.log(array, 'array')
      return array
    },
    // 表数据合并
    objectSpanMethod({ rowIndex, columnIndex }) {
      return this.spanArr[rowIndex][columnIndex]
    }
  }
}
</script>

<style scoped lang="scss">
.dt-header {
  margin-bottom: 0;
}
.inline-form {
  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 5px !important;
    margin-top: 5px;
  }
}
::v-deep .avue-form__group.avue-form__group--flex {
  padding: 15px 0;
}
.container {
  display: flex;

  .left {
    width: 40%;
    border-right: 1px solid #edeff0;
    //滚动条样式
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(0, 0, 0, 0.2);
    }
    ::-webkit-scrollbar-track {
      border-radius: 3px;
      background: rgba(0, 0, 0, 0.1);
    }
    .title {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
    .content {
      padding: 20px 0;
      height: 436px;
      overflow: auto;

      background: #ffffff;
      .target-item {
        margin-bottom: 20px;
        .target-item-title {
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 14px;
          margin-bottom: 20px;
        }
        .target-item-content {
          display: flex;
          margin-bottom: 12px;
          .target-type {
            min-width: 108px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .target-type-item {
              display: flex;
              flex-direction: column;
              .zbmc {
                height: 14px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #222222;
                line-height: 14px;
              }
              .zblx {
                margin-top: 4px;
                height: 12px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 12px;
              }
            }
          }
          .tags {
            flex: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            .tag-item {
              padding: 0 8px;
              margin-right: 4px;
              margin-bottom: 4px;
              height: 20px;
              background: rgba(91, 143, 249, 0.08);
              border-radius: 2px;
              border: 1px solid rgba(91, 143, 249, 0.4);
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #5b8ff9;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              &.gray {
                background: #f1f1f1;
                color: #666666;
              }
              &.deep {
                background: #1890ff;
                color: #fff;
              }
            }
          }
        }
      }
    }
  }

  .right {
    width: 60%;
    margin-left: 24px;
    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border-radius: 4px;
      border: 1px dashed #cbced1;
      &.dragover {
        border-color: #1890ff;
      }
    }

    .target {
      display: flex;
      align-items: center;
      margin-bottom: 20.004px;
      .target-wrap {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
      }
      .target-title {
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        margin-right: 16px;
      }
      .target-content {
        width: 250px;
        height: 30px;
        border: 1px dashed #cbced1;
        text-align: center;
        font-size: 12px;
        line-height: 30px;
        background: #ffffff;
        &.dragover {
          border-color: #1890ff;
        }
      }
    }

    .dimensionality {
      display: flex;
      align-items: center;
      margin-bottom: 20.004px;

      .dimensionality-title {
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        white-space: nowrap;
        margin-right: 16px;
      }
      .dimensionality-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    .summary {
      .summary-title {
        font-size: 15.996px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        margin-bottom: 9.996px;
      }
    }
  }
}
::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  max-height: 200px;
  overflow: auto;
  .el-checkbox {
    margin-bottom: 10px;
  }
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}
::v-deep .el-dialog__body {
  position: relative;
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
.search-item {
  display: flex;
  align-items: center;
  position: absolute;
  top: 10px;
  right: 24px;
  z-index: 9999;
  .tabs {
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    overflow: hidden;
    .tab {
      padding: 0 15px;
      border-right: 1px solid #e5e5e5;
      cursor: pointer;
      height: 30px;

      &:last-child {
        border: none;
      }
      &.active {
        background: #2361dbc7;
        color: #fff;
      }
    }
  }
  .name {
    font-size: 16px;
    padding-right: 10px;
  }
}
.chart-box {
  position: relative;
  width: 100%;
  height: 60vh;
  padding: 30px;
}
.pie-chart {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  .pie-item {
    width: 50%;
    height: 28vh;
  }
}
</style>
