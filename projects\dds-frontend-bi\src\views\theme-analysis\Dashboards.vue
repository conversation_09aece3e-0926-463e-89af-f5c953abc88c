<template>
  <Fold name="仪表盘看板" style="margin-top: 24px">
    <div class="content">
      <CardItem
        :type="2"
        v-for="(item, index) in dashboards"
        :info="item"
        :key="index"
      />
    </div>
  </Fold>
</template>

<script>
import Fold from "./Fold.vue"
import CardItem from "./CardItem.vue"
export default {
  components: { CardItem, Fold },
  props: {
    dashboards: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.content {
  background-color: #fff;
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(276px, 1fr));
  width: 100%;
  box-sizing: border-box;
  margin-top: 16px;
}
</style>
