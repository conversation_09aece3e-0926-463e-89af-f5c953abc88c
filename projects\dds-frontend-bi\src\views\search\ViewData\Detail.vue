<template>
  <div class="content-wrap" v-loading="loading">
    <div class="content">
      <div class="tip">
        <svg-icon icon-class="warning1" />
        <div class="warm-reminder-content">
          <div class="warm-reminder-title">温馨提示</div>
          <p>
            此处展示最近一次定时计算的结果，如果想查询该指标当前的实时数据，可以“手动查询”，根据查询数据量的差异，查询时间不同，若查询时间较长，请耐心等待。
          </p>
          <div class="btn" @click="handleManually">手动查询</div>
        </div>
      </div>

      <div class="no-dimension" v-if="detailData.isSingle">
        <div class="left-info">
          <div class="name">{{ detailData.zbmc }}</div>
          <div class="numer-info">
            <div class="bg"></div>
            <div class="top">
              <span class="number">
                {{ detailData.data.list[0].ZVAL | formatValue }}
              </span>
              <span class="unit">{{ detailData.data.list[0].JLDW }}</span>
              <el-tooltip
                class="item"
                effect="dark"
                :content="tooltip"
                placement="top"
              >
                <div
                  v-if="isThreshold"
                  class="tag success"
                  :class="[
                    Number(detailData.data.list[0].YJZT) === 1
                      ? 'warning'
                      : 'success'
                  ]"
                >
                  {{
                    Number(detailData.data.list[0].YJZT) === 1
                      ? "疑似异常"
                      : "正常"
                  }}
                </div>
              </el-tooltip>
            </div>
            <div class="date">数据计算时间：{{ detailData.calctime }}</div>
          </div>
          <div class="desc">
            <div class="label">描述:</div>
            <div class="text">
              {{ detailData.ms || "无" }}
            </div>
          </div>
          <div class="tags">
            <div class="label">标签:</div>
            <div class="tag-wrap">
              <div v-if="detailData.bq">
                <el-tag
                  v-for="item in detailData.bq"
                  :key="item"
                  style="margin: 0 4px 4px 0"
                >
                  {{ item }}
                </el-tag>
              </div>
              <span v-else>无</span>
            </div>
          </div>
          <div class="indicator-area">
            <div class="label">所属指标域:</div>
            <div class="text">
              {{ detailData.sysjy || "无" }}
            </div>
          </div>
          <div class="associated-indicator">
            <div class="label">关联指标:</div>
            <div class="text">
              <span v-for="(zb, i) in detailData.glzb" :key="zb.id">
                <span class="t" @click="handleDetail(zb)">
                  {{ zb.zbmc }}
                </span>
                <span class="symbol">
                  {{ i == detailData.glzb.length - 1 || i ? "" : "、" }}
                </span>
              </span>
            </div>
          </div>
        </div>
        <div class="bar-chart">
          <div class="chart-title">该指标近5次数据结果概览</div>
          <div class="chart-content">
            <ChartColumn
              :chart-data="detailData.chart"
              x-field="wdx"
              y-field="wdz"
              :y-axis-name="`单位 (${detailData.data.list[0].JLDW})`"
              :unit="detailData.data.list[0].JLDW"
              :series-name="detailData.zbmc"
              :color="['rgba(91, 143, 249, 0.8)']"
              :interval="0"
            />
          </div>
        </div>
      </div>
      <div class="dimensions" v-else ref="dimensions">
        <div class="name">{{ detailData.zbmc }}</div>
        <div class="info">
          <div class="indicator-area">
            <div class="label">所属指标域:</div>
            <div class="text">
              {{ detailData.sysjy }}
            </div>
          </div>
          <div class="associated-indicator">
            <div class="label">关联指标:</div>
            <div class="text">
              <span v-for="(zb, i) in detailData.glzb" :key="zb.id">
                <span class="t" @click="handleDetail(zb)">
                  {{ zb.zbmc }}
                </span>
                <span class="symbol">
                  {{ i == detailData.glzb.length - 1 || i ? "" : "、" }}
                </span>
              </span>
            </div>
          </div>
        </div>

        <div class="desc">
          <div class="label">描述:</div>
          <div class="text">
            {{ detailData.ms || "无" }}
          </div>
        </div>
        <div class="tags">
          <div class="label">标签:</div>
          <div class="tag-wrap">
            <div v-if="detailData.bq">
              <el-tag
                v-for="item in detailData.bq"
                :key="item"
                style="margin-right: 4px"
              >
                {{ item }}
              </el-tag>
            </div>
            <span v-else>无</span>
          </div>
        </div>
        <div class="date">
          <div class="label">数据计算时间:</div>
          <div class="text">
            {{ detailData.calctime || "无" }}
          </div>
        </div>
        <div class="line"></div>
        <el-form :inline="true" :model="form" class="search-form">
          <el-form-item
            v-for="(item, i) in searchList"
            :label="item.zdmc"
            :key="item.id"
          >
            <el-select
              v-model="searchParams[i].wdzval"
              style="width: 176px; margin-left: 0px"
              @visible-change="visibleChange"
              multiple
              collapse-tags
            >
              <el-option
                v-for="e in item.wd"
                :key="e.bm"
                :label="e.name"
                :value="e.bm"
                :disabled="isDisabled(e.bm, searchParams[i].wdzval)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-table
          :data="tableData"
          style="width: 100%"
          height="400"
          v-loading="tableLoading"
        >
          <!-- fixed="right" -->
          <el-table-column
            v-for="item in detailData.header"
            :key="item.id"
            :prop="item.wdzd"
            :label="item.zdmc"
            :fixed="item.wdzd === 'ZVAL' ? 'right' : null"
            :width="
              flexColumnWidth(
                'zdmc',
                'wdzd',
                tableData,
                detailData.header,
                item.wdzd,
                $refs.dimensions
              )
            "
          >
            <template #default="scope" v-if="item.wdzd === 'ZVAL'">
              <div class="table-number">
                <span class="number">{{ scope.row.ZVAL | formatValue }}</span>
                <span class="unit">{{ scope.row.JLDW }}</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="
                    forMatterTooltip(
                      scope.row.TVMIN,
                      scope.row.TVMAX,
                      scope.row.YJZT
                    )
                  "
                  placement="top"
                >
                  <div
                    v-if="scope.row.TVMIN || scope.row.TVMAX"
                    class="tag success"
                    :class="[
                      Number(scope.row.YJZT) === 1 ? 'warning' : 'success'
                    ]"
                  >
                    {{ Number(scope.row.YJZT) === 1 ? "疑似异常" : "正常" }}
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="page">
          <el-pagination
            small
            layout="prev, pager, next"
            :current-page="page.currentPage"
            :total="page.total"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 维度为空的手动查询 -->
    <div
      class="manual-result"
      id="manual-result"
      v-if="isManual && detailData.isSingle"
    >
      <div class="tag">手动查询</div>
      <div class="name">{{ manualDetailData.zbmc }}</div>
      <div class="number" v-loading="manualLoading">
        {{ manualDetailData.data.list[0].ZVAL | formatValue }}
        <div class="unit">{{ manualDetailData.data.list[0].JLDW }}</div>
      </div>
      <div class="loading" v-if="manualLoading">计算中，请耐心等待</div>
      <div class="date" v-else>
        数据计算时间：{{ manualDetailData.calctime }}
      </div>
    </div>
    <div
      class="manual-result-multiple"
      id="manual-result"
      v-if="isManual && !detailData.isSingle"
    >
      <div class="top">
        <div class="tag">手动查询</div>
        <div class="name">{{ manualDetailData.zbmc }}</div>
        <div class="loading" v-if="manualLoading">计算中，请耐心等待</div>
      </div>
      <div class="date">数据计算时间：{{ manualDetailData.calctime }}</div>
      <div class="line"></div>
      <el-form :inline="true" :model="form" class="search-form">
        <el-form-item
          v-for="(item, i) in manualSearchList"
          :label="item.zdmc"
          :key="item.id"
        >
          <el-select
            v-model="manualSearchParams[i].wdzval"
            style="width: 176px; margin-left: 0px"
            @visible-change="manualVisibleChange"
            multiple
            collapse-tags
          >
            <el-option
              v-for="e in item.wd"
              :key="e.bm"
              :label="e.name"
              :value="e.bm"
              :disabled="isDisabled(e.bm, manualSearchParams[i].wdzval)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table
        :data="manualTableData"
        style="width: 100%"
        height="400"
        v-loading="tableLoading1"
      >
        <!-- fixed="right" -->
        <el-table-column
          v-for="item in manualDetailData.header"
          :key="item.id"
          :prop="item.wdzd"
          :label="item.zdmc"
          :fixed="item.wdzd === 'ZVAL' ? 'right' : null"
          :width="
            flexColumnWidth(
              'zdmc',
              'wdzd',
              tableData,
              manualDetailData.header,
              item.wdzd,
              $refs.dimensions
            )
          "
        >
          <template #default="scope" v-if="item.wdzd === 'ZVAL'">
            <div class="table-number">
              <span class="number">{{ scope.row.ZVAL | formatValue }}</span>
              <span class="unit">{{ scope.row.JLDW }}</span>
              <el-tooltip
                class="item"
                effect="dark"
                :content="
                  forMatterTooltip(
                    scope.row.TVMIN,
                    scope.row.TVMAX,
                    scope.row.YJZT
                  )
                "
                placement="top"
              >
                <div
                  v-if="scope.row.TVMIN || scope.row.TVMAX"
                  class="tag success"
                  :class="[
                    Number(scope.row.YJZT) === 1 ? 'warning' : 'success'
                  ]"
                >
                  {{ Number(scope.row.YJZT) === 1 ? "疑似异常" : "正常" }}
                </div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page">
        <el-pagination
          small
          layout="prev, pager, next"
          :current-page="manualPage.currentPage"
          :total="manualPage.total"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import ChartColumn from "@/components/Charts/ChartColumn"
import { flexColumnWidth } from "@/utils"

export default {
  components: { ChartColumn },
  props: {},
  data() {
    return {
      tableData: [],
      manualTableData: [],
      isManual: false,
      manualLoading: false, // 手动查询loading
      loading: false,
      tableLoading: false,
      tableLoading1: false,
      manualDetailData: {},
      detailData: {},
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      manualPage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      id: "",
      lxbm: "",
      indCode: "",
      searchParams: [],
      manualSearchParams: [],
      searchList: [],
      manualSearchList: []
      // 手动查询结果
    }
  },
  computed: {
    // 开启阈值  (阈值开启条件：tvmin和tvmax同时存在且不是0)
    isThreshold() {
      return (
        this.detailData.data.list[0].TVMIN || this.detailData.data.list[0].TVMAX
      )
    },
    tooltip() {
      return this.isThreshold
        ? `数值${
            Number(this.detailData.data.list[0].YJZT) === 1 ? "不" : ""
          }在阈值内，阈值为 ${this.detailData.data.list[0].TVMIN}-${
            this.detailData.data.list[0].TVMAX
          }`
        : ""
    }
  },
  created() {
    const { id, lxbm, indCode } = this.$route.query
    ;(this.id = id), (this.indCode = indCode)
    this.lxbm = lxbm
    this.getDate()
  },
  mounted() {},
  watch: {},
  methods: {
    flexColumnWidth,
    //
    async handleManually() {
      this.isManual = true
      this.manualLoading = true

      const { data } = await this.$httpBi.search.getIndicatorLatestData({
        id: this.id,
        indCode: this.indCode,
        lxbm: this.lxbm,
        handle: 1,
        currentPage: 1,
        pageSize: 10
      })
      this.manualDetailData = {
        ...data,
        header: data.header.map(item => {
          return {
            ...item,
            // 转大写
            wdzd: item.wdzd.toUpperCase()
          }
        })
      }
      console.log(this.manualDetailData, "manualDetailData")
      this.manualPage.total = data.data.totalCount
      this.manualSearchParams = data.search.map(item => ({
        wdzd: item.wdzd,
        wdzval: item.wdzval
      }))
      this.manualTableData = data.data.list
      this.manualSearchList = data.search
      this.manualLoading = false

      this.$nextTick(() => {
        const section = document.getElementById("manual-result")
        section.scrollIntoView({ behavior: "smooth" })
      })
    },
    async getDate() {
      this.loading = true
      const { data } = await this.$httpBi.search.getIndicatorLatestData({
        id: this.id,
        indCode: this.indCode,
        lxbm: this.lxbm,
        handle: 0,
        currentPage: 1,
        pageSize: 10
      })
      this.detailData = {
        ...data,
        header: data.header.map(item => {
          return {
            ...item,
            // 转大写
            wdzd: item.wdzd.toUpperCase()
          }
        })
      }
      this.page.total = data.data.totalCount
      this.searchParams = data.search.map(item => ({
        wdzd: item.wdzd,
        wdzval: item.wdzval
      }))
      this.tableData = data.data.list
      this.searchList = data.search
      this.loading = false
    },
    async getTableData() {
      this.tableLoading = true
      const { data } = await this.$httpBi.search.getIndicatorSearchData({
        id: this.id,
        indCode: this.indCode,
        lxbm: this.lxbm,
        handle: 0,
        currentPage: this.page.currentPage,
        pageSize: this.page.pageSize,
        sort: "",
        order: "",
        search: this.searchParams
      })
      this.page.total = data.data.totalCount
      this.tableData = data.data.list || []
      this.detailData.calctime = data.calctime

      this.tableLoading = false
    },
    async getTableData1() {
      this.tableLoading1 = true
      const { data } = await this.$httpBi.search.getIndicatorSearchData({
        id: this.id,
        indCode: this.indCode,
        lxbm: this.lxbm,
        handle: 0,
        currentPage: this.manualPage.currentPage,
        pageSize: this.manualPage.pageSize,
        sort: "",
        order: "",
        search: this.manualSearchParams
      })
      this.manualPage.total = data.data.totalCount
      this.manualTableData = data.data.list || []
      this.manualDetailData.calctime = data.calctime

      this.tableLoading1 = false
    },
    visibleChange(isVisible) {
      if (!isVisible) {
        this.handleCurrentChange(1)
      }
    },
    manualVisibleChange(isVisible) {
      if (!isVisible) {
        this.handleManualCurrentChange(1)
      }
    },
    async handleManualCurrentChange(val) {
      this.manualPage.currentPage = val
      const { data } =
        await this.$httpBi.search.getIndicatorDimensionSearchCascade({
          id: this.id,
          indCode: this.indCode,
          lxbm: this.lxbm,
          currentPage: 1,
          pageSize: 10,
          search: this.manualSearchParams
        })
      this.manualSearchList = data
      this.getTableData1()
    },
    async handleCurrentChange(val) {
      this.page.currentPage = val
      const { data } =
        await this.$httpBi.search.getIndicatorDimensionSearchCascade({
          id: this.id,
          indCode: this.indCode,

          lxbm: this.lxbm,
          currentPage: 1,
          pageSize: 10,
          search: this.searchParams
        })
      this.searchList = data
      this.getTableData()
    },
    forMatterTooltip(min, max, type) {
      const isThreshold = min !== 0 || max !== 0
      return isThreshold
        ? `数值${Number(type) === 1 ? "不" : ""}在阈值内，阈值为 ${min}-${max}`
        : ""
    },
    isDisabled(val, arr) {
      console.log(val, arr, "arr")
      if (!arr.length) return false
      if (val !== "" && arr.includes("")) {
        return true
      }
      if (val === "" && !arr.includes("")) {
        return true
      }
    },
    // 查看详情
    handleDetail({ lxbm, indCode, id }) {
      let routerUrl = ""
      if (lxbm === "yz") {
        routerUrl = this.$router.resolve({
          path: "/ddsBi/atomDetail",
          query: {
            id,
            indCode,
            lxbm
          }
        })
      } else {
        routerUrl = this.$router.resolve({
          path: "/ddsBi/appDetail",
          query: {
            id,
            indCode,
            lxbm
          }
        })
      }

      window.open(routerUrl.href, "_blank")
    }
  }
}
</script>

<style scoped lang="scss">
.content-wrap {
  width: 100%;
  min-width: 1184px;
  background: #f0f2f5;
  padding: 20px;
  box-sizing: border-box;
  .content {
    background-color: #fff;
    padding: 24px;
  }
}
.tip {
  position: relative;
  height: 104px;
  background: #edf4ff;
  border-radius: 2px;
  border: 1px solid #94b4eb;
  display: flex;
  padding: 14px 0 0 12px;
  box-sizing: border-box;
  .warning {
    color: #3a79fa;
  }

  .warm-reminder-content {
    margin-left: 8px;
    .warm-reminder-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #323233;
      line-height: 14px;
      text-align: left;
      margin-bottom: 14px;
    }
    p {
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2f3338;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      margin-bottom: 15px;
      opacity: 0.6;
    }
    .btn {
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #1563ff;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      cursor: pointer;
      &:hover {
        color: #5b8ff9;
      }
    }
  }
}
.dimensions {
  margin-top: 24px;
  .name {
    height: 18px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    margin-bottom: 24px;
  }
  .info {
    display: flex;
    margin-bottom: 24px;
    .indicator-area {
      display: flex;

      margin-right: 24px;
      .label {
        width: 80px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        margin-right: 10px;

        font-style: normal;
      }
      .text {
        width: 110px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .associated-indicator {
      display: flex;
      width: calc(100% - 250px);
      .label {
        min-width: 65px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        margin-right: 10px;
        font-style: normal;
      }
      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1463ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1463ff;
        text-align: left;
        font-style: normal;
        span {
          .t {
            text-decoration: underline;
            cursor: pointer;
          }
          .symbol {
            text-decoration: none;
          }
        }
      }
    }
  }
  .desc,
  .tags {
    display: flex;
    margin-bottom: 24px;
    .label {
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #060607;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      margin-right: 10px;
      white-space: nowrap;
      min-width: max-content;
    }
    .text {
      flex: 1;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2f3338;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .tag-wrap {
      display: flex;
      flex-wrap: wrap;
    }
  }
  .date {
    display: flex;
    margin-bottom: 24px;

    .label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #86909c;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      margin-right: 10px;
      width: 90px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #86909c;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    margin-bottom: 24px;
  }
  .search-form {
    background: #ffffff;
    ::v-deep .el-input__inner {
      height: 32px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
    }
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
  ::v-deep .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  ::v-deep .el-form-item__label {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #1d2129;
  }
  .page {
    display: flex;
  }
}
.no-dimension {
  padding: 12px;
  width: 100%;
  min-height: 448px;
  background: #f9fafc;
  border-radius: 8px;
  box-sizing: border-box;
  margin-top: 24px;
  display: flex;
  .left-info {
    min-height: 424px;
    background: #ffffff;
    border-radius: 6px;
    padding: 24px;
    box-sizing: border-box;
    min-width: 584px;
    flex: 1;
    .name {
      height: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      margin-bottom: 24px;
    }
    .numer-info {
      position: relative;
      width: 100%;
      height: 128px;
      border-radius: 8px;
      padding: 26px 0 0 24px;
      box-sizing: border-box;
      background: #ebf1ff;
      margin-bottom: 36px;

      .bg {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 120px;
        height: 120px;
        background: url("~@/assets/imgs/bi/yz.png") no-repeat center;
      }
      .top {
        position: relative;
        display: flex;
        align-items: baseline;
        z-index: 2;
        .number {
          height: 40px;
          font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
          font-weight: 500;
          font-size: 40px;
          color: #1463ff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
        .unit {
          margin-left: 8px;
          margin-right: 16px;
          height: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #1d2129;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
        .tag {
          padding: 0 10px;
          height: 20px;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 20px;
          &.success {
            background: #00cc88;
          }
          &.warning {
            background: #ffc014;
          }
        }
      }
      .date {
        margin-top: 14px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #86909c;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
    .desc,
    .tags,
    .indicator-area,
    .associated-indicator {
      display: flex;
      margin-bottom: 20px;
      .label {
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 10px;
        white-space: nowrap;
      }
      .text {
        flex: 1;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .desc {
      .text {
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box; /* 定义为盒模型 */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        white-space: normal;
      }
    }
    .associated-indicator {
      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1463ff;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        span {
          .t {
            text-decoration: underline;
            cursor: pointer;
          }
          .symbol {
            text-decoration: none;
          }
        }
      }
    }
  }
  .bar-chart {
    flex: 1;
    padding: 68px 37px 68px 40px;
    box-sizing: border-box;
    .chart-title {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
    .chart-content {
      height: 272px;
      margin-top: 16px;
    }
  }
}
.manual-result-multiple {
  background: #ffffff;
  margin-top: 16px;
  padding: 24px;
  .top {
    display: flex;
    align-items: center;
    height: 24px;
    .tag {
      width: 72px;
      height: 24px;
      background: rgba(0, 204, 136, 0.1);
      border-radius: 2px;
      border: 1px solid #00cc88;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #04a675;
      line-height: 24px;
      text-align: center;
    }
    .name {
      height: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      margin: 0 48px 0 8px;
    }
  }
  .date {
    height: 22px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    margin-top: 13px;
    margin-bottom: 24px;
  }
  .line {
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    margin-bottom: 24px;
  }
  .search-form {
    background: #ffffff;
    ::v-deep .el-input__inner {
      height: 32px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
    }
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
  ::v-deep .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  ::v-deep .el-form-item__label {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #1d2129;
  }
  .page {
    display: flex;
  }
}
.manual-result {
  width: 100%;
  height: 72px;
  background: #ffffff;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding: 0 24px;
  .tag {
    width: 72px;
    height: 24px;
    background: rgba(0, 204, 136, 0.1);
    border-radius: 2px;
    border: 1px solid #00cc88;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #04a675;
    line-height: 24px;
    text-align: center;
  }
  .name {
    height: 18px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    margin: 0 48px 0 8px;
  }
  .number {
    min-width: 60px;
    height: 32px;
    font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
    font-weight: 500;
    font-size: 32px;
    color: #1463ff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    display: flex;
    align-items: baseline;
    .unit {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #1d2129;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      margin-left: 6px;
    }
  }
  .loading {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 18px;
    text-align: left;
    font-style: normal;
  }
  .date {
    height: 22px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    margin-left: auto;
  }
}
.table-number {
  display: flex;
  align-items: baseline;
  .number {
    height: 20px;
    font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
    font-weight: 500;
    font-size: 20px;
    color: #1463ff;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
  .unit {
    margin-right: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1463ff;
    text-align: left;
    font-style: normal;
  }
  .tag {
    padding: 0 8px;
    height: 18px;
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 20px;
    &.success {
      background: #00cc88;
    }
    &.warning {
      background: #ffc014;
    }
  }
}
</style>
