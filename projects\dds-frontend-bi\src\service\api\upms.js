import service from "../base"
import config from "../config"

export default {
  /**
   *   {
   *      key : value,
   *      key :value
   *   }
   * @param param
   */
  paramGet(url, param) {
    let getParam = "?"
    for (var key in param) {
      getParam += key + "=" + param[key] + "&"
    }

    return service({
      url: config.VUE_MODULE_UPMS + url + getParam,
      method: "get"
    })
  },
  paramDel(url, param) {
    let getParam = "?"
    for (var key in param) {
      getParam += key + "=" + param[key] + "&"
    }
    return service({
      url: config.VUE_MODULE_UPMS + url + getParam,
      method: "delete"
    })
  },
  paramPost(url, param) {
    return service({
      url: config.VUE_MODULE_UPMS + url,
      method: "post",
      data: param
    })
  },
  sendGet(url) {
    return service({
      url: config.VUE_MODULE_UPMS + url,
      method: "get"
    })
  },
  sendPost(url) {
    return service({
      url: config.VUE_MODULE_UPMS + url,
      method: "post",
      data: {}
    })
  },
  paramPostQuery(url, params) {
    return service({
      url: config.VUE_MODULE_UPMS + url,
      method: "post",
      params
    })
  },

}
