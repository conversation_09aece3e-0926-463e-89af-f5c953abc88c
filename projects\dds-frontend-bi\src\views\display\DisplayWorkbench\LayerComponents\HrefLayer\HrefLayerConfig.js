export const H<PERSON>f<PERSON>ayerConfig = {
  code: 7,
  type: 'text',
  tabName: '文本栏',
  label: '超链接',
  icon: 'el-icon-link',
  component: "HrefLayer",
  options: {
    // 配置
    style: [
      {
        type: 'el-input-number',
        label: '左边距',
        name: 'left',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '上边距',
        name: 'top',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '宽度',
        name: 'width',
        required: false,
        placeholder: '该容器在1920px大屏中的宽度',
        value: 100,
      },
      {
        type: 'el-input-number',
        label: '高度',
        name: 'height',
        required: false,
        placeholder: '该容器在1080px大屏中的高度',
        value: 40,
      },
      {
        type: 'el-input-text',
        label: '文本内容',
        name: 'text',
        required: false,
        placeholder: '',
        value: '超链接',
      },
      {
        type: 'el-input-number',
        label: '字体字号',
        name: 'fontSize',
        required: false,
        placeholder: '',
        value: '26',
      },
      {
        type: 'el-input-color',
        label: '字体颜色',
        name: 'color',
        required: false,
        placeholder: '',
        value: '#FAD400',
      },
      {
        type: 'el-input-number',
        label: '字体间距',
        name: 'letterSpacing',
        required: false,
        placeholder: '',
        value: '0',
      },
      {
        type: 'el-input-color',
        label: '字体背景',
        name: 'background',
        required: false,
        placeholder: '',
        value: 'rgba(115,170,229,0)',
      },
      {
        type: 'el-select',
        label: '文字粗细',
        name: 'fontWeight',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'normal', name: '正常' },
          { code: 'bold', name: '粗体' },
          { code: 'bolder', name: '特粗体' },
          { code: 'lighter', name: '细体' }
        ],
        value: 'normal'
      },
      {
        type: 'el-select',
        label: '对齐方式',
        name: 'textAlign',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'center', name: '居中' },
          { code: 'left', name: '左对齐' },
          { code: 'right', name: '右对齐' },
        ],
        value: 'center'
      },
      {
        type: 'el-radio-group',
        label: '跳转方式',
        name: 'jumpMode',
        required: false,
        placeholder: '',
        selectOptions: [
          {
            code: '_self',
            name: '本窗口',
          },
          {
            code: '_blank',
            name: '新窗口',
          },
        ],
        value: '_self',
      },
      {
        type: 'el-input-text',
        label: '超链地址',
        name: 'linkAdress',
        required: false,
        placeholder: '',
        value: 'http://www.baidu.com',
      },
    ],
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: '外链'
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
   
  }
}
