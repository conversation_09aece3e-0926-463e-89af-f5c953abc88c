<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"

import { toThousands } from "@/utils/index.js"
import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String || Array,
      default: []
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 系列字段
    seriesField: {
      type: String || null,
      default: null
    },
    // 维度名称
    seriesName: {
      type: String || Array || null,
      default: null
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#2361DB",
        "#0EACCC",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 柱状图宽
    barWidth: {
      type: Number,
      default: 16
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: "auto"
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: false
    },
    // 是否堆叠
    isStack: {
      type: Boolean,
      default: false
    },
    // 标线
    isMarkLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      seriesLength: 0
    }
  },
  computed: {
    dimensions() {
      if (Array.isArray(this.xField)) {
        return [this.yField, ...this.xField]
      } else {
        return [this.yField, this.xField]
      }
    },
    // 判断是否为多系列
    isMultipleSeries() {
      return Array.isArray(this.xField) || this.seriesField !== null
    },
    // 是否数据大于显示长度才显示滚动
    isShowScroll() {
      if (this.seriesField) {
        return (
          this.showDataZoom &&
          this.chartData.length / this.seriesLength > this.showNum
        )
      } else {
        return this.showDataZoom && this.chartData.length > this.showNum
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      let dataset = null
      if (this.seriesField) {
        // 去重获取系列
        const uniqueSeriesNames =
          this.chartData.length &&
          Array.from(
            new Set(this.chartData.map(item => item[this.seriesField]))
          )
        this.seriesLength = uniqueSeriesNames.length
        const transforms = []
        uniqueSeriesNames &&
          uniqueSeriesNames.forEach((seriesName, index) => {
            transforms.push({
              transform: {
                type: "filter",
                config: { dimension: this.seriesField, value: seriesName }
              }
            })
            series.push(
              this.createSeriesObject({
                name: seriesName,
                index: index + 1,
                color: this.color[index]
              })
            )
          })

        dataset = [
          {
            source: this.chartData
          },
          ...(transforms || [])
        ]
      } else {
        if (Array.isArray(this.xField)) {
          this.xField.forEach((seriesName, index) => {
            series.push(
              this.createSeriesObject({
                name: (this.seriesName && this.seriesName[index]) ?? seriesName,
                color: this.color[index],
                xField: seriesName
              })
            )
          })
        } else {
          series.push(
            this.createSeriesObject({
              name: this.seriesName ?? this.xField,
              color: this.color[0]
            })
          )
        }

        dataset = {
          dimensions: this.dimensions,
          source: this.chartData
        }
      }
      console.log(series, "series222222222222222222222222")
      console.log(dataset, "series222222222222222222222222")

      this.chart.setOption({
        dataset,
        tooltip: {
          // trigger: 'axis',
          trigger: "axis",
          confine: true,

          axisPointer: {
            type: "shadow",
            label: { show: false, backgroundColor: "transparent" },
            shadowStyle: {
              color: "rgba(35,97,219,0.05)"
            }
          },
          formatter: params => {
            if (!this.isMultipleSeries) {
              return `<div>
              <p class="tooltip-title">${params[0].seriesName}</p>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
                   params[0].color
                 }" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${toThousands(
                  params[0].value[
                    params[0].dimensionNames[params[0].encode.x[0]]
                  ]
                )}${this.unit}
                </span>
              </div>
            </div>`
            }
            return `<div>
            <p class="tooltip-title">${params[0].name}</p>
            ${this.tooltipItemsHtmlString(params)}
          </div>`
          },
          className: "echarts-tooltip-diy"
        },

        dataZoom: this.isShowScroll
          ? [
              {
                type: "slider",
                yAxisIndex: 0,
                zoomLock: true,
                width: 6,

                showDetail: false,
                start: 0,
                endValue: this.showNum - 1,
                showDataShadow: false,
                fillerColor: "#DBDBDB", // 滑块的颜色

                backgroundColor: "transparent", // 滑块轨道的颜色
                borderColor: "transparent", // 滑块轨道边框的颜色
                moveHandleIcon: "none",

                zoomOnMouseWheel: false,
                brushSelect: false,

                handleIcon:
                  "M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z",
                handleSize: "100%",
                handleStyle: {
                  color: "#DBDBDB",
                  borderColor: "transparent"
                }
              },
              {
                type: "inside",
                id: "insideY",
                yAxisIndex: 0,
                zoomOnMouseWheel: false,
                moveOnMouseMove: true,
                moveOnMouseWheel: true
              }
            ]
          : [
              {
                show: false
              }
            ],
        grid: {
          top: 40,
          bottom: 0,
          left: 0,
          right: "4%",
          containLabel: true
        },
        legend: {
          show: true,
          right: 0,
          top: 0,
          icon: "rect",
          itemWidth: 6,
          itemHeight: 6,
          itemGap: 20,
          orient: "horizontal",
          // color: this.color,
          textStyle: {
            color: "#646566",
            fontSize: 12
          }
        },
        graphic: [
          {
            type: "text",
            top: 5,
            left: 0,
            style: {
              text: this.yAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          }
        ],
        xAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              color: "#EBEDF0",
              type: "dashed"
            }
          },
          axisLabel: {
            fontSize: 12,
            color: "#646566",
            fontWeight: "400",
            formatter: name => {
              return name + this.unit
            }
            // formatter: metricAxisLabelFormatter,
          }
        },

        yAxis: [
          {
            type: "category",
            inverse: true,
            splitLine: {
              lineStyle: {
                color: "#8B9FB3",
                type: "dashed"
              }
            },
            axisLabel: {
              fontSize: 12,
              color: "#323233",
              lineHeight: 20,
              fontWeight: "400",
              formatter: value => {
                let startName = value.substring(0, 5)
                let endName = value.substring(5)
                if (endName.length > 5) {
                  return `${startName}\n${value.substring(5, 9)}...`
                }
                return `${startName}\n${endName}`
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series
      })
    },
    // 创建系列对象
    createSeriesObject({ name, color, index = 0, xField = this.xField }) {
      return {
        type: "bar",
        barWidth: this.barWidth,
        name: name,
        stack: this.isStack ? "total" : null,
        itemStyle: {
          color: color
        },
        encode: {
          y: this.yField,
          x: xField
        },
        datasetIndex: index
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.x[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
