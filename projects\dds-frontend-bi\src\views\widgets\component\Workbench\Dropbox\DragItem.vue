<template>
  <el-dropdown
    trigger="click"
    @command="clickItem"
    :hide-on-click="false"
  >
    <span class="el-dropdown-link">
      <el-tag
        size="small"
        class="item-axis"
        effect="dark"
        :type="type == 'metrics' || type == 'secondaryMetrics' ? 'success' : ''"
        closable
        @close="onItemRemove"
      >
        <span>
          <i class="el-icon-arrow-down el-icon--right" />
          <svg-icon
            v-if="item.sort.sortType === 'asc'"
            icon-class="sort-asc"
            class-name="field-icon-sort"
          />
          <svg-icon
            v-if="item.sort.sortType === 'desc'"
            icon-class="sort-desc"
            class-name="field-icon-sort"
          />

          <span class="item-span-style">
            {{ item.agg | formatType }}
            {{ item.displayName }}
            {{ item.field.alias ? "[" + item.field.alias + "]" : "" }}</span>
        </span>
      </el-tag>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-if="type == 'metrics' || type == 'secondaryMetrics'">
        <el-dropdown
          placement="right-start"
          style="width: 100%"
          @command="collect"
        >
          <span class="el-dropdown-link inner-dropdown-menu">
            <span>
              <i class="el-icon-c-scale-to-original" />
              <span>汇总方式</span>
            </span>
            <i class="el-icon-arrow-right el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              command="sum"
              v-show="item.visualType == 'number'"
            >
              总计
            </el-dropdown-item>
            <el-dropdown-item
              command="avg"
              v-show="item.visualType == 'number'"
            >
              平均数
            </el-dropdown-item>
            <el-dropdown-item command="count">计数</el-dropdown-item>
            <el-dropdown-item command="COUNTDISTINCT">
              去重计数
            </el-dropdown-item>
            <el-dropdown-item
              command="max"
              v-show="item.visualType == 'number'"
            >
              最大值
            </el-dropdown-item>
            <el-dropdown-item
              command="min"
              v-show="item.visualType == 'number'"
            >
              最小值
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-dropdown-item>
      <el-dropdown-item>
        <el-dropdown
          v-if="type!=='filters'"

          placement="right-start"
          style="width: 100%"
          @command="sort"
        >
          <span class="el-dropdown-link inner-dropdown-menu">
            <span>
              <i class="el-icon-sort" />
              <span>排序</span>
            </span>
            <i class="el-icon-arrow-right el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="none">默认</el-dropdown-item>
            <el-dropdown-item command="asc">升序</el-dropdown-item>
            <el-dropdown-item command="desc">降序</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-dropdown-item>
      <el-dropdown-item
        v-if="type!=='filters'"
        icon="el-icon-edit-outline"
        command="rename"
      >
        别名设置
      </el-dropdown-item>
      <!-- <el-dropdown-item
        v-if="type == 'color'"
        icon="el-icon-edit-outline"
        command="setColor"
        >颜色配置</el-dropdown-item
      > -->
      <el-dropdown-item
        v-if="type==='filters'"
        icon="el-icon-setting"
        command="setFilters"
      >
        筛选设置
      </el-dropdown-item>
      <el-dropdown-item
        v-if="type == 'metrics' || type == 'secondaryMetrics'"
        icon="el-icon-setting"
        command="setFormat"
      >
        格式设置
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
  <!-- <div class="container">
      <span>0000</span>
      <span>标识卡</span>
    </div> -->
</template>

<script>
export default {
  components: {},
  filters: {
    formatType(val) {
      switch (val) {
      case "sum":
        return "[总计]"
      case "avg":
        return "[平均数]"
      case "count":
        return "[计数]"
      case "COUNTDISTINCT":
        return "[去重计数]"
      case "max":
        return "[最大值]"
      case "min":
        return "[最小值]"
      default:
        break
      }
    },
  },
  props: {
    item: {
      type: Object,
      default: () => { },
    },
    list: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() { },
  mounted() { },
  watch: {},
  methods: {
    beforeClickItem() { },

    clickItem(type) {
      switch (type) {
      case "rename":
        this.onSetAlias()
        break
      case "setColor":
        this.onSetColor()
        break
      case "setFormat":
        this.onSetFormat()
        break
      case "setFilters":
        this.onSetFilters()
        break
      default:
        break
      }
    },
    // 删除
    onItemRemove() {
      this.$emit("onItemRemove",this.list,this.item,this.type)
    },
    // 设置格式
    onSetFormat() {
      this.$emit("onSetFormat",this.list,this.item)
    },
    // 设置别名
    onSetAlias() {
      this.$emit("onSetAlias",this.list,this.item)
    },
    // 配置颜色
    onSetColor() {
      this.$emit("onSetColor",this.item)
    },
    // 排序
    sort(type) {
      this.$emit("onSort",this.list,this.item,type,this.type)
    },
    // 汇总方式
    collect(type) {
      this.$emit("onCollect",this.list,this.item,type)
    },
    // 筛选
    onSetFilters() {
      this.$emit("onSetFilters",this.list,this.item)
    },
  },
}
</script>

<style scoped lang="scss">
.el-dropdown {
  width: 100%;
}
.item-axis {
  margin: 0 3px 2px 3px;
  text-align: left;
  height: 24px;
  line-height: 22px;
  display: inline-block;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
}

.item-axis:hover {
  opacity: 0.8;
  cursor: pointer;
}
.field-icon-sort {
  margin: 0 5px;
}
span {
  font-size: 12px;
}

.item-span-style {
  width: 100px;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-tag--small {
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    display: flex;
    align-items: center;
  }
}
::v-deep .el-tag {
  margin-bottom: 5px;
}
</style>
