<template>
  <div class="chart-pie" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Chart<PERSON>ie',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持（二维数组格式）
    arrayData: {
      type: Array,
      default: () => []
    },
    // 字段配置
    colorField: {
      type: String,
      default: 'name'
    },
    angleField: {
      type: String,
      default: 'value'
    },
    seriesName: {
      type: String,
      default: '数据'
    },
    // 样式配置
    colors: {
      type: Array,
      default: () => ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
    },
    // 饼图配置
    innerRadius: {
      type: Number,
      default: 0
    },
    outerRadius: {
      type: Number,
      default: 80
    },
    roseType: {
      type: [String, Boolean],
      default: false // false, 'radius', 'area'
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: true
    },
    showLabelLine: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 标签配置
    labelPosition: {
      type: String,
      default: 'outside' // 'outside', 'inside', 'center'
    },
    labelFormatter: {
      type: String,
      default: '{b}: {c} ({d}%)'
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      // 判断使用哪种数据格式
      const useArrayFormat = this.arrayData.length > 0

      let pieData

      if (useArrayFormat) {
        // 新格式：二维数组格式
        // 第一行是表头，从第二行开始是数据
        const nameIndex = 0  // 名称列索引
        const valueIndex = 1 // 数值列索引

        pieData = this.arrayData.slice(1).map((item, index) => ({
          name: item[nameIndex],
          value: item[valueIndex],
          itemStyle: {
            color: this.colors[index % this.colors.length]
          }
        }))
      } else {
        // 旧格式：对象数组格式
        pieData = this.chartData.map((item, index) => ({
          name: item[this.colorField],
          value: item[this.angleField],
          itemStyle: {
            color: this.colors[index % this.colors.length]
          }
        }))
      }
      
      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: this.showTooltip,
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          show: this.showLegend,
          orient: 'vertical',
          left: 'left',
          data: pieData.map(item => item.name),
          textStyle: {
            color: '#666666',
            fontSize: 12
          }
        },
        series: [
          {
            name: this.seriesName,
            type: 'pie',
            radius: this.innerRadius > 0 ? 
              [this.innerRadius + '%', this.outerRadius + '%'] : 
              this.outerRadius + '%',
            center: ['50%', '50%'],
            data: pieData,
            roseType: this.roseType,
            label: {
              show: this.showLabel,
              position: this.labelPosition,
              formatter: this.labelFormatter,
              color: '#666666',
              fontSize: 12
            },
            labelLine: {
              show: this.showLabelLine && this.labelPosition === 'outside',
              length: 15,
              length2: 10,
              lineStyle: {
                color: '#d9d9d9'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              },
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: () => Math.random() * 200
          }
        ],
        animation: this.animation,
        animationDuration: this.animationDuration
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-pie {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
