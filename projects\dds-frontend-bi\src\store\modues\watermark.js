import store from "@/store"
import dayjs from "dayjs"
import Watermark from "@/utils/watermark"
import Request from "@/service"

let watermark = {
  namespaced: true,
  state: {
    watermarkText: "",
    portalWatermarkShow: false,
    currentWatermark: false,
  },
  mutations: {
    SET_WATERMARK_TEXT(state, text) {
      state.watermarkText = text
    },
    SET_WATERMARK_PORTAL(state, value) {
      state.portalWatermarkShow = value
    },
    SET_WATERMARK(state) {
      state.currentWatermark = true
      Watermark.set(state.watermarkText)
    },
    OUT_WATERMARK(state) {
      state.currentWatermark = false
      Watermark.out()
    },
  },

  actions: {
    getText({ commit }) {
      Request.watermark
        .get()
        .then((res) => {
          const data = JSON.parse(res.data)
          commit("SET_WATERMARK_PORTAL", data.portal)
          if (data.contentType === 1) {
            commit("SET_WATERMARK_TEXT", data.customizeText)
          } else {
            let str = ""
            data.options.forEach((item) => {
              if (item.enable && item.value === "username") {
                const { username } = store.state.user
                str += username
              } else if (item.enable && item.value === "date") {
                str += dayjs().format("YYYY-MM-DD")
              }
            })
            commit("SET_WATERMARK_TEXT", str)
          }
        })
        .catch(() => { })
    },
  },
  getters: {},
}
export default watermark
