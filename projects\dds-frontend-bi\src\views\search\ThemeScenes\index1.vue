<template>
  <div>
    <template v-if="themeScenesData.list">
      <p class="result">{{ themeScenesData.total }}条相关结果</p>
      <div class="list">
        <div
          class="list-item-custom"
          v-for="(item, index) in themeScenesData.list"
          @click="handleGo(item)"
          :key="index"
        >
          <div class="title" v-html="item.title"></div>
          <div class="desc" v-html="item.info"></div>
          <div class="type">
            {{ getType(item.type) }}
          </div>
        </div>
      </div>
      <div class="page">
        <el-pagination
          small
          layout="prev, pager, next"
          :total="themeScenesData.total"
          :page-size="10"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </template>
    <Empty v-else />
  </div>
</template>

<script>
import Request from "@/service"
import { brightenKeyword } from "@/utils"
import Empty from "../Empty"
export default {
  components: { Empty },
  props: {
    themeScenesData: {
      type: Array,
      default: () => []
    },
    searchContent: {
      type: String,
      default: ""
    },
    page: {
      type: Object,
      default: () => {
        return {
          currentPage: 1,
          pageSisze: 10
        }
      }
    }
  },
  data() {
    return {
      listData: [],
      currentPage: 1,
      themes: []
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getAllThemes()
  },
  watch: {},
  methods: {
    brightenKeyword,
    handleGo(item) {
      switch (item.type) {
        // 大屏
        case "display":
          window.open(item.redirect, "_blank")
          break
        // 仪表盘
        case "dashboard": {
          let [theme, dashboardId] = item.redirect.split("-")
          window.open(
            `/ddsBi/viewDashboards/Portalview/${dashboardId}?isFullPage=true&theme=${theme}&redirect=/ddsBi/themeAnalysis${theme.toUpperCase()}?theme=${theme}`,
            "_blank"
          )
          break
        }
        // 图表
        case "widge": {
          let [theme, dashboardId, widgetId] = item.redirect.split("-")
          window.open(
            `/ddsBi/viewDashboards/Portalview/${dashboardId}?isFullPage=true&theme=ykt&HighLight=true&widgetId=${widgetId}&redirect=/ddsBi/themeAnalysis${theme.toUpperCase()}?theme=${theme}`,
            "_blank"
          )
          break
        }
        default:
          break
      }
      // 大屏
      // if (item.type === "display") {
      //   window.open(item.redirect, "_blank")
      // } else {
      //   // 仪表盘
      //   // item.redirect字符串以-分割
      //   // const ids = item.redirect.split("-")
      //   // item.redirect.this.themes.filter(theme => {
      //   //   if (theme.value === item.theme) {
      //   //     window.open(
      //   //       `/ddsBi/viewDashboards/Portalview/${item.id}?isFullPage=true&theme=${item.theme}&redirect=/ddsBi/${theme.router}?theme=${item.theme}`,
      //   //       "_self"
      //   //     )
      //   //   }
      //   // })
      // }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.$emit("update:page", {
        ...this.page,
        currentPage: val
      })
      this.$emit("changePage")
    },
    // 初始化表格数据
    getAllThemes() {
      this.loading = true
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    getType(type) {
      switch (type) {
        case "display":
          return "大屏驾驶舱"
        case "dashboard":
          return "仪表盘看板"
        case "widge":
          return "数据图表"
        case "view":
          return "数据源指标"
        default:
          return ""
      }
    }
  }
}
</script>

<style scoped lang="scss">
.result {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
  margin-bottom: 16px;
}
.list {
  .list-item-custom {
    margin-bottom: 24px;
    .title {
      height: 28px;
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 28px;
      color: #323233;
      cursor: pointer;
    }
    .desc {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #86909c;
      line-height: 22px;
      margin: 8px 0;
    }
    .type {
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
      line-height: 20px;
    }
  }
}
.page {
  display: flex;
}
</style>
