<template>
  <div class="panel-header">
    <span class="back-button" @click="goBack">
      <i class="el-icon-back" />
      <span>返回</span>
    </span>
    <div class="divider"></div>
    <div class="title">
      <div class="name">
        <span>{{ currentPortal.name }}</span>
      </div>
      <div class="desc">
        <span>{{ currentPortal.description }}</span>
      </div>
    </div>
    <div>
      <!-- <el-button @click="onDownload" type="primary"
        >下载<i class="el-icon-download el-icon--right"></i
      ></el-button> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentPortal: {
      type: Object,
    },
  },
  data() {
    return {
      placeholder: {
        name: "请输入名称",
        description: "请输入描述…",
      },
    }
  },
  created() {},
  methods: {
    goBack() {
      this.$emit("onResetWidgets")
      this.$router.push("/ddsBi/portal")
    },
    // 下载
    onDownload() {
      this.$emit("saveWidget", this.name, this.description)
    },
  },
}
</script>

<style scoped lang="scss">
.panel-header {
  position: relative;
  background-color: var(--theme-color);
  height: 56px;
  @include flexbox();
  padding: 0 20px;
  z-index: 9;
  border-bottom: 1px solid var(--theme-border-color);
  color: var(--theme-text-color);
  box-sizing: border-box;
  .title {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: flex-end;

    .name {
      flex-shrink: 0;
      position: relative;
      height: 56px;
      line-height: 56px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }

    .desc {
      flex: 1;
      height: 56px;
      line-height: 56px;
      font-size: 10px;
      position: relative;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 10px;
    }
  }
}
.back-button {
  cursor: pointer;
  .el-icon-back {
    width: 13px;
    height: 9px;
    margin-right: 4px;
    color: var(--theme-text-color);
  }
  span {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: var(--theme-text-color);
  }
}
.divider {
  width: 1px;
  height: 14px;
  background: #ebedf0;
  margin: 0 8px;
}
</style>
