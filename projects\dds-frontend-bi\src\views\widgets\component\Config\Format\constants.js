
export const FieldFormatTypes = {
  Default: 'default',
  Numeric: 'numeric',
  Currency: 'currency',
  Percentage: 'percentage',
  ScientificNotation: 'scientificNotation',
  Date: 'date',
  Custom: 'custom'
}

export const FieldFormatTypesSetting = {
  'date': [ FieldFormatTypes.Default, FieldFormatTypes.Date ],
  'number': [
    FieldFormatTypes.Default,
    FieldFormatTypes.Numeric,
    FieldFormatTypes.Currency,
    FieldFormatTypes.Percentage,
    FieldFormatTypes.ScientificNotation
  ]
}

export const FieldFormatTypesLocale = {
  [FieldFormatTypes.Default]: '默认',
  [FieldFormatTypes.Numeric]: '数值',
  [FieldFormatTypes.Currency]: '货币',
  [FieldFormatTypes.Percentage]: '百分比',
  [FieldFormatTypes.ScientificNotation]: '科学型',
  [FieldFormatTypes.Date]: '日期',
  [FieldFormatTypes.Custom]: '自定义'
}
export const NumberFormatType = [ {
  label: "默认",
  type: "default",
},
{
  label: "数值",
  type: "numeric",
},
{
  label: "货币",
  type: "currency",
},
{
  label: "百分比",
  type: "percentage",
},
{
  label: "科学型",
  type: "scientificNotation",
},
]

export const NumericUnit = [ '无', '万','亿','k','M', 'G' ]


export const NumericUnitList = [
  NumericUnit.None,
  NumericUnit.TenThousand,
  NumericUnit.OneHundredMillion,
  NumericUnit.Thousand,
  NumericUnit.Million,
  NumericUnit.Giga
]

export const defaultFormatConfig = {
  formatType: FieldFormatTypes.Default,
  [FieldFormatTypes.Numeric]: {
    decimalPlaces: 2,
    unit: NumericUnit.None,
    useThousandSeparator: true
  },
  [FieldFormatTypes.Currency]: {
    decimalPlaces: 2,
    unit: NumericUnit.None,
    useThousandSeparator: true,
    prefix: '',
    suffix: ''
  },
  [FieldFormatTypes.Percentage]: {
    decimalPlaces: 2
  },
  [FieldFormatTypes.ScientificNotation]: {
    decimalPlaces: 2
  },
  [FieldFormatTypes.Date]: {
    format: 'YYYY-MM-DD'
  },
  [FieldFormatTypes.Custom]: {
    format: ''
  }
}
