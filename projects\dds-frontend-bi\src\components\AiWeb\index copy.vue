<template>
  <div class="container flex flex-col">
    <main class="flex-1 overflow-hidden">
      <div
        id="scrollRef"
        ref="scrollRef"
        class="h-full overflow-hidden overflow-y-auto"
      >
        <div
          id="image-wrapper"
          class="w-full max-w-screen-xl m-auto dark:bg-[#101014] p-4"
        >
          <template v-if="!dataSources.length">
            <div
              class="flex items-center flex-col justify-center mt-4 text-center"
            >
              <SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" />
              <div>功能内测研发中</div>
            </div>
          </template>
          <template v-else>
            <Message
              v-for="(item, index) of dataSources"
              :key="index"
              :date-time="item.dateTime"
              :text="item.text"
              :inversion="item.inversion"
              :error="item.error"
              :loading="item.loading"
              @regenerate="onRegenerate(index)"
              @delete="handleDelete(index)"
            />
            <div class="sticky bottom-0 left-0 flex justify-center">
              <el-button v-if="loading" type="warning" @click="handleStop">
                <template #icon>
                  <SvgIcon icon="ri:stop-circle-line" />
                </template>
                停止回答
              </el-button>
            </div>
          </template>
        </div>
      </div>
    </main>

    <div class="sticky bottom-0 w-full p-6 pb-8">
      <!-- <div class="-mt-2 mb-2 text-sm text-gray-500" v-if="isConfig">
        请输入 API Key：
      </div> -->
      <div class="flex">
        <el-input
          class="input"
          type="text"
          placeholder="请输入"
          v-model="prompt"
          @keyup.enter.native="handleSubmit"
        />
        <el-button
          @click="handleSubmit"
          type="primary"
          icon="el-icon-s-promotion"
          class="btn"
        ></el-button>
      </div>
    </div>
  </div>
</template>

<script>
// import { md } from "@/utils/markdown"
// import { chat } from "@/service/api/gpt"
import { fetchChatAPIProcess } from "@/service/api/gpt"

import { Message } from "./components"
import useChat from "./mixin/useChat"
import useScroll from "./mixin/useScroll"

export default {
  components: { Message },
  mixins: [useChat, useScroll],

  props: {},
  data() {
    return {
      decoder: new TextDecoder("utf-8"),
      messageContent: "",
      messageList: [],
      flag: true,
      roleAlias: { user: "ME", assistant: "ChatGPT", system: "System" },

      // dataSources: [],
      loading: false,
      controller: new AbortController(),
      prompt: "",
      // 最多请求3次
      maxRequest: 3,
      errorRequest: 0
    }
  },
  computed: {
    dataSources() {
      if (this.chat.length) {
        return [...this.chat[0].data]
      } else {
        return []
      }
    }
  },
  created() {},
  mounted() {
    this.scrollToBottom()
  },
  watch: {},
  methods: {
    handleSubmit() {
      this.onConversation()
    },
    async onConversation() {
      const message = this.prompt

      if (this.loading) return

      if (!message || message.trim() === "") return

      this.controller = new AbortController()

      this.addChat(+this.uuid, {
        dateTime: new Date().toLocaleString(),
        text: message,
        inversion: true,
        error: false,
        conversationOptions: null,
        requestOptions: { prompt: message, options: null }
      })
      this.scrollToBottom()

      this.loading = true
      this.prompt = ""

      const options = { conversationId: window.location.hash }
      // const lastContext = conversationList.value[conversationList.value.length - 1]?.conversationOptions

      // if (lastContext && usingContext.value)
      //   options = { ...lastContext }

      this.addChat(+this.uuid, {
        dateTime: new Date().toLocaleString(),
        text: "",
        loading: true,
        inversion: false,
        error: false,
        conversationOptions: null,
        requestOptions: { prompt: message, options: { ...options } }
      })
      this.scrollToBottom()
      console.log(this.dataSources, "dataSources")
      try {
        let lastText = ""

        const response = await fetchChatAPIProcess({
          prompt: message
        })
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        for (;;) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          console.log(chunk, "chunk/////")
          this.updateChat(+this.uuid, this.dataSources.length - 1, {
            dateTime: new Date().toLocaleString(),
            text: lastText + (chunk ?? ""),
            inversion: false,
            error: false,
            loading: false,
            conversationOptions: {},
            requestOptions: { prompt: message, options: { ...options } }
          })
          lastText += chunk

          this.scrollToBottom()
        }
      } catch (error) {
        console.log(error.status, "error")
        if (error.status === 0) {
          this.errorRequest++
          this.loading = false
          return this.onRegenerate(this.dataSources.length - 1)
        }
        const errorMessage = error?.content ?? "好像出错了，请稍后再试。"

        if (error.text === "canceled") {
          this.updateChatSome(+this.uuid, this.dataSources.length - 1, {
            loading: false
          })
          this.scrollToBottom()
          return
        }

        const currentChat = this.getChatByUuidAndIndex(
          +this.uuid,
          this.dataSources.length - 1
        )

        if (currentChat?.text && currentChat.text !== "") {
          this.updateChatSome(+this.uuid, this.dataSources.length - 1, {
            text: `${currentChat.text}\n[${errorMessage}]`,
            error: false,
            loading: false
          })
          return
        }

        this.updateChat(+this.uuid, this.dataSources.length - 1, {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: message, options: { ...options } }
        })
        this.scrollToBottom()
      } finally {
        this.loading = false
      }
    },
    // 刷新重新
    async onRegenerate(index) {
      console.log(index)
      if (this.loading) return

      this.controller = new AbortController()

      const { requestOptions } = this.dataSources[index]

      const message = requestOptions?.prompt ?? ""

      let options = {}

      if (requestOptions.options) options = { ...requestOptions.options }

      this.loading = true

      this.updateChat(+this.uuid, index, {
        dateTime: new Date().toLocaleString(),
        text: "",
        inversion: false,
        error: false,
        loading: true,
        conversationOptions: null,
        requestOptions: { prompt: message, ...options }
      })
      try {
        // await fetchChatAPIProcess({
        //   prompt: message,
        //   signal: this.controller.signal,
        //   onDownloadProgress: ({ event }) => {
        //     const xhr = event.target
        //     const { content } = JSON.parse(xhr.responseText)
        //     const chunk = content
        //     // if (lastIndex !== -1)
        //     // chunk = responseText.substring(lastIndex)
        //     try {
        //       // const data = JSON.parse(chunk)
        //       this.updateChat(+this.uuid, this.dataSources.length - 1, {
        //         dateTime: new Date().toLocaleString(),
        //         text: chunk ?? "",
        //         inversion: false,
        //         error: false,
        //         loading: false,
        //         conversationOptions: {},
        //         requestOptions: { prompt: message, options: { ...options } }
        //       })
        //       this.scrollToBottom()
        //     } catch (error) {
        //       //
        //     }
        //   }
        // })
        const response = await fetchChatAPIProcess({
          prompt: message
        })
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        for (;;) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          this.updateChat(+this.uuid, this.dataSources.length - 1, {
            dateTime: new Date().toLocaleString(),
            text: chunk ?? "",
            inversion: false,
            error: false,
            loading: false,
            conversationOptions: {},
            requestOptions: { prompt: message, options: { ...options } }
          })
          this.scrollToBottom()
        }
      } catch (error) {
        if (error.status === 0 && this.errorRequest < this.maxRequest) {
          this.errorRequest++
          this.loading = false
          return this.onRegenerate(this.dataSources.length - 1)
        }
        const errorMessage = error?.content ?? "好像出错了，请稍后再试。"
        if (error.text === "canceled") {
          this.updateChatSome(+this.uuid, index, {
            loading: false
          })
          return
        }

        this.updateChat(+this.uuid, index, {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: message, ...options }
        })
      } finally {
        this.loading = false
      }
    },
    handleDelete(index) {
      if (this.loading) {
        return
      }
      this.$confirm("是否删除此信息?", "删除信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.deleteChatByUuid(+this.uuid, index)
        })
        .catch(() => {})
    },
    handleStop() {
      if (this.loading) {
        this.loading = false
        this.controller.abort()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: calc(100vh - 20vh - 154px);
  .btn {
    margin-left: 16px;
    width: 80px;
  }
}
</style>
