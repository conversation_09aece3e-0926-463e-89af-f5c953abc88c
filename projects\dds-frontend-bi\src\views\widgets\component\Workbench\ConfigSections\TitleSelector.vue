<template>
  <div style="width: 100%">
    <el-col>
      <el-form
        ref="titleForm"
        :model="titleForm"
        label-width="80px"
        size="mini"
      >
        <el-form-item label="显示" class="form-item">
          <el-checkbox v-model="titleForm.show" @change="changeTitleStyle">
            显示
          </el-checkbox>
        </el-form-item>
        <div v-show="titleForm.show">
          <el-form-item label="标题" class="form-item">
            <el-input
              v-model="titleForm.text"
              size="mini"
              placeholder="标题"
              clearable
              @blur="changeTitleStyle"
              @input="inputOnInput($event)"
            />
          </el-form-item>
          <el-form-item label="字体大小" class="form-item">
            <el-select
              v-model="titleForm.fontSize"
              placeholder="字体大小"
              size="mini"
              @change="changeTitleStyle"
            >
              <el-option
                v-for="option in fontSize"
                :key="option.value"
                :label="option.name"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="字体颜色" class="form-item">
            <el-color-picker
              v-model="titleForm.color"
              class="color-picker-style"
              :predefine="predefineColors"
              @change="changeTitleStyle"
            />
          </el-form-item>
          <el-form-item label="水平位置" class="form-item">
            <el-radio-group
              v-model="titleForm.hPosition"
              size="mini"
              @change="changeTitleStyle"
            >
              <el-radio-button label="left">左</el-radio-button>
              <el-radio-button label="center">中</el-radio-button>
              <el-radio-button label="right">右</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="垂直位置" class="form-item">
            <el-radio-group
              v-model="titleForm.vPosition"
              size="mini"
              @change="changeTitleStyle"
            >
              <el-radio-button label="top">上</el-radio-button>
              <el-radio-button label="center">中</el-radio-button>
              <el-radio-button label="bottom">下</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="字体样式" class="form-item">
            <el-checkbox
              v-model="titleForm.isItalic"
              @change="changeTitleStyle"
            >
              倾斜
            </el-checkbox>
            <el-checkbox
              v-model="titleForm.isBolder"
              @change="changeTitleStyle"
            >
              加粗
            </el-checkbox>
          </el-form-item>
        </div>
      </el-form>
    </el-col>
  </div>
</template>

<script>
export default {
  name: "title-selector",
  props: {
    param: {
      type: Object,
      required: true
    },
    chart: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      fontSize: [],
      isSetting: false
    }
  },
  watch: {
    chart: {
      handler: function () {
        // this.initData()
      }
    }
  },
  mounted() {
    // this.init()
    // this.initData()
  },
  methods: {
    initData() {
      const chart = JSON.parse(JSON.stringify(this.chart))
      if (chart.customStyle) {
        let customStyle = null
        if (
          Object.prototype.toString.call(chart.customStyle) ===
          "[object Object]"
        ) {
          customStyle = JSON.parse(JSON.stringify(chart.customStyle))
        } else {
          customStyle = JSON.parse(chart.customStyle)
        }
        if (customStyle.text) {
          this.titleForm = customStyle.text
        }
        this.titleForm.title = this.chart.title
      }
    },
    init() {
      const arr = []
      for (let i = 10; i <= 60; i = i + 2) {
        arr.push({
          name: i + "",
          value: i + ""
        })
      }
      this.fontSize = arr
    },
    changeTitleStyle() {
      this.$emit("onTextChange", this.titleForm)
    },
    inputOnInput: function () {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.shape-item {
  padding: 6px;
  border: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-item-slider >>> .el-form-item__label {
  font-size: 12px;
  line-height: 38px;
}
.form-item >>> .el-form-item__label {
  font-size: 12px;
}
.el-select-dropdown__item {
  padding: 0 20px;
}
span {
  font-size: 12px;
}
.el-form-item {
  margin-bottom: 6px;
}

.switch-style {
  position: absolute;
  right: 10px;
  margin-top: -4px;
}
.color-picker-style {
  cursor: pointer;
  z-index: 1003;
}
</style>
