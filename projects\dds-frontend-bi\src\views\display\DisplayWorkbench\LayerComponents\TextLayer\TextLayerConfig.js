export const TextLayerConfig = {
  code: 2,
  type: "text",
  tabName: "文本栏",
  label: "文本",
  icon: "el-icon-edit-outline",
  component: "TextLayer",
  options: {
    // 配置
    style: [
      {
        type: "el-input-number",
        label: "左边距",
        name: "left",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "上边距",
        name: "top",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "宽度",
        name: "width",
        required: false,
        placeholder: "",
        value: 100
      },
      {
        type: "el-input-number",
        label: "高度",
        name: "height",
        required: false,
        placeholder: "",
        value: 40
      },
      {
        type: "el-input-textarea",
        label: "文本内容",
        name: "text",
        required: false,
        placeholder: "",
        value: "文本框"
      },
      {
        type: "el-input-number",
        label: "字体字号",
        name: "fontSize",
        required: false,
        placeholder: "",
        value: "26"
      },
      {
        type: "el-input-color",
        label: "字体颜色",
        name: "color",
        required: false,
        placeholder: "",
        value: "rgba(250, 212, 0, 1)"
      },
      {
        type: "el-input-number",
        label: "字体间距",
        name: "letterSpacing",
        required: false,
        placeholder: "",
        value: "0"
      },
      {
        type: "el-input-color",
        label: "字体背景",
        name: "background",
        required: false,
        placeholder: "",
        value: "rgba(115,170,229,.0)"
      },
      {
        type: "el-select",
        label: "字体类型",
        name: "fontFamily",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "Microsoft YaHei", name: "微软雅黑" },
          { code: "SimSun", name: "宋体" },
          { code: "NSimSun", name: "新宋体" },
          { code: "SimHei", name: "黑体" },
          { code: "KaiTi_GB2312", name: "楷体" },
          { code: "YouYuan", name: "幼圆" },
          { code: "MingLiU", name: "明细体" },
          { code: "YouSheBiaoTiHei", name: "优设标题黑" },
          { code: "PangMenZhengDaoBiaoTiTi", name: "庞门正道标题体" },
          { code: "DingTalk JinBuTi", name: "进步体" },
          { code: "DIN Condensed Bold", name: "DIN粗体" },
          { code: "DINCond-Regular", name: "DIN常规" }
        ],
        value: "Microsoft YaHei"
      },
      {
        type: "el-select",
        label: "文字粗细",
        name: "fontWeight",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "normal", name: "正常" },
          { code: "bold", name: "粗体" },
          { code: "bolder", name: "特粗体" },
          { code: "lighter", name: "细体" }
        ],
        value: "normal"
      },
      {
        type: "el-select",
        label: "水平对齐",
        name: "textAlign",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "center", name: "居中" },
          { code: "left", name: "左对齐" },
          { code: "right", name: "右对齐" }
        ],
        value: "center"
      },
      {
        type: "el-select",
        label: "垂直对齐",
        name: "verticalAlign",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "middle", name: "居中" },
          { code: "top", name: "上对齐" },
          { code: "bottom", name: "下对齐" }
        ],
        value: "middle"
      },
      {
        type: "el-input-number",
        label: "边框圆角",
        name: "borderRadius",
        placeholder: "",
        children: [
          {
            type: "el-input-number",
            label: "左上",
            name: "borderRadiusLT",
            required: false,
            placeholder: "",
            value: "20"
          },
          {
            type: "el-input-number",
            label: "右上",
            name: "borderRadiusRT",
            required: false,
            placeholder: "",
            value: "40"
          },
          {
            type: "el-input-number",
            label: "右下",
            name: "borderRadiusRB",
            required: false,
            placeholder: "",
            value: "20"
          },
          {
            type: "el-input-number",
            label: "左下",
            name: "borderRadiusLB",
            required: false,
            placeholder: "",
            value: "30"
          }
        ]
      }
    ],
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: "外链"
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
  }
}
