<template>
  <div class="common-table">
    <el-table
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :stripe="stripe"
      :border="border"
      :size="size"
      :height="height"
      :max-height="maxHeight"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="rowClassName"
      :cell-class-name="cellClassName"
      :header-cell-class-name="headerCellClassName"
      :row-style="rowStyle"
      :cell-style="cellStyle"
      :header-cell-style="headerCellStyle"
      :span-method="spanMethod"
      :default-expand-all="defaultExpandAll"
      :expand-row-keys="expandRowKeys"
      :default-sort="defaultSort"
      :tooltip-effect="tooltipEffect"
      :show-summary="showSummary"
      :sum-text="sumText"
      :summary-method="summaryMethod"
      :row-key="rowKey"
      :empty-text="emptyText"
      :fit="fit"
      :show-overflow-tooltip="showOverflowTooltip"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @cell-click="handleCellClick"
      @cell-dblclick="handleCellDblclick"
      @row-click="handleRowClick"
      @row-contextmenu="handleRowContextmenu"
      @row-dblclick="handleRowDblclick"
      @header-click="handleHeaderClick"
      @header-contextmenu="handleHeaderContextmenu"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange"
      @current-change="handleCurrentChange"
      @header-dragend="handleHeaderDragend"
      @expand-change="handleExpandChange"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        align="center"
      />
      
      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        :label="indexLabel"
        :width="indexWidth"
        align="center"
      />
      
      <!-- 动态列 -->
      <el-table-column
        v-for="column in finalColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :render-header="column.renderHeader"
        :sortable="column.sortable"
        :sort-method="column.sortMethod"
        :sort-by="column.sortBy"
        :sort-orders="column.sortOrders"
        :resizable="column.resizable"
        :formatter="column.formatter"
        :show-overflow-tooltip="column.showOverflowTooltip"
        :align="column.align || 'left'"
        :header-align="column.headerAlign || column.align || 'left'"
        :class-name="column.className"
        :label-class-name="column.labelClassName"
        :selectable="column.selectable"
        :reserve-selection="column.reserveSelection"
        :filters="column.filters"
        :filter-placement="column.filterPlacement"
        :filter-multiple="column.filterMultiple"
        :filter-method="column.filterMethod"
        :filtered-value="column.filteredValue"
      >
        <template #default="scope">
          <!-- 自定义渲染 -->
          <div v-if="column.render" v-html="column.render(scope.row, column, scope.$index)"></div>
          <!-- 插槽渲染 -->
          <slot v-else-if="column.slot" :name="column.slot" :row="scope.row" :column="column" :index="scope.$index"></slot>
          <!-- 默认渲染 -->
          <span v-else>{{ scope.row[column.prop] }}</span>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column
        v-if="showOperation"
        :label="operationLabel"
        :width="operationWidth"
        :fixed="operationFixed"
        align="center"
      >
        <template #default="scope">
          <slot name="operation" :row="scope.row" :index="scope.$index">
            <el-button
              v-for="btn in operationButtons"
              :key="btn.key"
              :type="btn.type || 'primary'"
              :size="btn.size || 'small'"
              :icon="btn.icon"
              :disabled="btn.disabled && btn.disabled(scope.row)"
              :loading="btn.loading && btn.loading(scope.row)"
              @click="handleOperation(btn.key, scope.row, scope.$index)"
            >
              {{ btn.label }}
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div v-if="showPagination" class="table-pagination">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :layout="paginationLayout"
        :total="total"
        :small="paginationSmall"
        :background="paginationBackground"
        :pager-count="pagerCount"
        :hide-on-single-page="hideOnSinglePage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 表格列配置
    tableColumns: {
      type: Array,
      default: () => []
    },
    // 表头列表配置（兼容新格式）
    headList: {
      type: Array,
      default: () => []
    },
    // 表格属性
    loading: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: false
    },
    border: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    },
    height: {
      type: [String, Number],
      default: undefined
    },
    maxHeight: {
      type: [String, Number],
      default: undefined
    },
    fit: {
      type: Boolean,
      default: true
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false
    },
    rowClassName: {
      type: [String, Function],
      default: ''
    },
    cellClassName: {
      type: [String, Function],
      default: ''
    },
    headerCellClassName: {
      type: [String, Function],
      default: ''
    },
    rowStyle: {
      type: [Object, Function],
      default: () => ({})
    },
    cellStyle: {
      type: [Object, Function],
      default: () => ({})
    },
    headerCellStyle: {
      type: [Object, Function],
      default: () => ({})
    },
    spanMethod: {
      type: Function,
      default: undefined
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    expandRowKeys: {
      type: Array,
      default: () => []
    },
    defaultSort: {
      type: Object,
      default: () => ({})
    },
    tooltipEffect: {
      type: String,
      default: 'dark'
    },
    showSummary: {
      type: Boolean,
      default: false
    },
    sumText: {
      type: String,
      default: '合计'
    },
    summaryMethod: {
      type: Function,
      default: undefined
    },
    rowKey: {
      type: [String, Function],
      default: undefined
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    showOverflowTooltip: {
      type: Boolean,
      default: false
    },
    // 选择列
    showSelection: {
      type: Boolean,
      default: false
    },
    // 序号列
    showIndex: {
      type: Boolean,
      default: false
    },
    indexLabel: {
      type: String,
      default: '序号'
    },
    indexWidth: {
      type: [String, Number],
      default: 60
    },
    // 操作列
    showOperation: {
      type: Boolean,
      default: false
    },
    operationLabel: {
      type: String,
      default: '操作'
    },
    operationWidth: {
      type: [String, Number],
      default: 150
    },
    operationFixed: {
      type: [String, Boolean],
      default: 'right'
    },
    operationButtons: {
      type: Array,
      default: () => []
    },
    // 分页
    showPagination: {
      type: Boolean,
      default: false
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    total: {
      type: Number,
      default: 0
    },
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    paginationSmall: {
      type: Boolean,
      default: false
    },
    paginationBackground: {
      type: Boolean,
      default: true
    },
    pagerCount: {
      type: Number,
      default: 7
    },
    hideOnSinglePage: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 计算最终的列配置，支持两种数据格式
    finalColumns() {
      // 如果有 headList，优先使用 headList
      if (this.headList && this.headList.length > 0) {
        return this.headList
      }
      // 否则使用 tableColumns
      return this.tableColumns
    }
  },
  methods: {
    // 表格事件处理
    handleSelect(selection, row) {
      this.$emit('select', selection, row)
    },
    handleSelectAll(selection) {
      this.$emit('select-all', selection)
    },
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    handleCellMouseEnter(row, column, cell, event) {
      this.$emit('cell-mouse-enter', row, column, cell, event)
    },
    handleCellMouseLeave(row, column, cell, event) {
      this.$emit('cell-mouse-leave', row, column, cell, event)
    },
    handleCellClick(row, column, cell, event) {
      this.$emit('cell-click', row, column, cell, event)
    },
    handleCellDblclick(row, column, cell, event) {
      this.$emit('cell-dblclick', row, column, cell, event)
    },
    handleRowClick(row, column, event) {
      this.$emit('row-click', row, column, event)
    },
    handleRowContextmenu(row, column, event) {
      this.$emit('row-contextmenu', row, column, event)
    },
    handleRowDblclick(row, column, event) {
      this.$emit('row-dblclick', row, column, event)
    },
    handleHeaderClick(column, event) {
      this.$emit('header-click', column, event)
    },
    handleHeaderContextmenu(column, event) {
      this.$emit('header-contextmenu', column, event)
    },
    handleSortChange({ column, prop, order }) {
      this.$emit('sort-change', { column, prop, order })
    },
    handleFilterChange(filters) {
      this.$emit('filter-change', filters)
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      this.$emit('current-change', currentRow, oldCurrentRow)
    },
    handleHeaderDragend(newWidth, oldWidth, column, event) {
      this.$emit('header-dragend', newWidth, oldWidth, column, event)
    },
    handleExpandChange(row, expandedRows) {
      this.$emit('expand-change', row, expandedRows)
    },
    // 操作按钮点击
    handleOperation(key, row, index) {
      this.$emit('operation', key, row, index)
    },
    // 分页事件处理
    handleSizeChange(size) {
      this.$emit('size-change', size)
    },
    handleCurrentPageChange(page) {
      this.$emit('current-change', page)
    }
  }
}
</script>

<style scoped>
.common-table {
  width: 100%;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
