<template>
  <div
    v-if="!inversion"
    class="bg-white rounded-lg p-4"
    ref="textDemo"
    style="width: 550px"
  >
    <div class="think-time" @click="show = !show">
      深度思考（用时4秒）
      <i class="el-icon-arrow-down" v-if="show"></i>
      <i class="el-icon-arrow-right" v-else></i>
    </div>
    <el-collapse-transition>
      <div v-show="show">
        <p class="think-desc">
          {{ desc }}
        </p>
      </div>
    </el-collapse-transition>
    <div class="mb-4">
      <p class="text-gray-800 mb-4 mt-4">以下是学校今年的生师比情况:</p>
      <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
        <!-- <div class="text-gray-600 text-sm">
          <span class="font-bold">维度:</span>
          全校, 2025年
        </div> -->
        <div class="text-4xl font-bold text-blue-500 my-3">
          14.69
          <span class="text-lg"></span>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between text-sm">
      <div class="text-gray-500">
        <span class="font-bold">相关链接:</span>
        <a href="#" @click="goPage" class="text-blue-500 hover:underline">
          生师比
        </a>
      </div>
      <div class="flex space-x-3">
        <el-tooltip class="item" effect="dark" content="下载" placement="top">
          <div class="btn-item download" @click="exportImg"></div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="复制" placement="top">
          <div class="btn-item copy" @click="copyAllText"></div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="赞同" placement="top">
          <div class="btn-item praise"></div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="不赞同" placement="top">
          <div class="btn-item hate"></div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from "html2canvas"
export default {
  components: {},
  props: {},
  data() {
    return {
      show: false,
      desc: "我将先在指标库查询；找到符合用户问题要求的指标：生师比"
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    goPage() {
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path: "/ddsBi/appDetail"
      })
      window.open(routerUrl.href, "_blank")
    },
    exportImg() {
      const content = this.$refs.textDemo
      console.log(content, "content")
      html2canvas(content, {
        scale: 2, // 放大倍数
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = +new Date() + ".png" // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    },
    getAllText(element) {
      let text = ""

      // 递归遍历所有子节点
      const walk = node => {
        if (node.nodeType === Node.TEXT_NODE) {
          text += node.textContent
        } else if (
          node.nodeType === Node.ELEMENT_NODE &&
          !["SCRIPT", "STYLE"].includes(node.tagName)
        ) {
          Array.from(node.childNodes).forEach(walk)
        }
      }

      walk(element)
      return text.replace(/\s+/g, " ").trim() // 清理多余空格
    },

    // 修改复制方法
    async copyAllText() {
      // 后续粘贴逻辑同上
      const text = this.getAllText(this.$refs.textDemo)
      // 支持 Clipboard API
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success("复制成功")
          })
          .catch(err => {
            console.error("Clipboard API 失败，尝试回退方法:", err)
            this.fallbackCopy(text) // 失败时使用回退方案
          })
      } else {
        // 不支持则直接使用回退
        this.fallbackCopy(text)
      }
    },
    fallbackCopy(text) {
      // 创建临时文本域并选中内容
      const textarea = document.createElement("textarea")
      textarea.value = text
      textarea.setAttribute("readonly", "") // 防止键盘弹出（移动端）
      textarea.style.position = "absolute"
      textarea.style.left = "-9999px"
      document.body.appendChild(textarea)
      textarea.select()

      try {
        document.execCommand("copy")
        this.$message.success("复制成功")
      } catch (err) {
        console.error("回退复制失败:", err)
      }

      // 清理DOM
      document.body.removeChild(textarea)
    }
  }
}
</script>

<style scoped lang="scss">
.think-time {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  align-items: center;
  width: 200px;
  height: 27px;
  background: #3875f6;
  border-radius: 27px;
  font-family: Source Han Sans SC, Source Han Sans SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 27px;
  cursor: pointer;
}
.think-desc {
  font-family: Source Han Sans SC, Source Han Sans SC;
  font-weight: 400;
  font-size: 12px;
  color: #6a6a6a;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  border-left: 3px solid #c8c8c8;
  padding-left: 10px;
  margin: 10px 5px 0 10px;
}
.btn-item {
  width: 25px;
  height: 25px;
  cursor: pointer;
  &:hover {
    transform: scale(1.1);
  }
  &.download {
    background: url("~@/assets/images/ai/download.png") no-repeat center;
    background-size: cover;
  }
  &.copy {
    background: url("~@/assets/images/ai/copy.png") no-repeat center;
    background-size: cover;
  }
  &.praise {
    background: url("~@/assets/images/ai/praise.png") no-repeat center;
    background-size: cover;
  }
  &.hate {
    background: url("~@/assets/images/ai/hate.png") no-repeat center;
    background-size: cover;
  }
}
</style>
