<template>
  <el-radio-group v-model="value" size="mini" @change="change">
    <template v-if="radioType == 'normal'">
      <el-radio
        v-for="(item, index) in options"
        :key="index"
        :label="item.value"
      >
        {{ item.text }}
      </el-radio
      >
    </template>
    <template v-else>
      <el-radio-button
        v-for="(item, index) in options"
        :label="item.value"
        :key="index"
      >
        {{ item.text }}
      </el-radio-button
      >
    </template>
  </el-radio-group>
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: String,
    },
    options: {
      type: Array,
      default: () => [],
    },
    radioType: {
      type: String,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    change() {
      this.$emit("update:value", this.value)
      this.$emit("change", { [this.item.key]: this.value })
    },
  },
}
</script>

<style scoped lang="less"></style>
