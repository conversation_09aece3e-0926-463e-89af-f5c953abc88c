import moment from 'moment'

export function getDefaultFieldConfig() {
  return {
    alias: '',
    desc: '',
    useExpression: false
  }
}

export function extractQueryVariableNames(expression, withBoundaryToken = false) {
  const names = []
  if (!expression) { return names }
  const varReg = /\$(\w+)\$/g
  expression.replace(varReg, (match, p) => {
    const name = withBoundaryToken ? match : p
    if (!names.includes(name)) {
      names.push(name)
    }
    return name
  })
  return names
}

export function getFieldAlias(fieldConfig, queryVariableMap) {
  if (!fieldConfig) { return '' }

  const { alias, useExpression } = fieldConfig
  if (!useExpression) { return alias }

  const queryKeys = extractQueryVariableNames(alias, true)
  const keys = []
  const vals = []
  queryKeys.forEach((queryKey) => {
    keys.push(queryKey)
    const queryValue = queryVariableMap[queryKey]
    if (queryValue === undefined) {
      vals.push('')
    } else {
      vals.push(
        typeof queryValue === 'number'
          ? queryValue
          : queryValue.replace(/^(['"])|(['"])$/g, ''))
    }
  })

  const Moment = moment
  let funcBody = alias
  if (!alias.includes('return')) {
    funcBody = 'return ' + funcBody
  }
  const paramNames = [ 'Moment', ...keys, funcBody ]
  try {
    const func = Function.apply(null, paramNames)
    const params = [ Moment, ...vals ]
    const dynamicAlias = func(...params)
    return dynamicAlias
  } catch (e) {
    this.$message.error(`字段别名转换错误：${e.message}`)
  }
}
