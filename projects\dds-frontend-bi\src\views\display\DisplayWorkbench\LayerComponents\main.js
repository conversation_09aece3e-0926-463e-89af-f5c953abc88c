// 引入当前目录下所有的组件配置文件
import Vue from "vue"

export const widgetTool = []
require
  .context("./", true, /\.js$/)
  .keys()
  .forEach((key) => {
    if (key === "./index.js" || key === "./main.js") return
    const k = key.split("/")[2].replace(".js", "")
    const config = require(`${key}`)
    widgetTool.push(config[k])
  })
const requireComponent = require.context("./", true, /\.vue$/) // 获取components目录下的所有.vue文件
const widgetComponents = []
requireComponent.keys().forEach((ele) => {
  if (ele.indexOf("LayerBox") !== -1) return
  const moduleObj = requireComponent(ele).default
  const k = ele.split("/")[1]
  // 如果widgetComponents中已经存在了该组件，就不再添加
  if (widgetComponents.indexOf(k) === -1) {
    widgetComponents.push(k)
    Vue.component(k, moduleObj)
  }
})
