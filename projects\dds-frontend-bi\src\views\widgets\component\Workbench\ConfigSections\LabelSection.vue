<template>
  <div class="paneBlock">
    <h4>标签</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox v-model="labelForm.showLabel" @change="changeLabelStyle">
            显示标签
          </el-checkbox>
        </el-col>
        <el-col span="4"> 位置 </el-col>

        <el-col span="10" v-if="chartData.chartType == 'pie'">
          <el-select
            v-model="labelForm.pieLabelPosition"
            placeholder="请选择"
            size="mini"
            @change="changeLabelStyle"
          >
            <el-option
              v-for="item in CHART_PIE_LABEL_POSITIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col v-else span="10">
          <el-select
            v-model="labelForm.labelPosition"
            placeholder="请选择"
            size="mini"
            @change="changeLabelStyle"
          >
            <el-option
              v-for="item in CHART_LABEL_POSITIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeLabelStyle"
            v-model="labelForm.labelFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeLabelStyle"
            v-model="labelForm.labelFontSize"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <el-color-picker
            v-model="labelForm.labelColor"
            @change="changeLabelStyle"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
        v-if="chartData.name == 'pie'"
      >
        <el-checkbox-group v-model="labelForm.labelParts">
          <el-col span="8">
            <el-checkbox @change="changeLabelStyle" label="dimensionValue">
              维度值
            </el-checkbox>
          </el-col>
          <el-col span="8">
            <el-checkbox @change="changeLabelStyle" label="indicatorValue">
              指标值
            </el-checkbox>
          </el-col>
          <el-col span="8">
            <el-checkbox @change="changeLabelStyle" label="percentage">
              百分比
            </el-checkbox>
          </el-col>
        </el-checkbox-group>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
        v-if="chartData.name == 'radar'"
      >
        <el-checkbox-group v-model="labelForm.labelParts">
          <el-col span="12">
            <el-checkbox @change="changeLabelStyle" label="indicatorName">
              指标名称
            </el-checkbox>
          </el-col>
          <el-col span="12">
            <el-checkbox @change="changeLabelStyle" label="indicatorValue">
              指标值
            </el-checkbox>
          </el-col>
        </el-checkbox-group>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  CHART_LABEL_POSITIONS,
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  CHART_PIE_LABEL_POSITIONS,
} from "@/globalConstants"
export default {
  name: "label-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      CHART_LABEL_POSITIONS,
      PIVOT_CHART_FONT_FAMILIES,
      PIVOT_CHART_FONT_SIZES,
      CHART_PIE_LABEL_POSITIONS,
      labelForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.labelForm = this._.cloneDeep(this.chartData.chartStyles.label)
    },
    changeLabelStyle() {
      this.$emit("changeStyle", "label", this.labelForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
