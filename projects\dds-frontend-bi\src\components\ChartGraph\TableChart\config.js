import ChartTypes from '../ChartTypes'
const table =  {
  id: ChartTypes.Table,
  name: 'table',
  title: '表格',
  icon: 'chart_table',
  rules: [ { dimension: [ 0, 9999 ], metrics: [ 0, 9999 ] } ],
  data: [
    {
      title: '维度',
      type: 'category',
      key: "dimension",
      label: "dimension"
    },
    {
      title: '指标',
      type: 'value',
      key: "metrics",
      label: "metrics"
    },
    {
      title: '筛选',
      type: 'all',
      key: "filters",
      label: "filters"

    }
  ],
  style: {
  }
}
export default table
