<template>
  <div class="portal-main" :theme="theme" v-loading="treeLoading">
    <div class="content-box">
      <editor-header :current-portal="currentPortal" />
      <div class="portalBody">
        <div class="siderbar">
          <div class="portal-btn">
            <el-input
              placeholder="请输入"
              size="mini"
              v-model="filterText"
              suffix-icon="el-icon-search"
            ></el-input>
            <el-dropdown @command="handleExpand">
              <div>
                <svg-icon class="icon" icon-class="more2" />
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="add">新增</el-dropdown-item>
                <el-dropdown-item :command="true">展开</el-dropdown-item>
                <el-dropdown-item :command="false">收起</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="portalTreeNode" v-if="refreshTree">
            <el-tree
              :data="treeData"
              draggable
              node-key="id"
              highlight-current
              @node-drop="handleDrop"
              @node-click="handleNodeClick"
              :default-expand-all="isExpandAll"
              :filter-node-method="filterNode"
              ref="tree"
            >
              <div class="custom-tree-node" icon-class slot-scope="{ data }">
                <span class="labelView">
                  <i v-show="data.type == 0" class="el-icon-folder-opened" />
                  <svg-icon
                    class="svg_icon"
                    v-show="data.type !== 0"
                    :icon-class="data.icon"
                    style="margin-right: 12px; font-size: 13px"
                  />
                  {{ data.name }}
                </span>

                <div class="buttonView">
                  <el-dropdown @command="handleCommand" trigger="click">
                    <span class="el-dropdown-link">
                      <i
                        class="el-icon-more"
                        style="transform: rotate(90deg)"
                      ></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{ type: 'edit', data }">
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="{ type: 'copy', data }"
                        v-if="!directory.type"
                      >
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="{ type: 'updateImage', data }"
                      >
                        更新缩略图
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ type: 'del', data }">
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </el-tree>
          </div>
        </div>
        <div class="gridClass">
          <router-view ref="child" />
          <!-- <div class="GolobalPanel" v-if="filters && filters.length">
          <GolobalPanelComponent
            ref="GolobalPanel"
            :controls.sync="filters"
            :queryMode="queryMode"
            :selectOptions="globalSelectOptions"
            @change="getFormValuesRelatedItems"
            @reset="handleResetGlobalFilters"
            type="golobal"
          />
        </div>

        <div class="dashboardList">
          <Dashboard
            ref="Dashboards"
            :isAuthorized="directory.roleIds"
            :globalControls="filters"
            :originalFilters="originalFilters"
            :queryMode="queryMode"
            :widgets="widgets"
            :FormValuesRelatedItems="FormValuesRelatedItems"
            :FormValuesRelatedItemsAll="FormValuesRelatedItemsAll"
            :currentDashborad.sync="currentDashborad"
            @onReload="getWidgetsConfig"
          />
        </div> -->
        </div>
      </div>
    </div>

    <!-- 新增/编辑dashboard -->
    <el-dialog
      :title="formType === 'add' ? '新增' : '设置'"
      :border="true"
      :visible.sync="dialogShow"
      width="560px"
    >
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="info">
            <el-form
              :model="directory"
              :rules="rules"
              ref="ruleForm"
              label-width="110px"
              class="demo-ruleForm"
            >
              <el-form-item label="所属分组: " prop="type">
                <el-select
                  v-model="directory.dashboardPortalId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in portalList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属文件夹: " prop="type">
                <el-select v-model="directory.parentId" placeholder="请选择">
                  <el-option
                    v-for="item in files"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="名称: " prop="name" style="margin-top: 10px">
                <el-input
                  v-model="directory.name"
                  placeholder="请输入名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="选择类型 " style="margin-top: 10px">
                <el-radio-group
                  v-model="directory.type"
                  :disabled="formType == 'edit'"
                >
                  <el-radio :label="0">文件夹</el-radio>
                  <el-radio :label="1">应用看板</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="显示水印"
                v-if="portalWatermarkShow && directory.type"
              >
                <el-switch
                  v-model="directory.watermark"
                  :active-value="1"
                  :inactive-value="0"
                ></el-switch>
              </el-form-item>
              <!-- <el-form-item label="更新缩略图" v-if="directory.type">
                <el-button
                  @click="updateThumbnail"
                  type="primary"
                  icon="el-icon-refresh"
                  circle></el-button>
              </el-form-item> -->
              <el-form-item label="图标" v-if="directory.type">
                <el-popover
                  trigger="click"
                  placement="right"
                  :style="{ display: 'inline-block' }"
                  v-model="menuIconPopover"
                >
                  <div slot="reference">
                    <DT-Icon
                      class="svg_icon"
                      v-if="directory.icon"
                      :icon-class="directory.icon"
                    />
                    <el-button v-else>选择图标</el-button>
                  </div>
                  <div class="menu_icon_popover_list_view">
                    <svg-icon
                      class="svg_icon"
                      v-for="index in 15"
                      :key="index"
                      :icon-class="'icon-' + index"
                      @click="selectMenuIcon('icon-' + index)"
                    />
                    <div class="svg_icon_null" @click="selectMenuIcon('')">
                      移除
                    </div>
                  </div>
                </el-popover>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="权限管理" name="permission">
            <el-checkbox v-model="directory.roleIds" label="1">
              管理员
            </el-checkbox>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EditorHeader from "./component/EditorHeader.vue"
import Request from "@/service"
import { mapState } from "vuex"
export default {
  components: {
    EditorHeader
  },
  props: {},
  data() {
    return {
      menuIconPopover: false,
      treeLoading: false,
      widgets: [],
      queryMode: 0, // 0:立即模式，1：手动模式
      isShowControlPanel: false,
      controlConfigVisible: false, // 控制器
      dialogShow: false, // 新增/编辑 portal  dialog
      filterText: "", // 过滤
      isExpandAll: true, // 是否展开全部tree
      refreshTree: true, // 重新渲染tree的状态,
      currentDashborad: [],
      widgetList: [],
      dashboardId: "",
      portalId: "",
      formType: "",
      activeName: "info",
      currentPortal: {
        description: "",
        name: ""
      },
      directory: {
        config: {},
        dashboardPortalId: 0,
        index: 0,
        name: "",
        parentId: 0,
        type: 0,
        roleIds: false,
        thumbnail: "",
        watermark: 0,
        icon: "",
        dashbordType: 0
      },
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        type: [{ required: true, message: "请选择目录", trigger: "change" }]
      },
      treeList: [],
      globalSelectOptions: {},
      FormValuesRelatedItems: [],
      FormValuesRelatedItemsAll: [],
      originalFilters: [],
      filters: [],
      portalList: []
    }
  },
  computed: {
    files() {
      return [
        { name: "根目录", id: 0 },
        ...this.treeList.filter(item => item.type === 0)
      ]
    },
    treeData() {
      return this.formatToTree(this.treeList)
    },
    ...mapState({
      theme: state => state.settings.theme,
      portalWatermarkShow: state => state.watermark.portalWatermarkShow
    })
  },
  created() {
    this.currentPortal = this.$route.query
    this.portalId = this.$route.query.id ?? null
    this.dashboardId = this.$route.params.id
    this.getPortalList()
    this.getTree()
    this.getAllWidgets()
  },
  mounted() {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },

  methods: {
    // 获取列表
    async getPortalList() {
      const { data } = await Request.dashboard.getAllPortal()
      this.portalList = data
    },
    handleResetGlobalFilters() {
      this.filters = this._.cloneDeep(this.originalFilters)
    },

    // 获取设置控制器关联那些图表
    setControlGetRelatedItems(filters) {
      this.FormValuesRelatedItems = filters.reduce((filter, nextFilter) => {
        const checkedItems = Object.entries(nextFilter.relatedItems)
          .filter(([, config]) => config.checked)
          .map(([itemId]) => itemId)
        return Array.from(new Set([...filter, ...checkedItems]))
      }, [])
    },
    // 获取控制器关联的所有图表
    // getFormValuesRelatedItemsAll() {
    //   this.FormValuesRelatedItemsAll = this.filters.reduce(
    //     (filter, nextFilter) => {
    //       const checkedItems = Object.entries(nextFilter.relatedItems)
    //         .filter(([itemId, config]) => config.checked)
    //         .map(([itemId]) => itemId);
    //       return Array.from(new Set([...filter, ...checkedItems]));
    //     },
    //     []
    //   );
    // },

    getFormValuesRelatedItems(formValues) {
      this.FormValuesRelatedItems = Object.keys(formValues).reduce(
        (items, key) => {
          const control = this.filters.find(c => c.key === key)
          const { relatedItems } = control
          const checkedItems = Object.entries(relatedItems)
            .filter(([, config]) => config.checked)
            .map(([itemId]) => itemId)
          return Array.from(new Set([...items, ...checkedItems]))
        },
        []
      )
    },
    getSelectOptions() {
      const selectOptions = {}
      this.filters.length &&
        this.filters.forEach(filter => {
          Object.entries(filter.relatedItems).forEach(async ([, v]) => {
            if (["select", "radio"].includes(filter.type)) {
              if (filter.optionType === "auto" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: filter.relatedViews[v.viewId].fields,
                  viewId: v.viewId
                }
                const { data } = await Request.view.getdistinctvalue(param)
                this.$set(selectOptions, filter.key, [...data])
              } else if (filter.optionType === "manual" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: [filter.valueField, filter.textField],
                  viewId: filter.valueViewId
                }
                // 如果加载过一次手动数据  不需要加载第二次
                if (!selectOptions[filter.key]) {
                  this.$set(selectOptions, filter.key, [])
                  const { data } = await Request.view.getdistinctvalue(param)
                  this.$set(selectOptions, filter.key, [...data])
                }
              } else if (
                filter.optionType === "custom" &&
                !selectOptions[filter.key]
              ) {
                this.$set(selectOptions, filter.key, [...filter.customOptions])
              }
            }
          })
        })

      this.globalSelectOptions = selectOptions
    },
    // 获取所有图表配置
    async getAllWidgets() {
      const { data } = await Request.widget.getAll()
      this.$store.commit("widget/SET_WIDGETS", data)
      this.widgets = data
    },
    // 获取当前页面所有图表关系
    getCurrentDashboradRelations() {
      this.widgetList = []
      let obj = {}
      const deepCloneWidgets = JSON.parse(
        JSON.stringify(this.currentDashborad.widgets)
      )
      deepCloneWidgets.forEach(item => (obj[item.id] = item))
      this.currentDashborad.relations.forEach(item => {
        obj[item.widgetId].widgetId = item.widgetId
        obj[item.widgetId].id = item.id
        this.widgetList.push(obj[item.widgetId])
      })
    },
    // 获取dashboard
    async getWidgetsConfig() {
      this.filters = []
      const { data, code } = await Request.dashboard.widgets({
        dashboardId: this.dashboardId
      })
      if (code === 200) {
        this.currentDashborad = data
        this.filters = (data.config && JSON.parse(data.config).filters) || []

        // 备份原始数据 为了重置
        this.originalFilters = this._.cloneDeep(this.filters)
        this.queryMode = (data.config && JSON.parse(data.config).queryMode) || 0
        this.getSelectOptions()
        this.getCurrentDashboradRelations()
        console.log(data.watermark, "data.watermark")
        if (data.watermark) {
          this.$store.commit("watermark/SET_WATERMARK")
        } else {
          this.$store.commit("watermark/OUT_WATERMARK")
        }
      }
    },
    // 收起/展开
    handleExpand(isExpandAll) {
      if (isExpandAll === "add") {
        this.addDashboard()
        return
      }
      this.refreshTree = false
      this.isExpandAll = isExpandAll
      this.$nextTick(() => {
        this.refreshTree = true
      })
    },
    handleCommand(command) {
      // 编辑
      if (command.type === "edit") {
        this.formType = "edit"
        this.directory = {
          ...command.data,
          config: JSON.parse(command.data.config)
        }
        delete this.directory.children
        this.dialogShow = true
      } else if (command.type === "copy") {
        this.formType = "copy"
        this.directory = {
          ...command.data,
          config: JSON.parse(command.data.config)
        }
        delete this.directory.children
        delete this.directory.id
        this.handleSave()
      } else if (command.type === "updateImage") {
        this.$store.commit("settings/SET_DASHBOARDS_INFO", command.data)
        this.updateThumbnail()
      } else {
        // 删除
        this.$confirm(
          `此操作将永久删除该${
            command.data.type === 0 ? "文件夹" : "仪表板"
          }, 是否继续?`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
        ).then(async () => {
          const { code } = await Request.dashboard.deleteDashboard({
            dashboardId: command.data.id
          })
          if (code === 200) {
            this.$message.success("删除成功")
            this.handleCancel()
            this.getTree()
          }
        })
      }
    },
    addDashboard() {
      Object.assign(this.$data.directory, this.$options.data().directory)

      this.dialogShow = true
      this.formType = "add"
    },
    async handleSave() {
      if (this.directory.watermark) {
        this.$store.commit("watermark/SET_WATERMARK")
      } else {
        this.$store.commit("watermark/OUT_WATERMARK")
      }
      // 新增
      if (this.formType === "add" || this.formType === "copy") {
        const { code } = await Request.dashboard.createDashboard({
          ...this.directory,
          config: JSON.stringify(this.directory.config),
          dashboardPortalId: this.portalId,
          index: this.treeList.length
            ? this.treeList[this.treeList.length - 1]?.index + 1
            : 1
        })
        if (code === 200) {
          this.$message.success(
            this.formType === "copy" ? "复制成功" : "新增成功"
          )
          Object.assign(this.$data.directory, this.$options.data().directory) // 重置
          this.handleCancel()
          this.getTree()
        }
        // 编辑
      } else {
        const { code } = await Request.dashboard.updateDashboards([
          { ...this.directory, config: JSON.stringify(this.directory.config) }
        ])
        if (code === 200) {
          this.$message.success("编辑成功")
          this.dialogShow = false
          this.getTree()
        }
      }
    },
    // 取消
    handleCancel() {
      this.dialogShow = false
      Object.assign(this.$data.directory, this.$options.data().directory)
    },
    // 获取tree
    async getTree() {
      this.treeLoading = true
      const { data } = await Request.dashboard.dashboards({
        id: this.portalId
      })

      const item = data.find(item => item.type !== 0)
      this.treeList = data.map((item, index) => {
        if (!item.icon) {
          item.icon = "icon-" + ((index + 1) % 11)
        }
        return item
      })
      this.treeLoading = false
      console.log(this.treeList, "this.treeList")
      this.$nextTick(() => {
        if (item?.id) {
          if (!this.dashboardId) {
            this.dashboardId = item.id
            this.$router.push({
              path: `/ddsBi/PortalDetail/Portalview/${item.id}${window.location.search}`
            })
            this.$store.commit("settings/SET_DASHBOARDS_INFO", item)
          }
          // this.getWidgetsConfig();
          this.$refs.tree.setCurrentKey(this.dashboardId)
          this.treeLoading = false
        } else {
          this.dashboardId = ""
          this.currentDashborad = []
        }
      })
    },

    // list转tree
    formatToTree(arr, pid) {
      return arr
        .filter(item =>
          // item.parentId === 0父级
          pid === undefined ? item.parentId === 0 : item.parentId === pid
        )
        .map(item => {
          // 通过父节点ID查询所有子节点
          item.children = this.formatToTree(arr, item.id)
          return item
        })
    },
    // tree过滤查询
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点点击
    handleNodeClick(data) {
      if (data.type !== 0 && this.dashboardId !== data.id) {
        this.dashboardId = data.id
        console.log("%cportal-detail.vue line:541,", "点击了")
        this.$store.commit("settings/SET_DASHBOARDS_INFO", data)

        this.$router.push({
          path: `/ddsBi/PortalDetail/Portalview/${data.id}${window.location.search}`
        })
        // this.getWidgetsConfig();
      }
    },
    // 选中图标时，赋值并隐藏弹窗
    selectMenuIcon(menuIcon) {
      console.log(menuIcon, "menuIcon")
      this.directory.icon = menuIcon
      this.menuIconPopover = false
    },
    // tree拖拽事件
    handleDrop() {},
    // 更新缩略图
    updateThumbnail() {
      this.$refs.child.updateThumbnail()
    }
  },
  beforeDestroy() {
    this.$store.commit("watermark/OUT_WATERMARK")
  }
}
</script>

<style scoped lang="scss">
@import "./theme/dark.scss";
@import "./theme/light.scss";

.portal-main {
  width: 100%;
  height: 100vh;
  // height: calc(100vh - 56px);
  background-color: var(--theme-color);
  .content-box {
    width: 1840px;
  }
  .portalBody {
    display: flex;
    // height: 100%;
    height: calc(100vh - 56px);
    background-color: var(--theme-color);

    .siderbar {
      width: 224px;
      border-right: 1px solid var(--theme-border-color);

      .portal-btn {
        height: 56px;
        padding: 12px 16px 13px 19px;
        display: flex;
        align-items: center;
        color: var(--theme-icon-color);

        ::v-deep .el-input {
          width: 160px;
          height: 32px;
          margin-right: 17px;
        }

        .add {
          margin-left: 14px;
          margin-right: 15px;
        }

        i {
          cursor: pointer;
        }
      }

      .portalTreeNode {
        height: calc(100% - 64px);
        box-sizing: border-box;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .gridClass {
      width: calc(100% - 224px);
      // width: 1468px;
      flex: 1;
      display: flex;
      flex-direction: column;
      background: var(--theme-bg-color);

      .grid-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 18px 16px;
        height: 56px;
        background-color: var(--theme-color);
        color: var(--theme-text-color);
        font-size: 14px;
      }

      .row-btn {
        margin: 0;

        & > * {
          margin-left: 24px;
        }
      }

      .GolobalPanel {
        margin: 16px 16px 0 16px;
        padding: 16px;
        background-color: var(--theme-color);
      }

      .dashboardList {
        box-sizing: border-box;
        padding-right: 0px;
        // flex: 1;
        overflow-y: auto;
      }
    }
  }
}

.labelView {
  display: flex;
  align-items: center;
}

.vue-grid-layout {
  background: #eee;
  touch-action: none;
}

::v-deep .custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .buttonView {
    opacity: 0;
    margin-right: 20px;
  }
}

.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}

.controlForm {
  height: auto;
  background-color: #fff;
}

i {
  color: var(--theme-icon-color);
}

::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner,
::v-deep .el-range-editor.el-input__inner {
  width: 100%;
}

// 菜单图标弹窗样式
.el-select__tags .el-tag .el-select__tags-text {
  max-width: 90px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu_icon_popover_list_view {
  display: flex;
  width: 300px;
  flex-wrap: wrap;

  .svg_icon {
    font-size: 20px;
    width: 25%;
    margin: 5px 0;
    cursor: pointer;
  }

  .svg_icon_null {
    width: 25%;
    margin: 5px 0;
    padding-top: 1px;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
  }
}

::-webkit-scrollbar {
  height: 4px;
  width: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
  background-color: var(--theme-color);
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-thumb {
  width: 4px;
  min-height: 15px;
  background: var(--scrollbar-color) !important;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5) !important;
}

::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  width: 10px;
  padding-left: 14px;
}
</style>
