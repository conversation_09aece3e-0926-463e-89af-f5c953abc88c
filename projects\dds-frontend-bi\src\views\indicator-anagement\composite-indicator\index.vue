<template>
  <DT-View
    :inner-style="{
      padding: 0
    }"
  >
    <div class="title">{{ indCode ? "编辑" : "新增" }}衍生指标</div>
    <div class="content">
      <!-- 指标域 -->
      <div class="pointer-field">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="指标域:">
            <el-cascader
              :options="groupData"
              v-model="group"
              @change="handleSearch"
              :show-all-levels="false"
              :props="{
                checkStrictly: true,
                expandTrigger: 'hover',
                emitPath: false,
                label: 'name',
                value: 'id',
                children: 'children'
              }"
              clearable
            ></el-cascader>
          </el-form-item>
          <el-form-item prop="sourceId" label="指标名称:">
            <el-input
              placeholder="请输入指标名称"
              v-model="indName"
              prefix-icon="el-icon-search"
              @input="handleSearch"
            ></el-input>
          </el-form-item>
        </el-form>
        <el-tree
          class="model-tree"
          ref="treeModel"
          draggable
          :data="indicatorList"
          :allow-drop="allowDrop"
          :props="{
            label: 'indName',
            value: 'indCode',
            children: 'children'
          }"
          @node-drag-start="handleTreeDragStart"
          @node-drag-end="handleTreeDragEnd"
          @scroll.native="handleScroll"
          v-load-more="handleScroll"
        >
          <template #default="{ data }">
            <el-tooltip
              class="item"
              effect="dark"
              :content="data.indName"
              :open-delay="500"
              placement="top-start"
            >
              <span :class="getNodeClass(data)">{{ data.indName }}</span>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <!--拖拽容器  -->
      <div class="drag-wrap" id="droppable">
        <!-- 新增按钮 -->
        <div class="add-btn">
          <el-button
            type="primary"
            :disabled="compositeIndicators.length == 3"
            @click="handleAdd"
            icon="el-icon-plus"
          >
            新增指标
          </el-button>
        </div>
        <!-- 删除区域 -->
        <div
          class="deletes-region drag"
          @dragover.prevent
          @drop="handleDeleteDragEnd"
        >
          <div class="icon"></div>
          <div class="text">删除区</div>
        </div>

        <!-- 拖拽区域 -->
        <div v-for="(item, i) in compositeIndicators" :key="i">
          <div class="thumbnail">
            <div class="number">
              {{ i + 1 }}
            </div>
            <div class="indicators">
              <div class="text">缩略图：</div>
              <div
                class="step-item"
                :class="{
                  active: item.tempIndex === -1
                }"
              ></div>

              <div class="symbol">=</div>
              <div class="steps" v-for="(e, index) in item.steps" :key="index">
                <div
                  v-if="e.ctype == 1 || e.ctype == 4"
                  class="step-item"
                  :class="{
                    active: index === item.tempIndex
                  }"
                ></div>
                <div
                  class="dot"
                  v-if="index !== item.steps.length - 1 && e.ctype == 3"
                  :class="{
                    isArithmetic: e.jsf
                  }"
                ></div>
              </div>
            </div>
          </div>
          <div
            class="drag-region"
            :class="{ active: i === currentIndex }"
            @click="currentIndex = i"
          >
            <!-- <div class="no" style="margin-right: 24px">
             
            </div> -->
            <div class="result-wrap">
              <input
                type="text"
                class="result"
                :class="{
                  active: item.tempIndex === -1,
                  'is-invalid': item.isInputInvalid
                }"
                v-model="item.zbmc"
                placeholder="请命名"
                @blur="validateInput"
              />
              <span v-if="item.isInputInvalid" class="error-message">
                {{ item.inputErrorMessage }}
              </span>
            </div>

            <div class="equal-sign"></div>
            <!-- 指标区域 -->
            <div
              class="indicator-region"
              :class="{
                dragging: isDragging
              }"
              @drop="handleDrop($event, -1, i, 'left')"
              @dragover.prevent
              v-if="!item.steps.length"
            >
              <div class="icon"></div>

              请将指标拖到此处
            </div>
            <!-- 已拖拽指标 -->
            <div class="steps" v-else>
              <template>
                <div
                  v-for="(idr, index) in item.steps"
                  :key="index"
                  class="list-item-custom"
                  :class="{ active: item.tempIndex === index }"
                  @dragend="handleDragEnd"
                >
                  <!-- <BracketItem
                    v-if="idr.direction == 'left'"
                    @handleDrop="handleDrop"
                    :type="idr.direction"
                    :index="index"
                    :is-dragging="isDragging"
                  /> -->
                  <DragIndicatorItem
                    @handleDrop="handleDrop"
                    :index="index"
                    :temp-index="item.tempIndex"
                    :steps="item.steps"
                    :parent-index="i"
                    :item-prop="idr"
                    @update:item-prop="item.steps[index] = $event"
                    @handleDragStart="handleDragStart"
                    :map-dimensions="mapDimensions"
                    @clickAdd="index => (item.tempIndex = index)"
                    :is-dragging="isDragging"
                    @dimensionChanged="getSelectExtendDimension"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 底部区域-->
        <div class="tools">
          <!-- 特殊计算符号 -->
          <div class="special-symbol">
            <div class="text">特殊计算符</div>
            <div
              class="bracket"
              :class="{
                draging: bracketDraging,
                dragstart: isBracketDragstart
              }"
            >
              <div
                class="left"
                draggable="true"
                @dragstart="bracketDragStart($event, '(')"
                @drag="bracketDraging = true"
                @dragend="handleDragEnd"
              >
                (
              </div>
              <div
                class="right"
                draggable="true"
                @drag="bracketDraging = true"
                @dragstart="bracketDragStart($event, ')')"
                @dragend="handleDragEnd"
              >
                )
              </div>
              <div
                draggable="true"
                class="ipt"
                @drag="bracketDraging = true"
                @dragstart="inputDragStart"
                @dragend="handleDragEnd"
              >
                _123
              </div>
            </div>
          </div>
          <!-- . -->
          <div class="btns">
            <el-button @click="$router.go(-1)">取消</el-button>
            <el-button @click="getCalculateResult">试计算</el-button>
            <el-button
              type="primary"
              :loading="saveLoading"
              @click="handleSave"
            >
              保存
            </el-button>
          </div>
        </div>
      </div>
      <!-- 基础属性 -->
      <div class="base-property">
        <div class="base-title">
          基础属性
          <!-- {{ compositeIndicators[currentIndex].zbmc }} -->
        </div>
        <el-form
          ref="form"
          :model="compositeIndicators[currentIndex]"
          :rules="rules"
          label-width="80px"
          class="form"
          label-position="top"
        >
          <el-form-item label="计算周期" prop="jszq">
            <el-select
              v-model="compositeIndicators[currentIndex].jszq"
              placeholder="请选择计算周期"
            >
              <el-option
                v-for="(item, index) in jszqList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="精度" prop="jd">
            <div style="display: flex; justify-content: space-between">
              <el-input
                style="width: 112px"
                v-model="compositeIndicators[currentIndex].jd"
              ></el-input>
              <el-checkbox
                v-model="compositeIndicators[currentIndex].sswr"
                :true-label="1"
                :false-label="0"
              >
                四舍五入
              </el-checkbox>
            </div>
          </el-form-item>
          <el-form-item label="所属数据域" v-if="viewGroup[0].children.length">
            <avue-input-tree
              default-expand-all
              v-model="compositeIndicators[currentIndex].sysjy"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择所属数据域"
              :dic="viewGroup"
            ></avue-input-tree>
          </el-form-item>
          <el-form-item label="归属部门">
            <el-cascader
              v-model="compositeIndicators[currentIndex].deptAllCode"
              style="width: 100%"
              placeholder="请选择归属部门"
              clearable
              :props="cascaderProps"
              @change="handleChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="数据格式" prop="dataFormat">
            <el-select
              v-model="compositeIndicators[currentIndex].dataFormat"
              placeholder="请选择数据格式"
            >
              <el-option
                v-for="item in sjgs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="单位"
            prop="jldw"
            v-if="compositeIndicators[currentIndex].dataFormat == 0"
          >
            <div style="display: flex">
              <el-select
                v-model="compositeIndicators[currentIndex].jldw"
                placeholder="请选择单位"
                :style="{
                  width:
                    compositeIndicators[currentIndex].jldw === '其他'
                      ? '87px'
                      : '100%'
                }"
                class="myselect"
              >
                <el-option
                  v-for="(item, index) in dwList"
                  :key="index"
                  :label="item.name"
                  :value="item.bm"
                ></el-option>
                <el-option label="无单位" value="无单位"></el-option>
              </el-select>
              <el-input
                v-if="compositeIndicators[currentIndex].jldw === '其他'"
                v-model="compositeIndicators[currentIndex].diydw"
                style="width: 134px; margin-left: 6px"
                placeholder="请输入单位"
                class="yz-input"
              ></el-input>
            </div>
          </el-form-item>

          <el-form-item label="设置阈值">
            <template #label>
              <span
                style="
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                "
              >
                设置阈值
                <el-tooltip
                  class="item"
                  effect="dark"
                  content=""
                  placement="top"
                >
                  <div slot="content">
                    1. 如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                    <br />
                    2. 如果不填写最大值，仅填写最小值，则表示大于等于最小值。
                  </div>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </span>
            </template>
            <div style="display: flex; justify-content: space-between">
              <el-form-item prop="tvmin" style="margin-bottom: 0">
                <el-input
                  v-model.number="compositeIndicators[currentIndex].tvmin"
                  placeholder="请输入最小值"
                  style="width: 87px"
                ></el-input>
              </el-form-item>
              -
              <el-form-item style="margin-bottom: 0">
                <el-input
                  v-model.number="compositeIndicators[currentIndex].tvmax"
                  style="width: 87px"
                  placeholder="请输入最大值"
                ></el-input>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item label="描述">
            <el-input v-model="compositeIndicators[currentIndex].ms"></el-input>
          </el-form-item>

          <el-form-item label="标签" prop="bq">
            <el-select
              v-model="compositeIndicators[currentIndex].bqList"
              filterable
              remote
              multiple
              allow-create
              default-first-option
              :multiple-limit="10"
              @visible-change="handleSelectVisibleChange"
              @change="changeTag"
              @remove-tag="removeTag"
              :remote-method="remoteMethod"
              placeholder="请创建或者选择标签"
              v-select-tag-tooltip="140"
            >
              <el-option
                v-for="item in formatLabels"
                :key="item.bqmc"
                :label="item.bqmc"
                :value="item.bqmc"
              >
                <span v-tooltip-content="180">
                  {{ item.bqmc }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="0值预警">
            <el-select
              v-model="compositeIndicators[currentIndex].zeroWarnTime"
              placeholder="请选择0值预警"
              filterable
            >
              <el-option
                v-for="(item, index) in zeroList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="聚类后0值成员过滤">
            <el-radio
              v-model="compositeIndicators[currentIndex].isZeroCluster"
              label="0"
            >
              关闭
            </el-radio>
            <el-radio
              v-model="compositeIndicators[currentIndex].isZeroCluster"
              label="1"
            >
              开启
            </el-radio>
          </el-form-item>

          <el-form-item
            label=""
            v-if="compositeIndicators[currentIndex].isZeroCluster == 1"
            prop="clusterDim"
          >
            <el-select
              v-model="compositeIndicators[currentIndex].clusterDim"
              placeholder="请选择聚类维度"
              filterable
            >
              <el-option
                v-for="item in compositeIndicators[currentIndex].sameExtendDims"
                :key="item"
                :label="item.dimName"
                :value="item.dimCol"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="可扩展维度">
            <el-select
              v-model="compositeIndicators[currentIndex].expandDims"
              placeholder="请选择一个或多个可扩展维度"
              value-key="dimCol"
              multiple
              filterable
              v-select-tag-tooltip="140"
            >
              <el-option
                v-for="item in compositeIndicators[currentIndex].sameExtendDims"
                :key="item"
                :label="item.dimAndLevelName || item.dimName"
                :value="item"
              >
                <span v-tooltip-content="180">
                  {{ item.dimAndLevelName || item.dimName }}
                </span>
              </el-option>
              <template #tag="{ item }">
                <span
                  class="custom-tag"
                  :title="item.dimAndLevelName || item.dimName"
                  style="
                    display: inline-block;
                    max-width: 140px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    vertical-align: middle;
                  "
                >
                  {{ item.dimAndLevelName || item.dimName }}
                </span>
              </template>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <CompositeIndicatorCalculate ref="CompositeIndicatorCalculate" />
    <AffectScopeDialog ref="AffectScopeDialog" />
  </DT-View>
</template>

<script>
import Request from "@/service"
import DragIndicatorItem from "./DragIndicatorItem"
import options from "../mixins/options"
import debounce from "lodash/debounce"
import CompositeIndicatorCalculate from "./Calculate.vue"
import AffectScopeDialog from "../components/AffectScopeDialog.vue"

export default {
  components: {
    DragIndicatorItem,
    CompositeIndicatorCalculate,
    AffectScopeDialog
  },

  mixins: [options],
  props: {},
  computed: {
    formatLabels() {
      return this.labels.filter(
        item => item.bqmc.includes(this.newTag) || !this.newTag
      )
    }
  },
  data() {
    const changeMin = (rule, value, callback) => {
      if (this.compositeIndicators[this.currentIndex].tvmax === "") {
        callback()
      } else if (
        Number(value) >
        Number(this.compositeIndicators[this.currentIndex].tvmax)
      ) {
        callback(new Error("不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.compositeIndicators[this.currentIndex].tvmin === "") {
        callback()
      } else if (
        Number(value) <
        Number(this.compositeIndicators[this.currentIndex].tvmin)
      ) {
        callback(new Error("不能小于最小值"))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.compositeIndicators[this.currentIndex].jldw === "") {
        callback(new Error("请选择单位"))
      } else if (
        this.compositeIndicators[this.currentIndex].jldw === "其他" &&
        this.compositeIndicators[this.currentIndex].diydw === ""
      ) {
        callback(new Error("请自定义单位"))
      } else {
        callback()
      }
    }
    return {
      group: "", // 指标域id
      indName: "", // 指标名称
      loading: false,
      page: {
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      groupData: [], // 指标域-树形列表
      indicatorList: [], // 指标列表
      compositeIndicators: [
        {
          zblx: "衍生指标", // 指标类型
          lxbm: "ys",
          execsql: "",
          ttab: "",
          expression: "",

          zbmc: "", // 指标名称
          sysjy: "0", // 所属数据域
          jszq: "", // 计算周期
          jd: 2, // 精度
          sswr: 1, // 四舍五入
          bq: "", // 标签
          bqids: "", // 标签
          ms: "", // 描述
          diydw: "", // 自定义单位
          jldw: "", // 计量单位
          tvmin: "", // 最小阈值
          tvmax: "", // 最大阈值
          steps: [],
          tempIndex: -1, // 当前操作的指标下标,
          dataFormat: 0,
          isInputInvalid: false,
          inputErrorMessage: "",
          expandDims: [],
          isZeroCluster: "0", // 是否开启0值聚类
          zeroWarnTime: null, // 0值预警
          isZeroWarn: null, // 是否开启0值预警,
          sameExtendDims: [] // 相同扩展维度
        }
      ],
      currentIndex: 0, // 选中衍生指标的下标
      rules: {
        jszq: {
          required: true,
          message: "请选择计算周期",
          trigger: "change"
        },
        jd: { required: true, message: "请输入精度", trigger: "change" },
        tvmin: { validator: changeMin, trigger: "blur" },
        tvmax: { validator: changeMax, trigger: "blur" },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: "change"
        },
        dataFormat: {
          required: true,
          message: "请选择数据格式",
          trigger: "change"
        },
        clusterDim: {
          required: true,
          message: "请选择聚类维度",
          trigger: "change"
        }
      },
      filterText: "", // tree过滤文本
      tableData: [], // 试计算结果
      dialogTableVisible: false,
      indCode: "", // 是否是编辑ID
      isIndicatorsDrag: false, // 是否是指标内的拖拽
      currentParentIndex: 0,
      bracketDraging: false,
      isBracketDragstart: false,
      isDragging: false, // 是否是拖拽中
      mapDimensions: {}, // 指标维度
      extendDimension: {}, // 扩展维度
      saveLoading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeModel.filter(val)
    },
    isDragging(val) {
      if (val) {
        const parent = document.querySelector(".drag-region.active")
        console.log(parent, "//////")
        const activeItems = parent.querySelector(".list-item-custom.active")
        console.log(activeItems, "activeItems")
        if (activeItems) {
          // 获取第一个激活的项
          const activeItem = activeItems

          this.$nextTick(() => {
            // 滚动父容器，使激活的项对齐
            parent.scrollLeft = activeItem.offsetLeft - parent.offsetLeft
          })
        }
      }
    }
  },
  created() {
    this.getAllViewGroup()
    this.getLabelSelectList()
    this.getBaseUnit()
    const { indCode } = this.$route.query
    if (indCode) {
      this.indCode = indCode
      this.currentIndex = 0
      this.getCompositeIndicatorInfo()
    } else {
      this.compositeIndicators[0].sysjy = Number(this.$route.query.sysjy) || 999
    }
    this.getIndicatorGroup()
    this.getInds()
  },
  mounted() {},

  methods: {
    async validateInput() {
      console.log("触发")
      const currentItem = this.compositeIndicators[this.currentIndex]
      // 根据自定义的校验规则进行校验
      if (currentItem.zbmc === "") {
        currentItem.isInputInvalid = true
        currentItem.inputErrorMessage = "内容不能为空"
      } else if (currentItem.zbmc.length > 40) {
        currentItem.isInputInvalid = true
        currentItem.inputErrorMessage = "最大为40个字符"
      } else {
        const { data } =
          await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
            zbmc: currentItem.zbmc,
            indCode: ""
          })
        if (data) {
          currentItem.isInputInvalid = true
          currentItem.inputErrorMessage = "该指标名称已存在，请重新输入"
        } else {
          currentItem.isInputInvalid = false
          currentItem.inputErrorMessage = ""
        }
      }
    },

    handleDragEnd() {
      this.bracketDraging = false
      this.isBracketDragstart = false
      this.isDragging = false
      this.isIndicatorsDrag = false
    },

    handleTreeDragEnd() {
      this.isDragging = false
      this.isIndicatorsDrag = false
    },
    // tree停止拖拽时节点可放置的位置
    allowDrop() {
      return false
    },

    // ----------------------------------------------------------------

    // 根据ID获取详情
    async getCompositeIndicatorInfo() {
      const { data } =
        await Request.compositeIndicator.getCompositeIndicatorInfo({
          indCode: this.indCode
        })
      this.compositeIndicators = [
        { ...data, sysjy: data.sysjy + "", bq: "", tempIndex: 0 }
      ]
      data.steps.forEach(item => {
        this.getSelectDimension(item)
      })
      this.getSelectExtendDimension()
    },
    // 新增衍生指标
    handleAdd() {
      // 最多新增三个
      if (this.compositeIndicators.length >= 3) {
        return this.$message({
          message: "最多新增三个衍生指标",
          type: "warning"
        })
      }
      if (!this.compositeIndicators[this.currentIndex].isInputInvalid) {
        this.validateInput()
      }
      this.verify(valid => {
        if (!valid) {
          return
        }
        this.compositeIndicators.push({
          zblx: "衍生指标", // 指标类型
          lxbm: "ys",
          execsql: "",
          ttab: "",
          expression: "",

          zbmc: "", // 指标名称
          sysjy: Number(this.$route.query.sysjy) || 999, // 所属数据域
          jszq: "", // 计算周期
          jd: 2, // 精度
          sswr: 1, // 四舍五入
          bq: "", // 标签
          bqids: "", // 标签
          ms: "", // 描述
          diydw: "", // 自定义单位
          jldw: "", // 计量单位
          tvmin: "", // 最小阈值
          tvmax: "", // 最大阈值
          steps: [],
          tempIndex: -1, // 当前操作的指标下标,
          dataFormat: 0,
          isInputInvalid: false,
          inputErrorMessage: "",
          expandDims: [],
          zeroWarnTime: null, // 0值预警
          isZeroWarn: null, // 是否开启0值预警
          sameExtendDims: []
        })
      })
    },
    // 校验
    verify(callback) {
      let someName = ""
      const isSomeZbmc = this.compositeIndicators.some((item, index) => {
        const currentIndex = this.compositeIndicators.findIndex(
          obj => obj.zbmc === item.zbmc
        )
        if (currentIndex !== index) {
          someName = item.zbmc
          return true
        } else {
          return false
        }
      })
      if (isSomeZbmc) {
        this.$message.warning(
          `本次创建了多个${someName}，请检查后修改重复的指标名称`
        )
        return callback(false) // 校验失败时调用callback
      }
      if (this.compositeIndicators.some(item => item.steps.length === 0)) {
        this.$message.warning("请拖拽选择指标")
        return callback(false) // 校验失败时调用callback
      }
      if (this.compositeIndicators.some(item => item.zbmc === "")) {
        this.$message.warning("请输入指标名称")
        return callback(false) // 校验失败时调用callback
      }

      const isHasOperator = this.compositeIndicators.every(item => {
        return item.steps.every(el => {
          if (el.ctype === 3) {
            return el.jsf
          } else if (el.ctype === 1 || el.ctype === 4) {
            return true
          } else {
            return true
          }
        })
      })
      if (!isHasOperator) {
        this.$message.warning("请添加计算方式和指标维度")
        return callback(false)
      }

      this.$refs.form.validate(async valid => {
        if (valid) {
          return callback(true)
        } else {
          return callback(false)
        }
      })
    },
    // 保存
    handleSave() {
      this.saveLoading = true

      this.verify(async valid => {
        if (!valid) {
          return (this.saveLoading = false)
        } else {
          try {
            const jldwMapping = {
              其他: 1,
              无单位: 2
            }
            if (this.indCode) {
              await this.$refs.AffectScopeDialog.isAffectSpace(
                {
                  indCode: this.indCode,
                  indType: this.lxbm
                },
                async () => {
                  await Request.compositeIndicator.addEditCompositeIndicator({
                    optType: this.indCode ? "update" : "insert",
                    ind: this.compositeIndicators.map(item => ({
                      ...item,
                      jldw: item.jldw === "其他" ? item.diydw : item.jldw,
                      diydw: jldwMapping[item.jldw] || 0,
                      isZeroWarn: item.zeroWarnTime === "0" ? 1 : 0
                    }))
                  })
                  this.$message.success("保存成功")
                  this.$router.push("/ddsBi/indicatorAnagement")
                }
              )
            } else {
              await Request.compositeIndicator.addEditCompositeIndicator({
                optType: this.indCode ? "update" : "insert",
                ind: this.compositeIndicators.map(item => ({
                  ...item,
                  jldw: item.jldw === "其他" ? item.diydw : item.jldw,
                  diydw: jldwMapping[item.jldw] || 0,
                  isZeroWarn: item.zeroWarnTime === "0" ? 1 : 0
                }))
              })
              this.$message.success("保存成功")
              this.$router.push("/ddsBi/indicatorAnagement")
            }
          } catch (error) {
            this.saveLoading = false
          } finally {
            this.saveLoading = false
          }
        }
      })
    },

    // 式计算
    async getCalculateResult() {
      this.verify(async valid => {
        if (!valid) {
          return
        } else {
          this.$refs.CompositeIndicatorCalculate.open()
          this.$refs.CompositeIndicatorCalculate.initDimensionList(
            this.compositeIndicators[this.currentIndex]
          )
        }
      })
    },
    // 指标内拖拽开始
    handleDragStart(e, item, parentIndex, index) {
      console.log(item, "itemitemitemitem")
      this.currentParentIndex = parentIndex
      setTimeout(() => {
        this.isDragging = true
      }, 0)
      // 指标内拖拽
      this.isIndicatorsDrag = true
      // 在拖拽开始时设置拖拽节点的数据
      e.dataTransfer.setData("tempData", JSON.stringify(item))
      // 设置拖动效果
      e.dataTransfer.effectAllowed = "move"

      this.$set(this.compositeIndicators[parentIndex], "tempIndex", index)
    },
    // 拖拽删除指标
    handleDeleteDragEnd(e) {
      const data = JSON.parse(e.dataTransfer.getData("tempData"))
      const pxh = data.pxh
      let steps = this.compositeIndicators[this.currentParentIndex].steps

      // 指标删除
      if (data.ctype === 1 || data.ctype === 4) {
        let countIndex = null
        // 第一个指标的下标
        let startIndicatorIndex = steps.findIndex(item => item.ctype === 1)

        if (pxh === startIndicatorIndex) {
          // 查找后一个计算符
          for (let i = pxh + 1; i < steps.length; i++) {
            if (steps[i].ctype === 3) {
              countIndex = i
              break
            }
          }

          if (countIndex) {
            steps.splice(countIndex, 1)
          }
          steps.splice(pxh, 1)
        } else {
          // 查找前一个计算符
          for (let i = pxh - 1; i >= 0; i--) {
            if (steps[i].ctype === 3) {
              countIndex = i
              break
            }
          }

          steps.splice(pxh, 1)
          if (countIndex) {
            steps.splice(countIndex, 1)
          }
        }
      }
      // 括号删除
      if (data.ctype === 2) {
        steps.splice(pxh, 1)
      }
      this.compositeIndicators[this.currentParentIndex].tempIndex =
        this.findType1AroundIndex(steps, pxh > steps.length ? pxh - 1 : pxh)
      // 删除指标
      // steps = steps.filter(item => item.pxh !== pxh)
      this.compositeIndicators[this.currentParentIndex].steps = steps.map(
        (item, index) => ({
          ...item,
          pxh: index
        })
      )

      // delete this.form.selfDimension[id]
      this.isDragging = false
      this.isIndicatorsDrag = false
      this.getSelectExtendDimension()
    },
    // 寻找当前拖拽相邻的指标元素
    findType1AroundIndex(array, targetIndex) {
      const length = array.length
      // 处理数组最左边的情况
      if (targetIndex === 0) {
        let rightIndex = targetIndex
        while (rightIndex < length) {
          if (array[rightIndex].ctype === 1) {
            return rightIndex // 只在右侧查找
          }
          rightIndex++
        }
        return -1 // 右侧没有找到
      }

      // 处理数组最右边的情况
      if (targetIndex === length - 1) {
        let leftIndex = targetIndex
        while (leftIndex >= 0) {
          if (array[leftIndex].ctype === 1) {
            return leftIndex // 只在左侧查找
          }
          leftIndex--
        }
        return -1 // 左侧没有找到
      }
      let leftIndex = targetIndex - 1
      let rightIndex = targetIndex + 1
      while (leftIndex >= 0 || rightIndex < array.length) {
        if (leftIndex >= 0 && array[leftIndex].ctype === 1) {
          return leftIndex // 优先找到左侧的
        }
        if (rightIndex < array.length && array[rightIndex].ctype === 1) {
          return rightIndex // 如果左侧没有找到，返回右侧的
        }
        leftIndex--
        rightIndex++
      }

      return -1 // 没有找到任何符合条件的元素
    },
    // 特殊符号拖拽
    bracketDragStart(event, direction) {
      console.log(event, direction, "directiondirection")
      this.isDragging = true
      this.isBracketDragstart = true

      const item = {
        id: null,
        compositeid: null,
        ctype: 2,
        indicatorid: null,
        zblx: null,
        lxbm: null,
        jsf: direction,
        stepDimensionList: [],
        pxh: 0
      }
      event.dataTransfer.setData("text/plain", JSON.stringify(item))
    },
    // input拖拽
    inputDragStart(event) {
      console.log(event, "///////////////")
      this.isDragging = true
      this.isBracketDragstart = true

      const item = {
        id: null,
        compositeid: null,
        ctype: 4,
        indicatorid: null,
        zblx: null,
        lxbm: null,
        jsf: "",
        stepDimensionList: [],
        pxh: 0
      }
      event.dataTransfer.setData("text/plain", JSON.stringify(item))
    },
    // tree拖拽开始
    handleTreeDragStart(node, event) {
      this.isDragging = true
      // 在拖拽开始时设置拖拽节点的数据
      event.dataTransfer.setData(
        "text/plain",
        JSON.stringify({
          indicatorName: node.data.indName,
          stepIndCode: node.data.indCode,
          indicatorUnit: node.data.unit,
          compositeid: null,
          ctype: 1,
          lxbm: node.data.indType,
          jsf: null,
          pxh: 0,
          stepDimensionList: []
        })
      )
    },
    // 拖拽结束
    handleDrop_copy(event, index, parentIndex, type = "right") {
      let steps = this.compositeIndicators[parentIndex].steps
      const indicatorLength = steps.filter(
        item => item.ctype === 1 || item.ctype === 4
      ).length

      if (indicatorLength === 20) {
        return this.$message({
          message: "一个衍生指标最多支持由右侧20个指标关联生成",
          type: "warning"
        })
      }
      this.isDragging = false
      event.preventDefault()

      // 插入
      const insertItem = position => {
        steps.splice(position, 0, {
          id: null,
          compositeid: null,
          ctype: 3,
          indicatorid: null,
          zblx: null,
          lxbm: null,
          jsf: null,
          stepDimensionList: [],
          pxh: 0
        })
      }

      // 判断是否为左括号右侧
      const isAfterLeftBracket =
        steps[index - 1] &&
        steps[index - 1].ctype === 2 &&
        steps[index - 1].jsf === "("
      const nextNode = steps[index]
      // 判断是否为右括号左侧
      const isBeforeRightBracket =
        steps[index] && steps[index].ctype === 2 && steps[index].jsf === ")"
      const prevNode = steps[index - 1]

      // 指标内拖拽
      if (this.isIndicatorsDrag) {
        const data = JSON.parse(event.dataTransfer.getData("tempData"))
        const pxh = data.pxh
        console.log(pxh, index, "pxhindex2222")
        if (
          (pxh === index && type === "left") ||
          (pxh === index - 1 && type === "right")
        ) {
          return
        }
        // 如果是括号
        if (data.ctype === 2) {
          if (index < pxh) {
            steps.splice(pxh, 1)
            // 新增逻辑：左括号右侧且下一个是指标节点
            if (
              isAfterLeftBracket &&
              nextNode &&
              (nextNode.ctype === 1 || nextNode.ctype === 4)
            ) {
              steps.splice(index, 0, data)
              insertItem(index + 1)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index
              )
              // 统一右括号左侧插入逻辑
            } else if (
              isBeforeRightBracket &&
              index > 0 &&
              prevNode &&
              (prevNode.ctype === 1 || prevNode.ctype === 4)
            ) {
              steps.splice(index, 0, data)
              insertItem(index)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index + 1
              )
            } else {
              steps.splice(index, 0, data)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index
              )
            }
          } else {
            steps.splice(pxh, 1)
            // 新增逻辑：左括号右侧且下一个是指标节点
            if (
              isAfterLeftBracket &&
              nextNode &&
              (nextNode.ctype === 1 || nextNode.ctype === 4)
            ) {
              steps.splice(index - 1, 0, data)
              insertItem(index)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index - 1
              )
              // 统一右括号左侧插入逻辑
            } else if (
              isBeforeRightBracket &&
              index - 1 > 0 &&
              steps[index - 2] &&
              (steps[index - 2].ctype === 1 || steps[index - 2].ctype === 4)
            ) {
              steps.splice(index - 1, 0, data)
              insertItem(index - 1)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index
              )
            } else {
              steps.splice(index - 1, 0, data)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index - 1
              )
            }
          }
        } else {
          // 第一个指标的下标
          let startIndicatorIndex = steps.findIndex(
            item => item.ctype === 1 || item.ctype === 4
          )
          console.log(startIndicatorIndex, "startIndicatorIndex")
          if (pxh === startIndicatorIndex) {
            let countIndex = null

            // 查找后一个计算符
            for (let i = pxh + 1; i < steps.length; i++) {
              if (steps[i].ctype === 3) {
                countIndex = i
                break
              }
            }
            if (countIndex) {
              steps.splice(countIndex, 1)
            }
            steps.splice(pxh, 1)
          } else {
            let countIndex = null

            // 查找前一个计算符
            for (let i = pxh - 1; i >= 0; i--) {
              if (steps[i].ctype === 3) {
                countIndex = i
                break
              }
            }
            console.log("计算符下标", pxh, countIndex)

            steps.splice(pxh, 1)
            if (countIndex) {
              steps.splice(countIndex, 1)
            }
          }
          // 新增逻辑：左括号右侧且下一个是指标节点
          if (
            isAfterLeftBracket &&
            nextNode &&
            (nextNode.ctype === 1 || nextNode.ctype === 4)
          ) {
            steps.splice(index, 0, data)
            insertItem(index + 1)
            this.$set(this.compositeIndicators[parentIndex], "tempIndex", index)
            // 统一右括号左侧插入逻辑
          } else if (
            isBeforeRightBracket &&
            index > 0 &&
            prevNode &&
            (prevNode.ctype === 1 || prevNode.ctype === 4)
          ) {
            steps.splice(index, 0, data)
            insertItem(index)
            this.$set(
              this.compositeIndicators[parentIndex],
              "tempIndex",
              index + 1
            )
          } else {
            if (type === "right") {
              if (index < pxh) {
                steps.splice(index, 0, data)
                insertItem(index)
                this.$set(
                  this.compositeIndicators[parentIndex],
                  "tempIndex",
                  index
                )
              } else {
                insertItem(index + 1)
                steps.splice(index + 1, 0, data)
                this.$set(
                  this.compositeIndicators[parentIndex],
                  "tempIndex",
                  index - 1
                )
              }
            } else {
              console.log(index, pxh, "indexpxh")
              if (index < pxh) {
                insertItem(index)
                steps.splice(index, 0, data)
                this.$set(
                  this.compositeIndicators[parentIndex],
                  "tempIndex",
                  index
                )
              } else {
                console.log("老戴这里")
                insertItem(index - 2)
                steps.splice(index - 2, 0, data)
                this.$set(
                  this.compositeIndicators[parentIndex],
                  "tempIndex",
                  index - 2
                )
              }
            }
          }
        }
        console.log(steps, "steps拖拽")
      } else {
        console.log(event.dataTransfer.getData("text/plain"))
        const data = JSON.parse(event.dataTransfer.getData("text/plain"))
        if (data.ctype === 1) {
          this.getSelectDimension(data)
        }
        let indLength = steps.filter(
          item => item.ctype === 1 || item.ctype === 4
        ).length
        // 指标拖拽插入计算符
        if ((data.ctype === 1 || data.ctype === 4) && indLength) {
          // 新增逻辑：左括号右侧且下一个是指标节点
          if (
            isAfterLeftBracket &&
            nextNode &&
            (nextNode.ctype === 1 || nextNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data, pxh: index })
            insertItem(index + 1)
            this.$set(this.compositeIndicators[parentIndex], "tempIndex", index)
            // 统一右括号左侧插入逻辑
          } else if (
            isBeforeRightBracket &&
            index > 0 &&
            prevNode &&
            (prevNode.ctype === 1 || prevNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data, pxh: index })
            insertItem(index)
            this.$set(
              this.compositeIndicators[parentIndex],
              "tempIndex",
              index + 1
            )
          } else {
            if (type === "right") {
              // 添加计算符
              insertItem(index)
              steps.splice(index + 1, 0, {
                ...data,
                pxh: index + 1
              })
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index + 1
              )
            } else {
              steps.splice(index, 0, {
                ...data,
                pxh: index
              })
              insertItem(index + 1)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index
              )
            }
          }
        } else {
          // 括号时
          // 统一右括号左侧插入逻辑
          if (
            isBeforeRightBracket &&
            index > 0 &&
            prevNode &&
            (prevNode.ctype === 1 || prevNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data })
            insertItem(index)
            this.$set(
              this.compositeIndicators[parentIndex],
              "tempIndex",
              index + 1
            )
          } else {
            steps.splice(index, 0, { ...data })
            if (type === "right") {
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index - 1
              )
            } else {
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index + 1
              )
            }
          }
        }
        console.log(steps, "steps1")
      }

      this.compositeIndicators[parentIndex].steps = steps.map((item, index) => {
        return {
          ...item,
          pxh: index
        }
      })
      console.log(
        this.compositeIndicators[parentIndex],
        "this.compositeIndicators[parentIndex]"
      )
      this.getSelectExtendDimension()
    },
    // 拖拽结束
    handleDrop(event, index, parentIndex, type = "right") {
      // 获取所有步骤
      let steps = this.compositeIndicators[parentIndex].steps
      // 获取所以指标数量
      const indicatorLength = steps.filter(
        item => item.ctype === 1 || item.ctype === 4
      ).length

      if (indicatorLength === 20) {
        return this.$message({
          message: "一个衍生指标最多支持由右侧20个指标关联生成",
          type: "warning"
        })
      }
      this.isDragging = false
      event.preventDefault()

      // 插入括号方法
      const insertItem = position => {
        steps.splice(position, 0, {
          id: null,
          compositeid: null,
          ctype: 3,
          indicatorid: null,
          zblx: null,
          lxbm: null,
          jsf: null,
          stepDimensionList: [],
          pxh: 0
        })
      }

      // 判断是否为左括号右侧
      const isAfterLeftBracket =
        steps[index - 1] &&
        steps[index - 1].ctype === 2 &&
        steps[index - 1].jsf === "("
      const nextNode = steps[index]
      // 判断是否为右括号左侧
      const isBeforeRightBracket =
        steps[index] && steps[index].ctype === 2 && steps[index].jsf === ")"
      const prevNode = steps[index - 1]

      // 指标内拖拽
      if (this.isIndicatorsDrag) {
        // 获取拖拽数据
        const data = JSON.parse(event.dataTransfer.getData("tempData"))
        const fromIndex = data.pxh // 原位置
        let toIndex = index // 目标位置

        // 移除原 step
        const [movedStep] = steps.splice(fromIndex, 1)

        // 计算插入位置（如果拖后面的往前拖，toIndex要减1）
        if (fromIndex < toIndex) toIndex--

        // 插入到目标位置
        steps.splice(toIndex, 0, movedStep)
        // 删除所有计算符
        steps = steps.filter(item => item.ctype !== 3)
        console.log(steps, "steps")
        // 重新整理 steps，保证两个指标之间有计算符
        for (let i = 0; i < steps.length; i++) {
          const cur = steps[i]
          if (cur.ctype === 1 || cur.ctype === 4) {
            // 找下一个指标
            let j = i + 1
            while (
              j < steps.length &&
              !(steps[j].ctype === 1 || steps[j].ctype === 4)
            ) {
              j++
            }
            if (j < steps.length) {
              // 检查i和j之间是否有运算符
              let hasOperator = false

              let leftBracketIndex = null
              let rightBracketIndex = null
              for (let k = i + 1; k < j; k++) {
                if (steps[k].ctype === 3) {
                  hasOperator = true
                }
                // 左括号指标内
                if (
                  steps[k].ctype === 2 &&
                  steps[k].jsf === "(" &&
                  !leftBracketIndex
                ) {
                  leftBracketIndex = k
                }
                // 右括号指标内
                if (
                  steps[k].ctype === 2 &&
                  steps[k].jsf === ")" &&
                  !rightBracketIndex
                ) {
                  rightBracketIndex = k
                }
              }
              if (!hasOperator) {
                if (leftBracketIndex) {
                  steps.splice(leftBracketIndex, 0, {
                    id: null,
                    compositeid: null,
                    ctype: 3,
                    indicatorid: null,
                    zblx: null,
                    lxbm: null,
                    jsf: null,
                    stepDimensionList: [],
                    pxh: 0
                  })
                } else if (rightBracketIndex) {
                  steps.splice(rightBracketIndex + 1, 0, {
                    id: null,
                    compositeid: null,
                    ctype: 3,
                    indicatorid: null,
                    zblx: null,
                    lxbm: null,
                    jsf: null,
                    stepDimensionList: [],
                    pxh: 0
                  })
                } else {
                  steps.splice(i + 1, 0, {
                    id: null,
                    compositeid: null,
                    ctype: 3,
                    indicatorid: null,
                    zblx: null,
                    lxbm: null,
                    jsf: null,
                    stepDimensionList: [],
                    pxh: 0
                  })
                }
                i++ // 跳过新插入的
              }
            }
          }
        }
        this.compositeIndicators[parentIndex].steps = steps.map(
          (item, idx) => ({
            ...item,
            pxh: idx
          })
        )

        // 选中拖拽后的 step
        this.$set(this.compositeIndicators[parentIndex], "tempIndex", toIndex)

        this.getSelectExtendDimension()
      } else {
        console.log(event.dataTransfer.getData("text/plain"))
        const data = JSON.parse(event.dataTransfer.getData("text/plain"))
        if (data.ctype === 1) {
          this.getSelectDimension(data)
        }
        let indLength = steps.filter(
          item => item.ctype === 1 || item.ctype === 4
        ).length
        // 指标拖拽插入计算符
        if ((data.ctype === 1 || data.ctype === 4) && indLength) {
          // 新增逻辑：左括号右侧且下一个是指标节点
          if (
            isAfterLeftBracket &&
            nextNode &&
            (nextNode.ctype === 1 || nextNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data, pxh: index })
            insertItem(index + 1)
            this.$set(this.compositeIndicators[parentIndex], "tempIndex", index)
            // 统一右括号左侧插入逻辑
          } else if (
            isBeforeRightBracket &&
            index > 0 &&
            prevNode &&
            (prevNode.ctype === 1 || prevNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data, pxh: index })
            insertItem(index)
            this.$set(
              this.compositeIndicators[parentIndex],
              "tempIndex",
              index + 1
            )
          } else {
            if (type === "right") {
              // 添加计算符
              insertItem(index)
              steps.splice(index + 1, 0, {
                ...data,
                pxh: index + 1
              })
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index + 1
              )
            } else {
              steps.splice(index, 0, {
                ...data,
                pxh: index
              })
              insertItem(index + 1)
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index
              )
            }
          }
        } else {
          // 括号时
          // 统一右括号左侧插入逻辑
          if (
            isBeforeRightBracket &&
            index > 0 &&
            prevNode &&
            (prevNode.ctype === 1 || prevNode.ctype === 4)
          ) {
            steps.splice(index, 0, { ...data })
            insertItem(index)
            this.$set(
              this.compositeIndicators[parentIndex],
              "tempIndex",
              index + 1
            )
          } else {
            steps.splice(index, 0, { ...data })
            if (type === "right") {
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index - 1
              )
            } else {
              this.$set(
                this.compositeIndicators[parentIndex],
                "tempIndex",
                index + 1
              )
            }
          }
        }
        console.log(steps, "steps1")
      }

      this.compositeIndicators[parentIndex].steps = steps.map((item, index) => {
        return {
          ...item,
          pxh: index
        }
      })
      console.log(
        this.compositeIndicators[parentIndex],
        "this.compositeIndicators[parentIndex]"
      )
      this.getSelectExtendDimension()
    },
    // 获取拓展维度
    async getSelectExtendDimension() {
      let params = this.compositeIndicators[this.currentIndex].steps
        .filter(item => item.ctype === 1)
        .map(item => ({
          indCode: item.stepIndCode,
          lxbm: item.lxbm
        }))
      if (params.length === 0) {
        return
      }

      const { data } =
        await Request.compositeIndicator.getSelectExtendDimension(params)

      // 收集所有step中stepDimensionList已选的dimCol
      const selectedDimCols = new Set()
      this.compositeIndicators[this.currentIndex].steps.forEach(step => {
        if (Array.isArray(step.stepDimensionList)) {
          step.stepDimensionList.forEach(dim => {
            if (typeof dim === "object" && dim.dimCol) {
              selectedDimCols.add(dim.dimCol)
            } else if (typeof dim === "string") {
              selectedDimCols.add(dim)
            }
          })
        }
      })

      // 过滤sameExtendDims和expandDims
      const filteredDims = data.filter(dim => !selectedDimCols.has(dim.dimCol))

      this.$set(
        this.compositeIndicators[this.currentIndex],
        "sameExtendDims",
        filteredDims
      )
      this.$set(
        this.compositeIndicators[this.currentIndex],
        "expandDims",
        filteredDims
      )
    },
    // 获取维度
    async getSelectDimension(itemProp) {
      if (!this.mapDimensions[itemProp.stepIndCode] && itemProp.ctype === 1) {
        const { data } = await this.$httpBi.api.paramGet(
          "/DimManage/getDimLevelByIndCode",
          {
            indCode: itemProp.stepIndCode
          }
        )
        this.mapDimensions[itemProp.stepIndCode] = data || []
      }
    },
    // 获取指标域展示列表
    async getIndicatorGroup() {
      const { data } = await Request.analyzeTheme.getIndicatorGroup()
      this.groupData = data
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // tree节点能否被拖拽
    allowDrag(draggingNode) {
      if (draggingNode.data.type === "1") {
        return draggingNode
      }
    },
    // 区分文件夹和节点
    getNodeClass(data) {
      return data.type === "0" ? "folder-style" : "item-style"
    },
    handleScroll() {
      if (!this.loading && this.indicatorList.length < this.page.total) {
        this.page.currentPage++
        this.getInds()
      }
    },
    handleSearch: debounce(function () {
      this.page.currentPage = 1
      this.indicatorList = []
      this.getInds()
    }, 500),
    async getInds() {
      this.loading = true
      const { data } = await Request.analyzeTheme.getIndicatorList({
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage,
        group: this.group,
        indName: this.indName
      })
      this.indicatorList.push(...data.records)
      this.page.total = data.total
      this.loading = false
      const treeEl = document.querySelector(".model-tree")
      if (
        treeEl.scrollTop === 0 &&
        treeEl.scrollHeight === treeEl.clientHeight &&
        this.indicatorList.length < this.page.total
      ) {
        this.handleScroll()
      }

      // if(this.$refs.treeModel.scrollHeight)
    }
  }
}
</script>

<style scoped lang="scss">
.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 56px;
  width: 100%;
  height: 56px;
  padding-left: 20px;
  box-sizing: border-box;
  box-shadow: inset 0px -1px 0px 0px #ebedf0;
}

.content {
  display: flex;
  padding: 20px;

  box-sizing: border-box;
  height: calc(100vh - 195px);
  min-height: 640px;

  .pointer-field {
    max-width: 224px;
    flex: 0 0 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 5px 0px 0px 5px;
    border: 1px solid #e4e7ed;
    padding: 16px;

    ::v-deep .el-form-item--small.el-form-item {
      margin-bottom: 5px;
    }

    ::v-deep .el-form--label-top .el-form-item__label {
      padding: 0 0 5px 0;
      line-height: 24px;
    }

    // width: 208px;
    // padding-right: 24px;
    // box-sizing: border-box;
    // height: 100%;
    // border-right: 1px solid #edeff0;
    // overflow: auto;
    .title {
      width: 224px;
      height: 70px;
      border-radius: 5px 0px 0px 0px;
      box-shadow: inset 0px -1px 0px 0px #ebedf0;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      line-height: 70px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 12px;
      box-sizing: border-box;
    }

    .model-tree {
      margin-top: 12px;
      box-sizing: border-box;
      height: calc(100% - 140px);
      overflow: auto;

      ::v-deep .el-tree-node__label {
        width: 100%;
      }

      // ::v-deep .el-tree-node.is-current.is-focusable {
      //
      //   .el-tree-node__label {
      //     background: #f4f7ff;
      //     box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
      //       0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      //     border-radius: 4px;
      //     border: 1px solid #1563ff;
      //     color: #1563ff;
      //   } }

      //滚动条样式
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }

    .tabs {
      display: flex;
      padding: 0 12px;
      box-sizing: border-box;
      margin: 16px 0;

      .tab-item {
        width: 100px;
        height: 30px;
        border-radius: 15px;
        border: 1px solid #dcdfe6;
        cursor: pointer;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 30px;
        text-align: center;

        &:first-child {
          margin-right: 4px;
        }

        &.active {
          background: rgba(21, 99, 255, 0.1);
          color: #1563ff;
          font-weight: 500;
        }
      }
    }

    //滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #cbced1;
    }

    &::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: #ffffff;
    }
  }

  .drag-wrap {
    flex: 1;
    overflow: hidden;

    height: 100%;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    position: relative;
    padding-top: 150px;
    box-sizing: border-box;

    .add-btn {
      position: absolute;
      left: 16px;
      top: 24px;
    }

    .deletes-region {
      position: absolute;
      right: 16px;
      top: 24px;
      width: 164px;
      height: 64px;
      background: rgba(255, 82, 86, 0.12);
      border-radius: 6px;

      border: 1px dashed rgba(255, 82, 86, 0.8);
      // border-spacing: 20px; /* 边框间距 */
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 11px;
      box-sizing: border-box;

      .icon {
        width: 14px;
        height: 14px;
        background: url("~@/assets/images/del.png") no-repeat center;
        background-size: cover;
      }

      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ff5256;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        margin-top: 11px;
      }
    }

    .thumbnail {
      position: relative;
      height: 28px;
      z-index: 99;

      .number {
        position: absolute;
        left: 20px;
        width: 38px;
        height: 28px;
        padding-left: 13px;
        box-sizing: border-box;
        background: url("~@/assets/images/number.png") no-repeat center;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        line-height: 24px;
        z-index: 9;
      }

      .indicators {
        position: absolute;
        top: 6px;
        left: 38px;
        height: 16px;
        background: #e6eefa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding-right: 12px;

        .text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #5c646e;
          line-height: 10px;
          text-align: right;
          padding-left: 26px;
          line-height: 16px;
          height: 16px;

          font-style: normal;
        }

        .step-item {
          width: 32px;
          height: 4px;
          background: #ceddf5;
          border-radius: 2px;

          &.active {
            background: #1563ff;
          }
        }

        .symbol {
          margin: 0 6px;
        }

        .steps {
          display: flex;

          .dot {
            width: 4px;
            height: 4px;
            background: #ff800e;
            border-radius: 1px;
            opacity: 0.5;
            margin: 0 6px;

            &.isArithmetic {
              background: #00cc88;
            }
          }
        }
      }
    }

    .drag-region {
      width: 100%;
      position: relative;
      // padding-top: 56px;
      padding-left: 20px;
      height: 72px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      overflow-x: auto;
      margin-bottom: 38px;

      &.active {
        background-color: #e6eefa;
      }

      //滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #cbced1;
      }

      &::-webkit-scrollbar-track {
        border-radius: 2px;
        background-color: #ffffff;
      }

      .result-wrap {
        flex: 0 0 144px;
        position: relative;
        display: flex;

        .error-message {
          position: absolute;
          bottom: 4px;
          left: 50%;
          transform: translate(-50%);
          white-space: nowrap;
          color: #f56c6c;
          font-size: 12px;
        }
      }

      .result {
        width: 100%;
        height: 56px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #c8cbd1;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 10px;
        box-sizing: border-box;

        &.active {
          border: 1px solid #1563ff;
        }

        &.is-invalid {
          border: 1px solid #f56c6c;
        }

        &::placeholder {
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #979da6;
        }
      }

      .equal-sign {
        min-width: 16px;
        height: 16px;
        margin: 0 12px;
        background: url("~@/assets/images/equal-sign.png") no-repeat;
      }

      .steps {
        display: flex;
        align-items: center;
      }

      .indicator-region {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 160px;
        height: 56px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px dashed #d7d9db;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #979da6;
        text-align: center;
        line-height: 56px;

        &.dragging {
          background: #e5f9f3;
          border: 1px dashed #00cc88;
          color: #060607;

          .icon {
            width: 21px;
            height: 20px;
            background: url("~@/assets/images/drag-enter.png") no-repeat center;
            background-size: cover;
            margin-right: 4px;
          }
        }
      }
    }

    .tools {
      position: absolute;
      width: 100%;
      bottom: 0;
      padding: 0 16px 24px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .special-symbol {
        display: flex;
        flex-direction: column;

        .text {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-bottom: 12px;
        }

        .bracket {
          display: flex;
          align-items: center;
          width: 160px;
          height: 40px;
          background: #e6eefa;
          border-radius: 4px;

          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #2f3338;
          text-align: center;
          padding-left: 10px;
          box-sizing: border-box;

          &.dragstart {
            .left {
              box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
                0px 6px 6px -4px rgba(0, 42, 128, 0.12);
              border: 1px solid #1563ff;
              color: #1563ff;
            }

            .right {
              box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
                0px 6px 6px -4px rgba(0, 42, 128, 0.12);
              border: 1px solid #1563ff;
              color: #1563ff;
            }
          }

          &.draging {
            .left {
              box-shadow: none;
              border: 1px solid #c8cbd1;
              color: #2f3338;
            }

            .right {
              box-shadow: none;
              border: 1px solid #c8cbd1;
              color: #2f3338;
            }
          }

          .left {
            width: 20px;
            height: 24px;
            line-height: 20px;
            background: #ffffff;
            border-radius: 3px;
            border: 1px solid #c8cbd1;
            margin-right: 12px;
            cursor: move;
          }

          .right {
            width: 20px;
            height: 24px;
            line-height: 20px;
            margin-right: 12px;

            background: #ffffff;
            border-radius: 3px;
            cursor: move;

            border: 1px solid #c8cbd1;
          }

          .ipt {
            width: 75px;
            height: 24px;
            line-height: 24px;
            background: #ffffff;
            cursor: move;
            border-radius: 4px;
            border: 1px solid #c8cbd1;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #979da6;
            text-align: left;
            padding-left: 15px;
            box-sizing: border-box;
          }
        }
      }

      .btns {
        margin-top: 32px;
        margin-left: auto;
      }
    }
  }

  .base-property {
    flex: 0 0 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 0px 5px 5px 0px;
    border: 1px solid #e4e7ed;

    .base-title {
      display: flex;
      align-items: center;
      height: 40px;
      border-bottom: 1px solid #e4e7ed;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 12px;
      box-sizing: border-box;
    }

    .form {
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      margin-bottom: 10px;
      padding: 20px 12px;
      box-sizing: border-box;

      //滚动条样式
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }

      ::v-deep .el-form-item--small .el-form-item__label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        line-height: 14px;
        padding: 0 0 8px 0;
      }

      ::v-deep .el-checkbox:last-of-type {
        margin-right: 0px;
      }

      ::v-deep .el-checkbox__label {
        padding-left: 6px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
      }
    }

    // ::v-deep .el-input--small .el-input__inner {
    //   width: 160px;

    //   font-size: 14px;
    // }

    // ::v-deep .yz-input.el-input--small .el-input__inner {
    //   width: 100%;

    //   font-size: 14px;
    // }
  }
}

::v-deep .jd.el-input--small .el-input__inner {
  width: 90px !important;

  font-size: 14px;
}

::v-deep .el-drawer__body {
  position: relative;
}

.footer-btn {
  position: absolute;
  right: 24px;
  bottom: 0;
  border-top: 1px solid #f0f0f0;
  height: 52px;
  box-sizing: border-box;
  width: 480px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

::v-deep .el-row {
  margin-bottom: 0px;
}

::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 0px 24px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

/* 为了确保自定义样式能覆盖默认样式，你可能需要使用更具体的选择器 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置滚动条高度 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 3px;
  /* 滚动条圆角 */
  background: rgba(0, 0, 0, 0.2);
  /* 滚动条颜色 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  /* 滚动条轨道阴影 */
  background-color: #f0f0f0;
  /* 滚动条轨道颜色 */
  border-radius: 3px;
  /* 滚动条圆角 */
}
</style>

<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  &::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 9px;
    width: 12px;
    height: 10px;
    background: url("~@/assets/images/tree-icon.png") no-repeat center;
  }

  .el-tree-node__label {
    background: transparent !important;
  }
}

.item-style {
  width: 140px !important;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}

#project_frame {
  .el-tree-node:not(.is-expanded)
    > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      &::after {
        content: "";
        position: absolute;
        right: 10px;
        top: 9px;
        width: 12px;
        height: 10px;
        background: url("~@/assets/images/tree-icon.png") no-repeat center;
      }

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }

  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}

::v-deep .el-select .el-select__tags .el-tag {
  position: relative;
  max-width: 120px;
  overflow: hidden;
  padding-right: 18px !important;
  /* 留出关闭按钮空间 */
}

::v-deep .el-select .el-select__tags .el-tag .el-select__tags-text {
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-select .el-select__tags .el-tag::after {
  content: "";
  position: absolute;
  right: 18px;
  top: 0;
  width: 24px;
  height: 100%;
  background: linear-gradient(to right, transparent, #fff 80%);
  pointer-events: none;
  z-index: 1;
}

::v-deep .custom-tag {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
