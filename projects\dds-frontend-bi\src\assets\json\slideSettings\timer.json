{"name": "timer", "title": "时间器", "params": [{"name": "size", "title": "矩形尺寸", "items": [{"name": "width", "title": "宽度（像素）", "component": "inputnumber", "default": 600}, {"name": "height", "title": "高度（像素）", "component": "inputnumber", "default": 120}]}, {"name": "position", "title": "位置", "items": [{"name": "positionX", "title": "x轴位置（像素）", "component": "inputnumber", "labelCol": 15, "wrapperCol": 8}, {"name": "positionY", "title": "y轴位置（像素）", "component": "inputnumber", "labelCol": 15, "wrapperCol": 8}]}, {"name": "format", "title": "时间设置", "items": [{"name": "timeFormat", "tip": "时间格式", "title": "时间格式", "component": "input", "default": "YYYY-MM-DD HH:mm:ss", "labelCol": 8, "wrapperCol": 24}, {"name": "timeDuration", "tip": "间隔时间（ms）", "title": "间隔时间（ms）", "component": "inputnumber", "default": 1000, "min": 1, "labelCol": 14, "wrapperCol": 10}]}, {"name": "font", "title": "文字", "items": [{"name": "fontFamily", "title": "字体", "component": "select", "values": [{"name": "默认", "value": ""}, {"name": "微软雅黑", "value": "Microsoft Yahei"}, {"name": "宋体", "value": "Sim<PERSON>un"}, {"name": "黑体", "value": "Heiti"}, {"name": "华文细黑", "value": "STXihei"}, {"name": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Times New Roman", "value": "Times New Roman"}, {"name": "Times", "value": "Times"}, {"name": "MS Sans Serif", "value": "MS Sans Serif"}], "default": "", "wrapperCol": 24}, {"name": "fontColor", "title": "字体颜色", "component": "colorPicker", "default": [0, 0, 0], "labelCol": 10, "wrapperCol": 14}, {"name": "fontSize", "title": "字体大小", "component": "inputnumber", "default": 40, "placeholder": "像素", "labelCol": 10, "wrapperCol": 14}, {"name": "textAlign", "title": "对齐方式", "component": "select", "values": [{"name": "左对齐", "value": "left"}, {"name": "右对齐", "value": "right"}, {"name": "居中", "value": "center"}, {"name": "两端对齐", "value": "justify"}], "default": "justify", "labelCol": 10, "wrapperCol": 14}, {"name": "fontWeight", "title": "字体粗细", "component": "select", "values": [{"name": "normal", "value": "normal"}, {"name": "bold", "value": "bold"}, {"name": "bolder", "value": "bolder"}, {"name": "lighter", "value": "lighter"}, {"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}, {"name": "400", "value": "400"}, {"name": "500", "value": "500"}, {"name": "600", "value": "600"}, {"name": "700", "value": "700"}, {"name": "800", "value": "800"}, {"name": "900", "value": "900"}], "default": "normal", "labelCol": 10, "wrapperCol": 14}, {"name": "textStyle", "title": "样式", "component": "checkboxGroup", "values": [{"label": "斜体", "value": "italic"}, {"label": "下划线", "value": "underline"}], "default": [], "labelCol": 10, "wrapperCol": 14}, {"name": "lineHeight", "title": "行高", "component": "inputnumber", "default": 40, "placeholder": "像素", "labelCol": 10, "wrapperCol": 14}, {"name": "textIndent", "title": "首行缩进", "component": "inputnumber", "default": 0, "placeholder": "像素", "labelCol": 10, "wrapperCol": 14}]}, {"name": "padding", "title": "内边距（像素）", "items": [{"name": "paddingTop", "tip": "px", "title": "上", "component": "inputnumber", "default": 0, "labelCol": 6, "wrapperCol": 18, "span": 12}, {"name": "paddingBottom", "tip": "px", "title": "下", "component": "inputnumber", "default": 0, "labelCol": 6, "wrapperCol": 18, "span": 12}, {"name": "paddingLeft", "tip": "px", "title": "左", "component": "inputnumber", "default": 0, "labelCol": 6, "wrapperCol": 18, "span": 12}, {"name": "paddingRight", "tip": "px", "title": "右", "component": "inputnumber", "default": 0, "labelCol": 6, "wrapperCol": 18, "span": 12}]}, {"name": "background", "title": "背景", "items": [{"name": "backgroundColor", "title": "背景颜色", "component": "colorPicker", "default": "rgba(0,0,0,1)", "labelCol": 10, "wrapperCol": 14}]}, {"name": "border", "title": "边框", "items": [{"name": "borderColor", "title": "边框颜色", "component": "colorPicker", "default": "rgba(252,252,252,1)", "labelCol": 10, "wrapperCol": 14}, {"name": "borderWidth", "title": "边框粗细", "component": "inputnumber", "default": 0, "placeholder": "像素", "labelCol": 10, "wrapperCol": 14}, {"name": "borderStyle", "title": "边框样式", "component": "select", "values": [{"name": "实线", "value": "solid"}, {"name": "虚线", "value": "dashed"}, {"name": "点线", "value": "dotted"}, {"name": "双框", "value": "double"}], "default": "solid", "labelCol": 10, "wrapperCol": 14}, {"name": "borderRadius", "tip": "px", "title": "圆角半径", "component": "inputnumber", "default": 0, "placeholder": "像素", "labelCol": 10, "wrapperCol": 14}]}, {"name": "timeFormat", "title": "时间格式", "items": [{"name": "timeFormat", "title": "时间格式", "default": "YYYY-MM-DD HH:mm:ss"}]}]}