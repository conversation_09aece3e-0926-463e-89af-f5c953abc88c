export default {
  data() {
    return {}
  },
  props: {
    type: String,
    data: Object,
    controls: Array,
    operators: Array,
  },
  methods: {
    changeType(val) {
      // 清空radio,select默认值加载的数据
      this.columnsValues = []
      if (val === "numberRange") this.$set(this.data, "defaultValue", [ 0, 1 ])
      // 对应关系
      if ("select,radio,date,dateRange,inputText,treeSelect".includes(val)) {
        this.data.operator = "="
        //   this.$set(this.currentControl, "operator", "=");
        if ("date,dateRange".includes(this.data.type))
          this.$emit("update:operatorType", "all")
        else this.$emit("update:operatorType", "portion")
      }
      // 默认值
      this.data.defaultValueType = "fixed"
      if (val === "dateRange") this.$set(this.data, "defaultValue", [])
      else if (val === "slider" || val === "numberRange")
        this.$set(this.data, "defaultValue", [ 0, 1 ])
      else this.$set(this.data, "defaultValue", "")
      this.$emit("changeType", val)
      // this.currentControl.operator = ''
    },
    changeMultiple(val) {
      if (val) {
        this.data.operator = "in"
        this.$emit("update:operatorType", "range")
      } else {
        this.data.operator = "="
        this.$emit("update:operatorType", "portion")
        if (this.data.type === "date") {
          this.$emit("update:operatorType", "all")
        }
      }
    },
  },
  render() {
    let elCongrolType = (
      <el-form-item label="类型：">
        <el-select
          value={this.data.type}
          onInput={(val) => {
            this.data.type = val
            this.changeType(val)
          }}
        >
          <el-option label="文本输入框" value="inputText" />
          <el-option label="下拉选择" value="select" />
          <el-option label="单选按钮" value="radio" />
          <el-option label="日期选择" value="date" />
          <el-option label="日期范围选择" value="dateRange" />
          <el-option label="数字范围输入框" value="numberRange" />
          <el-option label="数字滑块" value="slider" />
          <el-option label="下拉树" value="treeSelect" />
        </el-select>
      </el-form-item>
    )
    // 日期格式
    let elFormdateType = (
      <el-form-item label="日期格式：" prop="data-format">
        <el-select
          value={this.data.dateFormat}
          onInput={(val) => (this.data.dateFormat = val)}
        >
          <el-option value="yyyy-MM-dd" label="日期" />
          <el-option value="yyyy-MM-dd HH:mm:ss" label="日期时间"></el-option>
          <el-option value="yyyy-MM" label="月">
            月
          </el-option>
          <el-option value="yyyy-ww" label="周">
            周
          </el-option>
          <el-option value="yyyy" label="年">
            年
          </el-option>
        </el-select>
      </el-form-item>
    )
    let elFormMax = (
      <el-form-item label="最大值：" prop="max">
        <el-input
          style="width: 200px"
          value={this.data.max}
          onInput={(val) => (this.data.max = val)}
        />
      </el-form-item>
    )
    let elFormMin = (
      <el-form-item label="最小值：" prop="min">
        <el-input
          style="width: 200px"
          value={this.data.min}
          onInput={(val) => (this.data.min = val)}
        />
      </el-form-item>
    )
    let elFormStep = (
      <el-form-item label="步长：" prop="step">
        <el-input
          min={1}
          value={this.data.step}
          style="width: 200px"
          onInput={(val) => (this.data.step = val)}
        />
      </el-form-item>
    )
    let elFormTag = (
      <el-form-item label="显示标签：" prop="step">
        <el-switch
          value={this.data.label}
          onInput={(val) => (this.data.label = val)}
        ></el-switch>
        {/* <el-checkbox value={this.data.label} onInput={(val) => (this.data.label = val)} /> */}
      </el-form-item>
    )
    let elFormBtnStyle = (
      <el-form-item label="按钮样式：" prop="radio-type">
        <el-radio-group
          value={this.data.radioType}
          onInput={(val) => (this.data.radioType = val)}
        >
          <el-radio-button label="normal">常规</el-radio-button>
          <el-radio-button label="button">按钮</el-radio-button>
        </el-radio-group>
      </el-form-item>
    )
    // 多选el-form-item
    let elFormMultiple = (
      <el-form-item label="多选：" prop="multiple">
        <el-switch
          value={this.data.multiple}
          onInput={(val) => {
            this.data.multiple = val
            // this.data.operator = ''
            if (val) this.data.defaultValue = []
            else this.data.defaultValue = ""
            this.changeMultiple(val)
          }}
        />
      </el-form-item>
    )
    // 缓存el-form-item
    let elFormCache = (
      <el-form-item label="缓存：" prop="cache">
        <el-radio-group
          value={this.data.cache}
          onInput={(val) => (this.data.cache = val)}
        >
          <el-radio-button label={true}>开启</el-radio-button>
          <el-radio-button label={false}>关闭</el-radio-button>
        </el-radio-group>
      </el-form-item>
    )
    // 有效期（秒）：el-form-item
    let elFormExpire = (
      <el-form-item label="有效期（秒）：" prop="expired">
        <el-input-number
          value={this.data.expired}
          onInput={(val) => (this.data.expired = val)}
          min={0}
          step={10}
          controls-position="right"
          step-strictly
        />
      </el-form-item>
    )
    // 通用部分el-form-item
    let elCommon = (
      <span>
        <el-form-item label="宽度：" prop="width">
          <el-select
            value={this.data.width}
            onChange={(val) => (this.data.width = val)}
          >
            <el-option label="自动适应" value={0}></el-option>
            <el-option label="100%" value={24} />
            <el-option label="50%" value={12} />
            <el-option label="33.33%" value={8} />
            <el-option label="25%" value={6} />
            <el-option label="16.67%" value={4} />
            <el-option label="12.5%" value={3} />
            <el-option label="8.33%" value={2} />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可见：" prop="visibility">
          <el-radio-group
            value={this.data.visibility}
            onInput={(val) => (this.data.visibility = val)}
          >
            <el-radio-button label="visible">显示</el-radio-button>
            <el-radio-button label="hidden">隐藏</el-radio-button>
            <el-radio-button label="conditional">条件</el-radio-button>
          </el-radio-group>
        </el-form-item>
        {this.data.visibility === "conditional" ? (
          <el-form-item label="显示条件：" prop="conditional">
            <div class="control-item_conditions">
              <el-select
                value={this.data.conditions[0].control}
                onInput={(val) => (this.data.conditions[0].control = val)}
              >
                {this.controls.map((con) => (
                  <el-option
                    key={con.key}
                    value={con.key}
                    label={con.name}
                    v-if={con.key !== this.data.key}
                  />
                ))}
              </el-select>
              <el-select
                style="width: 100px;margin:0 5px;"
                value={this.data.conditions[0].operator}
                onInput={(val) => (this.data.conditions[0].operator = val)}
              >
                <el-option label="等于" value="=" />
                <el-option label="不等于" value="!=" />
              </el-select>
              <el-input
                style="width: 200px"
                value={this.data.conditions[0].value}
                onInput={(val) => (this.data.conditions[0].value = val)}
              />
            </div>
          </el-form-item>
        ) : (
          ""
        )}
      </span>
    )
    // 组装下拉框配置
    let select = (
      <span>
        {elCongrolType}
        {elFormMultiple}
        {elFormCache}
        {elFormExpire}
        {elCommon}
      </span>
    )
    let inputText = (
      <span>
        {elCongrolType}
        {elCommon}
      </span>
    )
    let radio = (
      <span>
        {elCongrolType}
        {elFormBtnStyle}
        {elFormCache}
        {elFormExpire}
        {elCommon}
      </span>
    )
    let date = (
      <span>
        {elCongrolType}
        {elFormdateType}
        {elFormMultiple}
        {elCommon}
      </span>
    )
    let dateRange = (
      <span>
        {elCongrolType}
        {elFormdateType}
        {elCommon}
      </span>
    )
    let numberRange = (
      <span>
        {elCongrolType}
        {elFormdateType}
        {elCommon}
      </span>
    )
    let slider = (
      <span>
        {elCongrolType}
        {elFormdateType}
        {elFormMax}
        {elFormMin}
        {elFormStep}
        {elFormTag}
        {elCommon}
      </span>
    )
    let treeSelect = (
      <span>
        {elCongrolType}
        {elFormdateType}
        {elFormMultiple}
        {elFormCache}
        {elFormExpire}
        {elCommon}
      </span>
    )
    const res = {
      select,
      inputText,
      radio,
      date,
      dateRange,
      numberRange,
      slider,
      treeSelect,
    }
    return res[this.type]
  },
}
