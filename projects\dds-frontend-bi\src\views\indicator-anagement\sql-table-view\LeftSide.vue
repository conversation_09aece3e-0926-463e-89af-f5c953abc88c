<template>
  <div class="left">
    <el-form label-width="75px">
      <el-form-item prop="sourceId" label="数据源:">
        <el-select
          @change="sourceDb"
          v-model="form.sourceId"
          placeholder="请选择数据源"
          style="width: 100%"
        >
          <el-option
            v-for="item in sources"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库表:">
        <DraggableSource v-bind="$attrs" v-on="$listeners" :tables="tables" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import Request from "@/service"
import DraggableSource from "./DraggableSource.vue"
export default {
  components: {
    DraggableSource
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      viewId: "",
      options: [],
      sources: [],
      tables: [],
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        code: [{ required: true, message: "请输入编码", trigger: "blur" }]
      }
    }
  },
  computed: {},
  created() {
    this.initSource()
  },
  mounted() {},
  methods: {
    viewSelect() {
      this.$emit("viewSelect", this.viewId)
    },
    // 数据源库
    async sourceDb(sourceId) {
      this.dbs = []
      if (sourceId) {
        const { data: Databases } = await Request.view.getDatabases({
          id: sourceId
        })
        console.log(Databases, "Databases")

        const { data } = await Request.view.getTables({
          id: sourceId,
          dbName: Databases[0]
        })
        this.$emit("changeSourceDb", sourceId, Databases[0])
        this.tables = data.tables
      }
    },
    // 初始化数据源
    async initSource() {
      const { data } = await Request.view.getSources()
      this.sources = data
    },
    validate(callback) {
      // 这个form是子组件内部el-form 的ref="form"
      this.$refs.ruleFormRef.validate(valid => {
        callback(valid)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.leftSide {
  width: 300px;
  height: calc(100vh - 110px);

  padding: 16px;
  box-sizing: border-box;
  background-color: #fff;
  border: 0.1px;
  border-color: #d2d2d3;
  border-style: solid;
}
::v-deep .el-cascader--small {
  width: 100%;
}
</style>
