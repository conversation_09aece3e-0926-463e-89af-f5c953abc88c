// 手机号脱敏
const mobileFilter = mobile => {
  return (mobile + "").replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3")
}
// 姓名脱敏显示
const capitalize = val => {
  if (!val || val === "") return ""
  let name = ""
  if (val.length === 2) {
    name = val.substring(0, 1) + "*" // 截取name 字符串截取第一个字符，
  } else if (val.length === 3) {
    name = val.substring(0, 1) + "*" + val.substring(2, 3) // 截取第一个和第三个字符
  } else if (val.length === 4) {
    name = val.substring(0, 2) + "*" + "*" // 4个字隐藏后面两个
  } else if (val.length > 4) {
    name = val.substring(0, 1) // 5个字只显示第一个字
    for (let i = 0; i < val.length - 1; i++) {
      name = name + "*"
    }
  }
  return name
}
// 千分号
const num = value => {
  if (Number.isInteger(value)) {
    return (value + "").replace(/(?!^)(?=(\d{3})+$)/g, ",")
  }
  return (value + "").replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
}
// 千分分割兼容小数点
const toThousands = num => {
  if (!num) return 0
  let numStr = num.toString()
  let index = numStr.indexOf(".")
  let decimal = ""
  if (index > -1) {
    decimal = numStr.slice(index)
    numStr = numStr.slice(0, index)
  }
  return (
    (numStr || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,") + decimal
  )
}
const formatValue = value => {
  if (value === "" || value === undefined || value === null) {
    return "-"
  } else if (isStringNumberOrNumber(value)) {
    return toThousands(value) // 格式化数字为千分位表示
  } else {
    return value
  }
}

function isStringNumberOrNumber(value) {
  return /^-?\d+(\.\d+)?$/.test(value)
}
export default {
  mobileFilter,
  capitalize,
  num,
  formatValue,
  toThousands
}
