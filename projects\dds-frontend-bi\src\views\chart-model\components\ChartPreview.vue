<template>
  <div class="chart-preview">
    <div class="chart-selector">
      <h3>图表类型选择</h3>
      <div class="chart-types">
        <div
          v-for="(config, type) in chartTypeConfig"
          :key="type"
          class="chart-type-item"
          :class="{ active: selectedType === type }"
          @click="selectChartType(type)"
        >
          <div class="chart-icon">{{ config.icon }}</div>
          <div class="chart-name">{{ config.name }}</div>
          <div class="chart-category">{{ config.category }}</div>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <h3>{{ chartTypeConfig[selectedType]?.name }} 预览</h3>
      <div class="chart-wrapper">
        <component
          :is="currentComponent"
          v-bind="currentProps"
          v-if="currentComponent"
        />
      </div>
    </div>

    <div class="chart-config">
      <h3>配置信息</h3>
      <pre>{{ JSON.stringify(currentData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { 
  chartComponentMap, 
  getStaticPreviewData, 
  getChartProps, 
  chartTypeConfig 
} from './chartComponents.js'

export default {
  name: 'ChartPreview',
  setup() {
    const selectedType = ref('bar')
    const currentComponent = ref(null)

    // 计算当前图表数据
    const currentData = computed(() => {
      return getStaticPreviewData(selectedType.value)
    })

    // 计算当前图表属性
    const currentProps = computed(() => {
      return getChartProps(selectedType.value, currentData.value)
    })

    // 选择图表类型
    const selectChartType = async (type) => {
      selectedType.value = type
      await loadComponent(type)
    }

    // 加载组件
    const loadComponent = async (type) => {
      try {
        const componentLoader = chartComponentMap[type]
        if (componentLoader) {
          const component = await componentLoader()
          currentComponent.value = component.default || component
        }
      } catch (error) {
        console.error(`加载图表组件失败: ${type}`, error)
      }
    }

    // 初始化
    onMounted(() => {
      loadComponent(selectedType.value)
    })

    return {
      selectedType,
      currentComponent,
      currentData,
      currentProps,
      chartTypeConfig,
      selectChartType
    }
  }
}
</script>

<style scoped>
.chart-preview {
  padding: 20px;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 20px;
  height: 100vh;
}

.chart-selector {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  overflow-y: auto;
}

.chart-types {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chart-type-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.chart-type-item:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.chart-type-item.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.chart-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.chart-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.chart-category {
  font-size: 12px;
  color: #666;
}

.chart-container {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.chart-wrapper {
  flex: 1;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin-top: 16px;
}

.chart-config {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  overflow-y: auto;
}

.chart-config pre {
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
}
</style>
