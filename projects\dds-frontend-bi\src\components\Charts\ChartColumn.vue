<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
import { formatValue, getTextWidth } from "@/utils/index.js"

import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String || Array,
      default: []
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 系列字段
    seriesField: {
      type: String || null,
      default: null
    },
    // 维度名称
    seriesName: {
      type: String || Array || null,
      default: null
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#2361DB",
        "#0EACCC",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 单位
    barWidth: {
      type: Number,
      default: 24
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // X轴展示数量  auto会自动折叠隐藏
    interval: {
      type: [String, Number],
      default: "auto"
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: "auto"
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: false
    },
    // 是否堆叠
    isStack: {
      type: Boolean,
      default: false
    },
    // 标线
    isMarkLine: {
      type: Boolean,
      default: false
    },
    // tip标题
    tooltipTitle: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      chart: null,
      seriesLength: 0
    }
  },
  computed: {
    dimensions() {
      if (Array.isArray(this.yField)) {
        return [this.xField, ...this.yField]
      } else {
        return [this.xField, this.yField]
      }
    },
    // 判断是否为多系列
    isMultipleSeries() {
      return Array.isArray(this.yField) || this.seriesField !== null
    },
    // 是否数据大于显示长度才显示滚动
    isShowScroll() {
      if (this.seriesField) {
        return (
          this.showDataZoom &&
          this.chartData.length / this.seriesLength > this.showNum
        )
      } else {
        return this.showDataZoom && this.chartData.length > this.showNum
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    },
    yField: {
      deep: true,
      handler(val) {
        console.log(val,'///////////////')
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      } else {
        this.chart.off('click') // 解绑之前的 click 事件，防止重复绑定
      }
      this.chart.on('click', (params) => {
        console.log(params,'params')
        if (params.componentType==="series"){
          this.$emit('clickSeries', params)
        }
      })
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      console.log(
        this.chartData.length,
        this.showNum,
        "----------------------------------------"
      )

      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      let dataset = null
      if (this.seriesField) {
        // 去重获取系列
        const uniqueSeriesNames =
          this.chartData.length &&
          Array.from(
            new Set(this.chartData.map(item => item[this.seriesField]))
          )
        this.seriesLength = uniqueSeriesNames.length
        const transforms = []
        uniqueSeriesNames &&
          uniqueSeriesNames.forEach((seriesName, index) => {
            transforms.push({
              transform: {
                type: "filter",
                config: { dimension: this.seriesField, value: seriesName }
              }
            })
            series.push(
              this.createSeriesObject({
                name: seriesName,
                index: index + 1,
                color: this.color[index]
              })
            )
          })

        dataset = [
          {
            source: this.chartData
          },
          ...(transforms || [])
        ]
      } else {
        if (Array.isArray(this.yField)) {
          this.yField.forEach((seriesName, index) => {
            series.push(
              this.createSeriesObject({
                name: (this.seriesName && this.seriesName[index]) ?? seriesName,
                color: this.color[index],
                yField: seriesName
              })
            )
          })
        } else {
          series.push(
            this.createSeriesObject({
              name: this.seriesName ?? this.yField,
              color: this.color[0]
            })
          )
        }

        dataset = {
          dimensions: this.dimensions,
          source: this.chartData
        }
      }

      this.chart.setOption({
        dataset,
        tooltip: {
          // trigger: 'axis',
          confine: true,
          trigger: "axis",

          axisPointer: {
            type: "shadow",
            label: { show: false, backgroundColor: "transparent" },
            shadowStyle: {
              color: "rgba(35,97,219,0.05)"
            }
          },
          formatter: params => {
            if (!this.isMultipleSeries) {
              return `<div>
              <p class="tooltip-title">${params[0].seriesName}</p>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
                   params[0].color
                 }" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${formatValue(
                  params[0].value[
                    params[0].dimensionNames[params[0].encode.y[0]]
                  ]
                )}${this.unit}
                </span>
              </div>
            </div>`
            }
            return `<div>
            <p class="tooltip-title">${params[0].name}${this.tooltipTitle}</p>
            ${this.tooltipItemsHtmlString(params)}
          </div>`
          },
          className: "echarts-tooltip-diy"
        },
        dataZoom: this.isShowScroll
          ? [
              // 给x轴设置滚动条
              {
                type: "slider",
                show: true,
                xAxisIndex: 0,
                zoomLock: true,
                height: 6,
                bottom: 0,

                showDetail: false,
                start: 0,
                endValue: this.showNum - 1,

                showDataShadow: false,
                fillerColor: "#DBDBDB", // 滑块的颜色

                backgroundColor: "transparent", // 滑块轨道的颜色
                borderColor: "transparent", // 滑块轨道边框的颜色
                moveHandleIcon: "none",

                zoomOnMouseWheel: false,
                brushSelect: false,

                handleIcon:
                  "M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z",
                handleSize: "100%",
                handleStyle: {
                  color: "#DBDBDB",
                  borderColor: "transparent"
                }
              },
              // 下面这个属性是里面拖到
              {
                type: "inside",
                show: true,
                xAxisIndex: 0,
                zoomOnMouseWheel: false,
                moveOnMouseMove: true,
                moveOnMouseWheel: true
              }
            ]
          : [
              {
                show: false
              }
            ],
        grid: {
          top: 40,
          bottom: this.showDataZoom ? 12 : 0,
          left: 0,
          right: 0,
          containLabel: true
        },
        legend: {
          show: true,
          right: 0,
          top: 0,

          icon: "rect",
          itemWidth: 6,
          itemHeight: 6,
          itemGap: 20,
          orient: "horizontal",
          // color: this.color,
          textStyle: {
            color: "#646566",
            fontSize: 12
          },
          formatter: name => {
            console.log(getTextWidth(name))

            return name.length > 17 ? name.substr(0, 17) + "..." : name
          }
        },
        graphic: [
          {
            type: "text",
            top: 5,
            left: 0,
            style: {
              text: this.yAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          }
        ],
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              color: "#EBEDF0",
              type: "dashed"
            }
          },
          axisLabel: {
            fontSize: 12,
            color: "#646566",
            fontWeight: "400"
            // formatter: metricAxisLabelFormatter,
          }
        },

        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#EBEDF0"
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            lineHeight: 16,
            color: "#323233",
            fontWeight: "400",
            interval: this.interval,
            rotate: 0, // 倾斜角度
            formatter: this.isFormatterXAxis
              ? value => {
                  let startName = value.substring(0, 5)
                  let endName = value.substring(5)
                  if (endName.length > 5) {
                    return `${startName}\n${value.substring(5, 9)}...`
                  }
                  return `${startName}\n${endName}`
                }
              : value => value
          }
        },
        series
      },true)
    },
    // 创建系列对象
    createSeriesObject({ name, color, index = 0, yField = this.yField }) {
      return {
        type: "bar",
        barWidth: this.barWidth,
        name: name,
        stack: this.isStack ? "total" : null,
        itemStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },
        datasetIndex: index,
        markLine: this.isMarkLine
          ? {
              symbol: "none", // 去掉箭头
              data: [
                {
                  name: "均值：",
                  yAxis: this.getAvg(this.chartData)
                }
              ],
              label: {
                show: true,
                position: "insideEndTop",
                formatter: "{b}{c}" + this.unit,
                color: "#2361DB",
                fontSize: 12
              },
              lineStyle: {
                color: "rgba(35,97,219,0.6)",
                width: 1
              }
            }
          : null
      }
    },
    // 获取平均值
    getAvg(data) {
      // 检查数据是否为空
      if (data.length === 0) {
        return 0
      }
      // 提取每个对象中的数字并计算总和
      const sum = data.reduce((acc, obj) => acc + obj[this.yField], 0)

      // 计算平均值
      const average = sum / data.length

      // 保留两位小数
      const averageWithTwoDecimals = average.toFixed(2)
      return averageWithTwoDecimals
    },
    tooltipItemsHtmlString(items) {
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${formatValue(el.value[el.dimensionNames[el.encode.y[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
