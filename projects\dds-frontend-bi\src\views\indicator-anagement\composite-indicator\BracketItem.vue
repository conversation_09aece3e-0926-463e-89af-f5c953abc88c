<template>
  <div
    class="bracket"
    :class="{
      left: type === '(' ? true : false,
      right: type === ')' ? true : false
    }"
    @dragstart="handleDragStart"
    @drag="handleDraging"
  >
    {{ type }}
    <!-- <div
      v-if="isDragging"
      class="indicator-item-before"
      @dragover.prevent
      @drop="handleDrop($event, index)"
    ></div>
    <div
      v-if="isDragging"
      class="indicator-item-after"
      @dragover.prevent
      @drop="handleDrop($event, index + 1)"
    ></div> -->
  </div>
</template>

<script>

export default {
  components: {},
  props: {
    type: {
      type: String,
      default: "("
    },
    isDragging: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleDrop(e, index) {
      this.$emit("handleDrop", e, index)
    },
    handleDragStart(e) {
      console.log("拖拽开始")
      this.$emit("handleDragStart", e)
    },
    handleDraging(e) {
      console.log("拖拽中")
      this.$emit("handleDraging", e)
    }
  }
}
</script>

<style scoped lang="scss">
.bracket {
  position: relative;
  width: 20px;
  height: 56px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #c8cbd1;

  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 20px;
  color: #2f3338;
  line-height: 56px;
  text-align: center;

  &.left {
    margin-right: 4px;
  }
  &.right {
    margin-left: 4px;
  }
}
.indicator-item-before {
  position: absolute;
  left: 0px;
  top: 0;
  width: 16px;
  height: 24px;
  background: #ffffff;
  border-radius: 2px;
  background-color: #eafdcd;
  border: 1px dashed #cbced1;
}
.indicator-item-after {
  position: absolute;
  right: 0;
  top: 0;
  width: 16px;
  height: 24px;
  background-color: #eafdcd;
  border: 1px dashed #cbced1;
}
</style>
