<template>
  <DT-View :inner-style="{ padding: '20px' }">
    <tempalte v-if="!currentType">
      <SearchForm
        @search="initData"
        :columns="columns"
        :search-param.sync="form"
        :is-card="false"
        style="margin-bottom: 2px"
      />
      <div class="content">
        <div class="left-tree">
          <div class="left-head">
            <div class="left-title">指标域</div>
            <div class="left-btn" @click="handleDataDomainVisible">
              新建指标域
            </div>
          </div>
          <el-tree
            class="model-tree"
            style="padding: 16px 6px"
            :data="viewGroup[0].children"
            v-loading="treeLoading"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :expand-on-click-node="false"
            ref="tree"
          >
            <!-- draggable
            :allow-drop="allowDrop"
            @node-drag-start="handleDragStart"
            @node-drag-end="handleDragEnd"
            @node-drop="handleDrop" -->
            <span
              class="custom-tree-node"
              slot-scope="{ node, data }"
              @click="treeNodeClick(data)"
            >
              <div class="node-item">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="node.label"
                  placement="top"
                >
                  <div class="node-label">
                    {{ node.label }}
                  </div>
                </el-tooltip>
                <div class="node-num">({{ data.zbsl }})</div>
              </div>
              <span class="custom-tree-btns">
                <el-dropdown @command="handleCommand($event, data)">
                  <span class="el-dropdown-link">
                    <i
                      class="el-icon-more"
                      style="transform: rotate(90deg)"
                    ></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="add">新增</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="del">删除</el-dropdown-item>
                    <el-dropdown-item command="auth">权限</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </span>
          </el-tree>
        </div>
        <div class="right-content">
          <el-row>
            <el-col :span="12">
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="addDialogVisible = true"
              >
                新建指标
              </el-button>
              <el-button
                type="primary"
                icon=""
                @click="goToAnalysis"
                v-if="$checkPermission(['index:analysis'])"
              >
                多维数据分析
              </el-button>
              <!-- <el-button
                type="primary"
                icon=""
                @click="goPage('/ddsBi/indicatorRelationAnalysis')"
              >
                关联分析
              </el-button> -->
            </el-col>
            <el-col :span="12" style="text-align: right">
              <el-button
                @click="goPage('/ddsMain/system/warningManage')"
                v-if="$checkPermission(['ind:warning'])"
              >
                指标预警管理
              </el-button>
            </el-col>
          </el-row>
          <el-table
            :data="tableData"
            v-loading="loading"
            ref="table"
            height="calc(100vh - 350px)"
            row-key="id"
            @sort-change="sortChange"
          >
            <el-table-column
              prop="zbmc"
              label="指标名称"
              min-width="150"
            ></el-table-column>
            <el-table-column
              label="指标类型"
              width="100"
              prop="zblx"
            ></el-table-column>
            <el-table-column
              prop="cjr"
              label="创建人"
              sortable="customer"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="编辑日期"
              sortable="customer"
              width="170"
            ></el-table-column>
            <el-table-column prop="bq" label="标签" min-width="150">
              <template #default="{ row }">
                <div v-if="row.bq">
                  <el-tag
                    v-for="item in row.bq.split(',')"
                    :key="item"
                    style="margin: 2px"
                  >
                    {{ item }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="220px"
              #default="{ row }"
              fixed="right"
            >
              <template v-if="row.lxbm !== 'yz'">
                <el-button type="text" @click="calculation(row)">
                  试计算
                </el-button>
                <el-divider direction="vertical"></el-divider>
              </template>

              <el-button type="text" @click="editIndicator(row)">
                编辑
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" @click="delIndicator(row)">删除</el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" @click="goDetail(row)">查看详情</el-button>

              <!-- <template v-if="row.zblx !== '原子指标'">
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" @click="normConfiguration(row)">
                  常模配置
                </el-button>
              </template> -->
            </el-table-column>
          </el-table>
          <DT-Pagination
            :hidden="page.total === 0"
            :total="page.total"
            :page-size="page.pageSize"
            :current-page="page.currentPage"
            @sizeChange="handleSizeChange"
            @currentChange="handleCurrentChange"
          />
        </div>
      </div>

      <el-dialog
        title="新建指标"
        :visible.sync="addDialogVisible"
        :before-close="handleClose"
        width="800px"
      >
        <div class="card-list">
          <template v-for="item in addTypeList">
            <div
              class="card-item"
              v-if="item.isShow"
              :key="item.id"
              @click="clickCard(item.id)"
            >
              <div class="name">
                {{ item.name }}
              </div>
              <div class="desc">
                {{ item.desc }}
              </div>
              <div class="card-item-children" v-if="item.children">
                <div
                  class="card-item-children-item"
                  v-for="(child, index) in item.children"
                  :key="index"
                  @click.stop="clickCard(child.id)"
                >
                  <div class="card-item-children-item-name">
                    {{ child.name }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-dialog>
    </tempalte>
    <!-- 新增数据域 -->
    <AddDataDomain
      v-if="addDataDomainVisible"
      :title="title"
      :add-data-domain-visible.sync="addDataDomainVisible"
      :view-group="viewGroup"
      :form="dataDomainForm"
      @refresh="initData"
    />
    <!-- 指标分析 -->
    <!-- <IndicatorAnalysis v-if="currentType == 'analysis'" @closeAdd="closeAdd" /> -->
    <!-- 编辑指标 -->
    <EditAtomIndicator
      ref="EditAtomIndicator"
      :yzzb-list="yzzbList"
      :form="editForm"
      :view-group="viewGroup"
      @refresh="initData"
    />
    <EidtDeriveIndicator
      ref="EidtDeriveIndicator"
      :yzzb-list="yzzbList"
      :form="editForm"
      :view-group="viewGroup"
      @refresh="initData"
    />
    <DeriveIndicatorCalculate ref="DeriveIndicatorCalculate" />
    <CompositeIndicatorCalculate ref="CompositeIndicatorCalculate" />
    <SqlIndicatorCalculate ref="SqlIndicatorCalculate" />
    <el-dialog
      title="常模配置"
      :visible.sync="normalVisible"
      width="678px"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="normalForm" label-width="120px">
        <el-form-item label="指标名称:">
          {{ normalForm.name }}
        </el-form-item>
        <el-form-item label="选择常模类型:">
          <el-select
            v-model="normalForm.types"
            placeholder="请选择"
            style="width: 100%"
            multiple
            @change="handleNomalType"
            value-key="id"
          >
            <el-option
              v-for="item in normalOPtions"
              :key="item.id"
              :label="item.name"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置常模值:">
          <el-table
            :data="normalForm.zbNormativeTypes"
            style="width: 100%; margin-top: 0px"
            :max-height="300"
            border
          >
            <el-table-column
              prop="zbnormativetypename"
              label="常模类型"
            ></el-table-column>
            <el-table-column prop="zbnormativevalue" label="常模值">
              <template #default="{ row }">
                <el-input
                  style="width: 200px; padding-right: 20px"
                  v-model="row.zbnormativevalue"
                  placeholder="请输入常模值"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="normalVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveNomal">确 定</el-button>
      </span>
    </el-dialog>
    <DT-Transfer
      type="dialog"
      :visible.sync="visible"
      title="授权范围"
      v-model="defaultVal"
      single-check
      :data="data"
      @getChildren="transferChild"
    />
    <AffectScopeDialog ref="AffectScopeDialog" />
  </DT-View>
</template>

<script>
import SearchForm from "@/components/SearchForm/index.vue"
import options from "./mixins/options"
// 添加指标域
import AddDataDomain from "./components/AddDataDomain"
// 原子指标编辑
import EditAtomIndicator from "./atom-indicator/Edit.vue"
// 编辑派生指标
import EidtDeriveIndicator from "./derive-indicator/EditIndicator.vue"
// 派生指标试计算
import DeriveIndicatorCalculate from "./derive-indicator/Calculate.vue"
// 复合指标试计算
import CompositeIndicatorCalculate from "./composite-indicator/Calculate.vue"
// SQL指标试计算
import SqlIndicatorCalculate from "./sql-indicator/Calculate.vue"
// 变更影响弹窗
import AffectScopeDialog from "./components/AffectScopeDialog.vue"

export default {
  components: {
    SearchForm,
    AddDataDomain,
    EditAtomIndicator,
    EidtDeriveIndicator,
    DeriveIndicatorCalculate,
    CompositeIndicatorCalculate,
    SqlIndicatorCalculate,
    AffectScopeDialog,
  },
  mixins: [options],
  props: {},
  data() {
    return {
      treeLoading: false,
      loading: false,
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
      },
      dialogVisible: false, // 试计算弹框
      expandRowKeys: [],
      yzzbList: [], // 原子指标
      title: "新增数据域",
      editVisible: false,
      defaultExpandAll: false,
      editForm: {},
      dataDomainForm: {
        name: "",
        description: "",
        index: 0, // 排序
        parentId: null, // 父级主题id
        publish: 1, // 这里固定是 1
      },
      form: {
        zbmc: "",
        lxbm: "",
        sortKey: "", // 排序字段
        sortType: "", // 排序类型,
        currentId: "0", // 当前数据域id
      },
      currentType: null,
      pageShow: true,
      addDialogVisible: false,
      addDataDomainVisible: false, // 新建数据域

      columns: [
        {
          label: "指标名称",
          prop: "zbmc",
          search: {
            el: "input",
            props: {
              placeholder: "请输入指标名称、标签名称",
            },
          },
        },
        {
          label: "指标类型",
          prop: "lxbm",
          type: "select",
          search: {
            el: "select",
          },
          enum: this.getIndicatorType,
        },
      ],
      addTypeList: [
        {
          name: "创建原子指标",
          id: "atomIndicator",
          desc: "适合基于数据表创建大量基础指标",
          isShow: true,
        },
        {
          name: "创建派生指标",
          id: "deriveIndicator",
          desc: "在原子指标的基础上，约定特定维度；如某学院、某学科维度的指标",
          isShow: true,
        },
        {
          name: "创建衍生指标",
          id: "compositeIndicator",
          desc: "创建需要多个指标关联计算才能得出的进阶指标；如生师比",
          isShow: true,
        },
        {
          name: "创建算法指标",
          id: "algorithmIndex",
          desc: "通过算法创建挖掘指标",
          isShow: false,
        },
        {
          name: "创建SQL指标",
          id: "sqlIndicator",
          desc: "通过SQL语句创建自定义指标",
          isShow: true,
          // children: [
          //   {
          //     name: "SQL视图",
          //     id: "sqlIndicator"
          //   },
          //   {
          //     name: "表视图",
          //     id: "sqlTableview"
          //   }
          // ]
        },
        // {
        //   name: "创建SQL指标",
        //   id: "sql",
        //   desc: "通过SQL语句创建自定义指标"
        // }
      ],
      tableData: [], // 指标列表
      // 常模
      normalVisible: false,
      normalForm: {
        types: [],
        name: "",
        zbid: "",
        zblxm: "",
        zbNormativeTypes: [],
      },
      normalOPtions: [],
      dwList: [],
      labels: [],
      visible: false,
      defaultVal: [],
      data: [],
      draggingNode: null,
    }
  },
  computed: {
    username() {
      return this.$store.state.user.username
    },
  },
  created() {},
  mounted() {
    if (this.$route.query.code) {
      let { code } = this.$route.query
      this.$nextTick(() => {
        this.expandRowKeys = code.split(",")
        console.log(this.expandRowKeys, "this.expandRowKeys")
      })
    }
    this.initData()
    this.getYzList()
    this.getAllNormativeType()
    this.initTransferData()
  },
  watch: {},
  methods: {
    async getBaseUnit() {
      const { data } = await this.$httpBi.indicatorAnagement.getBaseUnit()
      this.dwList = data
      data.push({
        bm: "其他",
        name: "其他",
      })
    },
    // 获取所有标签
    async getLabelSelectList() {
      const { data } = await this.$httpBi.indicatorAnagement.getIndicatorTags(
        ""
      )
      this.labels = data
    },
    goToAnalysis() {
      this.$router.push("/ddsBi/indicatorAnalysis")
    },
    handleExpandChange(row, expanded) {
      console.log(row, expanded, "expanded")
      if (expanded) {
        if (row.parentId === 0) {
          this.expandRowKeys = ["0"]
        }
        this.expandRowKeys.push(String(row.id))
      }
      console.log(this.expandRowKeys, "this.expandRowKeys")
    },
    // 保存常模
    async handleSaveNomal() {
      const { code, data } =
        await this.$httpBi.indicatorAnagement.updateNormativeTypeByZb({
          zbid: this.normalForm.zbid,
          zblxm: this.normalForm.zblxm,
          zbNormativeTypes: this.normalForm.zbNormativeTypes,
        })
      if (code === 200) {
        this.normalVisible = false
        this.$message.success(data)
      }
    },
    // 选择常模
    handleNomalType(val) {
      console.log(val, "val")
      let tempList = [...this.normalForm.zbNormativeTypes]
      console.log(tempList, " let tempList")
      this.normalForm.zbNormativeTypes = val.map((item) => {
        return {
          ...item,
          zbnormativetypeid: item.id,
          zbnormativetypename: item.name,
          zbnormativevalue:
            tempList.find(
              (e) => Number(e.zbnormativetypeid) === Number(item.id)
            )?.zbnormativevalue || "",
        }
      })
      console.log(this.normalForm.zbNormativeTypes)
    },
    // 获取常模下拉选择
    async getAllNormativeType() {
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllNormativeType()
      this.normalOPtions = data
    },

    // 常模配置
    async normConfiguration(row) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getNormativeTypeByZb({
          zbid: row.id,
          zblxm: row.lxbm,
        })
      this.normalVisible = true
      this.normalForm.name = row.zbmc
      this.normalForm.zbid = row.id
      this.normalForm.zblxm = row.lxbm
      this.normalForm.zbNormativeTypes = data
      this.normalForm.types = data.map((item) => ({
        ...item,
        id: Number(item.zbnormativetypeid),
        name: item.zbnormativetypename,
      }))
    },
    // 跳转常模指标
    goNormIndicator() {
      const routeUrl = this.$router.resolve({
        path: `/ddsBi/normIndicator`,
      })
      window.open(routeUrl.href, "_blank")
    },
    goDetail(row) {
      const routeUrl = this.$router.resolve({
        path: `/ddsBi/appDetail`,
        query: {
          indCode: row.indCode,
          lxbm: row.lxbm,
        },
      })
      window.open(routeUrl.href, "_blank")
    },
    clickCard(id) {
      if (Number(this.form.currentId) === 0) {
        this.$router.push(id)
      } else {
        this.$router.push(id + "?sysjy=" + this.form.currentId)
      }
      // if (id === "sql") {
      //   this.$router.push("/ddsBi/EditView?isFullPage=true&group=0")
      // } else {
      //   this.currentType = id
      // }
    },
    // 节点操作
    handleCommand(command, data) {
      console.log(data, "data")
      if (command === "add") {
        // 初始化数据
        this.dataDomainForm = {
          name: "",
          description: "",
          index: 0, // 排序
          parentId: data.id, // 父级主题id
          publish: 1, // 这里固定是 1
        }
        this.title = "新增数据域"
        this.addDataDomainVisible = true
        this.addDataDomainVisible = true
      } else if (command === "edit") {
        this.editDataDomain(data)
      } else if (command === "del") {
        this.deleteDataDomain(data)
      } else if (command === "auth") {
        console.log("///")
        this.visible = true
      }
    },
    // tree点击
    treeNodeClick(data) {
      this.page.currentPage = 1
      this.form.currentId = data.id
      this.getAllIndicatorList()
    },

    handleCurrentChange(val) {
      this.page.currentPage = val.currentPage
      this.getAllIndicatorList()
    },
    handleSizeChange(val) {
      this.page.currentPage = 1
      this.page.pageSize = val.pageSize
      this.getAllIndicatorList()
    },
    sortChange({ prop, order }) {
      this.page.currentPage = 1
      this.form.sortKey = prop
      this.form.sortType = order === "ascending" ? "asc" : "desc"
      this.getAllIndicatorList()
    },
    initData() {
      this.page.currentPage = 1
      this.$nextTick(() => {
        this.$refs.table.clearSort()
      })
      this.getAllIndicatorList()
      this.getAllViewGroup()
    },
    // 获取所有数据域分组
    async getAllViewGroup() {
      this.treeLoading = true
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup({
        zbmc: this.form.zbmc,
        lxbm: this.form.lxbm,
        sysjyid: "",
      })
      this.viewGroup[0].children = data
      this.treeLoading = false
    },
    // 获取指标列表
    async getAllIndicatorList() {
      this.loading = true
      this.tableData = []
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllIndicatorList({
          zbmc: this.form.zbmc,
          lxbm: this.form.lxbm,
          sysjyid: this.form.currentId, // 数据域id
          pxmc: this.form.sortKey, // 排序字段名称
          px: this.form.sortType, // 排序方式
          pageSize: this.page.pageSize,
          currentPage: this.page.currentPage,
        })
      this.tableData = data.list || []
      this.page.total = data.totalCount || 0
      this.loading = false
      if (this.form.zbmc) {
        this.defaultExpandAll = true
      } else {
        this.defaultExpandAll = false
      }
      console.log(this.tableData, "this.tableData")
    },
    // 编辑指标
    async editIndicator(row) {
      console.log(row, "row")
      let Api = null
      if (row.lxbm === "yz") {
        Api = this.$httpBi.indicatorAnagement.getAtomIndicatorInfo
        const { data } = await Api({
          indCode: row.indCode,
        })
        this.editForm = {
          ...data,
          diydw: "",
          sysjy: data.sysjy + "",
        }
        this.$refs.EditAtomIndicator.open(this.editForm)

        return
      }
      if (row.lxbm === "ps") {
        Api = this.$httpBi.indicatorAnagement.getDeriveIndicatorInfo
        const { data } = await Api({
          id: row.id,
          indCode: row.indCode,
        })
        this.editForm = {
          ...data,
          diydw: "",
          initWarn: data.isWarnThreshold,
          sysjy: data.sysjy + "",
        }

        this.$refs.EidtDeriveIndicator.open()
        return
      }
      if (row.lxbm === "ys") {
        return this.$router.push({
          path: "/ddsBi/compositeIndicator",
          query: {
            indCode: row.indCode,
          },
        })
      }
      if (row.lxbm === "sq") {
        return this.$router.push({
          path: "/ddsBi/sqlIndicator",
          query: {
            indCode: row.indCode,
          },
        })
      }
    },
    // 编辑数据域
    editDataDomain(row) {
      this.dataDomainForm = {
        ...this.dataDomainForm,
        ...row,
      }
      this.title = "编辑数据域"

      this.addDataDomainVisible = true
    },
    // 新增数据域
    handleDataDomainVisible() {
      // 初始化数据
      this.dataDomainForm = {
        name: "",
        description: "",
        index: 0, // 排序
        parentId: 0, // 父级主题id
        publish: 1, // 这里固定是 1
      }
      this.title = "新增数据域"
      this.addDataDomainVisible = true
    },
    // 删除数据域
    deleteDataDomain(row) {
      this.$confirm("此操作将永久删除该数据域, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const { code } = await this.$httpBi.indicatorAnagement.deleteZty({
          id: row.id,
        })
        if (code === 200) {
          this.$message({
            type: "success",
            message: "删除成功!",
          })
          this.initData()
        }
      })
    },
    // 删除指标
    async delIndicator(row) {
      await this.$refs.AffectScopeDialog.isAffectSpace(
        {
          indCode: row.indCode,
          indType: row.lxbm,
        },
        async () => {
          var Api = null
          if (row.lxbm === "yz") {
            Api = this.$httpBi.indicatorAnagement.deleteYz
          }
          if (row.lxbm === "ps") {
            Api = this.$httpBi.indicatorAnagement.deletePs
          }
          if (row.lxbm === "ys") {
            Api = this.$httpBi.compositeIndicator.deleteCompositeIndicator
          }
          if (row.lxbm === "sq") {
            Api = this.$httpBi.indicatorAnagement.deleteSql
          }

          this.$confirm("此操作将永久删除该指标, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            const { code } = await Api({
              id: row.id,
              indCode: row.indCode,
            })
            if (code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              })
              this.initData()
            }
          })
        }
      )
    },
    // 获取原子指标
    async getYzList() {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: "",
      })
      this.yzzbList = data
    },
    closeAdd() {
      this.currentType = null
      this.addDialogVisible = false
      this.initData()
    },
    // 式计算
    async calculation(row) {
      if (row.lxbm === "ps") {
        this.$refs.DeriveIndicatorCalculate.calculation(row)
        return
      }
      if (row.lxbm === "ys") {
        this.$refs.CompositeIndicatorCalculate.initData(row.indCode)
      }
      if (row.lxbm === "sq") {
        this.$refs.SqlIndicatorCalculate.initData(row)
      }
    },
    async getCalculationResult() {
      const { data } = await this.$httpBi.indicatorAnagement.tryToCaluIndicator(
        {
          ...this.sjsForm,
          startTime: this.sjsForm.timeRange?.[0] || null,
          endTime: this.sjsForm.timeRange?.[1] || null,
          indCode: this.calculationRow.indCode,
          xsc: this.xsc.map((item) => ({
            indCode: item.indCode,
            id: item.id,
            lxbm: item.lxbm,
            zdmc: item.zdmc,
            wdzd: item.wdzd,
            wdzval: item.wdzval.filter((e) => e !== ""),
          })),
        }
      )
      this.sjstableData = data
    },
    goPage(path) {
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path,
        query: {
          type: "indicator",
        },
      })
      window.open(routerUrl.href, "_blank")
    },

    // 初始化数据
    async initTransferData() {
      const { data } = await this.$httpBi.api.orgMember({
        id: "",
      })
      this.data = data.map((item) => ({
        ...item,
        id: item.id,
        __id: item.id,
        title: item.name,
        hideChildren: false,
        hasChildren: item.type === 1,
      }))
    },
    // 获取下级数据
    async transferChild(event) {
      console.log(event.parent)
      const { data } = await this.$httpBi.api.orgMember({
        id: event.parent.id,
      })
      const children = data.map((item) => ({
        ...item,
        id: item.id,
        __id: item.id,
        title: item.name,
        hideChildren: false,
        hasChildren: item.type === 1,
        parentId: item.parentId,
      }))
      event.update(children)
    },
    // 拖拽开始
    handleDragStart(node) {
      this.draggingNode = node
    },
    // 拖拽结束
    handleDragEnd() {
      this.draggingNode = null
    },
    // 判断是否允许放置
    allowDrop(draggingNode, dropNode) {
      // 只允许同级且同父级拖拽
      // 如果是第一层级（父级都是null）也可以排序
      if (
        draggingNode.level === dropNode.level &&
        draggingNode.parent.data.id === dropNode.parent.data.id
      ) {
        return true
      }
      return false
    },
    // 处理拖拽放置
    async handleDrop(draggingNode, dropNode, dropType) {
      console.log(draggingNode, "draggingNode")
      console.log(dropNode, "dropNode")

      // 获取拖拽节点的父节点
      const parentNode = draggingNode.parent
      // 获取所有同级节点
      const siblings = parentNode?.childNodes || []

      // 获取目标位置
      const targetIndex =
        dropType === "before"
          ? siblings.findIndex((node) => node.data.id === dropNode.data.id)
          : siblings.findIndex((node) => node.data.id === dropNode.data.id) + 1

      // 构建新的排序数组
      const newOrder = siblings.map((node, index) => {
        // 如果是拖拽的节点，使用目标位置
        if (node.data.id === draggingNode.data.id) {
          return {
            id: node.data.id,
            index: targetIndex,
          }
        }
        // 其他节点根据是否在目标位置之前来调整索引
        const originalIndex = index
        if (originalIndex < targetIndex) {
          return {
            id: node.data.id,
            index: originalIndex,
          }
        } else {
          return {
            id: node.data.id,
            index: originalIndex + 1,
          }
        }
      })

      try {
        // 调用后端接口更新排序
        const { code } =
          await this.$httpBi.indicatorAnagement.updateViewGroupOrder({
            orders: newOrder,
          })
        if (code === 200) {
          this.$message.success("排序更新成功")
          // 刷新数据
          this.initData()
        }
      } catch (error) {
        this.$message.error("排序更新失败")
        // 刷新数据恢复原顺序
        this.initData()
      }
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-table:before {
  height: 0;
}
::v-deep .avue-form__group--flex {
  display: flex;

  padding: 15px 24px 0;
  align-items: center;
  background: #fff;
}

.content {
  box-sizing: border-box;
  background: #fff;
  height: calc(100vh - 240px);
  display: flex;

  .left-tree {
    flex: 0 0 224px;
    max-width: 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 5px 0px 0px 5px;
    border: 1px solid #e4e7ed;
    margin-right: 20px;

    .left-head {
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #e4e7ed;
      padding: 0 12px;
      display: flex;
      justify-content: space-between;

      .left-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2f3338;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .left-btn {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #1563ff;
        line-height: 40px;

        text-align: left;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
      }
    }
  }

  .right-content {
    flex: 1;
    width: 0;
  }
}

::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 15px;
}

.module-content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  height: calc(100vh - 232px);
  overflow: auto;
}

::v-deep .el-card__body {
  padding: 0 !important;
}

.page {
  display: flex;
  justify-content: flex-end;
}

.card-list {
  display: grid;
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));

  .card-item {
    position: relative;
    height: 170px;
    padding: 40px 19px 0;
    box-sizing: border-box;
    background: #f9f9f9;
    border-radius: 4px;
    text-align: center;

    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
      background: rgba(91, 143, 249, 0.06);
      border-radius: 4px;
      border: 1px solid rgba(91, 143, 249, 0.6);

      .name {
        color: #5b8ff9;
      }

      .desc {
        color: #5b8ff9;
      }

      .card-item-children {
        height: 44px;
      }
    }

    .name {
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #222222;
    }

    .desc {
      height: 60px;
      margin-top: 16px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 20px;
    }

    .card-item-children {
      height: 0;
      overflow: hidden;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background: rgba(2, 31, 86, 0.5);
      border-radius: 0px 0px 4px 4px;
      transition: all 0.3s;

      .card-item-children-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50%;
        height: 100%;

        &:last-child {
          position: relative;

          &::after {
            position: absolute;
            left: 0;
            content: "";
            width: 1px;
            height: 14px;
            border: 1px solid #ffffff;
            opacity: 0.55;
          }
        }

        .card-item-children-item-name {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 14px;
          text-align: right;
          font-style: normal;
          text-decoration-line: underline;

          &:hover {
            color: #409eff;
          }
        }
      }
    }
  }
}

.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .sub-text {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-right: 5px;
  }

  .sub-line {
    height: 1px;
    flex: 1;
    background: #e5e6eb;
  }
}

::v-deep .el-button--small {
  line-height: 13px;
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-dialog__wrapper.ys {
  .el-dialog__header {
    margin: 0 24px;
    padding: 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #edeff0;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
  }

  .el-dialog__body {
    padding: 0px 24px 24px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

.el-tree-node.dragging > .el-tree-node__content {
  opacity: 0.2;
}
.el-tree.model-tree {
  height: calc(100% - 40px);
  overflow: auto;
  //滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
  &::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }
}

.el-tree-node > .el-tree-node__content {
  &.is-current {
    background: #f5f7fa !important;
  }

  &.is-focusable {
    background: #f5f7fa !important;
  }

  &:hover {
    background: #f5f7fa !important;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-btns {
      opacity: 1;
    }
  }
}

#project_frame .el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa !important;

  .el-tree-node__label {
    background-color: transparent;
  }

  .custom-tree-btns {
    opacity: 1;
  }
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__label {
  background-color: transparent !important;
}

.custom-tree-node {
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  .node-item {
    display: flex;
    width: calc(100% - 20px);

    .node-label {
      max-width: calc(100% - 10px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .custom-tree-btns {
    opacity: 0;
  }
}

/* 主体滚动条 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  border-radius: 8px;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #d4d7de;
  border-radius: 8px;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 8px;
}

/* 固定列滚动条 */
.el-table__fixed-body-wrapper::-webkit-scrollbar,
.el-table__fixed-right-body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  border-radius: 8px;
}
.el-table__fixed-body-wrapper::-webkit-scrollbar-thumb,
.el-table__fixed-right-body-wrapper::-webkit-scrollbar-thumb {
  background: #d4d7de;
  border-radius: 8px;
}
.el-table__fixed-body-wrapper::-webkit-scrollbar-track,
.el-table__fixed-right-body-wrapper::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 8px;
}

/* Firefox */
.el-table__body-wrapper,
.el-table__fixed-body-wrapper,
.el-table__fixed-right-body-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #d4d7de #f5f7fa;
  border-radius: 8px;
}

/* 修复固定列底部多余空白 */
.el-table__fixed-right::before,
.el-table__fixed::before {
  display: none !important;
}
</style>
