<template>
  <div class="center-workbench">
    <Step v-bing="$attrs" :current-step="currentStep" />

    <!-- 分析群体 -->
    <div class="analyze-group">
      <div class="analyze-group-title">分析群体</div>
      <div
        class="analyze-group-content"
        @dragover.prevent
        @dragenter="dragEnter"
        @dragleave="dragLeave"
        @drop="dropItem"
        :style="{
          pointerEvents: isIndexDragStart ? 'none' : 'auto'
        }"
        :class="{
          'forbidden-childe-pointer-events': isGroupDragStart
        }"
      >
        <div class="tags">
          <el-tag
            class="tag-item"
            type="info"
            closable
            effect="plain"
            v-for="(item, index) in groupList"
            :key="index"
          >
            {{ item.label }}
          </el-tag>
        </div>
        <div
          class="empty"
          v-if="
            (!isDraggingOver && !groupList.length) ||
            (isIndexDragStart && !groupList.length)
          "
        >
          请从左侧选择群体拖入此处
        </div>

        <div class="is-dragging-over" v-if="isDraggingOver && isGroupDragStart">
          <div class="icon"></div>
          松开添加
        </div>
      </div>
    </div>
    <!-- 特征指标 -->
    <div class="characteristic-index">
      <div class="characteristic-index-title">特征指标</div>
      <div
        class="characteristic-index-content"
        @dragover.prevent
        @dragenter="dragEnter"
        @dragleave="dragLeave"
        @drop="dropItem"
        :style="{
          pointerEvents: isGroupDragStart ? 'none' : 'auto'
        }"
        :class="{
          'forbidden-childe-pointer-events': isIndexDragStart
        }"
      >
        <div class="tags">
          <el-tag
            class="tag-item"
            type="info"
            closable
            effect="plain"
            v-for="(item, index) in indexList"
            :key="index"
          >
            {{ item.label }}
          </el-tag>
        </div>
        <div
          class="empty"
          v-if="
            (!isDraggingOver && !indexList.length) ||
            (isGroupDragStart && !indexList.length)
          "
        >
          请从右侧选择指标拖入此处，仅支持单选
        </div>

        <div class="is-dragging-over" v-if="isDraggingOver && isIndexDragStart">
          <div class="icon"></div>
          松开添加
        </div>
      </div>
    </div>

    <div class="step-btn">
      <el-button
        size="small"
        @click="$emit('update:currentStep', currentStep - 1)"
      >
        上一步
      </el-button>
      <el-button
        size="small"
        type="primary"
        :disabled="false"
        @click="$emit('update:currentStep', currentStep + 1)"
      >
        下一步
      </el-button>
    </div>
    <el-drawer
      title="设置表关联字段"
      :visible.sync="drawer"
      size="480px"
      :before-close="handleClose"
    >
      <div class="relate-list">
        <div
          class="relate-item"
          v-for="(item, index) in relateList"
          :key="item"
        >
          <div class="relate-title">关联1</div>
          <div class="relate-content">
            <div class="field-name">
              <span>{{ item.zb1 }}</span>
              <span>{{ item.zb2 }}</span>
            </div>
            <div
              class="relate-content-input"
              v-for="(ele, i) in item.children"
              :key="ele"
            >
              <span>{{ i + 1 }}</span>

              <el-select
                v-model="value"
                placeholder="请选择"
                style="width: 168px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <span class="relation"></span>
              <el-select
                v-model="value"
                placeholder="请选择"
                style="width: 168px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <span class="del" v-if="item.children.length > 1"></span>
              <span
                class="add"
                @click="addField(index)"
                v-if="i === item.children.length - 1"
              ></span>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-btn">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Step from "../Step"

export default {
  components: { Step },
  props: {
    isGroupDragStart: {
      type: Boolean,
      default: false
    },
    isIndexDragStart: {
      type: Boolean,
      default: false
    },
    isDraggingOver: {
      type: Boolean,
      default: false
    },
    currentStep: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      relateList: [
        {
          name: "关联一",
          zb1: "人群1表",
          zb2: "指标1",
          children: [
            {
              zd1: "",
              zd2: ""
            }
          ]
        },
        {
          name: "关联二",
          zb1: "人群2表",
          zb2: "指标2",
          children: [
            {
              zd1: "",
              zd2: ""
            }
          ]
        }
      ],
      drawer: false, // 关联指标
      groupList: [], // 分析群体,
      indexList: [] // 分析指标
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    dragEnter() {
      this.isDraggingOver = true
    },
    dragLeave() {
      this.isDraggingOver = false
    },
    dropItem(event) {
      const itemType = event.dataTransfer.getData("itemType")
      console.log(itemType)

      if (itemType === "groupType") {
        this.groupList.push(
          ...JSON.parse(event.dataTransfer.getData("groupList"))
        )
      } else {
        if (this.indexList.length > 0) {
          this.$message.warning("指标只能选择一个")
          return
        } else {
          this.indexList.push(
            ...JSON.parse(event.dataTransfer.getData("indexList"))
          )
        }
      }
      this.isDraggingOver = false
    },
    addField(index) {
      this.relateList[index].children.push({
        zd1: "",
        zd2: ""
      })
    }
  }
}
</script>

<style scoped lang="scss">
.center-workbench {
  position: relative;
  flex: 1;
  height: 100%;
  background: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  padding: 32px 56px;
  box-sizing: border-box;
  .analyze-group {
    width: 568px;
    margin: 48px auto 32px;
    .analyze-group-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }
    .analyze-group-content {
      position: relative;

      width: 568px;
      height: calc(100vh - 700px);
      max-height: 72px;
      background: #ffffff;
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      &.forbidden-childe-pointer-events * {
        pointer-events: none;
      }
      .tags {
        margin: 10px auto;
        height: calc(100% - 10px);
        // pointer-events: none;
        overflow: auto;
        .tag-item {
          margin-left: 10px;
          margin-bottom: 10px;
        }
      }
      .is-dragging-over {
        position: absolute;
        top: 8px;
        left: 8px;
        height: calc(100% - 16px);
        width: 552px;
        // height: 56px;
        background: rgba(0, 204, 136, 0.1);
        border-radius: 4px;
        border: 1px dashed #00cc88;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2f3338;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        .icon {
          width: 25px;
          height: 24px;
          background: url("~@/assets/images/drag-enter.png") no-repeat center;
          margin-right: 10px;
        }
      }
      .empty {
        position: absolute;
        top: 8px;
        left: 8px;
        height: calc(100% - 16px);
        width: 552px;
        // height: 56px;
        border-radius: 4px;
        border: 1px dashed #ebedf0;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #979da6;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }
      .relevance {
        position: absolute;
        right: -20px;
        top: 50%;
        width: 20px;
        height: calc(100% + 50px);
        border: 1px solid var(--relevance-color);
        border-left-color: transparent;
        z-index: 9;
        &::after {
          position: absolute;
          left: 0px;
          top: -6px;
          content: "";
          width: 6px;
          height: 12px;
          background: var(--relevance-color);
          border-radius: 0 6px 6px 0;
        }
        &::before {
          position: absolute;
          left: 0;
          bottom: -6px;
          content: "";
          width: 6px;
          height: 12px;
          background: var(--relevance-color);
          border-radius: 0 6px 6px 0;
        }
        .relevance-title {
          position: absolute;
          cursor: pointer;
          top: 50%;
          right: 0%;
          transform: translate(50%, -50%);
          display: flex;
          justify-content: center;
          align-items: center;
          width: 24px;
          height: 80px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid var(--relevance-color);

          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: var(--relevance-color);
          line-height: 14px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          z-index: 99;
        }
      }
    }
  }
  .characteristic-index {
    width: 568px;
    margin: 0 auto 32px;
    .characteristic-index-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }
    .characteristic-index-content {
      position: relative;
      width: 568px;
      height: calc(100vh - 700px);
      max-height: 72px;

      background: #ffffff;
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      &.forbidden-childe-pointer-events * {
        pointer-events: none;
      }
      .tags {
        margin: 10px auto;
        height: calc(100% - 10px);
        // pointer-events: none;
        overflow: auto;
        .tag-item {
          margin-left: 10px;
          margin-bottom: 10px;
        }
      }
      .is-dragging-over {
        position: absolute;
        top: 8px;
        left: 8px;
        height: calc(100% - 16px);
        width: 552px;
        // height: 56px;
        background: rgba(0, 204, 136, 0.1);
        border-radius: 4px;
        border: 1px dashed #00cc88;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2f3338;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        .icon {
          width: 25px;
          height: 24px;
          background: url("~@/assets/images/drag-enter.png") no-repeat center;
          margin-right: 10px;
        }
      }
      .empty {
        position: absolute;
        top: 8px;
        left: 8px;
        height: calc(100% - 16px);
        width: 552px;
        // height: 56px;
        border-radius: 4px;
        border: 1px dashed #ebedf0;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #979da6;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }
    }
  }
  .cluster {
    width: 568px;
    margin: 0px auto 52px;
    .cluster-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }
    .cluster-content {
      width: 568px;
      height: calc(100vh - 700px);
      max-height: 72px;

      background: #ffffff;
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: center;
      .cluster-content-label {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #2f3338;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-right: 6px;
      }
    }
  }
  .step-btn {
    position: absolute;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    margin-bottom: 0;
    .el-button--primary {
      background: #1563ff;
      border-color: #1563ff;
      &.is-disabled {
        color: #fff;
        background-color: #a0cfff;
        border-color: #a0cfff;
        &:hover {
          color: #fff;
          background-color: #a0cfff;
          border-color: #a0cfff;
        }
      }
      &:hover {
        background-color: rgba(64, 128, 255, 1);
        border-color: rgba(64, 128, 255, 1);
      }
    }
  }
  .relate-list {
    padding: 18px 12px;
    box-sizing: border-box;
    height: calc(100vh - 115px);
    overflow: auto;
    .relate-item {
      .relate-title {
        position: relative;
        width: 100%;
        height: 32px;
        background: #f7f8fa;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #323233;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        padding-left: 23px;
        &::after {
          content: "";
          position: absolute;
          left: 12px;
          top: 9px;
          width: 3px;
          height: 14px;
          background: #1563ff;
        }
      }
      .relate-content {
        .field-name {
          display: flex;
          span:nth-child(1) {
            width: 168px;
            height: 32px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #2f3338;
            line-height: 32px;
            text-align: left;
            font-style: normal;
            margin-left: 23px;
            margin-right: 34px;
          }
          span:nth-child(2) {
            width: 168px;
            height: 32px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #2f3338;
            line-height: 32px;
            text-align: left;
            font-style: normal;
          }
        }
        .relate-content-input {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          span:nth-child(1) {
            width: 23px;
            height: 32px;
            line-height: 32px;
            font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
            font-weight: 500;
            font-size: 14px;
            color: #2f3338;
            text-align: center;
          }
          .relation {
            margin: 0 9px;
            width: 16px;
            height: 8px;
            background: url("~@/assets/images/relation.png") no-repeat center;
            background-size: cover;
          }
          .del {
            margin-left: 14px;
            width: 16px;
            height: 16px;
            background: url("~@/assets/images/del.png") no-repeat center;
            background-size: cover;
            cursor: pointer;
          }
          .add {
            width: 18px;
            height: 18px;
            margin-left: 12px;
            background: url("~@/assets/images/add.png") no-repeat center;
            background-size: cover;
            cursor: pointer;
          }
        }
      }
    }
  }
  .footer-btn {
    padding-right: 24px;
    border-top: 1px solid #f0f0f0;
    height: 52px;
    box-sizing: border-box;
    width: 480px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
::v-deep #el-drawer__title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 24px;
  text-align: left;
  font-style: normal;
  margin-bottom: 0;
  padding-bottom: 18px;
  border-bottom: 1px solid #f0f0f0;
}
</style>
