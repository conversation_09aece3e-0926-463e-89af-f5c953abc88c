<template>
  <div class="paneBlock">
    <h4>柱状图</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="12">
          <el-checkbox v-model="barForm.barChart" @change="changeBarChange">
            条形图
          </el-checkbox>
        </el-col>
        <el-col span="12">
          <el-checkbox v-model="barForm.stack" @change="changeBarChange">
            堆叠图
          </el-checkbox>
        </el-col>
        <el-col span="12">
          <el-checkbox v-model="barForm.percentage" @change="changeBarChange">
            百分比
          </el-checkbox>
        </el-col>
      </el-row>
    </div>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow">
      <el-col span="10">
        <el-select
          placeholder="请选择"
          @change="changeBarChange"
          v-model="barForm.border.type"
          size="mini"
        >
          <el-option
            v-for="item in PIVOT_CHART_LINE_STYLES"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col span="10">
        <el-select
          placeholder="请选择"
          @change="changeBarChange"
          v-model="barForm.border.width"
          size="mini"
        >
          <el-option
            v-for="item in 11"
            :key="item"
            :label="item - 1"
            :value="item - 1"
          >
          </el-option>
        </el-select>
      </el-col>
      <!-- <el-col span="4">
        <el-color-picker
          @change="changeBarChange"
          v-model="barForm.border.color"
        ></el-color-picker>
      </el-col> -->
    </el-row>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow">
      <el-col span="14">边框圆角</el-col>
      <el-col span="10">
        <el-input-number
          controls-position="right"
          placeholder=""
          v-model="barForm.border.radius"
          @change="changeBarChange"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow">
      <el-col span="14">柱条宽度</el-col>
      <el-col span="10">
        <el-input-number
          controls-position="right"
          placeholder=""
          v-model="barForm.width"
          @change="changeBarChange"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow">
      <el-col span="14">不同系列间柱条间隔</el-col>
      <el-col span="10">
        <el-input-number
          controls-position="right"
          placeholder=""
          v-model="barForm.gap"
          @change="changeBarChange"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow">
      <el-col span="14">
        <el-checkbox v-model="barForm.showDataZoom" @change="changeBarChange">
          滚动
        </el-checkbox>
      </el-col>
    </el-row>
    <el-row
      gutter="8"
      type="flex"
      align="middle"
      class="blockRow"
      v-if="barForm.showDataZoom"
    >
      <el-col span="14">默认显示数量</el-col>
      <el-col span="10">
        <el-input-number
          controls-position="right"
          placeholder=""
          v-model="barForm.endValue"
          @change="changeBarChange"
        ></el-input-number>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { PIVOT_CHART_LINE_STYLES } from "@/globalConstants"
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      PIVOT_CHART_LINE_STYLES,
      barForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.barForm = this._.cloneDeep(this.chartData.chartStyles.bar)
    },
    changeBarChange() {
      this.$emit("changeStyle", "bar", this.barForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
