<template>
  <el-date-picker
    v-if="dateFormat == 'yyyy-MM'"
    v-model="resetValue"
    type="monthrange"
    range-separator="至"
    start-placeholder="开始月份"
    end-placeholder="结束月份"
    @change="change"
    :value-format="dateFormat"
  >
  </el-date-picker>
  <YearPicker
    v-else-if="dateFormat == 'yyyy'"
    v-model="resetValue"
    @change="change"
  />
  <el-date-picker
    v-else-if="dateFormat == 'yyyy-MM-dd HH:mm:ss'"
    v-model="resetValue"
    type="datetimerange"
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    @change="change"
    :value-format="dateFormat"
  >
  </el-date-picker>
  <el-date-picker
    v-else
    v-model="resetValue"
    type="daterange"
    @change="change"
    range-separator="~"
    size="mini"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    :value-format="dateFormat"
  >
  </el-date-picker>
</template>

<script>
import dayjs from "dayjs"
import YearPicker from "./YearPicker.vue"
export default {
  components: {
    YearPicker,
  },
  props: {
    value: {
      type: Array,
    },
    dateFormat: {
      type: String,
    },
    defaultValueType: {
      type: String,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {
      resetValue: null,
    }
  },
  computed: {},
  created() {
    // this.getPreciseDefaultValue();
  },
  mounted() {},
  watch: {
    value: {
      immediate: true,
      handler(val, ) {
        if (this.defaultValueType === "dynamic" && val[0].valueType) {
          this.getPreciseDefaultValue()
        } else {
          this.resetValue = val
        }
        console.log("触发两次", this.resetValue)

        this.$emit("change", { [this.item.key]: this.resetValue })
      },
    },
  },
  methods: {
    change() {
      console.log(this.resetValue, "this.resetValue")
      this.$emit("update:value", this.resetValue)
      // this.$emit("change", { [this.item.key]: this.resetValue });
    },
    // 获取精准的默认值
    getPreciseDefaultValue() {
      this.resetValue = this.value.map((item) => {
        if (this.dateFormat === "yyyy") {
          return Number(this.transformRelativeDateValue(item))
        }
        return this.transformRelativeDateValue(item)
      })
    },
    // 转换相对日期值
    transformRelativeDateValue({ type, value, valueType }) {
      if (valueType === "prev") {
        return dayjs()
          .subtract(value, type)
          .format(this.dateFormat.toUpperCase())
      } else {
        return dayjs().add(value, type).format(this.dateFormat.toUpperCase())
      }
    },
  },
}
</script>

<style scoped lang="less"></style>
