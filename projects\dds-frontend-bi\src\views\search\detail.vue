<template>
  <DT-View :show="pageShow">
    <!-- <SourceDetail v-if="type == 3" /> -->
    <!-- <ViewDetail v-if="type == 4" /> -->
    <WarningDetail v-if="type == 5" />
  </DT-View>
</template>

<script>
// 预警详情
import WarningDetail from "./Warning/Detail.vue"
// import SourceDetail from "./Source/Detail.vue"
// import ViewDetail from "./ViewData/Detail.vue"
export default {
  components: {
    WarningDetail
    // SourceDetail
    // ViewDetail,
  },
  props: {},
  data() {
    return {
      type: ""
    }
  },
  computed: {},
  created() {
    const { type } = this.$route.query
    this.type = type

    this.$dt_loading.show()
    this.$dt_loading.hide()
  },
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="less"></style>
