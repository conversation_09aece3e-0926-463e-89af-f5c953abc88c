<template>
  <div>
    <el-input
      placeholder="请输入内容"
      class="input-with-select"
      v-model="value"
      @input="changeInput"
      size="mini"
    >
    </el-input>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: String,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    changeInput() {
      this.$emit("update:value", this.value)
      this.$emit("change", { [this.item.key]: this.value })
    },
  },
}
</script>

<style scoped lang="scss"></style>
