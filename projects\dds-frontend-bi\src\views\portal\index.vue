<template>
  <div :show="pageShow" class="portal-main">
    <el-button type="primary" @click="dialogShow = true">新建看板</el-button>
    <el-button type="primary" @click="$router.push('/ddsBi/dashboardManage')">
      个性化看板
    </el-button>

    <div class="portalList">
      <PortalItem
        v-for="(item, index) in portalList"
        :key="index"
        :index="index"
        :item="item"
        @onSet="handleSet"
        @onDel="handleDel"
      />
    </div>
    <DialogForm
      :dialog-show.sync="dialogShow"
      :form="portalInfo"
      @onSave="handleSave"
    />
  </div>
</template>

<script>
import PortalItem from "./PortalItem.vue"
import DialogForm from "./DialogForm.vue"
import Request from "@/service"

export default {
  components: { PortalItem, DialogForm },
  props: {},
  data() {
    return {
      pageShow: false,
      dialogShow: false,
      portalList: [],
      portalInfo: {
        theme: "",
        description: "",
        id: null,
        name: "",
        publish: true,
        permission: 1,
        roles: []
      }
    }
  },
  computed: {},
  created() {
    this.getPortalList()
  },
  mounted() {},
  watch: {},
  methods: {
    // 获取列表
    async getPortalList() {
      this.$dt_loading.show()
      const { data } = await Request.dashboard.getAllPortal()
      this.portalList = data
      this.$dt_loading.hide()
      this.pageShow = true
    },
    // 设置
    handleSet(item) {
      this.dialogShow = true
      this.portalInfo = {
        ...item,
        roles: item.roles ? item.roles : []
      }
    },
    // 删除
    async handleDel(id) {
      await Request.dashboard.delPortal({ id })
      this.$message.success("删除成功")
      this.getPortalList()
    },
    // 提交保存
    async handleSave(portalInfo) {
      // 新增
      if (!portalInfo.id) {
        const { code } = await Request.dashboard.addPortal(portalInfo)
        if (code === 200) {
          this.handleCancel()
          this.$message.success("新增成功")
          this.getPortalList()
        }
      } else {
        // 设置
        const { code } = await Request.dashboard.updPortal(portalInfo)
        if (code === 200) {
          this.handleCancel()
          this.$message.success("修改成功")
          this.getPortalList()
        }
      }
    },
    handleCancel() {
      this.dialogShow = false
      Object.assign(this.$data.portalInfo, this.$options.data().portalInfo)
    }
  }
}
</script>

<style scoped lang="scss">
.portal-main {
  background-color: #fff;
  margin: 15px 16px;
  padding: 24px 16px;
  box-sizing: border-box;
  min-height: calc(100vh - 139px);
  .portalList {
    margin-top: 24px;
    display: grid;
    gap: 24px 16px;
    grid-template-columns: repeat(auto-fill, minmax(276px, 1fr));
  }
}
</style>
