.portal-main[theme="light"] {
    --theme-color: #ffffff;
    --theme-text-color: #323233;
    --theme-border-color: #EBEDF0;
    --theme-bg-color: #f7f8fa;
    --theme-content-color: #323233;
    --footer1-text-color: #969799;
    --footer2-text-color: #646566;
    --scrollbar-color: #cccccc;
    --theme-icon-color: #323233;




    ::v-deep .el-row {
        background-color: #ffffff;
        margin-bottom: 0;
    }
  

    ::v-deep .el-form-item--small.el-form-item {
        margin-bottom: 16px;
    }
    ::v-deep .el-tree .custom-tree-node{
        padding: 0!important;   

    }


   
    ::v-deep .el-tree-node.is-current>.el-tree-node__content .el-tree-node__labe,
    .el-tree-node.is-current>.el-tree-node__content .custom-tree-node {
        background-color: none !important;
        color: #2361DB !important;
    }

    ::v-deep .el-tree-node>.el-tree-node__content, ::v-deep .el-tree-node>.el-tree-node__content .custom-tree-node {
        height: 46px !important;
        display: flex;
        align-items: center;
        flex: 1;
        margin-bottom: 4px;

    }
    ::v-deep .vxe-table th.is-leaf,
    ::v-deep .vxe-table td {
        border-bottom: 1px solid var(--theme-border-color) !important
    }
    ::v-deep .el-tree .el-tree-node>.el-tree-node__content .el-tree-node__expand-icon{
        height: 46px !important;
        width: 46px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 4px;


    }
    ::v-deep .el-tree .el-tree-node>.el-tree-node__content .el-tree-node__expand-icon.is-leaf{
        height: 46px !important;
        width: 0 !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 14px;
        margin-bottom: 4px;
        margin-left: 0!important;


    }
    ::v-deep .el-tree-node>.el-tree-node__content {
        &:hover {
            background: none;

            >.el-checkbox {
                    background-color: #2361DB1A !important;

            }

            .el-tree-node__expand-icon {
                    background-color: #2361DB1A !important;

            }

            .custom-tree-node,
            .el-tree-node__label {
                padding: 0!important;
                    background-color: #2361DB1A !important;


            }
        }
    }
    ::v-deep .el-tree-node.is-current>.el-tree-node__content {
        i {
            color: #2361DB !important;

        }

        .el-tree-node__expand-icon {
            background-color: #2361DB1A !important;


        }

        .custom-tree-node,
        .el-tree-node__label {
            background-color: #2361DB1A !important;
            color: #2361DB !important;
            padding: 0!important;





        }
    }
        //菜单-------------------------------------------------------------------------------------------------------
        ::v-deep .el-menu{
            background-color: var(--theme-color);
            border-right:var(--theme-border-color)
        }
        
        ::v-deep .el-menu-item{
            height: 46px;
            line-height: 46px;
            background-color: var(--theme-color);
            color: var(--theme-text-color);
            margin-bottom: 4px;
            span{
                margin-left: 13px;
            }
    
        }
        ::v-deep .el-menu-item.is-active,
        ::v-deep .el-menu-item:hover{
            background: rgba(35,97,219,0.1);
            color: #2361DB;
        }
     
}
