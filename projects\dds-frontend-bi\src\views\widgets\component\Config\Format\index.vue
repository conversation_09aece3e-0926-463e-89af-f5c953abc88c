<template>
  <el-dialog
    title="设置数值格式"
    :visible.sync="foramtVisible"
    width="50%"
    @close="foramtVisible = false"
  >
    <el-form
      :model="format"
      :rules="rules"
      ref="formatForm"
      label-width="100px"
      class="formatForm"
    >
      <div style="margin-bottom: 30px">
        <el-radio-group v-model="format.formatType" @change="changeType">
          <el-radio
            v-for="item in NumberFormatType"
            :key="item"
            :label="item.type"
          >
            {{ item.label }}
          </el-radio
          >
        </el-radio-group>
      </div>

      <el-form-item
        label="小数位数"
        :prop="`${[format.formatType]}.decimalPlaces`"
        v-if="
          ['numeric', 'percentage', 'scientificNotation'].includes(
            format.formatType
          )
        "
      >
        <el-input-number
          v-model="format[format.formatType].decimalPlaces"
          controls-position="right"
          :min="0"
          :max="6"
        ></el-input-number>
      </el-form-item>
      <el-form-item
        label="小数位数"
        :prop="`${[format.formatType]}.decimalPlaces`"
        v-if="['currency'].includes(format.formatType)"
      >
        <el-input-number
          v-model="format[format.formatType].decimalPlaces"
          controls-position="right"
          :min="0"
          :max="6"
        ></el-input-number>
      </el-form-item>

      <template v-if="['numeric', 'currency'].includes(format.formatType)">
        <el-form-item label="单位">
          <el-select
            v-model="format[format.formatType].unit"
            placeholder="请选择"
          >
            <el-option
              v-for="item in NumericUnit"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            v-model="format[format.formatType].useThousandSeparator"
          >
            使用千分位分隔符
          </el-checkbox
          >
        </el-form-item>
      </template>
      <template v-if="['currency'].includes(format.formatType)">
        <el-form-item label="前缀">
          <el-input v-model="format[format.formatType].prefix"></el-input>
        </el-form-item>
        <el-form-item label="后缀">
          <el-input v-model="format[format.formatType].suffix"></el-input>
        </el-form-item>
      </template>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="foramtVisible = false">取 消</el-button>
      <el-button type="primary" @click="onSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { NumberFormatType, NumericUnit } from "./constants"
import { getFormatConfig } from "./util"
export default {
  props: {},
  data() {
    return {
      format: {
        formatType: "default",
      },
      foramtVisible: false,
      NumberFormatType,
      NumericUnit,
      rules: {
        "numeric.decimalPlaces": [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
        "currency.decimalPlaces": [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
        "percentage.decimalPlaces": [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
        "scientificNotation.decimalPlaces": [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
      },
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {
    "format.formatType": {
      handler(formatType) {
        if (formatType !== "default" && !this.format[formatType]) {
          this.$set(this.format, [ formatType ], getFormatConfig(formatType))
        }
      },
    },
  },
  methods: {
    changeType() {},
    onSave() {
      this.$refs.formatForm.validate((valid) => {
        if (valid) {
          const { formatType } = this.format
          const config = {
            formatType,
          }
          if (formatType !== "default") {
            config[formatType] = this.format[formatType]
          }
          this.$emit("onSaveFormat", config)
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="scss"></style>
