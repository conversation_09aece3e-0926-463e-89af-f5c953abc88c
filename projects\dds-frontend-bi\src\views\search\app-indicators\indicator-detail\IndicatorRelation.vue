<template>
  <div class="relation">
    <div class="left-content">
      <div class="head-search">
        <div class="search-item">
          <div class="search-label">分析范围：</div>
          <div class="select-wrap">
            <div class="select-item">
              <div class="select-label">指标域</div>

              <avue-input-tree
                default-expand-all
                style="width: 100%"
                v-model="form.correlatedIndDomainId"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择内容"
                :dic="parent.viewGroup"
              ></avue-input-tree>
            </div>
            <div class="select-item">
              <div class="select-label">相关度</div>
              <el-input
                v-model="form.minCorrelationDegree"
                placeholder="最小值"
                style="width: 85px"
              ></el-input>
              <span class="separator"></span>

              <el-input
                v-model="form.maxCorrelationDegree"
                placeholder="最大值"
                style="width: 85px"
              ></el-input>
            </div>
            <div class="select-item">
              <div class="select-label">指标名称</div>

              <el-input
                v-model="form.correlatedIndName"
                placeholder="请输入指标名称"
                style="width: 160px"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="search-item">
          <div class="search-label" v-if="!isPortraits">分析指标：</div>
          <div class="select-wrap" v-if="!isPortraits">
            <div class="select-item">
              <!-- <div class="select-label">指标域</div> -->
              <el-select v-model="analysisName" placeholder="请选择">
                <el-option label="生师比" :value="1"></el-option>
              </el-select>
            </div>
          </div>
          <div class="btns" style="margin-left: auto">
            <el-button @click="handleReset">重置</el-button>
            <el-button type="primary" @click="getAnalysisRange">分析</el-button>
          </div>
        </div>
      </div>
      <div class="chart-box">
        <ChartsGraph
          :chart-data="chartData"
          :indicator-data="parent.indicatorData"
          ref="ChartsGraph"
          @openDialog="getIndDetail"
        />
      </div>
    </div>
    <div class="right-content">
      <div class="right-content-title" v-if="!isPortraits">指标信息</div>
      <div class="ind-info" v-if="!isPortraits">
        <div class="ind-title">生师比</div>
        <div class="ind-info-content">
          <div class="ind-info-item">
            <div class="ind-info-label">当前值</div>
            <div class="ind-info-value">231项</div>
          </div>
          <div class="ind-info-item">
            <div class="ind-info-label">标签</div>
            <div class="ind-info-value">
              本科基本状态数据；本科办学基本条件；综合校情
            </div>
          </div>
          <div class="ind-info-item">
            <div class="ind-info-label">描述</div>
            <div class="ind-info-value">
              生师比=折合在校生数/专任教师总数（参照教育部教发〔2004〕2号文件），综合、师范、民族院校，工科、农、林院校和语文、财经、政法院校≤18:1；医学院校≤16:1；体育、艺术院校≤11:1。
            </div>
          </div>
        </div>
      </div>
      <div class="top5">
        <div class="top5-title">TOP5正相关指标</div>
        <div class="top5-content">
          <template v-if="top5Positive.length > 0">
          <div
            class="top5-item"
            v-for="(item, index) in top5Positive"
            :key="index"
          >
            <div class="top5-label">{{ item.correlatedIndName }}</div>
            <div class="top5-value">{{ item.correlationDegree }}</div>
          </div>
          </template>
          <Empty description="暂无指标" size="80" v-else />  
        </div>
      </div>
      <div class="top5">
        <div class="top5-title">TOP5负相关指标</div>
        <div class="top5-content">
          <template v-if="top5Negative.length > 0">
          <div
            class="top5-item"
            v-for="(item, index) in top5Negative"
            :key="index"
          >
            <div class="top5-label">{{ item.correlatedIndName }}</div>
            <div class="top5-value">{{ item.correlationDegree }}</div>
          </div>
          </template>
          <Empty description="暂无指标" size="80" v-else />  
        </div>
      </div>
    </div>

    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="240px"
      :show-close="false"
      :before-close="handleClose"
    >
      <template #title>
        <div class="ind-head">
          <div class="ind-title">
            {{ currentInd.zbmc }}
          </div>
          <!-- <div class="ind-change">切换为分析指标</div> -->
        </div>
      </template>
      <div class="dialog-ind-info">
        <div class="ind-info-content">
          <div class="ind-info-item">
            <div class="ind-info-label">当前值</div>
            <div class="ind-info-value">{{ currentInd.indValue }}</div>
          </div>
          <div class="ind-info-item">
            <div class="ind-info-label">标签</div>
            <div class="ind-info-value">
              {{ currentInd.bq }}
            </div>
          </div>
          <div class="ind-info-item">
            <div class="ind-info-label">描述</div>
            <div class="ind-info-value">
              {{ currentInd.ms }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ChartsGraph from "./charts/ChartsGraph.vue"
import Empty from "@/components/Empty.vue"
export default {
  name:"IndicatorRelation",
  components: { ChartsGraph,Empty },  
  props: {
    // 是否是指标画像
    isPortraits: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      form: {
        minCorrelationDegree: null,
        maxCorrelationDegree: null,
        correlatedIndDomainId: "0",
        correlatedIndName: null
      },
      analysisName: 1,
      tableId: "1",
      indCode: "1",
      // 指标域id
      zbyId: 1,
      indName: "",
      dialogVisible: false,
      // 相关度
      // 模拟动态数据示例
      chartData: {
        positive: [],
        negative: []
      },
      // 正向TOP5
      top5Positive: [],
      // 负向TOP5
      top5Negative: [],
      // 当前指标
      currentInd: {
        zbmc: "",
        bq: "",
        ms: ""
      }
    }
  },
  inject: ["parent"],
  created() {
    this.getAnalysisRange()
    this.getTop5Positive()
    this.getTop5Negative()
  },
  methods: {
    // 获取分析范围
    async getAnalysisRange() {
      const { data } = await this.$httpBi.api.paramPost(
        "/indicator/correlation/getIndCorList",
        {
          minCorrelationDegree: this.form.minCorrelationDegree,
          maxCorrelationDegree: this.form.maxCorrelationDegree,
          correlatedIndDomainId: this.form.correlatedIndDomainId,
          correlatedIndName: this.form.correlatedIndName,
          indCode: this.parent.indCode
        }
      )
      this.chartData.positive = data
        .filter(item => item.correlationDegree > 0)
        .map(item => ({
          name: item.correlatedIndName,
          value: item.correlationDegree,
          correlatedIndCode: item.correlatedIndCode,
          ind_type: item.ind_type
        }))
      this.chartData.negative = data
        .filter(item => item.correlationDegree < 0)
        .map(item => ({
          name: item.correlatedIndName,
          value: item.correlationDegree,
          correlatedIndCode: item.correlatedIndCode,
          ind_type: item.ind_type
        }))
      this.$nextTick(() => {
        this.$refs.ChartsGraph.initChart()
      })
    },
    // 获取正TOP5
    async getTop5Positive() {
      const { data } = await this.$httpBi.api.paramPost(
        "/indicator/correlation/sortedPositive",
        {
          minCorrelationDegree: this.form.minCorrelationDegree,
          maxCorrelationDegree: this.form.maxCorrelationDegree,
          correlatedIndDomainId: this.form.correlatedIndDomainId,
          correlatedIndName: this.form.correlatedIndName,
          indCode: this.parent.indCode
        }
      )
      this.top5Positive = data || []
    },
    // 获取负TOP5
    async getTop5Negative() {
      const { data } = await this.$httpBi.api.paramPost(
        "/indicator/correlation/sortedNegative",
        {
          minCorrelationDegree: this.form.minCorrelationDegree,
          maxCorrelationDegree: this.form.maxCorrelationDegree,
          correlatedIndDomainId: this.form.correlatedIndDomainId,
          correlatedIndName: this.form.correlatedIndName,
          indCode: this.parent.indCode
        }
      )
      this.top5Negative = data || []
    },
    // 获取指标详情
    async getIndDetail(item) {
      console.log(item)
      const { data } = await this.$httpBi.api.paramGet(
        "/zbgl/getIndicatorBaseDetail",
        {
          lxbm: item.ind_type,
          indCode: item.correlatedIndCode
        }
      )
      this.currentInd = data
      this.dialogVisible = true
    },
    handleReset() {
      this.form = {
        minCorrelationDegree: null,
        maxCorrelationDegree: null,
        correlatedIndDomainId: 0,
        correlatedIndName: null
      }
      this.getAnalysisRange()
      // this.getTop5Positive()
      // this.getTop5Negative()
    },
    resize() {
      this.$refs.ChartsGraph.resize()
    }
  }
}
</script>

<style scoped lang="scss">
.relation {
  display: flex;
  width: 100%s;

  .left-content {
    position: relative;
    width: calc(100% - 280px);
    height: 694px;
    border-radius: 6px 0px 0px 6px;
    padding: 20px;
    background: #f7f8fa;
    box-sizing: border-box;

    .head-search {
      position: relative;
      z-index: 2;
      width: 100%;
      height: 112px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      padding: 16px 20px;
      box-sizing: border-box;

      .search-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .search-label {
          white-space: nowrap;
          height: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #222222;
          line-height: 14px;
          text-align: center;
          font-style: normal;
        }
      }
    }

    .chart-box {
      position: absolute;
      left: 20px;
      top: 20px;
      width: calc(100% - 40px);
      height: 100%;
    }
  }

  .right-content {
    min-width: 280px;
    height: 694px;
    background: #fff;
    border-radius: 0px 8px 8px 0px;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #e4e7ed;
    overflow: auto;

    .right-content-title {
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 12px;
    }

    .ind-info {
      box-sizing: border-box;

      .ind-title {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #222222;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 12px;
      }

      .ind-info-content {
        min-width: 240px;
        background: #f5f7fa;
        border-radius: 8px;
        padding: 16px;

        .ind-info-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .ind-info-label {
            width: 36px;
            height: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
            text-align: center;
            font-style: normal;
            margin-right: 16px;
          }

          .ind-info-value {
            width: 156px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #222222;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }

    .top5 {
      .top5-title {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #222222;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        margin-bottom: 12px;
        margin-top: 20px;
      }

      .top5-content {
        width: 240px;
        height: 172px;
        background: #f5f7fa;
        border-radius: 8px;
        padding: 16px;
        box-sizing: border-box;

        .top5-item {
          margin-bottom: 20px;
          display: flex;
          justify-content: space-between;

          &:last-child {
            margin-bottom: 0;
          }

          .top5-label {
            height: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 12px;
            color: #222222;
            line-height: 12px;
            text-align: left;
            font-style: normal;
          }

          .top5-value {
            height: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #222222;
            line-height: 12px;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
  }
}

.ind-head {
  display: flex;
  .ind-title {
    height: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    line-height: 14px;
    text-align: left;
    font-style: normal;
  }
  .ind-change {
    height: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #1563ff;
    line-height: 12px;
    text-align: center;
    font-style: normal;
    margin-left: 28px;
    cursor: pointer;
  }
}
::v-deep .el-dialog__header {
  padding: 16px 12px 0;
}
::v-deep .el-dialog__body {
  padding: 12px;
}
.dialog-ind-info {
  box-sizing: border-box;

  .ind-info-content {
    width: 216px;
    background: #f5f7fa;
    border-radius: 8px;
    padding: 8px;

    .ind-info-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .ind-info-label {
        width: 40px;
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        margin-right: 12px;
      }

      .ind-info-value {
        width: 152px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}

.select-wrap {
  display: flex;
  align-items: center;

  .select-item {
    display: flex;
    align-items: center;
    margin-right: 32px;

    &:last-child {
      margin-right: 0;
    }

    .select-label {
      white-space: nowrap;
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 14px;
      text-align: center;
      font-style: normal;
      margin-right: 12px;
    }
  }
}

.separator {
  width: 9px;
  height: 1px;
  background-color: #222222;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  text-align: center;
  font-style: normal;
  margin: 0 8px;
}
</style>
