<template>
  <div class="conditionalFilterPanel">
    <div class="conditionalFilterForm">
      <RenderFilters
        @onForkTreeNode="onForkTreeNode"
        @onAddTreeNode="onAddTreeNode"
        @onAddRoot="onAddRoot"
        @onDeleteTreeNode="onDeleteTreeNode"
        v-bind="$attrs"
        :filter-tree="filterTree"
      />
    </div>
  </div>
</template>

<script>
import { uuid } from "@/utils/index.js"
import RenderFilters from "./RenderFilters.vue"
export default {
  components: {
    RenderFilters,
  },
  props: {
    filterTree: {
      type: Object,
      default: () => {},
    },
    filtersName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {
    filterTree: {
      handler() {
        this.flattenTree = this.initFlattenTree(this.filterTree, {})
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onAddRoot() {
      this.$emit("onAddRoot")
    },
    initFlattenTree(tree, flatten) {
      flatten[tree.id] = tree
      if (tree.children) {
        tree.children.forEach((c) => {
          this.initFlattenTree(c, flatten)
        })
      }
      return flatten
    },
    onAddTreeNode(nodeId) {
      const { flattenTree } = this
      const currentNode = flattenTree[nodeId]
      const newNode = {
        id: uuid(8, 16),
        type: "node",
        name:this.filtersName,
        parent: 0,
      }

      if (currentNode.parent) {
        const parent = flattenTree[currentNode.parent]
        newNode.parent = parent.id
        parent.children.push(newNode)
        flattenTree[newNode.id] = newNode
        this.flattenTree = { ...flattenTree }
        this.$emit("onUpdateTreeNode", this.flattenTree)
      } else {
        const parent = {
          id: uuid(8, 16),
          root: true,
          type: "link",
          rel: "and",
          children: [],
        }

        newNode.parent = parent.id
        parent.children.push(currentNode)
        parent.children.push(newNode)

        delete currentNode.root
        delete flattenTree[currentNode.id]
        currentNode.id = uuid(8, 16)
        currentNode.parent = parent.id

        flattenTree[currentNode.id] = currentNode
        flattenTree[parent.id] = parent
        flattenTree[newNode.id] = newNode

        this.flattenTree = { ...flattenTree }
        this.$emit("onAddTreeNode", parent)
      }
    },
    onForkTreeNode(nodeId) {
      const { flattenTree } = this
      const currentNode = flattenTree[nodeId]
      const cloneNode = {
        ...currentNode,
        id: uuid(8, 16),
        parent: currentNode.id,
      }
      const newNode = {
        id: uuid(8, 16),
        type: "node",
        parent: currentNode.id,
      }

      currentNode.type = "link"
      currentNode.rel = "and"
      currentNode.children = [ cloneNode, newNode ]

      flattenTree[cloneNode.id] = cloneNode
      flattenTree[newNode.id] = newNode
      this.flattenTree = { ...flattenTree }
      // 修改filterTree
      // 深度克隆
      this.$emit(
        "onUpdateTreeNode",
        JSON.parse(JSON.stringify(this.flattenTree))
      )
    },
    onDeleteTreeNode(nodeId) {
      const { flattenTree } = this

      const currentNode = flattenTree[nodeId]
      delete flattenTree[nodeId]
      if (currentNode.parent) {
        const parent = flattenTree[currentNode.parent]
        parent.children = parent.children.filter((c) => c.id !== nodeId)

        if (parent.children.length === 1) {
          const onlyChild = parent.children[0]
          this.refreshTreeId(onlyChild)

          const originParentId = parent.id
          parent.id = onlyChild.id
          parent.type = onlyChild.type
          parent.rel = onlyChild.rel
          parent.filterKey = onlyChild.filterKey
          parent.filterOperator = onlyChild.filterOperator
          parent.filterValue = onlyChild.filterValue
          parent.children = onlyChild.children

          delete flattenTree[originParentId]
          flattenTree[onlyChild.id] = parent
        }

      
        this.$emit(
          "onUpdateTreeNode",
          JSON.parse(JSON.stringify(this.flattenTree))
        )

      } else {
     
        this.flattenTree=null
        this.$emit('onDeleteTreeNode')
      }
    },
    refreshTreeId(treeNode) {
      const { flattenTree } = this
      const oldId = treeNode.id
      delete flattenTree[oldId]

      treeNode.id = uuid(8, 16)
      flattenTree[treeNode.id] = treeNode

      if (treeNode.children) {
        treeNode.children.forEach((c) => {
          c.parent = treeNode.id
          this.refreshTreeId(c)
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.conditionalFilterPanel {
  height: 240px;
}

.conditionalFilterForm {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
