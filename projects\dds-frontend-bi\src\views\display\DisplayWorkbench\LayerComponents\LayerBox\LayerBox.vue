<template>
  <div class="layer-box" :style="layerStyle">
    <LayerTool
      :is-active="isActive"
      :class="{ chartToolActive: layer.type === 1 }"
      :id="layer.widgetId"
    />
    <LayerCore :layer="layer" v-on="$listeners" />
  </div>
</template>

<script>
import LayerTool from "./LayerTool"
import LayerCore from "./LayerCore"
import { mapGetters } from "vuex"
export default {
  components: {
    LayerTool,
    LayerCore
  },
  props: {
    layer: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentLayer: "currentLayer"
    }),
    layerStyle() {
      console.log(this.layer, "layer")
      const {
        backgroundColor,
        borderColor,
        borderWidth,
        borderStyle,
        borderRadius,
        backgroundImage,
        backgroundSize,
        backgroundRepeat
      } = this.layer.params
      const style = {
        backgroundColor,
        borderColor,
        borderWidth: borderWidth + "px",
        borderStyle,
        borderRadius: borderRadius + "px"
      }
      if (backgroundImage) {
        style.background = `url("${backgroundImage}") 0% 0% / ${backgroundSize} ${backgroundRepeat}`
      } else {
        style.backgroundColor = backgroundColor
      }
      return style
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="scss">
.layer-box {
  width: 100%;
  height: 100%;
}
.avue-draggable--active {
  .chartToolActive {
    display: block;
  }
}
</style>
