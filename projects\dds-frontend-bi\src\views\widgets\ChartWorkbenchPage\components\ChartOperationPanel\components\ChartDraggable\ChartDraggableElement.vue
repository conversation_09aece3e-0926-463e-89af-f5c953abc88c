<template>
  <el-dropdown trigger="click" @command="clickItem" :hide-on-click="false">
    <el-tag
      size="small"
      class="draggable-item"
      effect="dark"
      :type="draggableElement.type == 'category' ? '' : 'success'"
      closable
      @close="onItemRemove"
    >
      <span>
        <i class="el-icon-arrow-down el-icon--right" />
        <!-- 是否排序 -->
        <template v-if="draggableElement.sort">
          <svg-icon
            v-if="draggableElement.sort.sortType === 'asc'"
            icon-class="sort-asc"
            class-name="field-icon-sort"
          />
          <svg-icon
            v-if="draggableElement.sort.sortType === 'desc'"
            icon-class="sort-desc"
            class-name="field-icon-sort"
          />
        </template>

        <span class="item-span-style">
          <!-- 聚合 -->
          {{ draggableElement.agg && formatType(draggableElement.agg) }}
          <!-- 字段名 -->
          {{ draggableElement.name }}
          <!-- 别名 -->
          {{
            draggableElement.alias && "[" + draggableElement.alias + "]"
          }}</span
        >
      </span>
    </el-tag>
    <el-dropdown-menu slot="dropdown">
      <components
        :is="item"
        v-for="item in action"
        :key="item"
        :action="item"
      />
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import OrderAction from "../ChartFieldAction/OrderAction/OrderAction.vue"
import AliasAction from "../ChartFieldAction/AliasAction/AliasAction.vue"
import AggAction from "../ChartFieldAction/AggAction/AggAction.vue"
import FilterAction from "../ChartFieldAction/FilterAction/FilterAction.vue"
import FormatAction from "../ChartFieldAction/FormatAction/FormatAction.vue"
export default {
  components: {
    order: OrderAction,
    alias: AliasAction,
    agg: AggAction,
    filters: FilterAction,
    format: FormatAction,
  },
  props: {
    draggableElement: {
      type: Object,
      default: () => {},
    },
    action: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    formatType(val) {
      switch (val) {
      case "sum":
        return "[总计]"
      case "avg":
        return "[平均数]"
      case "count":
        return "[计数]"
      case "COUNTDISTINCT":
        return "[去重计数]"
      case "max":
        return "[最大值]"
      case "min":
        return "[最小值]"
      default:
        break
      }
    },
    // 移除
    onItemRemove() {
      this.$emit("onItemRemove", this.draggableElement)
    },
  },
}
</script>

<style scoped lang="scss">
.el-dropdown {
  width: 100%;
}
.draggable-item {
  margin: 0 3px 5px 3px;
  text-align: left;
  height: 24px;
  line-height: 22px;
  display: inline-block;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.draggable-item:hover {
  opacity: 0.8;
  cursor: pointer;
}
::v-deep .el-tag--small {
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    display: flex;
    align-items: center;
  }
}
</style>
