import { decodeMetricName } from "../component/util.js"
import { getFormattedValue } from "../component/Config/Format/util"
import { getFieldAlias } from "../component/Config/Field/util"
import { getLegendOption, getLabelOption } from "./util"
export default function(chartProps) {
  const { data, cols, metrics, chartStyles, color } = chartProps

  const { label, legend, radar,  } = chartStyles

  const labelOption = {
    label: getLabelOption("radar", label, metrics),
  }
  let dimensions = []
  if (cols.length) {
    dimensions = dimensions.concat(cols)
  }

  const metricsNames = metrics.map((m) => m.field.alias ?? m.displayName)

  let seriesData
  let indicator
  let indicatorMax = -Infinity
  let legendData
  if (!dimensions.length) {
    if (color.length) {
      dimensions = dimensions.concat(color.map((c) => c.name))
    }
    const metricsData = !data.length
      ? []
      : metrics.map((m) => data[0][`${m.agg}(${m.displayName})`])
    seriesData = !data.length ? [] : [ { value: metricsData } ]
    indicatorMax = Math.max(...metricsData)
    indicatorMax = indicatorMax + Math.round(indicatorMax * 0.1)
    indicator = metrics.map((m) => ({
      name: m.displayName,
      max: indicatorMax,
    }))
  } else {
    legendData = metricsNames
    const dimension = dimensions[0]
    const indicatorData = {}
    const dimensionData = metricsNames.reduce((acc, name) => {
      return {
        ...acc,
        [name]: {},
      }
    }, {})

    data.forEach((row) => {
      if (!indicatorData[row[dimension.displayName]]) {
        indicatorData[row[dimension.displayName]] = -Infinity
      }

      metrics.forEach((m) => {
        const name = m.field.alias ?? m.displayName
        const cellVal = row[`${m.agg}(${m.displayName})`]
        indicatorMax = Math.max(indicatorMax, cellVal)
        if (!dimensionData[name][row[dimension.displayName]]) {
          dimensionData[name][row[dimension.displayName]] = 0
        }
        dimensionData[name][row[dimension.displayName]] += cellVal
      })
    })
    indicator = Object.keys(indicatorData).map((name) => ({
      name,
      max: indicatorMax + Math.round(indicatorMax * 0.1),
    }))
    seriesData =
      data.length > 0
        ? Object.entries(dimensionData).map(([ name, value ], ) => ({
          name,
          value: Object.values(value),
          label: {
            show: label.show,
          },
        }))
        : []
  }

  const tooltip = {
    // ...TOOLTIP_STYLE,
    confine: true,
    formatter(params) {
      const { dataIndex, data, color } = params
      let tooltipLabels = []
      if (dimensions.length) {
        const metric = metrics[dataIndex]
        tooltipLabels.push(
          getFieldAlias(metric.field, {}) || decodeMetricName(metric.name)
        )
        tooltipLabels = tooltipLabels.concat(
          indicator.map(
            ({ name }, idx) =>
              `${name}: ${getFormattedValue(data.value[idx], metric.format)}`
          )
        )
        if (color) {
          tooltipLabels[0] =
            `<span style="background:${color};width:10px;height:10px;border-radius:50%;display:inline-block;margin-right: 5px;"></span>` +
            tooltipLabels[0]
        }
      } else {
        tooltip,
        (tooltipLabels = tooltipLabels.concat(
          indicator.map(
            ({ name }, idx) =>
              `${name}: ${getFormattedValue(
                data.value[idx],
                metrics[idx].format
              )}`
          )
        ))
      }
      return tooltipLabels.join("<br/>")
    },
  }
  return {
    tooltip,
    legend: legendData && getLegendOption(legend, legendData),
    radar: {
      // type: 'log',
      indicator,
      ...radar,
    },
    series: [
      {
        name: "",
        type: "radar",
        data: seriesData,
        ...labelOption,
      },
    ],
  }
}
