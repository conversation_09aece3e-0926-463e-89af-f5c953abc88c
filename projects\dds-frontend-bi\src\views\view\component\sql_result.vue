<template>
  <el-table
    :data="sqlResult"
    :row-style="{ height: 0 + 'px' }"
    :cell-style="{ padding: 0 + 'px' }"
    border
    stripe
    size="mini"
    height="100%"
    style="margin: 0; width: 100%"
    v-loading="isRunning"
  >
    <el-table-column
      v-for="item in sqlColumns"
      :prop="item.name"
      :label="item.name"
      :key="item"
    >
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: "sql-result",
  props: {
    sqlResult: {
      type: Array,
      default: ()=>[],
    },
    sqlColumns: {
      type: Array,
      default: ()=>[],
    },
    isRunning: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
<style scoped lang="scss"></style>
