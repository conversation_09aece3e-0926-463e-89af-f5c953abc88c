<template>
  <div class="right-index">
    <div class="title">派生指标&复合指标</div>
    <div class="tabs">
      <div class="tab-item active">按指标域</div>
      <div class="tab-item">按标签</div>
    </div>
    <el-input
      style="width: 196px; margin: 0 12px"
      placeholder="请输入指标名称"
      v-model="filterText"
      suffix-icon="el-icon-search"
    ></el-input>
    <el-tree
      class="index-tree"
      ref="treeModel"
      draggable
      :data="data"
      :allow-drop="() => false"
      :allow-drag="allowDrag"
      :props="defaultProps"
      :filter-node-method="filterNode"
      @node-drag-start="handleIndexTreeDragStart"
      @node-drag-end="handleIndexTreeDragEnd"
    ></el-tree>
  </div>
</template>

<script>
export default {
  props: {
    isIndexDragStart: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: "",
      data: [
        {
          label: "学校",
          children: [
            {
              label: "计算机学院",
              children: [
                {
                  label: "软件工程专业",
                  children: [
                    {
                      label: "张三"
                    },
                    {
                      label: "李四"
                    }
                  ]
                },
                {
                  label: "网络工程专业",
                  children: [
                    {
                      label: "王五"
                    },
                    {
                      label: "赵六"
                    }
                  ]
                }
              ]
            },
            {
              label: "文学院",
              children: [
                {
                  label: "汉语言文学专业",
                  children: [
                    {
                      label: "小红"
                    },
                    {
                      label: "小明"
                    }
                  ]
                },
                {
                  label: "英语专业",
                  children: [
                    {
                      label: "小花"
                    },
                    {
                      label: "小杰"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeModel.filter(val)
    }
  },
  methods: {
    // 过滤节点
    filterNode(value, data) {
      console.log(value)
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // tree节点能否被拖拽
    allowDrag(node) {
      return node.isLeaf
    },
    // 群体tree拖拽开始
    handleIndexTreeDragStart(node, event) {
      this.$emit("update:isIndexDragStart", true)
      let arr = []
      if (node.isLeaf) {
        arr = [node.data]
      } else {
        arr = this.getAllNodes(node.childNodes)
      }
      event.dataTransfer.setData("itemType", "indexType")
      event.dataTransfer.setData("indexList", JSON.stringify(arr))
    },
    getAllNodes(treeData) {
      const result = []
      function traverse(node) {
        if (node.childNodes.length) {
          node.childNodes.forEach(child => {
            traverse(child)
          })
        } else {
          result.push(node.data)
        }
      }

      treeData.forEach(node => {
        traverse(node)
      })

      return result
    },
    handleIndexTreeDragEnd() {
      this.$emit("update:isIndexDragStart", false)
    }
  }
}
</script>

<style scoped lang="scss">
.right-index {
  width: 224px;
  height: 100%;
  background: #ffffff;
  border-radius: 5px 0px 0px 5px;
  border: 1px solid #e4e7ed;
  .title {
    width: 224px;
    height: 40px;
    border-radius: 5px 0px 0px 0px;
    box-shadow: inset 0px -1px 0px 0px #ebedf0;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2f3338;
    line-height: 40px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding-left: 12px;
    box-sizing: border-box;
  }
  .index-tree {
    padding: 0 12px;
    box-sizing: border-box;
    height: calc(100% - 120px);
    overflow: auto;
    ::v-deep .el-tree-node__label {
      width: 100%;
    }
    // ::v-deep .el-tree-node.is-current.is-focusable {
    //
    //   .el-tree-node__label {
    //     background: #f4f7ff;
    //     box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    //       0px 6px 6px -4px rgba(0, 42, 128, 0.12);
    //     border-radius: 4px;
    //     border: 1px solid #1563ff;
    //     color: #1563ff;
    //   } }

    //滚动条样式
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 6px;
      height: 2px;
      background-color: #cfd6e6;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: transparent;
      border-radius: 6px;
    }
  }
  .tabs {
    display: flex;
    padding: 0 12px;
    box-sizing: border-box;
    margin: 16px 0;

    .tab-item {
      width: 96px;
      height: 30px;
      border-radius: 15px;
      border: 1px solid #dcdfe6;
      cursor: pointer;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #606266;
      line-height: 30px;
      text-align: center;
      &:first-child {
        margin-right: 8px;
      }
      &.active {
        background: rgba(21, 99, 255, 0.1);
        color: #1563ff;
        font-weight: 500;
      }
    }
  }
}
</style>
<style lang="scss">
#project_frame
  .index-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .index-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.is-leaf) {
  position: relative;
  background: #f4f7ff;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;

  &::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 9px;
    width: 12px;
    height: 10px;
    background: url("~@/assets/images/tree-icon.png") no-repeat center;
  }
  .el-tree-node__label {
    background: transparent;
  }
}

#project_frame .index-tree .el-tree-node {
  border: 1px solid transparent !important;
}
</style>
