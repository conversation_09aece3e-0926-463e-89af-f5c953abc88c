import line from "./line"
import bar from "./bar"
import pie from "./pie"
import doubleYAxis from "./doubleYAxis"
import radar from "./radar"
import wordCloud from "./wordCloud"
import bubble from "./bubble"
import map from "./map"
import funnel from "./funnel"
import liquidFill from "./liquidFill"
export default function(type, chartProps, drillOptions) {
  switch (type) {
  case "line":
    return line(chartProps, drillOptions)
  case "bar":
    return bar(chartProps, drillOptions)
  case "pie":
    return pie(chartProps, drillOptions)
  case "doubleYAxis":
    return doubleYAxis(chartProps, drillOptions)
  case "radar":
    return radar(chartProps)
  case "wordCloud":
    return wordCloud(chartProps)
  case "bubble":
    return bubble(chartProps)
  case "map":
    return map(chartProps)
  case "funnel":
    return funnel(chartProps)
  case "liquidFill":
    return liquidFill(chartProps)
    // case 'scatter': return scatter(chartProps, drillOptions)
    // case 'funnel': return funnel(chartProps, drillOptions)
    // // case 'area': return area(chartProps)
    // case 'sankey': return sankey(chartProps)
    // case 'parallel': return parallel(chartProps)
    // case 'wordCloud': return wordCloud(chartProps)
    // case 'waterfall': return waterfall(chartProps)
    // case 'gauge': return gauge(chartProps, drillOptions)
  }
}
