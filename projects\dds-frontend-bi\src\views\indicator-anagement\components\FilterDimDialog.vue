<template>
  <el-dialog
    title="请选择要添加的过滤维度"
    :visible.sync="dialogVisible"
    width="550px"
    append-to-body
  >
    <div v-if="allFilterDims.length">
      <el-input
        v-model="searchText"
        placeholder="请输入关键字搜索维度"
        clearable
        size="small"
        style="margin-bottom: 10px;"
      />
      <el-checkbox-group
        v-model="filterDimsCols"
        @change="handleFilterDimsChange"
      >
        <el-checkbox
          v-for="(item, index) in filteredFilterDims"
          :label="item[props.value]"
          :key="index"
        >
          {{ item[props.label] }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <span v-else>暂无维度选项</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleAddDims">添  加</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    filterDims: {
      type: Array,
      default: () => []
    },
    props:{
      type:Object,
      default:() => ({
        label:"levelName",
        value:"dimCol",
        valueList:"wdzval"
      })
    }
 
  },
  data() {
    return {
      searchText:"",
      dialogVisible: false,
      allFilterDims: [],
      filterDimsCols: [],
      tempFilterDims: [],
    }
  },
  computed: {
    filteredFilterDims() {
      if (!this.searchText) return this.allFilterDims
      const text = this.searchText.toLowerCase()
      return this.allFilterDims.filter(item =>
        (item[this.props.label] && item[this.props.label].toLowerCase().includes(text)) ||
        (item[this.props.value] && item[this.props.value].toLowerCase().includes(text))
      )
    }
  },
  created() {
  },
  mounted() {},
  watch: {},
  methods: {
    open(allFilterDims) {
      this.searchText= ''
      this.dialogVisible = true
      this.filterDimsCols = this.filterDims.map(item => item[this.props.value])
      console.log(this.filterDimsCols ,'this.filterDimsCols ')

      this.allFilterDims = allFilterDims
      this.tempFilterDims=[]
    },
    handleFilterDimsChange(val) {
      console.log(val,'valvalvalvalval')
      console.log(this.filterDims,'this.filterDims')
      // 获取当前选择的维度临时存储filterDims
      this.tempFilterDims = this.allFilterDims
        .filter(item => val.includes(item[this.props.value]))
        .map(item => {
          return {
            ...item,
            lxbm: item.dimType,
            dimType:2,
            clusterCodes:this.filterDims.find(i => i[this.props.value] === item[this.props.value])?.clusterCodes || [],
            [this.props.valueList]:this.filterDims.find(i => i[this.props.value] === item[this.props.value])?.[this.props.valueList] || [],
          }
        })
        console.log(this.tempFilterDims,'this.tempFilterDims')
    },
    handleAddDims() {
     this.handleFilterDimsChange(this.filterDimsCols)
     
      this.$emit("update:filterDims",  this.tempFilterDims)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">

.el-checkbox-group{
  display: flex;
  flex-direction: column;
  height: 200px;
  overflow: auto;
    //滚动条样式
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 6px;
      height: 2px;
      background-color: #cfd6e6;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: transparent;
      border-radius: 6px;
    }
  .el-checkbox{
    margin-bottom: 10px;
  }
}
</style>
