<template>
  <div>
    <!-- 头部 -->
    <DT-Header :content="title" @back="$emit('handleBack')" />
    <!-- 表单 -->
    <DT-Form
      v-model="formData"
      :render="formRender"
      @confirm="handleConfirm"
      label-width="120px"
    >
      <!-- 行内插槽 -->
      <template #slotA v-if="data.id">
        <el-tag
          v-if="form.uploadPercentage == 100 && excelName"
          closable
          @close="handleClose(data.id)"
        >
          {{ excelName }}
        </el-tag>
        <el-button v-else icon="el-icon-download" @click="dialogVisible = true">
          导入
        </el-button>
        <el-button type="text" @click="downloadTemplates">
          点击此处下载模板
        </el-button>
      </template>
    </DT-Form>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form" label-width="100px" style="width: 100%">
        <el-form-item label="选择导入文件">
          <!-- webkitdirectory代表选择文件夹 -->
          <input
            type="file"
            id="file"
            ref="fileInput"
            hidden
            @change="fileChange"
            accept=".xls,.xlsx"
          />
          <el-input
            placeholder="文件夹目录"
            v-model="excelName"
            class="input-with-select"
          >
            <el-button slot="append" type="primary" @click="btnChange">
              浏览
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <div>
            {{
              form.uploadPercentage == 100
                ? "成功上传"
                : uploadIng
                ? "正在上传…"
                : "等待上传.."
            }}
          </div>
          <el-progress
            :show-text="false"
            :percentage="form.uploadPercentage"
          ></el-progress>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios"
// import { exportExcel } from "@/utils"
export default {
  name: "dict-form-view",
  props: {
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 表单数据（编辑）
    data: {
      type: Object,
      default: () => {}
    },
    excelName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploadIng: false,
      form: {
        name: "",
        uploadPercentage: 0
      },
      // 表单数据
      formData: {
        type: 1,
        bqlxid: 1
      },
      // 表单渲染配置
      formRender: [
        // 名单名称
        {
          label: "名单名称",
          type: "input",
          key: "bqmc",
          props: {
            placeholder: "请输入名单名称"
          },
          rules: [
            { required: true, message: "请输入名单名称", trigger: "blur" }
          ]
        },
        // 名单描述
        {
          label: "名单描述",
          type: "input",
          key: "bqms",
          props: {
            type: "textarea",
            placeholder: "请输入名单描述"
          }
        },
        // 标签类型
        {
          label: "关联指标类型",
          type: "select",
          key: "bqlxid",
          option: [
            {
              label: "手动标记",
              value: 1
            },
            {
              label: "自动生成",
              value: 0
            }
          ],
          props: {
            multiple: true,
            placeholder: "请选择标签类型"
          },
          rules: [
            { required: true, message: "请选择标签类型", trigger: "blur" }
          ]
        },

        // 学生名单导入
        {
          label: this.data.id ? "白名单" : "",
          type: "slot",
          key: "slotA",
          slotName: "slotA"
        }
      ]
    }
  },
  created() {
    // “编辑”时回显数据
    if (Object.keys(this.data).length > 0) {
      this.formData = { ...this.data }
    }
  },
  methods: {
    handleSave() {
      if (this.form.uploadPercentage === 100) {
        this.dialogVisible = false
      } else {
        this.$message.error("请等待上传完成或者选择上传文件")
      }
    },
    // 提交
    handleConfirm(form) {
      let fn = this.data.id
          ? this.$httpBi.focusGroups.updateLabel
          : this.$httpBi.focusGroups.addLabel,
        param = {
          ...this.formData,
          bqmc: form.bqmc,
          bqms: form.bqms
        }
      this.$dt_loading.show()
      fn(param)
        .then(() => {
          this.$dt_loading.hide()
          this.$message.success(
            this.data.id
              ? this.$t("message.success.update")
              : this.$t("message.success.create")
          )
          this.$emit("handleSuccess")
          this.$emit("handleBack")
        })
        .catch(() => this.$dt_loading.hide())
    },
    fileChange() {
      this.uploadIng = true
      const fileInput = this.$refs.fileInput
      const file = fileInput.files[0]
      this.$emit("update:excelName", file.name)

      // 创建 FormData 对象
      const formData = new FormData()
      formData.append("file", file)
      formData.append("id", this.data.id)

      // 发送文件上传请求
      axios
        .post("/api/dds-school-hnnd/label/importStudent", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            ...this.$utils.auth.getAdminheader()
          },
          onUploadProgress: progressEvent => {
            this.form.uploadPercentage = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            )
          }
        })
        .then(() => {
          // 处理上传成功后的逻辑
        })
        .catch(() => {
          // 处理上传失败或错误的逻辑
        })
    },
    btnChange() {
      var file = document.getElementById("file")
      file.click()
      this.uploadIng = false
      this.form.uploadPercentage = 0
    },
    handleClose(id) {
      this.uploadIng = false
      this.form.uploadPercentage = 0
      this.$emit("closeExcel", id)
    },
    // 下载模板
    async downloadTemplates() {
      // exportExcel("/api/dds-school-hnnd/label/templateDownload", {}, "模板")
    }
  }
}
</script>
