<template>
  <keep-alive>
    <DT-View
      class="cardList"
      :show="showPage"
    >
      <el-dropdown @command="handleCommand">
        <!-- @click="handleAddOrEdit('', 0)" -->
        <el-button type="primary" icon="el-icon-plus">新建指标 </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            :command="{
              isVisc: 0,
              id: 0,
            }"
          >
            SQL视图
          </el-dropdown-item
          >
          <el-dropdown-item
            :command="{
              isVisc: 1,
              id: 0,
            }"
          >
            表视图
          </el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>

      <el-button
        type="primary"
        style="margin-left: 20px"
        icon="el-icon-plus"
        @click="addGroup()"
      >
        新建主题库
      </el-button>

      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="code"
        type="expand"
      >
        <el-table-column>
          <template slot="header">
            <i class="el-icon-folder" style="color: silver" /> 主题 ||
            <i
              class="el-icon-document-checked"
              style="color: deepskyblue"
            />数据集
          </template>
          <template slot-scope="{ row }">
            <span v-show="row.type == 0">
              <i class="el-icon-wallet" style="color: silver" /> {{ row.name }}
            </span>
            <span v-show="row.type == 1">
              <i
                class="el-icon-document-checked"
                style="color: deepskyblue"
              />{{ row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="编码" align="center">
          <template slot-scope="{ row }">
            <span v-show="row.type == 0">
              <i class="el-icon-wallet" />
            </span>
            <span v-show="row.type == 1">
              {{ row.code }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceName" label="数据源" align="center">
          <template slot-scope="{ row }">
            <span v-show="row.type == 0">
              <i class="el-icon-wallet" />
            </span>
            <span v-show="row.type == 1">
              {{ row.sourceName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="usedCount" label="看板应用" align="center">
          <template slot-scope="{ row }">
            <span v-show="row.type == 0">
              <i class="el-icon-wallet" />
            </span>
            <span v-show="row.type == 1"> {{ row.usedCount }} 个 </span>
          </template>
        </el-table-column>

        <el-table-column prop="usedCount" label="是否支持预警" align="center">
          <template slot-scope="{ row }">
            <span v-show="row.type == 0">
              <i class="el-icon-wallet" />
            </span>
            <span v-show="row.type == 1">
              {{ row.isSimple == 1 ? "支持" : "不支持" }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" align="center">
        </el-table-column>
        <el-table-column prop="operate" label="操作" align="center">
          <template slot-scope="{ row }">
            <span v-show="row.type == 1">
              <el-link
                :underline="false"
                type="primary"
                @click="changeGroup(row)"
              >变更主题库
              </el-link>
              <div class="el-divider el-divider--vertical" />
              <el-link
                :underline="false"
                type="primary"
                @click="handleAddOrEdit(row.id, '', row.isVisc)"
              >修改
              </el-link>
              <div class="el-divider el-divider--vertical" />
              <el-link
                :underline="false"
                type="primary"
                @click="delLine(row)"
              >删除
              </el-link>
            </span>

            <span v-show="row.type == 0">
              <el-dropdown @command="handleCommand">
                <!-- @click="handleAddOrEdit('', 0)" -->
                <el-link :underline="false" type="primary">新建指标 </el-link>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="{
                      isVisc: 0,
                      id: row.id,
                    }"
                  >SQL视图</el-dropdown-item
                  >
                  <el-dropdown-item
                    :command="{
                      isVisc: 1,
                      id: row.id,
                    }"
                  >表视图</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
              <div class="el-divider el-divider--vertical" />
              <el-link
                :underline="false"
                type="primary"
                @click="updGroup(row)"
              >修改
              </el-link>
              <div class="el-divider el-divider--vertical" />
              <el-link
                :underline="false"
                type="primary"
                @click="delGroup(row.id)"
              >删除
              </el-link>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
        title="新建主题库"
        v-if="dialogVisible"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible"
        width="30%"
      >
        <span slot="footer" class="dialog-footer">
          <el-form ref="editGroup" :model="editGroup" label-width="50px">
            <el-form-item label="上级:" prop="">
              <treeselect
                v-model="editGroup.parentId"
                :options="groups"
                style="width: 100%"
                :clearable="false"
                :normalizer="normalizer"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item label="名称:">
              <el-input v-model="editGroup.name"></el-input>
            </el-form-item>
            <el-form-item label="描述:">
              <el-input
                type="textarea"
                :rows="2"
                v-model="editGroup.description"
              ></el-input>
            </el-form-item>
          </el-form>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveGroup">确 定</el-button>
        </span>
      </el-dialog>

      <el-dialog
        title="移动到主题库"
        v-if="changeGroupDialogVisible"
        :close-on-click-modal="false"
        :visible.sync="changeGroupDialogVisible"
        width="20%"
      >
        <treeselect
          v-model="editView.parentId"
          :options="groups"
          :clearable="false"
          :normalizer="normalizer"
          placeholder="请选择"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="changeGroupDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveChangeGroup">确 定</el-button>
        </span>
      </el-dialog>
    </DT-View>
  </keep-alive>
</template>
<script>
import Request from "@/service"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  name: "data-source-list",
  components: {
    Treeselect,
  },
  data() {
    return {
      showPage: false,
      show: false,
      loading: true,
      dialogVisible: false,
      changeGroupDialogVisible: false,
      expandAll: false,
      queryForm: {
        code: "",
        name: "",
        sourceName: "",
      },
      total: 0,
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      editGroup: {},
      editView: {},
      groups: [],
    }
  },
  created() {
    this.initData()
    // this.initQuery();
  },
  methods: {
    handleCommand({ isVisc, id }) {
      this.handleAddOrEdit("", id, isVisc)
    },
    // 初始化分组
    initGroup() {
      Request.view
        .getAllViewGroup()
        .then((res) => {
          let rt = [ { code: 0, name: "根目录" } ]
          this.groups = rt.concat(res.data)
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    normalizer(node) {
      return {
        id: node.code,
        label: node.name,
        children: node.children,
      }
    },
    updGroup(row) {
      this.dialogVisible = true
      this.initGroup()
      this.editGroup = row
    },
    delGroup(id) {
      this.$confirm(
        '此操作将删除选中数据, 是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: "warning",
        }
      ).then(() => {
        Request.view
          .delViewGroup({ id: id })
          .then(() => {
            this.$message.success("操作成功")
            this.initData()
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
      })
    },
    addGroup() {
      this.dialogVisible = true
      this.initGroup()
      this.editGroup = {
        id: "",
        name: "",
        description: "",
        parentId: 0,
      }
    },
    saveGroup() {
      this.$refs["editGroup"].validate((valid) => {
        // 表单验证
        if (valid) {
          // 修改
          if (this.editGroup.id) {
            Request.view
              .updViewGroup(this.editGroup)
              .then(() => {
                this.$message.success("操作成功")
              })
              .catch(() => {
                this.$message.error("操作失败")
              })
              .finally(() => {
                this.dialogVisible = false
                this.initData()
              })
          } else {
            Request.view
              .addViewGroup(this.editGroup)
              .then(() => {
                this.$message.success("操作成功")
              })
              .catch(() => {
                this.$message.error("操作失败")
              })
              .finally(() => {
                this.dialogVisible = false
                this.initData()
              })
          }
        }
      })
    },
    handleAddOrEdit(id, group, isVisc) {
      // 如果有id为编辑
      if (id) {
        if (isVisc) {
          this.$router.push(`EditTableView?isFullPage=true&id=${id}`)
        } else {
          this.$router.push(`EditView?isFullPage=true&id=${id}`)
        }
      } else {
        if (isVisc) {
          this.$router.push(`EditTableView?isFullPage=true&group=${group}`)
        } else {
          // 新增
          this.$router.push(`EditView?isFullPage=true&group=${group}`)
        }
      }
    },

    delLine(row) {
      this.$confirm(
        '此操作将删除选中数据, 是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: "warning",
        }
      ).then(() => {
        this.loading = true
        Request.view
          .delete({ id: row.id })
          .then((res) => {
            this.$message.success(res.data)
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
          .finally(() => {
            this.initData()
          })
      })
    },
    // 查询
    searchData() {
      this.currentPage = 1
      this.initData()
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.searchData()
    },
    // 查询条件初始化
    initQuery() {
      Request.view
        .getAllSource()
        .then((res) => {
          for (let i = 0; i < res.data.length; i++) {
            this.allSource.push({
              text: res.data[i].name,
              value: res.data[i].name,
            })
          }
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    changeGroup(editView) {
      this.changeGroupDialogVisible = true
      this.initGroup()
      this.editView = editView
    },
    saveChangeGroup() {
      this.changeGroupDialogVisible = false
      Request.view
        .changeViewGroup(this.editView)
        .then(() => {
          this.$message.success("操作成功")
        })
        .catch(() => {
          this.$message.error("操作失败")
        })
        .finally(() => {
          this.dialogVisible = false
          this.initData()
        })
    },
    initData() {
      this.showPage = false
      this.$dt_loading.show()
      Request.view
        .getGroupAndView()
        .then((res) => {
          this.tableData = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.showPage = true
          this.$dt_loading.hide()
        })
    },
    // 页面大小变化
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.initData()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.initData()
    },
  },
}
</script>

<style lang="scss" scoped>
.cardList {
  .title {
    margin-top: 100px;
  }

  .buttons {
    margin-top: 50px;
  }
}

.last_item {
  width: auto;
}
</style>
