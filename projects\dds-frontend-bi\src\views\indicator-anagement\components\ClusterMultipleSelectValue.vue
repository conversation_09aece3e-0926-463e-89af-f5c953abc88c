<template>
  <el-select-v2
    v-model="selectedValue"
    filterable
    multiple
    collapse-tags
    @change="handleChange"
    ref="my-select"
    style="width: 100%"
    v-bind="$attrs"
    :placeholder="placeholder"
    :props="props"
    :options="options"
  >
    <template #header>
      <div class="checkboxWrapper" v-if="isSelectedAll">
        <el-checkbox
          v-model="checked"
          @change="checkChange"
          :true-label="true"
          :false-label="false"
        >
          {{ isSelectedAllName }}
        </el-checkbox>
      </div>
    </template>
  </el-select-v2>
</template>

<script>
export default {
  name: "ClusterMultipleSelect",
  directives: {},
  props: {
    value: {
      type: [String, Number, Object],
      default: null,
    },
    size: {
      type: Number,
      default: 20,
    },
    placeholder: {
      type: String,
      default: "请选择维度值",
    },
    fetchDataMethod: {
      type: Function,
      required: true,
    },
    levelCode: {
      type: String,
      required: true,
    },
    dimId: {
      type: String,
      default: "",
    },
    indType: {
      type: String,
      default: "",
    },
    isSelectedAll: {
      type: Boolean,
      default: true,
    },
    clusterList: {
      type: Array,
      default: () => [],
    },
    isSelectedAllName: {
      type: String,
      default: "全部",
    },
    dimValues: {
      type: Array,
      default: () => [],
    },
    valueCode: {
      type: String,
      default: "valueCode",
    },
    clusterNameMapValue: {
      type: Object,
      default: () => ({}),
    },
    clusterCodeMapClusterName: {
      type: Object,
      default: () => ({}),
    },
    dimCol: {
      type: String,
      default: "",
    },
    props: {
      type: Object,
      default() {
        return {
          label: "cluName",
          value: "clusterCode",
        }
      },
    },
  },
  data() {
    return {
      selectedValue: this.value, // 双向绑定的值
      options: [], // 存储下拉框选项
      loading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: "",
      total: 0,
      isDropdownOpen: false, // 下拉框是否打开
      tempValue: [],
      checked: false,
      clusterNameMapValue_temp: {},
     clusterCodeMapClusterName_temp: {},
    }
  },
  watch: {
    levelCode: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      },
    },
    // 监听父组件传入的value，保持双向绑定
    value: {
      handler(newVal) {
        this.selectedValue = newVal
      },
      immediate: true,
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    checkChange(val) {
      if (val) {
        // 全选：只选中“全部”标识码
        this.selectedValue = this.options.map((item) => item[this.props.value])
      } else {
        // 取消全选：清空
        this.selectedValue = []
      }
      this.$emit("input", this.selectedValue)
      this.$emit("change", this.selectedValue)
      this.getDimValueByClusterCodes(this.selectedValue)
    },
    async fetchData() {
      const { data } = await this.$httpBi.api.paramPostQuery(
        "/DimManage/getDimClusterByLevelCode",
        {
          levelCode: this.levelCode,
        }
      )
      this.$emit("update:clusterList", data)
      this.options = data
      this.loading = false
    },
    handleChange(val) {
      if (val.length === 0) {
        this.checked = false
      } else {
        this.checked = val.length === this.options.length
      }
  
      this.getDimValueByClusterCodes(val)
    },
    // 批量通过聚类编码查维度值
    async getDimValueByClusterCodes(val) {
      if (val.length === 0) {
        this.$emit("update:clusterNameMapValue", {})
        return
      }

      this.clusterNameMapValue_temp={}
      await Promise.all(val.map(async (v) => {  
        let cluName = this.options.find(
          (item) => item.clusterCode === v
        ).cluName

          const { data } = await this.$httpBi.api.paramPost(
            "/DimManage/getDimValueByClusterCodes",
            [v]
          )
          this.$set(
            this.clusterNameMapValue_temp,
            v,
            data.map((item) => ({
              dimVal: item[this.valueCode],
              dimCol: this.dimCol,
            }))
          )
          this.clusterCodeMapClusterName_temp[v]=cluName
          this.$emit(
            "update:clusterCodeMapClusterName",
            this.clusterCodeMapClusterName_temp
          )
          this.$emit(
            "update:clusterNameMapValue",
            this.clusterNameMapValue_temp
          )
      }))
      this.$emit("input", val)
      this.$emit("change", val)
    },
  },
}
</script>

<style scoped lang="scss">
/* 可根据需求自定义样式 */

::v-deep .el-select__tags {
  display: flex;
  flex-wrap: nowrap;
}
</style>
