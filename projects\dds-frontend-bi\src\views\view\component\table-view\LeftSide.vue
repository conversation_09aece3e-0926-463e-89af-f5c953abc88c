<template>
  <div class="leftSide">
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="rules"
    >
      <el-form-item
        label=""
        prop="groupId"
      >
        <el-cascader
          v-model="form.groupId"
          :options="options"
          @change="viewSelect"
          :show-all-levels="false"
          filterable
          :props="{
            value: 'code',
            label: 'name',
            emitPath: false,
            checkStrictly: true,
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="form.code"
          placeholder="编码"
        ></el-input>
      </el-form-item>
      <el-form-item prop="name">
        <el-input
          v-model="form.name"
          placeholder="名称"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="form.description"
          placeholder="描述"
        ></el-input>
      </el-form-item>
      <el-form-item prop="sourceId">
        <el-select
          v-model="form.sourceId"
          placeholder="数据源"
          style="width: 100%"
        >
          <el-option
            v-for="item in sources"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <DraggableSource
      v-bind="$attrs"
      v-on="$listeners"
      :tables="tables"
    />
  </div>
</template>
<script>
import DraggableSource from "./DraggableSource.vue"
export default {
  components: {
    DraggableSource,
  },
  props: {
    form: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    var checkCode=(rule,value,callback) => {
      if (!value) {
        return callback(new Error("编码不能为空"))
      }
      console.log('%cLeftSide.vue line:63 object','color: #007acc;',this.form.id,)
      this.$httpBi.view
        .checkViewCode({
          id: this.form.id||'-1',
          code: value,
        })
        .then((res) => {
          if (res.data) {
            return callback(new Error("编码已存在"))
          } else {
            return callback()
          }
        })
        .catch(() => { })
    }
    return {
      viewId: "",
      options: [],
      sources: [],
      tables: [],
      rules: {
        name: [ { required: true,message: "请输入名称",trigger: "blur" } ],
        code: [
          { required: true,message: "请输入编码",trigger: "blur" },
          { validator: checkCode,trigger: "blur" },
        ],
      },
    }
  },
  computed: {},
  created() {
    this.initView()
    this.initSource()
  },
  mounted() { },
  watch: {
    "form.sourceId": {
      handler(val) {
        this.sourceDb(val)
      },
    },
  },
  methods: {
    // 初始化视图目录
    async initView() {
      const { data }=await this.$httpBi.view.getAllViewGroup()
      this.options=[ { code: "0",name: "根目录",children: data } ]
    },
    viewSelect() {
      this.$emit("viewSelect",this.viewId)
    },
    // 数据源库
    async sourceDb(sourceId) {
      this.dbs=[]
      if (sourceId) {
        // 获取name
        let sourceName=null
        console.log(sourceName,"sourceName")
        if (this.sources.length) {
          sourceName=this.sources.find((item) => item.id===sourceId).name
        } else {
          sourceName=this.form.source.name
        }
        const { data }=await this.$httpBi.view.getTables({
          id: sourceId,
          dbName: sourceName,
        })
        this.$emit("changeSourceDb",sourceId,sourceName)
        this.tables=data.tables
      }
    },
    // 初始化数据源
    async initSource() {
      const { data }=await this.$httpBi.view.getSources()
      this.sources=data
    },
    validate(callback) {
      // 这个form是子组件内部el-form 的ref="form"
      this.$refs.ruleFormRef.validate((valid) => {
        callback(valid)
      })
    },
  },
}
</script>

<style scoped lang="scss">
.leftSide {
  width: 300px;
  height: calc(100vh - 110px);

  padding: 16px;
  box-sizing: border-box;
  background-color: #fff;
  border: 0.1px;
  border-color: #d2d2d3;
  border-style: solid;
}
::v-deep .el-cascader--small {
  width: 100%;
}
</style>
