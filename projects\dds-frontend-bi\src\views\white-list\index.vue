<template>
  <DT-View :show="pageShow">
    <!-- 主视图 -->
    <main-view
      v-if="mainView.show"
      :data="mainView.data"
      :search.sync="mainView.search"
      :pagination.sync="mainView.pagination"
      @search="getTableData"
      @handleAdd="handleAdd"
      @handleEdit="handleEdit"
      @handleDelete="handleDelete"
      @handleOpen="handleOpen"
      @handleClose="handleClose"
      @paginationChange="getTableData"
    />
    <!-- 表单视图 -->
    <form-view
      v-if="formView.show"
      :role-id="roleId"
      :title="formView.title"
      :data="formView.data"
      @handleBack="handleBack"
      @handleSuccess="getTableData"
    />
  </DT-View>
</template>

<script>
// 管理员管理
import Request from "@/service"
import MainView from "./components/mainView"
import FormView from "./components/formView"
export default {
  name: "role",
  components: {
    MainView,
    FormView
  },
  data() {
    return {
      pageShow: true,
      // 用户id
      roleId: null,
      // 主视图
      mainView: {
        show: true,
        // 数据
        data: [
          {
            md: "学生名单2",
            mdms: "名单描述",
            status: 1,
            zbs: 2,
            yjs: 3,
            date: "2023-12-4 14:02:21"
          },
          {
            md: "学生名单2",
            mdms: "名单描述",
            status: 0,
            zbs: 2,
            yjs: 3,
            date: "2023-12-4 14:02:21"
          }
        ],
        // 搜索
        search: {},
        // 分页配置
        pagination: {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      },
      // 表单视图
      formView: {
        show: false,
        // 标题
        title: "",
        data: {
          id: 23
        }
      },
      // 可分配角色
      grantRole: {
        show: false
      },
      // 设置管理员
      setUsers: {
        show: false
      }
    }
  },
  mounted() {
    // this.getTableData()
  },
  methods: {
    // ------------------------ 主视图 ------------------------
    // 拉取表格数据
    getTableData() {
      let param = {
        ...{
          sidx: "role_id",
          order: "asc",
          page: this.mainView.pagination.currentPage,
          limit: this.mainView.pagination.pageSize
        },
        ...this.mainView.search
      }
      this.$dt_loading.show()
      Request.permission.role
        .roleList(param)
        .then(res => {
          this.mainView.pagination.total = res.data.totalCount
          this.mainView.data = res.data.list
          this.mainView.show = true
          this.pageShow = true
          this.$dt_loading.hide()
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 新增
    handleAdd() {
      this.roleId = null
      this.formView.title = this.$t("button.add_")
      this.formView.show = true
      this.mainView.show = false
    },
    // 编辑
    handleEdit(row) {
      this.roleId = row.roleId
      this.formView.title = this.$t("button.modify")
      this.formView.show = true
      this.mainView.show = false
    },
    // 删除
    handleDelete(row) {
      this.$confirm("是否删除选中数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()
          Request.permission.role
            .delRole([row.roleId])
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 开启
    handleOpen(row) {
      this.$confirm(
        "该名单一旦开启后，关联的预警指标或学生预警应用在后续的计算时自动去除白名单里的人群",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$dt_loading.show()
          Request.permission.role
            .delRole([row.roleId])
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 关闭
    handleClose(row) {
      this.$confirm(
        "该名单一旦禁用后，关联的预警指标或学生预警应用在后续的计算时不再去除白名单里的人群，直到再次开启",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$dt_loading.show()
          Request.permission.role
            .delRole([row.roleId])
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 返回
    handleBack() {
      this.mainView.show = true
      this.formView.show = false
      this.grantRole.show = false
      this.setUsers.show = false
    }
  }
}
</script>
