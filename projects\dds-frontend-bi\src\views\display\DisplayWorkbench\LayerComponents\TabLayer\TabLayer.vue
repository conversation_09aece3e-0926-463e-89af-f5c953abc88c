<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="标签1" name="1">标签1</el-tab-pane>
    <el-tab-pane label="标签2" name="2">标签2</el-tab-pane>
  </el-tabs>
</template>
<script>
export default {
  name: "image-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: "1"
    }
  },
  computed: {
    imgStyle() {
      const {
        imageAdress,
        borderRadius,
        transparency,
        startRotate,
        rotationSpeed
      } = this.layer.params
      return {
        imageAdress:
          (process.env.NODE_ENV === "development"
            ? "http://***************"
            : window.location.origin) + imageAdress,
        borderRadius: borderRadius + "px",
        opacity: transparency / 100,
        animation: startRotate
          ? "turn " + (101 - rotationSpeed) / 10 + "s linear infinite"
          : "none"
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
.imagebox {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.imagebox img {
  width: 100%;
  height: 100%;
}
</style>
