<template>
  <div class="widgetBox" v-loading="loading" :style="{ height }">
    <Widgets
      ref="WidgetsRefs"
      :widget-props="chartData"
      @onPageChange="onPageChange"
      :loading="loading"
    />
  </div>
</template>

<script>
import Widgets from "@/views/widgets/component/Widget"
import { widgetParamsFormat } from "@/views/widgets/component/util"
import { mapState } from "vuex"
export default {
  components: {
    Widgets,
  },
  props: {
    selectedChartId: {
      type: Number,
      default: 0,
    },
    Drillconfig: {
      type: Object,
      default: () => {},
    },
    chartParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      height: "350px",
      chartData: {},
      updatedPagination: {
        pageNo: 0,
        pageSize: 0,
      },
      page: {
        pageNo: 0,
        pageSize: 0,
      },
      loading: true,
    }
  },
  computed: {
    ...mapState({
      widgets: (state) => state.widget.widgets,
    }),
  },
  created() {
    console.log(this.$route.query.config)
    if (this.$route.query.config) {
      const { selectedChartId, Drillconfig, chartParams } = JSON.parse(
        this.$route.query.config
      )
      this.selectedChartId = selectedChartId
      this.Drillconfig = Drillconfig
      this.chartParams = chartParams
      this.height = "100vh"
    }
  },
  mounted() {},
  watch: {
    Drillconfig: {
      immediate: true,
      deep: true,
      handler(config) {
        console.log(this.widgets, "widgets")

        let widgetConfig = this.widgets.find(
          (item) => item.id === config.target
        )
        if (widgetConfig) {
          this.chartData = widgetConfig.config

          if (config.relation && this.selectedChartId === 1) {
            config.relation.forEach((item) => {
              var index = this.chartData.controls.findIndex(
                (e) => e.key === item.kzq
              )
              this.chartData.controls[index].defaultValue =
                this.chartParams[item.zd]
            })
          }
          if (config.xAxis) {
            const index = this.chartData.controls.findIndex(
              (item) => item.key === config.xAxis
            )
            this.chartData.controls[index].defaultValue = this.chartParams.name
          }
          if (config.series) {
            const index = this.chartData.controls.findIndex(
              (item) => item.key === config.series
            )
            this.chartData.controls[index].defaultValue =
              this.chartParams.seriesName
          }
          if (config.valueName) {
            const index = this.chartData.controls.findIndex(
              (item) => item.key === config.valueName
            )
            this.chartData.controls[index].defaultValue =
              this.chartParams.seriesName
          }
          this.item = widgetConfig
          this.getData()
        }
      },
    },
  },
  methods: {
    onPageChange(page) {
      this.updatedPagination.pageNo = page.currentPage
      this.updatedPagination.pageSize = page.pageSize
      this.getData()
      // this.page = page;
    },
    getData() {
      const filters = [] // 字段
      const params = [] // 变量
      const [ lf, lp ] = this.getControlsPrams(this.chartData.controls, "local")
      filters.push(...lf)
      params.push(...lp)
      let parameter = {
        ...widgetParamsFormat(
          this.chartData,
          this.item.viewId,
          this.updatedPagination
        ),
        filters,
        params,
      }
      this.onLoadData(parameter)
      clearInterval(this.pollingTimer)
      if (this.item.polling) {
        this.pollingTimer = setInterval(() => {
          this.onLoadData(parameter)
        }, Number(this.item.frequency) * 1000)
      }
    },
    onLoadData(parameter) {
      this.loading = true
      this.$httpBi.view.getdata(parameter).then((res) => {
        this.chartData.data = res.data.resultList
        if (this.chartData.name === "table") {
          this.chartData.pagination = {
            pageNo: res.data.pageNo,
            pageSize: res.data.pageSize,
            totalCount: res.data.totalCount,
            withPaging: this.chartData.chartStyles.table.withPaging,
          }
        }
        this.loading = false
      })
    },
    getControlsPrams(controls, ControlType) {
      let filters = []
      let params = []
      controls.length &&
        controls.forEach((item) => {
          let name = item.relatedViews[this.item.viewId].fields[0]
          let fieldType = item.relatedViews[this.item.viewId].fieldType
          let model = JSON.parse(this.chartData.model)
          if (item.defaultValue !== "" && item.defaultValue) {
            if (fieldType === "column") {
              let sqlType = model[name].sqlType
              if ([ "numberRange", "dateRange", "slider" ].includes(item.type)) {
                filters.push(
                  JSON.stringify({
                    value: item.defaultValue[0],
                    name,
                    operator: ">=",
                    sqlType: sqlType,
                    type: "filter",
                  }),
                  JSON.stringify({
                    value: item.defaultValue[1],
                    name,
                    operator: "<=",
                    sqlType: sqlType,
                    type: "filter",
                  })
                )
              } else {
                filters.push(
                  JSON.stringify({
                    value: item.defaultValue,
                    name,
                    operator: item.operator ?? "=",
                    sqlType: sqlType,
                    type: "filter",
                  })
                )
              }
            } else {
              params.push({
                value: item.defaultValue,
                name,
              })
            }
          }
          // 如果下拉选择控制器就请求 options   如果已经存在不需要二次请求
          if (
            [ "select", "radio" ].includes(item.type) &&
            !this.selectOptions[item.key] &&
            ControlType === "local"
          ) {
            this.getOptions(name, item)
          }
        })
      return [ filters, params ]
    },
  },
}
</script>

<style scoped lang="scss">
.widgetBox {
  width: 100%;
  overflow: auto;
}
</style>
