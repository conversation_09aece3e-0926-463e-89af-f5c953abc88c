<template>
  <div class="preview-excel">
    <el-button type="primary" @click="downloadExcel">下载Excel</el-button>
    <vue-office-excel
        :src="file"
        style="height:calc(100vh - 60px)"
      />
  </div>
</template>

<script>
import VueOfficeExcel from "@vue-office/excel"
import "@vue-office/excel/lib/index.css"
export default {
  components: {
    VueOfficeExcel
  },
  props: {},
  data () {
    return {
      file:"file",
    }
  },
  computed: {},
  created () {
    this.file = this.$route.query.file
  },
  methods: {
    downloadExcel () {
      if (this.file) {
        window.open(this.file)
      } else {
        this.$message.warning('暂无可下载的Excel文件')
      }
    }
  }

}
</script>

<style lang="scss">
.preview-excel {
  width: 100vw;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background: #f5f7fa;
}

#excel-preview {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  background: #fff;
  table {
    width: 100%;
    border: 1px solid #e4e7ed;
  }
  th,
  td {
    border: 1px solid #e4e7ed;
    padding: 8px;
    text-align: center;
    font-size: 14px;
    color: #303133;
    min-width: 80px;
    max-width: 300px;
    word-break: break-all;
  }
  th {
    background: #f5f7fa;
    font-weight: bold;
  }
  tr:hover {
    background: #f0f9ff;
  }
}
</style>
