<template>
  <div class="imagebox">
    <img
      :style="imgStyle"
      :src="imgStyle.imageAdress"
      alt=""
    />
  </div>
</template>
<script>
export default {
  name: "image-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {
    imgStyle() {
      const { imageAdress,borderRadius,transparency,startRotate,rotationSpeed } = this.layer.params
      return {
        imageAdress:(process.env.NODE_ENV === "development"?'http://***************' : window.location.origin) + imageAdress,
        borderRadius: borderRadius + "px",
        opacity: transparency / 100,
        animation: startRotate
          ? "turn " +
            (101 - rotationSpeed) / 10 +
            "s linear infinite"
          : "none",
      }
    },
  },
  watch: {
  
  },
  created() {
  },
  mounted() {},
  methods: {},
}
</script>

<style scoped lang="scss">
.imagebox {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.imagebox img {
  width: 100%;
  height: 100%;
}

</style>
