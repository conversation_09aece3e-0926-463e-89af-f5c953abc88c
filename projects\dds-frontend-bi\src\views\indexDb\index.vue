<template>
  <dt-single-page-view
    class="cardList"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <el-row>
      <el-col :span="12">
        <el-table :data="tableList" @row-click="getColumn" style="width: 95%">
          <el-table-column prop="tableName" label="表名" align="center">
            <template slot-scope="scope">
              {{ scope.row.tableComment }}{{ scope.row.tableName }}
            </template>
          </el-table-column>
          <el-table-column prop="tableComment" label="描述" align="center">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="12">
        <el-table :data="tableColumn" style="width: 95%">
          <el-table-column prop="columnName" label="字段" align="center">
          </el-table-column>
          <el-table-column prop="columnComment" label="字段名" align="center">
          </el-table-column>
          <el-table-column prop="columnType" label="字段类型" align="center">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </dt-single-page-view>
</template>
<script>
import Request from "@/service"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  name: "index-db",
  components: {
  },
  data() {
    return {
      show: false,
      loading: true,
      tableList: [],
      tableColumn: [],
    }
  },
  created() {
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
    this.initData()
    // this.initQuery();
  },
  methods: {
    initData() {
      this.loading = true
      Request.indexDb
        .getTable()
        .then((res) => {
          this.tableList = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    getColumn(row,) {
      Request.indexDb
        .getColumn({ tableName: row.tableName })
        .then((res) => {
          this.tableColumn = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.cardList {
  .title {
    margin-top: 100px;
  }

  .buttons {
    margin-top: 50px;
  }
}

.last_item {
  width: auto;
}
</style>
