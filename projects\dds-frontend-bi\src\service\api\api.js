import service from "../base"
import config from "../config"

export default {
  /**
   *   {
   *      key : value,
   *      key :value
   *   }
   * @param param
   */
  paramGet(url, param) {
    let getParam = "?"
    for (var key in param) {
      getParam += key + "=" + param[key] + "&"
    }

    return service({
      url: config.VUE_MODULE_DDS_BI + url + getParam,
      method: "get"
    })
  },
  paramDel(url, param) {
    let getParam = "?"
    for (var key in param) {
      getParam += key + "=" + param[key] + "&"
    }
    return service({
      url: config.VUE_MODULE_DDS_BI + url + getParam,
      method: "delete"
    })
  },
  paramPost(url, param) {
    return service({
      url: config.VUE_MODULE_DDS_BI + url,
      method: "post",
      data: param
    })
  },
  sendGet(url) {
    return service({
      url: config.VUE_MODULE_DDS_BI + url,
      method: "get"
    })
  },
  sendPost(url) {
    return service({
      url: config.VUE_MODULE_DDS_BI + url,
      method: "post",
      data: {}
    })
  },
  paramPostQuery(url, params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + url,
      method: "post",
      params
    })
  },
  orgMember(params) {
    return service({
      url: "/dds-server-main/pushSetting/object/orgMember",
      method: "get",
      params
    })
  },
   getAiReport(data){
    return service({
      url: '/dds-server-aiReport/report',
      method: 'post',
      data
    })
  }
}
