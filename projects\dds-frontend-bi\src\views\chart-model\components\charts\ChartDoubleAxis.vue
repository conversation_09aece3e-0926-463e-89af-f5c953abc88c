<template>
  <div class="chart-double-axis" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartDoubleAxis',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    xField: {
      type: String,
      default: 'name'
    },
    yField: {
      type: Array,
      default: () => ['value1', 'value2']
    },
    seriesName: {
      type: Array,
      default: () => ['系列1', '系列2']
    },
    // 样式配置
    colors: {
      type: Array,
      default: () => ['#1890ff', '#f5222d']
    },
    // 图表类型配置
    chartTypes: {
      type: Array,
      default: () => ['bar', 'line'] // 第一个系列为柱状图，第二个系列为折线图
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      // 判断使用哪种数据格式
      const useNewFormat = this.categories.length > 0 && this.series.length > 0

      let xData, seriesData, legendData

      if (useNewFormat) {
        // 新格式：categories + series
        xData = this.categories
        seriesData = this.series
        legendData = this.series.map(item => item.name)
      } else {
        // 旧格式：chartData + xField + yField
        xData = this.chartData.map(item => item[this.xField])
        const series1Data = this.chartData.map(item => item[this.yField[0]])
        const series2Data = this.chartData.map(item => item[this.yField[1]])
        seriesData = [
          {
            name: this.seriesName[0],
            type: this.chartTypes[0] || 'bar',
            data: series1Data
          },
          {
            name: this.seriesName[1],
            type: this.chartTypes[1] || 'line',
            data: series2Data
          }
        ]
        legendData = this.seriesName
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: this.showTooltip,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: (params) => {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          show: this.showLegend,
          data: legendData,
          top: 10,
          right: 20
        },
        grid: {
          show: this.showGrid,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
          borderColor: '#e6e6e6'
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            lineStyle: {
              color: '#d9d9d9'
            }
          },
          axisLabel: {
            color: '#666666',
            fontSize: 12
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            name: legendData[0],
            position: 'left',
            axisLine: {
              show: true,
              lineStyle: {
                color: this.colors[0]
              }
            },
            axisLabel: {
              color: '#666666',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: legendData[1],
            position: 'right',
            axisLine: {
              show: true,
              lineStyle: {
                color: this.colors[1]
              }
            },
            axisLabel: {
              color: '#666666',
              fontSize: 12
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: seriesData.map((seriesItem, seriesIndex) => ({
          name: seriesItem.name,
          type: seriesItem.type || (seriesIndex === 0 ? 'bar' : 'line'),
          yAxisIndex: seriesIndex,
          data: seriesItem.data,
          lineStyle: seriesItem.type === 'line' ? {
            color: this.colors[seriesIndex % this.colors.length],
            width: 2
          } : undefined,
          itemStyle: {
            color: this.colors[seriesIndex % this.colors.length],
            borderRadius: seriesItem.type === 'bar' ? [4, 4, 0, 0] : 0
          },
          symbol: seriesItem.type === 'line' ? 'circle' : undefined,
          symbolSize: seriesItem.type === 'line' ? 4 : undefined,
          label: {
            show: this.showLabel,
            position: 'top',
            color: '#666666',
            fontSize: 12
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          animationDelay: (idx) => idx * 100 + seriesIndex * 200
        })),
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-double-axis {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
