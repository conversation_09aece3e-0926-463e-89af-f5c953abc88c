<template>
  <el-dialog
    title="打标签"
    v-dialogDrag
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    class="avue-dialog avue-dialog--top"
    @close="$emit('update:dialogVisible', false)"
    width="30%"
  >
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="150px"
      class="ruleForm"
    >
      <el-form-item label="新建或选择现有标签" prop="pass">
        <el-select
          v-model="value"
          :multiple-limit="10"
          multiple
          filterable
          remote
          allow-create
          default-first-option
          @change="change"
          @visible-change="handleSelectVisibleChange"
          @remove-tag="removeTag"
          :remote-method="remoteMethod"
          placeholder="请选择或者创建标签"
        >
          <el-option
            v-for="item in formatLabels"
            :key="item.bqmc"
            :label="item.bqmc"
            :value="item.bqmc"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleSave" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      labels: [],
      newTag: "", // 新建标签
      tempTag: "", // 临时存储标签
      newTagList: []
    }
  },
  computed: {
    formatLabels() {
      return this.labels.filter(item => item.bqmc.includes(this.newTag))
    }
  },
  created() {
    this.newTagList = []
    this.getLabelSelectList()
  },
  mounted() {},
  watch: {},
  methods: {
    // 获取所有标签
    async getLabelSelectList() {
      const { data } = await this.$httpBi.indicatorAnagement.getIndicatorTags(
        ""
      )
      this.labels = data
      const tagObj = data.find(item => item.bqmc === this.tempTag)
      if (tagObj) {
        this.$nextTick(() => {
          this.value.splice(
            this.value.findIndex(e => e === this.tempTag),
            1,
            tagObj.bqmc
          )
        })
      }
    },
    change(val) {
      console.log(val, "val")
      const isNewLabel = this.labels.every(item =>
        val.every(element => {
          return item.bqmc === element
        })
      )
      if (!isNewLabel) {
        this.addLabel()
      }
    },
    removeTag() {
      this.newTag = ""
    },
    remoteMethod(tagName) {
      if (tagName) {
        this.newTag = tagName
      }
    },
    // 添加新标签
    async addLabel() {
      if (this.newTag) {
        this.newTagList.push(this.newTag)
      }
      this.tempTag = this.newTag
      this.newTag = ""
    },
    async handleSave() {
      if (this.newTagList.length) {
        await this.$httpBi.indicatorAnagement.addIndicatorTags(this.newTagList)
        this.getLabelSelectList()
      }

      this.$emit("handleSaveTags", this.value)
    },
    // 处理下拉框显示状态变化
    handleSelectVisibleChange(visible) {
      if (!visible) {
        this.newTag = ""
        this.change([])
      }
    }
  }
}
</script>

<style scoped lang="scss">
.ruleForm {
  height: 250px;
}
::v-deep .el-alert {
  margin-top: 0;
}
</style>
