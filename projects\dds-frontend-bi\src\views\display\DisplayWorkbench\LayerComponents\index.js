import { widgetTool } from "./main"
const ScreenConfig = {
  code: "screen",
  type: "screen",
  label: "大屏设置",
  icon: "",
  options: {
    style: [
      {
        type: "el-input-number",
        label: "大屏宽度",
        name: "width",
        required: false,
        placeholder: "",
        value: "1920"
      },
      {
        type: "el-input-number",
        label: "大屏高度",
        name: "height",
        required: false,
        placeholder: "",
        value: "1080"
      },
      {
        type: "el-input-color",
        label: "背景颜色",
        name: "backgroundColor",
        required: false,
        placeholder: "",
        value: "rgba(45, 86, 126, 1)"
      },
      {
        type: "dt-upload",
        label: "图片地址",
        name: "backgroundImage",
        required: false,
        placeholder: "",
        value: ""
      },
      {
        type: "el-radio-group",
        label: "预览方式",
        name: "scaleMode",
        require: false,
        placeholder: "",
        value: "scaleHeight",

        selectValue: true,
        selectOptions: [
          {
            code: "scaleWidth",
            name: "等比缩放宽度"
          },
          {
            code: "scaleHeight",
            name: "等比缩放高度"
          },
          {
            code: "scaleFull",
            name: "全屏铺满"
          }
        ]
      }
    ],
    event: []
  }
}
// 获取图层配置
function getLayerCode(code) {
  // 获取大屏底层设置属性
  if (code === "screen") {
    return ScreenConfig
  }
  // 获取组件
  return [...widgetTool].find(item => {
    return item.code === code
  })
}
// 获取默认图层配置
const getDefaultLayerConfig = code => {
  const defaultStyleConfig = {}
  getLayerCode(code).options.style.forEach(item => {
    if (item.children) {
      item.children.forEach(child => {
        defaultStyleConfig[child.name] = child.value
      })
    } else {
      defaultStyleConfig[item.name] = item.value
    }
  })
  getLayerCode(code).options.event.forEach(item => {
    defaultStyleConfig[item.name] = item.value
  })
  return {
    ...defaultStyleConfig,
    relation: []
  }
}
export { getLayerCode, ScreenConfig, getDefaultLayerConfig }
