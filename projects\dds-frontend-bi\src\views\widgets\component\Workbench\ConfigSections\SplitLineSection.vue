<template>
  <div class="paneBlock">
    <h4>分割线</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">
          <el-checkbox
            v-model="SplitLineForm.showHorizontalLine"
            @change="changeSplitLineStyle"
          >
            显示横向分隔线
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeSplitLineStyle"
            v-model="SplitLineForm.horizontalLineStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_LINE_STYLES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="粗细"
            @change="changeSplitLineStyle"
            v-model="SplitLineForm.horizontalLineSize"
            size="mini"
          >
            <el-option
              v-for="item in 10"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="SplitLineForm.horizontalLineColor"
            @change="changeSplitLineStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">
          <el-checkbox
            v-model="SplitLineForm.showVerticalLine"
            @change="changeSplitLineStyle"
          >
            显示纵向分隔线
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeSplitLineStyle"
            v-model="SplitLineForm.verticalLineStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_LINE_STYLES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="粗细"
            @change="changeSplitLineStyle"
            v-model="SplitLineForm.verticalLineSize"
            size="mini"
          >
            <el-option
              v-for="item in 10"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="SplitLineForm.verticalLineColor"
            @change="changeSplitLineStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
    </div>
  </div>
</template>

<script>
import { PIVOT_CHART_LINE_STYLES } from "@/globalConstants"
export default {
  components: {},
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      PIVOT_CHART_LINE_STYLES,
      SplitLineForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.SplitLineForm = this._.cloneDeep(
        this.chartData.chartStyles.splitLine
      )
    },
    changeSplitLineStyle() {
      this.$emit("changeStyle", "splitLine", this.SplitLineForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
