<template>
  <div class="module-wrap">
    <SectionTitle
      title="近6月异常趋势"
      content=""
      is-show-tip
      :is-show-select="false"
    />
    <div class="module-content">
      <ChartColumn
        :chart-data="chartData"
        x-field="weekRange"
        :y-field="['processedAlerts','unprocessedAlerts']"
        :series-name="['已处理异常次数','未处理异常次数 ']"
        :bar-width="16"
        y-axis-name="单位 (次数)"
        unit="次"
        is-stack
      />
    </div>
  </div>
</template>

<script>
import SectionTitle from "@/components/SectionTitle.vue"
import ChartColumn from "@/components/Charts/ChartColumn.vue"
export default {
  components: { SectionTitle, ChartColumn },
  data() {
    return {
      chartData: []
    }
  },
  inject: ["parent"],
  
  watch: {
    "parent.data": {
      handler(data) {
        this.chartData= data.recentSixMonthExceptionTrend
      },
      deep: true,
      immediate: true

    }
  },
}
</script>

<style scoped lang="scss">
.module-wrap {
  width: 100%;
  background: #ffffff;
  padding: 24px;
  border-radius: 4px;
  margin-bottom: 24px;
  box-sizing: border-box;
  .module-content {
    position: relative;
    width: 100%;
    height: 320px;
    padding: 10px 0 0;
    .unit {
      position: absolute;
      top: 14px;
      left: 0;
      width: 100%;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222;
    }
  }
}
::v-deep .el-carousel .el-carousel__button {
  width: 16px;
  height: 4px;
  background: rgba(35, 97, 219, 1);
  border-radius: 2px;
}
</style>
