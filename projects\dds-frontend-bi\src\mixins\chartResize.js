import * as echarts from "echarts"

import debounce from "lodash/debounce" // 防抖函数
// import * as echarts from 'echarts';
import { mapGetters } from "vuex"

export default {
  data() {
    return {
      chart: null
    }
  },
  computed: {
    ...mapGetters({
      isCollapse: "isCollapse"
    })
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        this.chart.resize({ animation: { duration: 300 } })
      }, 300)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener("resize", debounce(this.resize, 300))
  },
  methods: {
    resize() {
      if (this.chart) {
        this.chart.resize({ animation: { duration: 300 } })
        if (this.calculationLocation) {
          this.calculationLocation(this.maxWidth)
        }
      }
    },

    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          (el, i) => `<div class="content-panel">
        <p>
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${el.value[el.dimensionNames[el.encode.y[0]]]}${i > 2 ? "%" : "人"}
        </span>
      </div>`
        )
        .join("")
    },
    async handleDownload() {
      let echartsAppearanceDom = this.$refs.chartRef
      let myAppearanceChart = echarts.init(echartsAppearanceDom)
      let picInfo = myAppearanceChart.getDataURL({
        type: "png",
        pixelRatio: 1.5, // 放大两倍下载，之后压缩到同等大小展示。解决生成图片在移动端模糊问题
        backgroundColor: "#fff"
      }) // 获取到的是一串base64信息
      const elink = document.createElement("a")
      elink.download = "仪表盘"
      elink.style.display = "none"
      elink.href = picInfo
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart?.dispose()
    this.chart = null
  }
}
