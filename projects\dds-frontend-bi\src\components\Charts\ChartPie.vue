<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import { getTextWidth, formatNumber, convertPercentage } from "@/utils"
import ChartEmpty from "./ChartEmpty.vue"
import resize from "@/mixins/chartResize"
export default {
  components: {
    ChartEmpty
  },
  mixins: [resize],
  props: {
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "100%"
    },
    chartData: {
      type: Array,
      default: () => []
    },
    subtitle: {
      type: String,
      default: "合计"
    },

    // 角度字段
    angleField: {
      type: Object,
      default: () => {}
    },
    // 颜色字段
    colorField: {
      type: String,
      default: ""
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#2361DB",
        "#0EACCC",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // 是否显示总数
    isShowTotal: {
      type: Boolean,
      default: true
    },
    // 自定义总数
    customTotal: {
      type: Number,
      default: 0
    },
    // 是否开启自定义总数
    isCustomTotal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      legendData: [],
      total: 0,
      chart: null
    }
  },
  computed: {
    legendTextWidth() {
      return this.maxTextWidth + 20 > 95 ? 95 : this.maxTextWidth + 20
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    },
    angleField: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  mounted() {},
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
      this.chart.on('legendselectchanged', this.handleLegendSelectChanged)
    },
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const data = this.chartData.map(item => {
        return {
          ...item,
          [this.angleField]: Number(item[this.angleField])
        }
      })
      console.log(data,'data')
      this.maxTextWidth = 0
      // 计算总数
      this.total = 0
      data.forEach(item => {
        this.total += Number(item[this.angleField])
      })
      this.legendData = data.map((item, index) => {
        this.maxTextWidth = Math.max(
          getTextWidth(item[this.colorField], 12),
          this.maxTextWidth
        )

        return {
          name: item[this.colorField],
          value: item[this.angleField],
          itemStyle: {
            color: this.color[index % 6],
            borderWidth: 0
          }
        }
      })
      console.log(this.maxTextWidth, "this.maxTextWidth")
      // 获取legendData文字最大的宽度
      this.chart.setOption({
        dataset: {
          dimensions: [this.colorField, this.angleField],
          source: data
        },
        title: {
          show: this.isShowTotal,
          text: `{a|${formatNumber(this.isCustomTotal?this.customTotal:this.total).value}}{b|${
            formatNumber(this.isCustomTotal?this.customTotal:this.total).unit
          }${this.unit}}`,
          subtext: this.subtitle,
          x: "24.5%",
          y: "40%",
          textAlign: "center",
          textStyle: {
            fontSize: 28,
            fontWeight: "500",
            rich: {
              a: {
                fontSize: 26,
                fontFamily: "AlibabaSans102Ver2-Medium, AlibabaSans102Ver2",
                fontWeight: 500,
                color: "#323233"
              },
              b: {
                fontSize: 12,
                fontFamily: "PingFangSC-Regular, PingFang SC",
                fontWeight: 400,
                color: "#323233",
                padding: [4, 0, 0, 0],
                verticalAlign: "middle"
              }
            }
          },
          subtextStyle: {
            fontSize: 14,
            color: "#646566"
          }
        },
        tooltip: {
          trigger: "item",
          confine: true,
          className: "echarts-tooltip-diy",
          formatter: params => {
            console.log(params)
            return `<div class="content-panel">
        <p>
          <span style="background-color: ${
            params.color
          }" class="tooltip-item-icon"></span>
          <span>${params.name}</span>
        </p>
        <span class="tooltip-value">
          ${formatNumber(params.value[this.angleField]).value}
          ${formatNumber(params.value[this.angleField]).unit}
          ${this.unit}
        </span>
      </div>`
          }
        },
        color: this.color,
        legend: {
          show: true,
          orient: "vertical",
          type: "scroll",
          top: this.legendData.length > 5 ? "7%" : "middle",
          itemGap: 34,
          icon: "circle",
          left: "50%",
          itemWidth: 6,
          itemHeight: 6,
          data: this.legendData,
          pageButtonGap: 10,
          height: this.legendData.length > 5 ? "90%" : "auto",
          triggerEvent: true,
          pageIconColor: "#2361db",
          pageIconInactiveColor: "#cad9f7",
          tooltip: {
            show: true,
            trigger: "item" // 鼠标移动上去展示全称
          },
          textStyle: {
            fontSize: 14,
            fontWeight: 400,
            rich: {
              a: {
                width: this.legendTextWidth,
                color: "#646566",
                fontSize: 12
              },
              b: {
                color: "#323233",
                width: 70,
                fontSize: 14,
                fontWeight: 500,
                align: "right",
                fontFamily: "PingFangSC, PingFang SC",
                padding: [0, 0, 0, 5]
              },
              c: {
                width: 65,
                align: "right",
                color: "#323233",
                fontSize: 14,
                fontWeight: 500,
                fontFamily: "PingFangSC, PingFang SC"
              }
            }
          },
          formatter: name => {
            const data = this.chartData
            let total = 0
            let tarValue
            for (let i = 0; i < data.length; i++) {
              total += Number(data[i][this.angleField])
              if (data[i][this.colorField] === name) {
                tarValue = data[i][this.angleField]
              }
            }
            if (name.length > 6) {
              name = name.slice(0, 6) + "..."
            }
            let val = tarValue + ""

            let p = convertPercentage(Number(tarValue) / total)
            return `{a|${name}}{b| ${formatNumber(val).value}${
              formatNumber(val).unit
            }${this.unit}}{c|${p}}`
          }
        },
        series: [
          {
            name: "分布",
            type: "pie",
            center: ["50%", "50%"],
            radius: ["65%", "78%"],
            label: {
              show: false
            },
            width: "50%",
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              color: params => {
                // console.log(
                //   '......:',
                //   params.dataIndex % this.color.length,
                //   this.color[params.dataIndex % this.color.length]
                // )
                return this.color[params.dataIndex % this.color.length]
              }
            },
            emphasis: {
              label: {
                show: false
              }
            }
          }
        ]
      },true)
    },
    handleLegendSelectChanged(params) {
      // params.selected 是一个对象，key为图例名，value为是否选中
      const selected = params.selected
      // 只统计被选中的
      let total = 0
      this.chartData.forEach(item => {
        if (selected[item[this.colorField]]) {
          total += Number(item[this.angleField])
        }
      })
      // 更新 title
      this.chart.setOption({
        title: {
          text: `{a|${formatNumber(this.isCustomTotal?this.customTotal:total).value}}{b|${
            formatNumber(this.isCustomTotal?this.customTotal:total).unit
          }${this.unit}}`
        }
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.off('legendselectchanged', this.handleLegendSelectChanged)
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
.legend {
  position: absolute;
  right: 0;
  top: 0;
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 999;

  &-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222;
    height: 40px;
    line-height: 40px;
    padding: 0 8px;
    cursor: pointer;
    &-icon {
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }
    &-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 6px;
      font-size: 12px;
      text-align: left;
      color: #646566;
    }
    &-value {
      width: 80px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 16px;
    }
    &-proportion {
      width: 55px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover {
      background: rgba(35, 97, 219, 0.1);
      &-name {
        color: #2361db;
      }
    }
  }

  &-item:last-child {
    margin: 0;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}
.echarts-tooltip-drill {
  background: #000 !important;
}
</style>
