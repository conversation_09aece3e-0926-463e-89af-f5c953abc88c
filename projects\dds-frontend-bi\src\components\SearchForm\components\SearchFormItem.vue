<template>
  <component
    :is="componentName"
    v-bind="{
      ...handleSearchProps,
      ...placeholder,
      searchParam: _searchParam,
      clearable
    }"
    v-model.trim="
      _searchParam[
        (column.search && column.search.key) || handleProp(column.prop)
      ]
    "
    :data="
      column.search && column.search.el === 'tree-select' ? columnEnum : []
    "
    :options="
      ['cascader', 'select-v2'].includes(column.search && column.search.el)
        ? columnEnum
        : []
    "
  >
    <template
      v-if="column.search && column.search.el === 'cascader'"
      #default="{ data }"
    >
      <span>{{ data[fieldNames.label] }}</span>
    </template>
    <template v-if="column.search && column.search.el === 'select'">
      <component
        :is="`el-option`"
        v-for="(col, index) in columnEnum"
        :key="index"
        :label="col[fieldNames.label]"
        :value="col[fieldNames.value]"
      ></component>
    </template>
    <slot v-else></slot>
  </component>
</template>

<script>
import { handleProp } from "@/utils"
export default {
  components: {},
  props: {
    column: {
      type: Object,
      default: () => {} // 搜索配置列
    },
    searchParam: {
      type: Object,
      default: () => ({}) //  搜索参数
    }
  },
  inject: ["enumMapProvide"],
  data() {
    return {}
  },

  computed: {
    enumMap() {
      return this.enumMapProvide.enumMap
    },
    componentName() {
      return this.column.search?.render ?? `el-${this.column.search?.el}`
    },
    _searchParam() {
      return this.searchParam
    },
    fieldNames() {
      return {
        label: this.column.fieldNames?.label ?? "label",
        value: this.column.fieldNames?.value ?? "value",
        children: this.column.fieldNames?.children ?? "children"
      }
    },
    columnEnum() {
      let enumData = this.enumMap[this.column.prop]
      if (!enumData) return []
      if (this.column.search?.el === "select-v2" && this.column.fieldNames) {
        enumData = enumData.map(item => {
          return {
            ...item,
            label: item[this.fieldNames.label],
            value: item[this.fieldNames.value]
          }
        })
      }
      return enumData
    },
    handleSearchProps() {
      const label = this.fieldNames.label
      const value = this.fieldNames.value
      const children = this.fieldNames.children
      const searchEl = this.column.search?.el
      let searchProps = this.column.search?.props ?? {}
      if (searchEl === "tree-select") {
        searchProps = {
          ...searchProps,
          props: { ...searchProps.props, label, children },
          nodeKey: value
        }
      }
      if (searchEl === "cascader") {
        searchProps = {
          ...searchProps,
          props: { ...searchProps.props, label, value, children }
        }
      }
      return searchProps
    },
    placeholder() {
      const search = this.column.search
      if (
        ["datetimerange", "daterange", "monthrange"].includes(
          search?.props?.type
        ) ||
        search?.props?.isRange
      ) {
        return {
          rangeSeparator: search?.props?.rangeSeparator ?? "至",
          startPlaceholder: search?.props?.startPlaceholder ?? "开始时间",
          endPlaceholder: search?.props?.endPlaceholder ?? "结束时间"
        }
      }
      const placeholder =
        search?.props?.placeholder ??
        (search?.el?.includes("input")
          ? "请输入" + this.column.label
          : "请选择" + this.column.label)
      return { placeholder }
    },
    clearable() {
      const search = this.column.search
      return (
        search?.props?.clearable ??
        (search?.defaultValue == null || search?.defaultValue === undefined)
      )
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleProp
  }
}
</script>

<style scoped lang="scss"></style>
