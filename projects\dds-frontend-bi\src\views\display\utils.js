import { divide, multiply } from "mathjs"
import { ScreenConfig } from "./DisplayWorkbench/LayerComponents/index"
import { ChartLayerConfig } from "./DisplayWorkbench/LayerComponents/ChartLayer/ChartLayerConfig"
import rectangle from "@/assets/json/slideSettings/rectangle.json"
import label from "@/assets/json/slideSettings/label.json"
import video from "@/assets/json/slideSettings/video.json"
import timer from "@/assets/json/slideSettings/timer.json"
import borderImage from "@/assets/json/slideSettings/borderImage.json"

export const slideSettings = {
  chart: ChartLayerConfig,
  20: rectangle,
  21: label,
  22: video,
  23: timer,
  borderImage
}
// 获取默认图层配置
export function getDefaultLayerSetting(LayerType) {
  const defaultSetting = {}
  const setting = slideSettings[LayerType]
  if (!setting) {
    return defaultSetting
  }
  setting.params.forEach(param => {
    param.items.forEach(item => {
      defaultSetting[item.name] = item.default
    })
  })
  return defaultSetting
}
// 获取默认slide配置
export function getDefaultSlideParams() {
  const params = ScreenConfig.options.style
  const defaultSlideParams = {}
  params.forEach(param => {
    defaultSlideParams[param.name] = param.value || null
  })
  return defaultSlideParams
}

// 转换为精度百分比
export function changeStyleWithScale(value, scale) {
  return multiply(value, divide(parseInt(scale), 100))
}

// 编辑器自定义事件
const events = {
  redirect(url) {
    if (url) {
      window.location.href = url
    }
  },

  alert(msg) {
    if (msg) {
      // eslint-disable-next-line no-alert
      alert(msg)
    }
  }
}

const mixins = {
  methods: events
}

const eventList = [
  {
    key: "redirect",
    label: "跳转事件",
    event: events.redirect,
    param: ""
  },
  {
    key: "alert",
    label: "alert 事件",
    event: events.alert,
    param: ""
  }
]

export { mixins, events, eventList }
