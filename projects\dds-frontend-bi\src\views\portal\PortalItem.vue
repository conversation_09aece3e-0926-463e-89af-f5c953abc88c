<template>
  <div
    class="portal-item"
    @click="goDashboard(item)"
    :style="{
      backgroundColor: isHover ? getIconColor(index) : getBgColor(index)
    }"
    @mouseleave="isHover = false"
    @mouseover="isHover = true"
  >
    <div class="portal-header">
      <div class="left">
        <svg-icon
          class="svg_icon"
          icon-class="portal-icon"
          :style="{ color: getIconColor(index) }"
        />
        <span class="portal-title">{{ item.name }}</span>
      </div>

      <div @click.stop>
        <el-dropdown @command="handleCommand" trigger="click">
          <span>
            <svg-icon v-if="isHover" class="more" icon-class="more" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="{ type: 'set', item }">
              设置
            </el-dropdown-item>
            <el-dropdown-item :command="{ type: 'del', item }">
              删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="portal-desc">{{ item.description }}</div>
    <div class="footer-img">
      <img :src="isHover ? getActiveImgUrl(index) : getImgUrl(index)" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      isHover: false
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    getImgUrl(index) {
      return require(`@/assets/imgs/bi/ic_0${index % 8}.png`)
    },
    getActiveImgUrl(index) {
      return require(`@/assets/imgs/bi/ic_0${index % 8}_active.png`)
    },
    getIconColor(index) {
      switch (index % 8) {
        case 0:
          return "#1463FF"
        case 1:
          return "#00C3AA"
        case 2:
          return "#00A1FF"
        case 3:
          return "#764DFF"
        case 4:
          return "#F57A00"
        case 5:
          return "#EB2F3E"
        case 6:
          return "#F431A3"
        case 7:
          return "#3AC208"
        default:
          return "#FFC107"
      }
    },
    getBgColor(index) {
      switch (index % 8) {
        case 0:
          return "#D9E6FF"
        case 1:
          return "#CBF5EF"
        case 2:
          return "#D4EFFF"
        case 3:
          return "#E7E0FF"
        case 4:
          return "#FCE7D2"
        case 5:
          return "#FCDCDE"
        case 6:
          return "#F3D9FC"
        case 7:
          return "#D8F5CE"
        default:
          return "#FFC107"
      }
    },
    handleCommand(command) {
      if (command.type === "del") {
        this.$confirm("此操作将永久删除该看板, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.$emit("onDel", command.item.id)
          })
          .catch(() => {})
      } else {
        this.$emit("onSet", command.item)
      }
    },
    // 跳转
    goDashboard(portalItem) {
      // this.$router.push(`PortalDetail?isFullPage=true&id=${portalItemid}`);
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading"
      })
      this.$router
        .push({
          name: "PortalDetail",
          query: {
            isFullPage: true,
            name: portalItem.name,
            description: portalItem.description,
            id: portalItem.id
          }
        })
        .then(() => {
          loading.close()
        })
    }
  }
}
</script>

<style scoped lang="scss">
.portal-item {
  position: relative;
  height: 160px;
  border-radius: 4px;
  padding: 16px;
  box-sizing: border-box;
  cursor: pointer;
  .portal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
    }
    .svg_icon {
      width: 24px;
      height: 24px;
    }
    .portal-title {
      height: 24px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #272b33;
      line-height: 24px;
      margin-left: 8px;
    }
    .more {
      width: 16px;
      height: 16px;
      color: #ffff;
    }
  }
  .portal-desc {
    margin-top: 6px;
    width: 100%;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4e5969;
    line-height: 20px;
    //两行超出省略
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .footer-img {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 80px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.portal-item:hover {
  .portal-header {
    .portal-title {
      color: #ffffff;
    }
  }
  .portal-desc {
    color: #ffffff;
    font-family: PingFangSC-Regular, PingFang SC;
  }
}
</style>
