export const ImageLayerConfig = {
  code: 3,
  type: 'text',
  tabName: '文本栏',
  label: '图片',
  icon: 'el-icon-picture-outline',
  component: "ImageLayer",
  options: {
    // 配置
    style: [
      {
        type: 'el-input-number',
        label: '左边距',
        name: 'left',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '上边距',
        name: 'top',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '宽度',
        name: 'width',
        required: false,
        placeholder: '',
        value: 300,
      },
      {
        type: 'el-input-number',
        label: '高度',
        name: 'height',
        required: false,
        placeholder: '',
        value: 200,
      },
      {
        type: 'el-switch',
        label: '开启旋转',
        name: 'startRotate',
        required: false,
        placeholder: '',
        value: false,
      },
      {
        type: 'el-slider',
        label: '旋转速度',
        name: 'rotationSpeed',
        required: false,
        placeholder: '',
        value: 70
      },
      {
        type: 'el-slider',
        label: '透明度',
        name: 'transparency',
        required: false,
        placeholder: '',
        value: 100
      },
      {
        type: 'el-input-number',
        label: '圆角',
        name: 'borderRadius',
        required: false,
        placeholder: '',
        value: '0'
      },
      {
        type: 'dt-upload',
        label: '图片地址',
        name: 'imageAdress',
        required: false,
        placeholder: '',
        value: 'https://pic.vjshi.com/2020-07-01/3497abb8b5173545da72ae9c079c676f/00003.jpg?x-oss-process=style/watermark',
      },
    ],
    // 数据
    // 事件
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: '外链'
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
    
  }
}
