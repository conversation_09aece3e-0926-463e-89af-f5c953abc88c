<template>
  <div class="visualize-card">
    <div class="vue-draggable-handle">
      <div
        class="name"
        :style="{
          opacity: chartData.name == 'scorecard' ? '0' : '1'
        }"
      >
        <span>{{ item.alias != "" ? item.alias : item.name }}</span>
        <el-tooltip v-if="showWarning" :content="warningMsg" placement="top">
          <i class="el-icon-warning" style="color: red"></i>
        </el-tooltip>
      </div>
      <div class="tool">
        <el-tooltip
          v-if="chartData.controls.length"
          content="控制器"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i
            class="el-icon-s-operation"
            @click="isShowControl = !isShowControl"
          />
        </el-tooltip>
        <el-tooltip
          content="同步数据"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-refresh-right" @click="reload"></i>
        </el-tooltip>
        <!-- <el-tooltip
          content="下载"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-download" @click="downloadVisible = true" />
        </el-tooltip> -->

        <!-- v-if="isAuthorized" -->
        <!-- <el-tooltip
          v-if="isEdit"
          content="编辑"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-edit" @click="handleEdit(item.id)" />
        </el-tooltip> -->

        <!-- <el-tooltip
          v-if="isEdit"
          :content="item.description"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-info" style="color: #409eff; cursor: pointer" />
        </el-tooltip> -->
        <el-dropdown @command="handleCommand" v-if="isEdit">
          <span class="el-dropdown-link">
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="download">下载</el-dropdown-item>
            <el-dropdown-item
              :command="{
                id: item.id,
                name: 'edit'
              }"
            >
              编辑
            </el-dropdown-item>
            <el-dropdown-item command="editInfo">基本信息</el-dropdown-item>
            <el-dropdown-item v-if="item.isSimple" command="setWarning">
              设置预警
            </el-dropdown-item>
            <el-dropdown-item
              v-if="chartData.name != 'scorecard'"
              command="linkageJump"
            >
              联动跳转
            </el-dropdown-item>
            <el-dropdown-item
              :command="{
                name: 'delete',
                id: item.relationsId
              }"
            >
              删除
            </el-dropdown-item>

            <!-- <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定删除这个图表吗？"
              @onConfirm="handleDel(item.relationsId)"
            >
              <el-dropdown-item slot="reference">删除</el-dropdown-item> -->
            <!-- </el-popconfirm> -->
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div
      class="no-drag"
      :id="'name' + item.id"
      :class="{
        active: item.id == $route.query.widgetId
      }"
      v-if="item.config"
      :style="{
        padding: chartData.name == 'scorecard' ? 0 : '40px 20px 0px'
      }"
    >
      <ControlPanelComponent
        v-if="isShowControl"
        :controls.sync="chartData.controls"
        :select-options="selectOptions"
        @search="getData"
        type="local"
      />
      <Widgets
        ref="WidgetsRefs"
        v-if="chartData.name"
        :widget-props="chartData"
        @onPageChange="onPageChange"
        :loading="loading"
        @handleChartClick="handleChartClick"
      />
    </div>
    <el-dialog
      title="下钻"
      v-if="drillVisible"
      append-to-body
      :visible.sync="drillVisible"
      width="60%"
      :before-close="handleClose"
    >
      <!-- <DrillWidget
        :Drillconfig="Drillconfig"
        :chartParams="chartParams"
        :selectedChartId="chartData.selectedChartId"
      /> -->
      <components
        :is="drillCon"
        :drillconfig="Drillconfig"
        :chart-params="chartParams"
        :selected-chart-id="chartData.selectedChartId"
      />
    </el-dialog>
    <WarningDialog
      :warning-visible.sync="warningVisible"
      :metrics="chartData.metrics"
      ref="WarningDialog"
      :current-widget="item"
    />
    <DownloadDialog
      :download-visible.sync="downloadVisible"
      :export-title="item.name"
      @handleExport="handleExport"
    />
  </div>
</template>

<script>
import html2canvas from "html2canvas"
import { widgetParamsFormat } from "../../widgets/component/util"
import ControlPanelComponent from "@/components/Control/Control"
import Widgets from "../../widgets/component/Widget"
import { GridItem } from "vue-grid-layout"
import DrillWidget from "@/views/drill-down/drill-widget.vue"
import DrillPortal from "@/views/drill-down/drill-portal.vue"
import DownloadDialog from "@/views/portal/component/DownloadDialog"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import Request from "@/service"
import WarningDialog from "./WarningDialog"
import dayjs from "dayjs"
import { mapGetters } from "vuex"
let quarterOfYear = require("dayjs/plugin/quarterOfYear") // import plugin
dayjs.extend(quarterOfYear)
export default {
  components: {
    Widgets,
    GridItem,
    ControlPanelComponent,
    DrillWidget,
    WarningDialog,
    DrillPortal,
    DownloadDialog
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number
    },
    onlyView: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {}
    },
    isAuthorized: {
      type: Boolean,
      default: false
    },
    dashboardId: {
      type: Number
    },
    globalControls: {
      type: Array
    },
    originalFilters: {
      type: Array,
      default: () => []
    },
    queryMode: {
      type: Number,
      default: 0
    },
    FormValuesRelatedItemsAll: {
      type: Array,
      default: () => []
    },
    FormValuesRelatedItems: {
      type: Array
    }
  },
  data() {
    return {
      cloneTableParams: {},
      downloadVisible: false,
      warningMsg: "",
      showWarning: false,
      drillCon: null, // 下钻组件名称
      warningVisible: false,
      chartParams: "",
      Drillconfig: {},
      drillVisible: false,
      loading: false,
      pollingTimer: null,
      controlConfigVisible: false,
      isShowControl: false,
      isRouterAlive: true,
      updatedPagination: {
        pageNo: 0,
        pageSize: 0
      },
      selectOptions: {},
      page: {
        pageNo: 0,
        pageSize: 0
      },
      chartData: {
        name: null,
        controls: []
      },
      exportData: []
    }
  },
  computed: {
    ...mapGetters({
      currentTheme: "currentTheme"
    })
  },
  created() {},
  mounted() {},
  watch: {
    item: {
      immediate: true,
      handler(val) {
        this.chartData = val.config
          ? JSON.parse(val.config)
          : {
              name: null,
              controls: []
            }
        if (this.chartData.name) {
          this.getData()
        }
      }
    },
    // FormValuesRelatedItemsAll: {
    //   deep: true,
    //   handler(val) {
    //     const isReload = val.some((id) => id == this.item.relationsId);
    //     if (isReload) {
    //       console.log(val, "FormValuesRelatedItemsAll");
    //       this.getData();
    //     }
    //   },
    // },

    FormValuesRelatedItems: {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val) return
        const isReload = val.some(id => id === String(this.item.relationsId))
        console.log(
          "%cDashboardItem.vue line:317 object",
          "color: #007acc;",
          val,
          isReload,
          this.item.relationsId
        )
        if (isReload && !this.queryMode) {
          this.getData()
        }
      }
    }
  },
  methods: {
    handleChartClick(params) {
      console.log(params, "params")
      const drillConfig = JSON.parse(this.item.drillConfig)
      console.log(drillConfig, "drillConfig")
      // 针对表格单元格点击处理
      if (this.chartData.selectedChartId === 1) {
        const fieldArr = drillConfig.relation.filter(
          item => item.zd === params.field
        )
        if (!fieldArr.length) return
      }
      // 外链打开
      if (drillConfig.drillType === 3) {
        let url = drillConfig.url + "?"
        if (this.chartData.selectedChartId !== 1) {
          if (drillConfig.xAxis !== "") {
            url += drillConfig.xAxis + "=" + params.name
          }
          if (drillConfig.series !== "") {
            url += drillConfig.series + "=" + params.seriesName
          }
        } else {
          drillConfig.relation.forEach(({ zd, kzq }) => {
            url += kzq + "=" + params[zd]
          })
        }
        window.open(url, "_blank")
        return
      }
      // 当前页窗口打开
      if (drillConfig.revealType === 1) {
        if (!drillConfig.target || !drillConfig.isOpen) return

        if (drillConfig.drillType === 1) {
          this.drillCon = "DrillWidget"
        }
        if (drillConfig.drillType === 2) {
          this.drillCon = "DrillPortal"
        }
        this.chartParams = params
        this.drillVisible = true
        this.Drillconfig = drillConfig
      }
      // 新窗口打开
      if (drillConfig.revealType === 2) {
        let obj = {}
        if (this.chartData.selectedChartId !== 1) {
          obj = {
            selectedChartId: this.chartData.selectedChartId,
            Drillconfig: drillConfig,
            chartParams: { name: params.name, seriesName: params.seriesName }
          }
        } else {
          obj = {
            selectedChartId: this.chartData.selectedChartId,
            Drillconfig: drillConfig,
            chartParams: params
          }
        }
        const strConfig = JSON.stringify(obj)
        let routeUrl = null
        // 图表下钻
        if (drillConfig.drillType === 1) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillWidget?isFullPage=true&config=${strConfig}`
          })
        }
        // 看板下钻
        if (drillConfig.drillType === 2) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillPortal?isFullPage=true&config=${strConfig}`
          })
        }
        window.open(routeUrl.href, "_blank")
      }
    },
    reload() {
      this.getData()
    },
    // 下载

    async handleExport({ exportType, title }) {
      if (exportType === "IMG") {
        this.exportImg(title)
      }
      if (exportType === "PDF") {
        this.ExportSavePdf(title, this.$el)
      }
      if (exportType === "EXCEL") {
        if (this.chartData.name === "table") {
          if (this.chartData.pagination.totalCount > 20000) {
            this.$message.error("数据量过大，无法导出")
            return
          }
          const { data } = await this.$httpBi.view.getdata({
            ...this.cloneTableParams,
            pageNo: 1,
            pageSize: this.chartData.pagination.totalCount
          })
          this.exportData = data.resultList

          this.$emit(
            "setExceldata",
            data.resultList,
            this.index,
            this.item.name
          )
          jsonToSheetXlsx({
            data: this.exportData,
            filename: title
          })
        } else {
          jsonToSheetXlsx({
            data: this.exportData,
            filename: title
          })
        }
      }
      this.downloadVisible = false
    },
    async handleCommand(command) {
      if (!command) return
      // 下载
      if (command === "download") {
        this.downloadVisible = true
        return
      }
      // 编辑
      if (command.name === "edit") {
        this.handleEdit(command.id)
        return
      }
      if (command.name === "delete") {
        this.$confirm("此操作将永久删除该仪表盘, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.handleDelete(command.id)
          })
          .catch(() => {})
        return
      }
      // 基本信息
      if (command === "editInfo") {
        this.$emit("handleEditInfo", this.item)
      } else if (command === "linkageJump") {
        const { data } = await this.$httpBi.widget.getAll()
        this.$store.commit("widget/SET_WIDGETS", data)
        this.$emit("handleDrillDown", this.item)
      } else {
        this.warningVisible = true
        this.$refs.WarningDialog.init()
      }
    },
    async getOptions(name, item) {
      let param = {}
      // 手动
      if (item.optionType === "manual") {
        param = {
          cache: false,
          expired: 0,
          columns: [item.valueField, item.textField],
          viewId: this.item.viewId
        }
        // 自动关联
      } else if (item.optionType === "auto") {
        param = {
          cache: false,
          expired: 0,
          columns: [name],
          viewId: this.item.viewId
        }
      }
      // 自定义
      if (item.optionType === "custom") {
        this.selectOptions[item.key] = item.customOptions
      } else {
        const { data } = await Request.view.getdistinctvalue(param)
        this.selectOptions[item.key] = data
      }
    },
    handleEdit(id) {
      this.$router.push(
        `/ddsBi/Workbench?isFullPage=true&id=${id}&width=${this.$el.clientWidth}&height=${this.$el.clientHeight}`
      )
    },
    async handleDelete(id) {
      const { code } = await Request.dashboard.delMemDashboardWidget({
        relationId: id
      })
      if (code === 200) {
        this.$message.success("删除成功")
        this.$emit("onReload")
      }
    },
    onPageChange(page) {
      this.updatedPagination.pageNo = page.currentPage
      this.updatedPagination.pageSize = page.pageSize
      this.getData()
      // this.page = page;
    },
    getData() {
      const { HighLight, widgetId } = this.$route.query
      // 锚点
      if (HighLight && Number(this.item.id) === Number(widgetId)) {
        setTimeout(() => {
          this.goAnchor(widgetId)
        }, 2000)
      }
      const filters = [] // 字段
      const params = [] // 变量
      const [lf, lp] = this.getControlsPrams(this.chartData.controls, "local")
      // 过滤出关联自己的全局控制器
      const filterGlobalControls =
        this.globalControls.length &&
        this.globalControls.filter(item => {
          return Object.entries(item.relatedItems).some(([k, v]) => {
            return Number(k) === Number(this.item.relationsId) && v.checked
          })
        })
      const [gf, gp] = this.getControlsPrams(filterGlobalControls, "global")
      filters.push(...lf, ...gf)
      params.push(...lp, ...gp)
      let parameter = {
        ...widgetParamsFormat(
          this.chartData,
          this.item.viewId,
          this.updatedPagination
        ),
        filters,
        params
      }
      this.onLoadData(parameter)
      clearInterval(this.pollingTimer)
      if (this.item.polling) {
        this.pollingTimer = setInterval(() => {
          this.onLoadData(parameter)
        }, Number(this.item.frequency) * 1000)
      }
    },
    async onLoadData(parameter) {
      console.log(
        "%cDashboardItem.vue line:544 object",
        "color: #007acc;",
        parameter
      )
      this.loading = true
      Request.view.getdata(parameter).then(res => {
        if (res.data) {
          this.exportData = res.data.resultList
          this.$emit(
            "setExceldata",
            res.data.resultList,
            this.index,
            this.item.name
          )
          this.chartData.data = res.data.resultList

          if (this.chartData.name === "table") {
            this.chartData.pagination = {
              pageNo: res.data.pageNo,
              pageSize: res.data.pageSize,
              totalCount: res.data.totalCount,
              withPaging: this.chartData.chartStyles.table.withPaging
            }
            this.cloneTableParams = { ...parameter }
          }
        } else {
          this.warningMsg = res.message
          this.showWarning = true
        }
        this.loading = false
      })
    },
    // 锚点
    goAnchor(idName) {
      console.log("执行锚点")
      var id = "#name" + idName
      document.querySelector(id).scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "nearest"
      })
    },
    getControlsPrams(controls, ControlType) {
      let filters = []
      let params = []

      controls.length &&
        controls.forEach(item => {
          const isVisibility =
            item.visibility === "conditional"
              ? this.isMeet(item.conditions[0])
              : true
          if (!isVisibility) return
          if (JSON.stringify(item.relatedViews) !== "{}") {
            let name = item.relatedViews[this.item.viewId]?.fields[0]
            let fieldType = item.relatedViews[this.item.viewId]?.fieldType
            let model = JSON.parse(this.chartData.model)
            let _defaultValue = isVisibility
              ? item.defaultValue
              : this.originalFilters.find(f => f.key === item.key).defaultValue // 默认控制器值
            if (_defaultValue !== "" && _defaultValue) {
              // 如果是字段
              if (fieldType === "column" && item.optionType !== "custom") {
                let sqlType = model[name].sqlType
                // 如果是动态值
                if (item.defaultValueType === "dynamic") {
                  if (item.type === "dateRange") {
                    filters.push(
                      JSON.stringify({
                        value: this.transformRelativeDateValue(
                          _defaultValue[0],
                          item.dateFormat
                        ),
                        name,
                        operator: ">=",
                        sqlType: sqlType,
                        type: "filter"
                      }),
                      JSON.stringify({
                        value: this.transformRelativeDateValue(
                          _defaultValue[1],
                          item.dateFormat
                        ),
                        name,
                        operator: "<=",
                        sqlType: sqlType,
                        type: "filter"
                      })
                    )
                  } else {
                    filters.push(
                      JSON.stringify({
                        value: this.transformRelativeDateValue(
                          _defaultValue,
                          item.dateFormat
                        ),
                        name,
                        operator: item.operator ?? "=",
                        sqlType: sqlType,
                        type: "filter"
                      })
                    )
                  }
                } else if (
                  ["numberRange", "dateRange", "slider"].includes(item.type)
                ) {
                  filters.push(
                    JSON.stringify({
                      value: _defaultValue[0],
                      name,
                      operator: ">=",
                      sqlType: sqlType,
                      type: "filter"
                    }),
                    JSON.stringify({
                      value: _defaultValue[1],
                      name,
                      operator: "<=",
                      sqlType: sqlType,
                      type: "filter"
                    })
                  )
                } else {
                  filters.push(
                    JSON.stringify({
                      value: _defaultValue,
                      name,
                      operator: item.operator ?? "=",
                      sqlType: sqlType,
                      type: "filter"
                    })
                  )
                }
              } else {
                // 动态值
                if (item.defaultValueType === "dynamic") {
                  if (item.type === "dateRange") {
                    name.forEach((n, i) => {
                      params.push({
                        value: this.transformRelativeDateValue(
                          _defaultValue[i],
                          item.dateFormat
                        ),
                        name: n
                      })
                    })
                  } else {
                    params.push({
                      value: this.transformRelativeDateValue(
                        _defaultValue,
                        item.dateFormat
                      ),
                      name
                    })
                  }
                  // 固定值
                } else if (
                  ["numberRange", "dateRange", "slider"].includes(item.type)
                ) {
                  if (name instanceof Array) {
                    name.forEach((n, i) => {
                      params.push({
                        value: _defaultValue[i],
                        name: n
                      })
                    })
                  } else {
                    params.push({
                      value: _defaultValue[0],
                      name
                    })
                  }
                } else if (
                  ["radio", "select"].includes(item.type) &&
                  item.optionType === "custom"
                ) {
                  const optionsOBJ = item.customOptions.find(
                    e => e.value === _defaultValue
                  )
                  const n = optionsOBJ.variables[this.item.viewId]
                  if (n) {
                    params.push({
                      value: _defaultValue,
                      name: n
                    })
                  }
                } else {
                  params.push({
                    value: _defaultValue,
                    name
                  })
                }
              }
            }
            // 如果下拉选择控制器就请求 options   如果已经存在不需要二次请求
            if (
              ["select", "radio"].includes(item.type) &&
              !this.selectOptions[item.key] &&
              ControlType === "local"
            ) {
              this.getOptions(name, item)
            }
          }
        })
      return [filters, params]
    },
    transformRelativeDateValue(val, dateFormat) {
      console.log(val, "val>>>>>>>>>>>>>>>>>>>")
      // 转大写
      dateFormat = dateFormat.toUpperCase()
      const { type, value, valueType } = val
      if (!valueType) {
        return val
      }
      return valueType === "prev"
        ? dayjs().subtract(value, `${type}`).startOf(type).format(dateFormat)
        : dayjs().add(value, `${type}`).startOf(type).format(dateFormat)
    },
    isMeet({ control, value, operator }) {
      const itemControl = this.globalControls.find(item => item.key === control)
      if (!itemControl) return false
      if (operator === "=") {
        return itemControl.defaultValue === value
      } else if (operator === "!=") {
        return itemControl.defaultValue !== value
      }
    },
    exportImg(name) {
      const content = this.$el
      console.log(content, "content")
      html2canvas(content, {
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = `${name}${dayjs().format("YYYY-MM-DD")}.jpg` // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.pollingTimer)
    this.pollingTimer = null
  }
}
</script>

<style scoped lang="scss">
.visualize-card {
  position: relative;
  border-radius: 5px;
  height: 100%;
  background-color: var(--theme-color);
  color: var(--theme-text-color);
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;

  .vue-draggable-handle {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    // height: 30px;
    // line-height: 30px;
    padding: 16px 0 0 16px;
    width: 100%;
    z-index: 99;

    .name {
      font-size: 16px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 25px;
    }

    .tool {
      opacity: 0;
    }

    i {
      margin-right: 10px;
      color: #409eff;
      cursor: pointer;
    }
  }

  .no-drag {
    position: absolute;
    top: 0;
    left: 0;
    touch-action: none;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .no-drag.active {
    background-color: var(--theme-color);
    box-shadow: inset 0px 0px 15px 5px rgba(255, 165, 0, 1);
  }

  .vue-draggable-handle:hover .tool {
    opacity: 1 !important;
  }
}
</style>
