import Axios from "axios"
import Vue from "vue"
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}
export function convertPercentage(percentage) {
  if (!percentage) return "0.00%"
  if (percentage < 0.0001) {
    // 小于0.01%的情况
    const permille = percentage * 1000 // 转换为千分数并截取小数点后两位
    return permille.toFixed(2) + "‰" // 添加千分号
  } else {
    const formattedPercentage = percentage * 100
    return formattedPercentage.toFixed(2) + "%" // 添加百分号
  }
}
// 防抖
export function debounce(fn, delay) {
  let delays = delay || 500
  let timer
  return () => {
    let th = this
    let args = arguments
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      timer = null
      fn.apply(th, args)
    }, delays)
  }
}
// 获取url中参数
export function getUrlParams(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i") // 定义正则表达式
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}
/**
 * UUID生成器
 * @param len 长度 number
 * @param radix 随机数基数 number
 * @returns {string}
 */
export const uuid = (len, radix = 62) => {
  const chars =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("")
  const uuid = []
  let i

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) {
      uuid[i] = chars[Math.floor(Math.random() * radix)]
    }
  } else {
    // rfc4122, version 4 form
    let r

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-"
    uuid[14] = "4"

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = Math.floor(Math.random() * 16)
        uuid[i] = chars[i === 19 ? ((r % 4) % 8) + 8 : r]
      }
    }
  }
  return uuid.join("")
}
/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)")
    ele.className = ele.className.replace(reg, " ")
  }
}
export function brightenKeyword(val, keyword) {
  const Reg = new RegExp(keyword, "i")
  if (val) {
    return val.replace(Reg, `<span style="color: #1463FF;">${keyword}</span>`)
  }
}
export function formatValue(value, isSplit = true) {
  if (value === "" || value === undefined || value === null || isNaN(value)) {
    return "-"
  } else if (isStringNumberOrNumber(value) && isSplit) {
    return toThousands(Number(value)) // 格式化数字为千分位表示
  } else {
    return value
  }
}

export function isStringNumberOrNumber(value) {
  return /^-?\d+(\.\d+)?$/.test(value)
}
// js将数值每超过三位数截取加单位
// js将数值每超过三位数截取加单位
export function formatNumber(number, isSplit = true) {
  var params = {}
  if (!Number(number) || number === "") {
    return {
      value: formatValue(number, isSplit),
      unit: ""
    }
  }
  if (number > 99999999) {
    const formattedNumber = (number / 100000000).toFixed(2)
    params.value = formattedNumber
    params.unit = "亿"
  } else if (number > 99999) {
    const formattedNumber = (number / 10000).toFixed(2)
    params.value = formattedNumber
    params.unit = "万"
  } else {
    const formattedNumber =
      number % 1 === 0 ? number : Number(number).toFixed(2)
    params.value = isSplit ? toThousands(formattedNumber) : formattedNumber
    params.unit = ""
  }
  console.log(params, "params")
  return {
    value: params.value,
    unit: params.unit
  }
}
export function formatPercentage(value) {
  if (value >= 10 || value <= 0) {
    return {
      value: value,
      unit: "%"
    }
  } else if (value >= 1) {
    return {
      value: (value * 10).toFixed(0),
      unit: "‰"
    }
  } else {
    return {
      value: (value * 100).toFixed(0),
      unit: "‱"
    }
  }
}
// 数据过十万，处理成万单位数据
export function bigNumber(value, type) {
  // type 1-判断数据是否大于100000，返回数据，除以万    2-判断数据是否大于100000，返回单位，大于十万返回万，否则返回空
  if (type === 1) {
    if (value >= 100000) {
      return toThousands((value / 10000).toFixed(2))
    }
    return Number(value)
  } else {
    if (value >= 100000) {
      return "万"
    }
    return ""
  }
}
// 处理图表数据
export function metricAxisLabelFormatter(value) {
  if (value >= Math.pow(10, 12)) {
    return `${precision(value / Math.pow(10, 12))}T`
  } else if (value >= Math.pow(10, 9) && value < Math.pow(10, 12)) {
    return `${precision(value / Math.pow(10, 9))}B`
  } else if (value >= Math.pow(10, 6) && value < Math.pow(10, 9)) {
    return `${precision(value / Math.pow(10, 6))}M`
  } else if (value >= Math.pow(10, 3) && value < Math.pow(10, 6)) {
    return `${precision(value / Math.pow(10, 3))}K`
  } else {
    return value
  }

  function precision(num) {
    return num >= 10 ? Math.floor(num) : num.toFixed(1)
  }
}

// 获取文字的宽度
export function getTextWidth(
  text,
  fontSize = 16,
  fontFamily = "PingFangSC-Regular,PingFang SC",
  fontWeight = "normal"
) {
  let canvas = document.createElement("canvas")
  let context = canvas.getContext("2d")
  context.font = fontWeight + " " + fontSize + "px " + fontFamily
  let metrics = context.measureText(text)

  return metrics.width
}
// 千分分割兼容小数点
export function toThousands(num) {
  if (!num) return 0
  let numStr = num.toString()
  let index = numStr.indexOf(".")
  let decimal = ""
  if (index > -1) {
    decimal = numStr.slice(index)
    numStr = numStr.slice(0, index)
  }
  return (
    (numStr || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,") + decimal
  )
}
// 二进制流导出excel
export function exportExcel(url, params,) {
  return new Promise((resolve, reject) => {
    Axios({
      method: "POST",
      url: url,
      data: params,
      headers: Vue.prototype.$utils.auth.getAdminheader(),
      responseType: "blob"
    })
      .then(res => {
        let endfileName=''
        if ( res.headers["content-disposition"]){
           endfileName = decodeURI(
            res.headers["content-disposition"].split("=")[1]
          )
        }
      
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        let downloadElement = document.createElement("a")
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = endfileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
        resolve()
      })
      .catch(err => reject(err))
  })
}

// 处理图片地址
export function handleImgUrl(url) {
  return process.env.NODE_ENV === "development"
    ? `http://192.168.113.153${url}`
    : `${window.location.origin}${url}`
}

// 格式化
export function formatContrast(count, unit) {
  if (count > 0) {
    return `增长了${count}${unit}`
  } else if (count < 0) {
    return `降低了${Math.abs(count)}${unit}`
  } else {
    return "持平"
  }
}
export function extension(chart, axis) {
  console.log("2222")
  var elementDiv = document.getElementById("extension")
  if (!elementDiv) {
    var div = document.createElement("div")
    div.setAttribute("id", "extension")
    div.style.display = "block"
    document.querySelector("html").appendChild(div)
  }
  chart.on("mouseover", function (params) {
    console.log(params)
    if (params.componentType === axis) {
      var elementDiv = document.querySelector("#extension")
      // 设置悬浮文本的位置以及样式
      var elementStyle =
        "position: absolute;z-index: 99999;color: #fff;font-size: 12px;padding: 5px;display: inline;border-radius: 4px;background-color: #303133;box-shadow: rgba(0, 0, 0, 0.3) 2px 2px 8px"
      elementDiv.style.cssText = elementStyle
      elementDiv.innerHTML = params.value
      document.querySelector("html").onmousemove = function (event) {
        var elementDiv = document.querySelector("#extension")
        var xx = event.pageX - 10
        var yy = event.pageY + 15
        elementDiv.style.top = yy + "px"
        elementDiv.style.left = xx + "px"
      }
    }
  })
  chart.on("mouseout", function (params) {
    // 注意这里，我是以X轴显示内容过长为例，如果是y轴的话，需要改为yAxis
    if (params.componentType === axis) {
      var elementDiv = document.querySelector("#extension")

      elementDiv.style.cssText = "display:none"
    }
  })
}
export function trimObjectValues(obj) {
  const trimmedObj = {}

  for (let key in obj) {
    if (typeof obj[key] === "string") {
      trimmedObj[key] = obj[key].trim()
    } else if (Array.isArray(obj[key])) {
      trimmedObj[key] = obj[key]
    } else if (typeof obj[key] === "object") {
      trimmedObj[key] = trimObjectValues(obj[key])
    } else {
      trimmedObj[key] = obj[key]
    }
  }

  return trimmedObj
}
/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
export function handleProp(prop) {
  const propArr = prop.split(".")
  if (propArr.length === 1) return prop
  return propArr[propArr.length - 1]
}
