<template>
  <div>
    <h2 style="margin-bottom: 16px">{{ name }}</h2>
    <vxe-table
      border
      :span-method="mergeRowMethod"
      :data="tableData"
      style="width: 100%;"

    >
      <vxe-column field="sjjnum" title="序号"> </vxe-column>
      <vxe-column field="warn_field" title="监控度量"> </vxe-column>
      <vxe-column field="warn_val" title="数据值"> </vxe-column>
      <vxe-column field="warn_sign" title="预警条件"> </vxe-column>
      <vxe-column field="warn_status" title="阀值"> </vxe-column>
      <vxe-column field="warn_mbz" title="对比值"> </vxe-column>
    </vxe-table>
    <el-pagination
      style="margin-top: 16px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="params.currentPage"
      :page-sizes="[10,20, 40, 80]"
      :page-size="params.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      name: "",
      total: 0,
      id: 0,
      params: {
        currentPage: 1,
        pageSize: 10,
        id: 0,
      },
      tableData: [],
    }
  },
  computed: {},
  created() {
    const { id, name } = this.$route.query
    this.id = id
    this.name = name
  },
  mounted() {},
  watch: {
    id: {
      handler() {
        this.params.id = this.id
        this.getData()
      },
    },
  },
  methods: {
    async getData() {
      const { data } = await this.$httpBi.warning.getWarningLogsById(this.params)
      this.total = data?.totalCount
      this.tableData = data?.resultList
    },
    goBack() {
      this.$router.go(-1)
    },
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.params.currentPage = val
      this.getData()
    },

    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = [ "sjjnum" ]
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
  },
}
</script>

<style scoped lang="less"></style>
