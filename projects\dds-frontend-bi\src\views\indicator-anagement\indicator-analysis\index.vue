<template>
  <DT-View>
    <DT-Header @back="goBack" content="指标对比分析" />
    <div class="container">
      <!-- 相关指标 -->
      <div class="left">
        <div class="left-title">
          可分析维度

          <div class="expand-button">
            <span class="expand-text" @click="toggleExpand">
              {{ isExpanded ? "收起筛选条件" : "更多筛选条件" }}
            </span>
          </div>
        </div>
        <el-form
          :inline="true"
          :model="form"
          class="search-form"
          label-width="70px"
        >
          <el-form-item label="名称">
            <el-input
              v-model="form.indName"
              placeholder="请输入指标名称"
              style="width: 190px"
              clearable
              @input="handleInputSearch"
            ></el-input>
          </el-form-item>
          <el-form-item label="指标域">
            <avue-input-tree
              default-expand-all
              style="width: 190px"
              v-model="form.group"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择指标域"
              :dic="viewGroup"
              @change="handleSearch"
            ></avue-input-tree>
          </el-form-item>
        </el-form>

        <div v-show="isExpanded" class="expanded-form">
          <el-form :inline="true" :model="form" label-width="70px">
            <el-form-item label="指标类型">
              <el-select
                v-model="form.indType"
                placeholder="请选择指标类型"
                style="width: 190px"
                clearable
                @change="handleSearch"
              >
                <el-option
                  v-for="item in getIndicatorType().data"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="包含维度">
              <el-select
                v-model="form.dimName"
                placeholder="请选择包含维度"
                style="width: 190px"
                clearable
                filterable
                @change="handleSearch"
              >
                <el-option label="全部" value=""></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="title">检索到{{ page.total }}项相关指标</div>
        <div class="content dims-content" v-load-more="handleScroll">
          <div
            class="target-item"
            v-for="(item, index) in allIndicators"
            :key="index"
          >
            <IndicatorItem
              :item="item"
              :indicator-type-list="indicatorTypeList"
              :select-indicators="selectIndicators"
              :same-dimension="sameDimension"
              :use-dims="useDims"
              @indicatorsdragStart="onDragStart"
              @indicatorsdragEnd="onDragEnd"
            />
          </div>
        </div>
      </div>
      <div class="right">
        <template v-if="selectIndicators.length">
          <!-- 已经选择指标 -->
          <div class="target">
            <div class="target-title">选择指标</div>
            <div class="target-wrap">
              <el-tag
                v-for="tag in selectIndicators"
                style="margin-right: 12px"
                :key="tag"
                closable
                effect="plain"
                @close="deleteSelectIndicators(tag)"
              >
                <!-- @close="selectIndicators.splice(index, 1)" -->

                {{ tag.indName }}
              </el-tag>
            </div>
            <div
              class="target-content"
              id="droppable"
              :class="isDragover ? 'dragover' : ''"
              v-show="dargging"
              @dragover.prevent="isDragover = true"
              @dragleave="isDragover = false"
              @drop="onDrop"
            >
              请将指标拖动到此处
            </div>
          </div>
          <!-- 已选择维度 -->
          <div class="dimensionality">
            <div class="dimensionality-title">分析维度</div>
            <div class="dimensionality-content">
              <el-tag
                v-for="(tag, index) in analyzeDims"
                style="margin-right: 12px; cursor: pointer"
                :key="tag"
                closable
                effect="plain"
                @click="editDimensionality(tag, index)"
                @close="closeDimensionality(index)"
              >
                {{ tag.alias }}
              </el-tag>
            </div>
            <el-button
              @click="addDimensionality"
              :loading="commonDimensionLoading"
            >
              请添加分析维度
            </el-button>
          </div>
          <!-- 过滤维度 -->
          <div class="filter">
            <div class="filter-title">过滤维度</div>
            <div class="filter-content">
              <el-form :inline="true" class="inline-form">
                <el-form-item
                  v-for="item in filterDims"
                  :label="item.dimAndLevelName || item.dimName"
                  :key="item.dimCol"
                >
                  <LevelMultipleSelect
                    v-model="item.dimValList"
                    v-if="item.levelCode && !item.enableClustering"
                    :is-selected-all="item.isSelectedAll"
                    :is-selected-all-name="item.isSelectedAllName"
                    :disabled="item.isDisabled"
                    :props="{
                      label: 'value',
                      value: 'valueItem'
                    }"
                    value-key="valueCode"
                    style="width: 170px; margin-left: 6px"
                    :level-code="item.levelCode"
                    @change="getTableData"
                  />
                  <ClusterMultipleSelectValue
                    v-if="item.levelCode && item.enableClustering"
                    :dim-values.sync="item.dimValList"
                    :cluster-list.sync="item.clusterList"
                    v-model="item.clusterCodes"
                    :dim-col="item.dimCol"
                    :cluster-code-map-cluster-name.sync="
                      item.clusterCodeMapClusterName
                    "
                    :cluster-name-map-value.sync="item.clusterNameMapValue"
                    value-code="value"
                    :is-selected-all="item.isSelectedAll"
                    :is-selected-all-name="item.isSelectedAllName"
                    :disabled="item.isDisabled"
                    :level-code="item.levelCode"
                    @change="getTableData"
                    style="width: 170px; margin-left: 6px"
                  />
                </el-form-item>
              </el-form>
              <el-button @click="openFilterDim">请选择过滤维度</el-button>
              <el-button
                style="margin-left: auto"
                @click="openSaveThemeDialog"
                type="primary"
                v-if="rowData.length"
              >
                保存为分析主题
              </el-button>
            </div>
          </div>
          <!-- 汇总表 -->
          <div class="summary">
            <div class="summary-title">
              {{ themeName || "汇总表" }}
              <div class="btn" v-if="rowData.length">
                <el-button
                  @click="openAiDialog"
                  v-if="$checkPermission(['ind:aiReport'])"
                >
                  生成AI报表
                </el-button>
                <el-button @click="openChartDialog">生成图表</el-button>
                <el-button type="primary" @click="handleExport">导出</el-button>
              </div>
            </div>
            <el-table
              :data="rowData"
              ref="tableEl"
              v-loading="tableLoading"
              style="width: 100%"
              height="calc(100% - 20px)"
            >
              <template v-for="(column, index) in headKeys">
                <el-table-column
                  v-if="!column.children"
                  :prop="column.prop"
                  :label="column.label"
                  :key="`single-${index}`"
                  :min-width="150"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ row[column.prop] == "" ? "-" : row[column.prop] }}
                    {{
                      column.prop === "indicator" && row.indUnit
                        ? `(${row.indUnit})`
                        : ""
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  :label="column.label"
                  align="center"
                  :min-width="150"
                  :key="`group-${index}`"
                >
                  <el-table-column
                    v-for="(child, childIndex) in column.children"
                    :key="`child-${index}-${childIndex}`"
                    :prop="child.prop"
                    :label="child.label"
                    :min-width="120"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      {{ row[child.prop] == "" ? "-" : row[child.prop] }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </template>
        <div
          v-else
          class="empty"
          :class="isDragover ? 'dragover' : ''"
          @dragover.prevent="isDragover = true"
          @dragleave="isDragover = false"
          @drop="onDrop"
        >
          <span>请将指标拖动到此处</span>
          <span style="margin: 10px">或</span>
          <el-button type="primary" @click="openThemeDialog">
            加载分析主题
          </el-button>
        </div>
      </div>
    </div>
    <el-dialog title="添加分析维度" :visible.sync="dialogVisible" width="600px">
      <div class="rlue">
        从已选择的指标的公共维度当中，选择至少一个维度，该维度将作为这些指标的分析维度
      </div>
      <el-form
        ref="formEl"
        :model="addDimsObject"
        :rules="rules"
        label-width="100px"
        class="form"
        label-position="left"
        hide-required-asterisk
      >
        <el-form-item label="添加维度" style="margin-bottom: 0" prop="dimList">
          <div
            class="form-item"
            v-for="(item, index) in addDimsObject.dimList"
            :key="index"
          >
            <div style="display: flex" gutter="10">
              <el-col>
                <el-form-item style="margin-bottom: 8px">
                  <div style="display: flex">
                    <el-select
                      v-model="item.dimCol"
                      clearable
                      filterable
                      placeholder="请选择派生维度"
                      @change="changeDim($event, index)"
                      style="width: 170px"
                      class="myselect"
                    >
                      <el-option
                        v-for="ele in sameDimension"
                        :disabled="
                          addDimsObject.dimList.some(
                            e => e.dimCol === ele.dimCol
                          )
                        "
                        :key="ele.dimCol"
                        :label="ele.dimAndLevelName || ele.dimName"
                        :value="ele.dimCol"
                      ></el-option>
                    </el-select>

                    <LevelMultipleSelect
                      v-model="item.dimValList"
                      v-if="item.levelCode && !item.enableClustering"
                      :is-selected-all="item.isSelectedAll"
                      :is-selected-all-name="item.isSelectedAllName"
                      :disabled="item.isDisabled"
                      :props="{
                        label: 'value',
                        value: 'valueItem'
                      }"
                      value-key="valueCode"
                      style="width: 170px; margin-left: 6px"
                      :level-code="item.levelCode"
                    />
                    <ClusterMultipleSelectValue
                      v-if="item.levelCode && item.enableClustering"
                      v-model="item.clusterCodes"
                      :dim-col="item.dimCol"
                      :cluster-code-map-cluster-name.sync="
                        item.clusterCodeMapClusterName
                      "
                      :cluster-name-map-value.sync="item.clusterNameMapValue"
                      value-code="value"
                      :is-selected-all="item.isSelectedAll"
                      :is-selected-all-name="item.isSelectedAllName"
                      :disabled="item.isDisabled"
                      :level-code="item.levelCode"
                      style="width: 170px; margin-left: 6px"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col style="margin-left: 10px">
                <el-button
                  type="danger"
                  icon="el-icon-minus"
                  circle
                  v-if="addDimsObject.dimList.length > 1"
                  @click="removeDerive(item)"
                ></el-button>
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="addDerive"
                  circle
                ></el-button>
              </el-col>
            </div>
          </div>
        </el-form-item>

        <div
          style="margin: 18px 0"
          v-if="addDimsObject.dimList && addDimsObject.dimList.length < 2"
        >
          <el-radio v-model="addDimsObject.isMerge" :label="0">
            会生成多列分析维度，每列名称取维度值
          </el-radio>
        </div>

        <template
          v-if="
            addDimsObject.isMerge == 0 &&
            addDimsObject.dimList &&
            addDimsObject.dimList.length < 2
          "
        >
          <div style="margin-bottom: 18px; margin-left: 24px">
            <el-checkbox
              v-model="addDimsObject.isShowName"
              :true-label="1"
              :false-label="0"
            >
              展示维度名称
            </el-checkbox>
          </div>
          <div style="margin-bottom: 18px; margin-left: 24px">
            <el-checkbox
              v-model="addDimsObject.isShowTotal"
              :true-label="1"
              :false-label="0"
            >
              展示合计
            </el-checkbox>
          </div>
        </template>
        <div
          style="margin-bottom: 18px"
          v-if="addDimsObject.dimList && addDimsObject.dimList.length < 2"
        >
          <el-radio v-model="addDimsObject.isMerge" :label="1">
            合并为一列分析维度
          </el-radio>
        </div>

        <el-form-item
          label="分析维度名称"
          v-if="
            addDimsObject.isMerge !== 0 ||
            (addDimsObject.dimList && addDimsObject.dimList.length > 1)
          "
        >
          <el-input
            v-model="addDimsObject.alias"
            placeholder="如果不命名，自动命名为所选择的维度"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddDims">确 定</el-button>
      </span>
    </el-dialog>

    <SaveTheme
      ref="SaveTheme"
      :indicators="selectIndicators"
      :analyze-dims="analyzeDims"
      :filter-dims="filterDims"
    />

    <FilterDimDialog
      ref="FilterDimDialog"
      :filter-dims.sync="filterDims"
      :same-dimension="sameDimension"
      :props="{
        label: 'dimName',
        value: 'dimCol',
        valueList: 'dimValList'
      }"
    />

    <ThemeDialog ref="ThemeDialog" @loadTheme="handleLoadTheme" />
    <ChartDialog
      ref="ChartDialog"
      :indicators="selectIndicators"
      :analyze-dims="analyzeDims"
      :row-data="rowData"
      :head-keys="headKeys"
      :theme-name="themeName"
      @exportExcel="handleExport"
    />

    <AiReportDialog
      ref="AiReportDialog"
      :row-data="rowData"
      :head-keys="headKeys"
      :use-dims="useDims"
      :analyze-dims="analyzeDims"
      :theme-name="themeName"
      @success="handleAiReportSuccess"
    />
  </DT-View>
</template>
<script>
import options from "../mixins/options"
import IndicatorItem from "./IndicatorItem"
import FilterDimDialog from "../components/FilterDimDialog.vue"
import ThemeDialog from "./ThemeDialog"
import SaveTheme from "./SaveTheme"
import ChartDialog from "./ChartDialog.vue"
import LevelMultipleSelect from "../components/LevelMultipleSelect.vue"
import ClusterMultipleSelectValue from "../components/ClusterMultipleSelectValue.vue"
import AiReportDialog from "./AiReportDialog.vue"
import { exportExcel } from "@/utils"
import { debounce } from "lodash"
export default {
  components: {
    IndicatorItem,
    ChartDialog,
    FilterDimDialog,
    ThemeDialog,
    SaveTheme,
    LevelMultipleSelect,
    ClusterMultipleSelectValue,
    AiReportDialog
  },
  mixins: [options],

  props: {},
  data() {
    return {
      dargging: false,
      dialogVisible: false,
      tableLoading: false,
      editingIndex: undefined, // 添加编辑索引
      page: {
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      allIndicators: [], // 全部指标
      indicatorTypeList: [], // 指标类型
      selectIndicators: [], // 已选择指标
      sameDimension: [], // 相同维度
      commonDimensionLoading: false, // 获取公共维度loading
      addDimsObject: {}, // 添加维度对象
      analyzeDims: [], // 分析维度
      pswdOptionsMap: {}, // 公共维度选项
      filterDims: [], // 过滤维度项
      rowData: [], // 表格数据
      headKeys: [], // 汇总表格表头
      loading: false,
      isDragover: false,
      isExpanded: false,
      form: {
        indName: "", // 指标名称
        group: "", // 指标域
        indType: "", // 指标类型
        dimName: "" // 包含维度
      },
      themeName: "", // 主题名称
      columns: [
        {
          label: "指标名称",
          prop: "indName",
          search: {
            el: "input"
          }
        },
        {
          label: "指标域",
          prop: "group",
          search: {
            el: "cascader",
            slot: true
          }
        },
        {
          label: "指标类型",
          prop: "indType",
          type: "select",
          search: {
            el: "select"
          },
          enum: this.getIndicatorType
        },
        {
          label: "包含维度",
          prop: "dimName",
          search: {
            el: "input"
          }
          // fieldNames: {
          //   label: "wdmc",
          //   value: "id"
          // },
        },
        {
          label: "计算方式",
          prop: "jsfs",
          search: {
            el: "select"
          },
          enum: []
        },
        {
          label: "支持预警",
          prop: "enableWarning",
          search: {
            el: "select"
          },
          enum: [
            {
              label: "是",
              value: 1
            },
            {
              label: "否",
              value: 0
            }
          ]
        }
      ],
      rules: {
        dimList: [
          {
            validator: (rule, value, callback) => {
              console.log(value, "value")
              if (!value || !value.length) {
                callback(new Error("请至少添加一个维度项"))
                return
              }
              const hasEmptyDim = value.some(item => !item.dimCol)
              if (hasEmptyDim) {
                callback(new Error("请选择维度"))
                return
              }
              const hasEmptyDimVal = value.some(
                item => item.clusterCodes?.length || item.dimValList?.length
              )
              if (!hasEmptyDimVal) {
                callback(new Error("请选择维度值"))
                return
              }
              callback()
            }
          }
        ]
      },
      currentThemeCode: ""
    }
  },
  created() {
    this.columns[4].enum = this.jsfsList
    this.indicatorTypeList = this.getIndicatorType().data
    // Create debounced version of handleSearch
    this.debouncedHandleSearch = debounce(this.handleSearch, 300)
  },
  computed: {
    // 将分析维度和过滤维度的dimCol合并去重
    useDims() {
      // 1. 提取分析维度的所有 dimCol
      const analyzeDimCols = this.analyzeDims.flatMap(item => item.dimList)
      // 3. 合并并去重
      console.log(analyzeDimCols, "analyzeDimCols")
      return [...new Set([...analyzeDimCols, ...this.filterDims])]
    }
  },
  mounted() {
    this.getAllViewGroup()
    // this.getAllIndicators()
  },
  watch: {
    // 监听过滤维度变化，选择过滤维度后清空主题代码
    filterDims: {
      handler(newVal, oldVal) {
        // 只有在过滤维度真正发生变化时才清空主题代码
        // 避免在初始化或加载主题时误清空
        if (oldVal && oldVal.length !== newVal.length) {
          this.currentThemeCode = ""
        }
      },
      deep: true
    }
  },
  methods: {
    handleInputSearch() {
      this.debouncedHandleSearch()
    },
    handleSearch() {
      this.$nextTick(() => {
        this.allIndicators = []
        this.page.currentPage = 1
        console.log(this.form.group)
        this.getAllIndicators()
      })
    },
    async getAllIndicators() {
      this.loading = true
      const { data } = await this.$httpBi.analyzeTheme.getIndicatorList({
        ...this.form,
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage
      })

      this.allIndicators.push(...data.records)
      this.page.total = data.total
      this.loading = false

      this.$nextTick(() => {
        const el = document.querySelector(".dims-content")
        if (
          el.scrollTop === 0 &&
          el.scrollHeight === el.clientHeight &&
          this.allIndicators.length < this.page.total
        ) {
          this.handleScroll()
        }
      })
    },
    handleScroll() {
      if (!this.loading && this.allIndicators.length < this.page.total) {
        this.page.currentPage++
        console.log("/////////////")
        this.getAllIndicators()
      }
    },
    // 返回指标列表
    goBack() {
      this.$router.push({
        path: "/ddsBi/indicatorAnagement"
      })
    },
    // 拖拽开始
    onDragStart(data) {
      this.dargging = true
      console.log("拖拽开始", this.dargging)
      this.dragged = data
    },
    // 拖拽结束
    onDragEnd() {
      console.log("拖拽结束")

      if (this.dragged) {
        this.dragged = null
      }
      this.dargging = false
    },
    // 拖拽放下
    onDrop(e) {
      console.log(e, "拖拽放下")
      console.log(this.dragged, "this.dragged")
      if (!this.dargging) return
      e.preventDefault()
      const isHas = this.selectIndicators.some(
        item => item.indCode === this.dragged.indCode
      )
      if (isHas) {
        this.$message.warning("该指标已存在")
        if (this.selectDimensionList.length) {
          this.getTableData()
        }
        this.dargging = false
      } else {
        this.selectIndicators.push(this.dragged)
        // 拖拽指标后清空主题代码
        this.currentThemeCode = ""
      }
      this.getSameDimension()

      this.isDragover = false
      this.tableData = []
      this.headKeys = []
    },
    // 获取相同维度
    async getSameDimension() {
      console.log(this.selectIndicators, "this.selectIndicators")
      let params = this.selectIndicators.map(item => ({
        indCode: item.indCode,
        lxbm: item.indType
      }))
      this.commonDimensionLoading = true
      const { data } = await this.$httpBi.analyzeTheme.getPublicDim(params)
      this.sameDimension = data
      this.commonDimensionLoading = false
      // 获取 sameDimension 中的 dimCol
      const sameDimCols = this.sameDimension.map(item => item.dimCol)

      // 过滤 filterDims 中的 dimCol，剔除不在 sameDimCols 中的 dimCol
      this.filterDims = this.filterDims.filter(item =>
        sameDimCols.includes(item.dimCol)
      )
      // 过滤 analyzeDims 中的 dimList，剔除不在 sameDimCols 中的 dimCol
      this.analyzeDims = this.analyzeDims.filter(item => {
        // 过滤 dimList
        return item.dimList.every(dim => sameDimCols.includes(dim.dimCol))
      })
      this.getTableData()
    },
    // 获取过滤维度值
    getDimsValue() {
      this.filterDims.forEach(item => {
        this.getPsOption(item.dimCol)
      })
    },
    // 选择维度值变化
    handleDimValChange() {
      this.getTableData()
    },
    // 选择维度项
    async changeDim(dimCol, index) {
      const item = {
        ...this.sameDimension.find(item => item.dimCol === dimCol)
      }
      this.$set(this.addDimsObject.dimList, index, {
        ...item,
        dimValType: item.dimType,
        dimType: 1,
        dimValList: [],
        clusterNameMapValue: {},
        clusterCodeMapClusterName: {}
      })
    },
    // 选择维度值
    handleAddDims() {
      this.$refs.formEl.validate(valid => {
        if (!valid) {
          this.$message.warning("请完善维度信息")
          return
        }
        console.log(this.addDimsObject, "this.addDimsObject")

        this.dialogVisible = false
        // 拼接命名
        if (
          (this.addDimsObject.dimList.length === 1 &&
            this.addDimsObject.isMerge === 0) ||
          !this.addDimsObject.alias
        ) {
          this.addDimsObject.alias = this.addDimsObject.dimList.reduce(
            (total, item, index) => {
              return total + (index === 0 ? "" : "、") + item.dimName
            },
            ""
          )
        }

        if (this.editingIndex !== undefined) {
          // 编辑模式
          this.$set(this.analyzeDims, this.editingIndex, {
            ...this.addDimsObject
          })
          this.editingIndex = undefined
        } else {
          console.log("新增模式")
          // 新增模式
          // 检查alias是否重复
          const newAlias = this.addDimsObject.alias
          const isDuplicate = this.analyzeDims.some(
            item => item.alias === newAlias
          )
          if (isDuplicate) {
            this.$message.error("维度别名不能重复，请修改别名")
            return
          }
          this.analyzeDims.push({
            ...this.addDimsObject
          })
        }

        // 选择分析维度后清空主题代码
        this.currentThemeCode = ""

        this.getTableData()
        let dimCols = this.addDimsObject.dimList.map(item => item.dimCol)
        // 如果选择使用某个维度,则过滤维度删除该维度
        this.filterDims = this.filterDims.filter(
          item => !dimCols.includes(item.dimCol)
        )

        console.log(this.analyzeDims, "this.analyzeDims")
      })
    },
    // 获取公共维度选项
    async getPsOption(dimCol) {
      const { data } =
        await this.$httpBi.compositeIndicator.getSelectDimensionValue({
          indType: this.selectIndicators[0].indType,
          dimCol,
          indCode: this.selectIndicators[0].indCode
        })
      console.log(data, "data")
      this.$set(
        this.pswdOptionsMap,
        dimCol,
        data.map(item => ({
          ...item,
          dimVal: item.dimValue || ""
        }))
      )
    },
    // 删除已经拖拽的指标
    deleteSelectIndicators(item) {
      this.selectIndicators = this.selectIndicators.filter(
        e => e.indCode !== item.indCode
      )
      // 删除指标后清空主题代码
      this.currentThemeCode = ""
      if (this.selectIndicators.length === 0) {
        this.selectDimensionIds = []
        this.sameDimension = []
        this.addDimsObject = {}
        this.analyzeDims = []
      } else {
        this.getSameDimension()
      }
    },

    // 添加维度
    addDimensionality() {
      this.dialogVisible = true
      this.editingIndex = undefined
      this.addDimsObject = {
        dimType: 1,
        alias: "",
        isMerge: 0,
        isShowName: 0,
        isShowTotal: 0,
        dimList: [
          {
            dimType: 1,
            indCode: "",
            dimCol: "",
            dimValList: [],
            clusterNameMapValue: {},
            clusterCodeMapClusterName: {}
          }
        ]
      }
      // 重置表单验证
      this.$nextTick(() => {
        this.$refs.formEl && this.$refs.formEl.clearValidate()
      })
    },
    // 添加维度项
    addDerive() {
      this.addDimsObject.dimList.push({
        indCode: "",
        dimType: 1,
        dimCol: "",
        dimValList: []
      })
    },
    // 删除维度项
    removeDerive(item) {
      var index = this.addDimsObject.dimList.indexOf(item)
      if (index !== -1 && this.addDimsObject.dimList.length > 1) {
        this.addDimsObject.dimList.splice(index, 1)
      }
    },
    // 打开filter弹窗
    openFilterDim() {
      let allDims = this.analyzeDims
        .flatMap(item => item.dimList)
        .map(item => item.dimCol) // 获取dimValue
        .filter((value, index, self) => self.indexOf(value) === index) // 去重
      const allFilterDims = this.sameDimension
        .filter(item => !allDims.includes(item.dimCol))
        .map(item => ({
          ...item,

          clusterNameMapValue: item.clusterNameMapValue || {},
          clusterCodeMapClusterName: item.clusterCodeMapClusterName || {}
        }))
      this.$refs.FilterDimDialog.open(allFilterDims)
    },
    async getTableData() {
      this.tableLoading = true
      console.log(this.filterDims, "this.filterDims")
      console.log(this.analyzeDims, "this.analyzeDims")

      const { data } = await this.$httpBi.analyzeTheme.getSummaryTable({
        indicators: this.selectIndicators,
        themeCode: this.currentThemeCode || null,
        analyzeDims: this.analyzeDims.map(item => {
          return {
            ...item,
            dimList: item.dimList.map(e => {
              return {
                ...e,
                dimType: 1,
                clusterCodes:
                  (e.clusterCodes && e.clusterCodes.join(",")) || "",
                dimValList: this.processDimValList(e)
              }
            })
          }
        }),
        filterDims: this.filterDims.map(item => ({
          ...item,
          dimType: 2,
          clusterCodes:
            (item.clusterCodes && item.clusterCodes.join(",")) || "",
          dimValList: this.processDimValList(item)
        }))
      })
      this.headKeys = this.filterData(data.head)

      this.$nextTick(() => {
        this.$refs.tableEl && this.$refs.tableEl.doLayout()
        this.rowData = data.rowData
      })
      console.log(data.rowData, "data.rowData")
      this.themeName = data.themeName
      this.tableLoading = false
    },
    // 打开主题弹窗
    openThemeDialog() {
      this.$refs.ThemeDialog.open()
    },
    // 打开保存主题
    openSaveThemeDialog() {
      this.$refs.SaveTheme.open()
    },
    // 加载主题
    handleLoadTheme(data) {
      this.currentThemeCode = data.themeCode
      this.selectIndicators = data.indicators
      this.analyzeDims = data.analyzeDims.map(item => {
        return {
          ...item,
          dimList: item.dimList.map(e => {
            return {
              ...e,
              clusterCodeMapClusterName: {},
              clusterNameMapValue:
                e.enableClustering === 1
                  ? e.dimValList.reduce((acc, user) => {
                      acc[user.dimVal] = user.clusterDimValList
                      return acc
                    }, {})
                  : {},
              clusterCodes: (e.clusterCodes && e.clusterCodes.split(",")) || "",
              dimType: 1,
              dimValList: e.dimValList.map(el => ({
                value: el.dimVal,
                valueCode: el.dimValCode,
                valueItem: {
                  valueCode: el.dimValCode,
                  value: el.dimVal
                }
              }))
            }
          })
        }
      })
      this.filterDims = data.filterDims.map(item => ({
        ...item,
        dimType: 2,
        clusterCodeMapClusterName: {},
        clusterNameMapValue:
          item.enableClustering === 1
            ? item.dimValList.reduce((acc, user) => {
                acc[user.dimVal] = user.clusterDimValList
                return acc
              }, {})
            : {},
        clusterCodes: (item.clusterCodes && item.clusterCodes.split(",")) || "",
        dimValList: item.dimValList.map(el => ({
          value: el.dimVal,
          valueCode: el.dimValCode,
          valueItem: {
            valueCode: el.dimValCode,
            value: el.dimVal
          }
        }))
      }))
      this.getSameDimension()
      // this.getTableData()
    },
    // 导出excel
    async handleExport() {
      await exportExcel("/api/dds-server-bi/analyzeTheme/export", {
        indicators: this.selectIndicators,
        themeCode: this.currentThemeCode || null,
        analyzeDims: this.analyzeDims.map(item => {
          return {
            ...item,
            dimList: item.dimList.map(e => {
              return {
                ...e,
                dimType: 1,
                clusterCodes:
                  (e.clusterCodes && e.clusterCodes.join(",")) || "",
                dimValList: this.processDimValList(e)
              }
            })
          }
        }),
        filterDims: this.filterDims.map(item => ({
          ...item,
          dimType: 2,
          clusterCodes:
            (item.clusterCodes && item.clusterCodes.join(",")) || "",
          dimValList: this.processDimValList(item)
        }))
      })
    },
    // 打开生成图表
    openChartDialog() {
      this.$refs.ChartDialog.open()
    },

    // 打开AI报表弹窗
    openAiDialog() {
      this.$refs.AiReportDialog.open()
    },

    // AI报表生成成功回调
    handleAiReportSuccess(data) {
      console.log("AI报表生成成功:", data)
      // 这里可以添加成功后的处理逻辑
    },
    closeDimensionality(index) {
      this.analyzeDims.splice(index, 1)
      // 删除分析维度后清空主题代码
      this.currentThemeCode = ""
      this.getTableData()
    },

    // 编辑维度
    editDimensionality(tag, index) {
      this.dialogVisible = true
      this.editingIndex = index
      this.addDimsObject = {
        ...tag,
        isShowName: tag.isShowName || 0,
        isShowTotal: tag.isShowTotal || 0,
        dimList: tag.dimList.map(item => ({
          ...item,
          dimValList: item.dimValList || []
        }))
      }
    },
    filterData(data) {
      return data.reduce((result, item) => {
        // 如果父级的 isDisplay 为 0，且子列有 isDisplay 为 1 的项
        if (item.isDisplay === 0 && item.children) {
          // 过滤子项中 isDisplay 为 1 的项
          const children = item.children.filter(child => child.isDisplay === 1)
          // 如果有符合条件的子项，添加到结果中
          if (children.length > 0) {
            result.push(...children)
          }
        } else {
          // 如果父级的 isDisplay 为 1，直接添加
          result.push(item)
        }
        return result
      }, [])
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    processDimValList(item) {
      if (item.enableClustering === 1) {
        return Object.entries(item.clusterNameMapValue).map(([key, value]) => ({
          dimVal: item.clusterCodeMapClusterName[key] || key,
          dimCol: item.dimCol,
          clusterDimValList: value
        }))
      }
      return item.dimValList.map(el => ({
        dimVal: el.value,
        dimCol: item.dimCol
      }))
    }
  }
}
</script>

<style scoped lang="scss">
.dt-header {
  margin-bottom: 0;
}

.inline-form {
  max-width: calc(100% - 120px);
  display: flex;
  overflow: auto;

  //滚动条样式
  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #3875f6;
  }

  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: transparent;
  }

  ::v-deep .el-form-item__label {
    min-width: max-content;
  }

  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 5px !important;
    margin-top: 5px;
    display: flex;
  }
}

::v-deep .avue-form__group.avue-form__group--flex {
  padding: 15px 0;
}

.container {
  position: relative;
  display: flex;

  .left {
    position: relative;
    height: calc(100vh - 240px);
    min-height: 680px;

    flex: 0 0 300px;
    margin-right: 24px;
    border-right: 1px solid #edeff0;
    padding-right: 24px;
    overflow-y: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .left-title {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #060607;
      line-height: 16px;
      text-align: left;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 0;

      .expand-button {
        .expand-text {
          color: #1890ff;
          cursor: pointer;
          font-size: 14px;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }

    //滚动条样式
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: #3875f6;
    }

    ::-webkit-scrollbar-track {
      border-radius: 3px;
      background: rgba(0, 0, 0, 0.1);
    }

    .title {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }

    .content {
      flex: 1;

      overflow: auto;
      box-sizing: border-box;

      background: #ffffff;
      margin-top: 24px;

      .target-item {
        margin-bottom: 20px;

        .target-item-title {
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 14px;
          margin-bottom: 20px;
        }

        .target-item-content {
          display: flex;
          margin-bottom: 12px;

          .target-type {
            min-width: 108px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .target-type-item {
              display: flex;
              flex-direction: column;

              .zbmc {
                height: 14px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #222222;
                line-height: 14px;
              }

              .zblx {
                margin-top: 4px;
                height: 12px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 12px;
              }
            }
          }

          .tags {
            flex: 1;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;

            .tag-item {
              padding: 0 8px;
              margin-right: 4px;
              margin-bottom: 4px;
              height: 20px;
              background: rgba(91, 143, 249, 0.08);
              border-radius: 2px;
              border: 1px solid rgba(91, 143, 249, 0.4);
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #5b8ff9;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              &.gray {
                background: #f1f1f1;
                color: #666666;
              }

              &.deep {
                background: #1890ff;
                color: #fff;
              }
            }
          }
        }
      }
    }
  }

  .right {
    position: absolute;
    right: 0;
    top: 0;
    width: calc(100% - 324px);
    height: 100%;
    margin-left: 24px;
    display: flex;
    flex-direction: column;

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border-radius: 4px;
      border: 1px dashed #cbced1;

      &.dragover {
        border-color: #1890ff;
      }
    }

    .target {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      height: 32px;

      .target-wrap {
        max-width: calc(100% - 90px);
        display: flex;
        align-items: center;
        overflow-x: auto;
        padding: 5px 0;
        box-sizing: border-box;

        ::v-deep .el-tag--small {
          height: 32px;
          padding: 0 8px;
          line-height: 30px;
        }

        //滚动条样式
        &::-webkit-scrollbar {
          height: 6px;
          width: 0px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: #3875f6;
        }

        &::-webkit-scrollbar-track {
          border-radius: 3px;
          background: rgba(0, 0, 0, 0.1);
        }
      }

      .target-title {
        flex: 0 0 70px;
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        margin-right: 16px;
      }

      .target-content {
        position: absolute;
        left: -10px;
        top: -5px;

        width: 100%;
        padding: 20px;
        text-align: center;
        font-size: 12px;
        height: 30px;
        border: 2px dashed #67c23a;
        background: rgba(103, 194, 58, 0.2);
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.dragover {
          border-color: #1890ff;
          background: rgba(56, 117, 246, 0.2);
        }
      }
    }

    .dimensionality {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      ::v-deep .el-tag--small {
        height: 32px;
        padding: 0 8px;
        line-height: 30px;
      }

      .dimensionality-title {
        flex: 0 0 70px;
        margin-right: 16px;
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        white-space: nowrap;
      }

      .dimensionality-content {
        max-width: calc(100% - 250px);
        display: flex;
        align-items: center;
        overflow-x: auto;
        padding: 5px 0;

        //滚动条样式
        &::-webkit-scrollbar {
          height: 3px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: #3875f6;
        }

        &::-webkit-scrollbar-track {
          border-radius: 3px;
          background: transparent;
        }
      }
    }

    .filter {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      height: 32px;
      width: 100%;

      .filter-title {
        flex: 0 0 70px;
        margin-right: 16px;
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        white-space: nowrap;
      }

      .filter-content {
        width: 100%;
        display: flex;
        align-items: center;
        overflow-x: auto;
        padding: 5px 0;
      }
    }

    .summary {
      height: calc(100% - 200px);

      .summary-title {
        font-size: 16px;
        line-height: 1.2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .btn{
        min-width: 250px;
      }
    }
  }
}

.rlue {
  height: 40px;
  background: rgba(91, 143, 249, 0.06);
  display: flex;
  align-items: center;
  padding-left: 16px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #222222;
  margin-bottom: 20px;
}

::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
  max-height: 200px;
  overflow: auto;

  .el-checkbox {
    margin-bottom: 10px;
  }
}

::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  position: relative;
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.search-item {
  display: flex;
  align-items: center;
  position: absolute;
  top: 10px;
  right: 24px;
  z-index: 9999;

  .tabs {
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    overflow: hidden;

    .tab {
      padding: 0 15px;
      border-right: 1px solid #e5e5e5;
      cursor: pointer;
      height: 30px;

      &:last-child {
        border: none;
      }

      &.active {
        background: #2361dbc7;
        color: #fff;
      }
    }
  }

  .name {
    font-size: 16px;
    padding-right: 10px;
  }
}

.chart-box {
  position: relative;
  width: 100%;
  height: 60vh;
  padding: 30px;
}

.pie-chart {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .pie-item {
    width: 50%;
    height: 28vh;
  }
}

.search-form {
  position: relative;
}

/* 为了确保自定义样式能覆盖默认样式，你可能需要使用更具体的选择器 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置滚动条高度 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 3px;
  /* 滚动条圆角 */
  background: rgba(0, 0, 0, 0.2);
  /* 滚动条颜色 */
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  /* 滚动条轨道阴影 */
  background-color: #f0f0f0;
  /* 滚动条轨道颜色 */
  border-radius: 3px;
  /* 滚动条圆角 */
}
</style>
