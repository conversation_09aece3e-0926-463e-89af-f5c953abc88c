import dark from "@/assets/json/echartsThemes/dark.json"
import light from "@/assets/json/echartsThemes/light.json"

const state = {
  theme: window.localStorage.getItem("BItheme") || "light",
  dashboardsInfo: window.localStorage.getItem("dashboardsInfo")
    ? JSON.parse(window.localStorage.getItem("dashboardsInfo"))
    : {},
  dashboards: window.localStorage.getItem("viewDashboards")
    ? JSON.parse(window.localStorage.getItem("viewDashboards"))
    : [],
  themeObj: {
    dark,
    light
  }
}

const mutations = {
  SET_DASHBOARDS_INFO: (state, dashboardsInfo) => {
    state.dashboardsInfo = dashboardsInfo
    window.localStorage.setItem(
      "dashboardsInfo",
      JSON.stringify(dashboardsInfo)
    )
  },
  VIEW_DASHBOARDS(state, data) {
    state.dashboards = data
    window.localStorage.setItem("viewDashboards", JSON.stringify(data))
  },
  CHANGE_SETTING: (state, name) => {
    state.theme = name
    window.localStorage.setItem("BItheme", name)
  }
}

const actions = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
