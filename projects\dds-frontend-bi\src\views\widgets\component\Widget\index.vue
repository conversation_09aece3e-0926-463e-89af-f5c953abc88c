<template>
  <div
    class="chart-box"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    {{ theme }}
    <Table
      v-if="widgetProps.selectedChartId == ChartTypes.Table && !loading"
      v-on="$listeners"
      ref="table"
      :widget-props="widgetProps"
    />
    <Scorecard
      v-else-if="
        widgetProps.selectedChartId == ChartTypes.Scorecard &&
          !loading &&
          widgetProps.data.length
      "
      :widget-props="widgetProps"
    />
    <Chart
      v-else-if="
        widgetProps.selectedChartId !== ChartTypes.Table &&
          !loading &&
          widgetProps.data.length
      "
      :widget-props="widgetProps"
      ref="chart"
      v-on="$listeners"
    />
    <div v-else class="empty">暂无数据</div>
  </div>
</template>

<script>
import Chart from "./Chart"
import Table from "./Table"
import Scorecard from "./Scorecard"
import ChartTypes from "../../config/ChartTypes.js"
export default {
  components: {
    Chart,
    Table,
    Scorecard,
  },
  props: {
    widgetProps: {
      type: Object,
    },
    loading: {
      type: Boolean,
    },
  },
  data() {
    return {
      ChartTypes,
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    download(name, viewId) {
      switch (this.widgetProps.name) {
      case "scorecard":
        break
      case "table":
        this.$refs.table.exportExcel(name, viewId)
        break
      default:
        this.$refs.chart.saveAsImage(name)
        break
      }
    },
  },
}
</script>

<style scoped lang="scss">
.chart-box {
  width: 100%;
  box-sizing: border-box;
  height: 100%;
  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
