<template>
  <div class="SlideThumbnailList">
    <div
      class="SlideThumbnailItem"
      :class="{ active: currentSlide.id == item.id }"
      v-for="(item, index) in displaySlides"
      :key="index"
      @click="setCurrentSlide(item)"
      @contextmenu.prevent="openMenu(item, $event)"
    >
      <div class="serial">{{ index + 1 }}</div>
      <div
        class="content"
        :style="{
          backgroundImage: item.config.slideParams
            ? env === 'development'
              ? `url('http://192.168.113.153${item.config.slideParams.avatar}')`
              : `url('${origin}${item.config.slideParams.avatar}')`
            : 'url()',
        }"
      >
        <div class="thumbnailCls"></div>
      </div>
      <!-- 右键点击菜单 -->
      <ul
        v-if="item.id == currentSlide.id && visible"
        :style="{ left: left + 'px', top: top + 'px' }"
        class="contextmenu"
      >
        <li @click="setDisplayImg(item.config.slideParams.avatar)">
          设为封面
        </li>
        <li @click="deleteSlide(item)">删除</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
export default {
  components: {},
  props: {},
  data() {
    return {
      tempIndex: 0,
      top: 0,
      left: 0,
      visible: false,
    }
  },

  computed: {
    ...mapGetters({
      displayInfo: "displayInfo",
      displaySlides: "displaySlides",
      currentSlide: "currentSlide",
    }),
    env(){
      return process.env.NODE_ENV
    },
    origin(){
      return window.location.origin
    }
  },
  created() {},
  mounted() {},
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu)
      } else {
        document.body.removeEventListener("click", this.closeMenu)
      }
    },
  },
  methods: {
    // 设置当前选中的slide
    setCurrentSlide(slide) {
      this.$store.commit("display/SET_DISPLAY_CUR_SLIDE", slide)
    },
    // 打开右键菜单
    openMenu(slide, e) {
      this.setCurrentSlide(slide)
      const menuMinWidth = 95
      const offsetLeft = this.$el.getBoundingClientRect().left
      const offsetWidth = this.$el.offsetWidth
      const maxLeft = offsetWidth - menuMinWidth
      const left = e.clientX - offsetLeft + 15
      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }
      this.top = e.clientY - 90
      this.visible = true
    },
    // 关闭右键菜单
    closeMenu() {
      this.visible = false
    },
    // 删除slide
    deleteSlide(silde) {
      this.$store.dispatch("display/deleteDisplaySlide", silde)
    },
    // 设置封面
    async setDisplayImg(url) {
      if (!url) {
        this.$message.warning("请先截取图片")
        return
      }
      const params = {
        ...this.displayInfo,
        config: this.displayInfo.config,
        avatar: url,
      }
      await this.$httpBi.display.updDisplay({
        ...params,
        config: JSON.stringify(this.displayInfo.config),
      })
      this.$store.commit("display/SET_DISPLAY_INFO", params)
    },
  },
}
</script>

<style scoped lang="scss">
.SlideThumbnailList {
    background-color: #f0f2f5;
    overflow: auto;
    .SlideThumbnailItem {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-bottom: 1px solid #cccccc;
        &.active {
            background-color: #99e4ff;
        }
        .content {
            width: 100px;
            height: 50px;
            background-color: #fff;
            border: 1px solid #d9d9d9;
            background-size: 100% 100%;
        }
    }
    .contextmenu {
        margin: 0;
        background: #fff;
        z-index: 3000;
        position: absolute;
        list-style-type: none;
        padding: 5px 0;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #333;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
        li {
            margin: 0;
            padding: 7px 16px;
            cursor: pointer;
            &:hover {
                background: #eee;
            }
        }
    }
}
</style>
