<template>
  <div>
    <Code v-if="shareType == 2&&!isLogin" @refresh="login" />
    <protal v-if="isLogin" :token="token"/>
    <ShareLinkExpired v-else />
  </div>
</template>

<script>
import Code from "./code"
import protal from "./protal"
import { getUrlParams } from "@/utils/index"
import axios from "axios"
import Share<PERSON>inkExpired from "./ShareLinkExpired.vue"
export default {
  components: {
    Code,
    protal,
    ShareLinkExpired
  },
  props: {},
  data() {
    return {
      shareType: null,
      token: null,
      isLogin: null,
    }
  },
  computed: {},
  created() {
    this.token = getUrlParams("xx")
    this.shareType = getUrlParams("shareType")
    Number(this.shareType) === 1 && this.login()
  },
  mounted() {},
  watch: {},
  methods: {
    async login(
      params = {
        password: "",
        username: "",
      }
    ) {
      axios
        .post("/dds-server-bi/shareClient/login", params, {
          headers: {
            token: this.token,
          },
        })
        .then((res) => {
          console.log(res.data.code)
          if (res.data.code === 200) {
            this.isLogin = true
          } else {
            this.$message({
              showClose: true,
              message: res.data.message,
              type: 'warning'
            })
            this.isLogin = false
          }
        }).catch((err) => {
          console.log(err)
          this.isLogin = false

        })
      // const res = await this.$httpBi.dashboard.shareLogin(this.token, params);
      // alert(res.code);
    },
  },
}
</script>

<style scoped lang="less"></style>
