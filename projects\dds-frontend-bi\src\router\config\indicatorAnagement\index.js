import { prefix } from '../../prefix'
// 指标管理
import indicatorAnagement from '@/views/indicator-anagement/MainList'
export default [
  {
    name: 'indicatorAnagement',
    component: indicatorAnagement,
    path: prefix + '/indicatorAnagement',
    meta: { title: '指标管理' }
  },
  {
    // 原子指标
    name: 'AtomIndicator',
    component: () => import('@/views/indicator-anagement/atom-indicator/index'),
    path: prefix + '/atomIndicator',
    meta: { title: '原子指标' }
  },
  {
    // 派生指标
    name: 'DeriveIndicator',
    component: () =>
      import('@/views/indicator-anagement/derive-indicator/index'),
    path: prefix + '/deriveIndicator',
    meta: { title: '派生指标' }
  },
  {
    // 复合指标
    name: 'CompositeIndicator',
    component: () =>
      import('@/views/indicator-anagement/composite-indicator/index.vue'),
    path: prefix + '/compositeIndicator',
    meta: { title: '复合指标' }
  },
  {
    // 算法指标
    name: 'AlgorithmIndicator',
    component: () =>
      import('@/views/indicator-anagement/algorithm-indicator/index.vue'),
    path: prefix + '/algorithmIndicator',
    meta: { title: '算法指标' }
  },

  // 创建执行类算法
  {
    // 算法指标
    name: 'CreateAlgorithmExecution',
    path: prefix + '/createAlgorithmExecution',

    component: () =>
      import(
        '@/views/indicator-anagement/algorithm-indicator/indicator/components/createAlgorithm.vue'
      ),
    meta: { title: '创建执行类算法' }
  },
  // 创建工具类算法
  {
    // 算法指标
    name: 'CreateAlgorithmTool',
    path: prefix + '/createAlgorithmTool',

    component: () =>
      import(
        '@/views/indicator-anagement/algorithm-indicator/tools/components/createAlgorithm.vue'
      ),
    meta: { title: '创建工具类算法' }
  },
  // 算法历史版本
  {
    // 算法指标
    name: 'AlgorithmHistory',
    path: prefix + '/algorithmHistory',

    component: () =>
      import(
        '@/views/indicator-anagement/algorithm-indicator/common/HistoryVersion.vue'
      ),
    meta: { title: '算法历史版本' }
  },
  {
    // 算法指标
    name: 'AlgorithmIndex',
    component: () => import('@/views/indicator-anagement/algorithm/index.vue'),
    path: prefix + '/algorithmIndex',
    meta: { title: '算法指标' }
  },
  {
    // 统计学特征
    name: 'statistics',
    component: () => import('@/views/indicator-anagement/statistics/index.vue'),
    path: prefix + '/statistics',
    meta: { title: '指标统计学特征' }
  },
  // sql指标
  {
    name: 'SqlIndicator',
    component: () =>
      import('@/views/indicator-anagement/sql-indicator/index.vue'),
    path: prefix + '/sqlIndicator',
    meta: { title: 'sql指标' }
  },
  // 表视图
  {
    name: 'SqlTableview',
    component: () =>
      import('@/views/indicator-anagement/sql-table-view/index.vue'),
    path: prefix + '/sqlTableview',
    meta: { title: 'sql表视图' }
  },
  {
    name: 'NormIndicator',
    component: () =>
      import('@/views/indicator-anagement/norm-indicator/index.vue'),
    path: prefix + '/normIndicator',
    meta: { title: '常模指标清单' }
  },
  // 指标分析
  {
    name: 'IndicatorAnalysis',
    component: () =>
      import('@/views/indicator-anagement/indicator-analysis/index.vue'),
    path: prefix + '/indicatorAnalysis',
    meta: { title: '指标分析' }
  },
  // 指标关联分析
  {
    name: 'IndicatorRelationAnalysis',
    component: () =>
      import(
        '@/views/indicator-anagement/indicator-relation-analysis/index.vue'
      ),
    path: prefix + '/indicatorRelationAnalysis',
    meta: { title: '指标关联分析' }
  },
  // AI报表预览
  {
    name: 'PreviewAiExcel',
    component: () =>
      import(
        '@/views/indicator-anagement/indicator-analysis/PreviewExcel.vue'
      ),
    path: prefix + '/PreviewAiExcel',
    meta: { title: 'AI报表预览' }
  }
]
