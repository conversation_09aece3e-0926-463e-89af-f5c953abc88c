<template>
  <DT-View
    :inner-style="{
      position: 'relative'
    }"
  >
    <!-- <el-alert title="XXXX" type="warning" show-icon></el-alert> -->
    <div class="warm-reminder">
      <svg-icon icon-class="warning" style="margin-top: 3px" />
      <div class="warm-reminder-content">
        <p>
          支持批量创建原子指标，数据源可多选，预览数据列时请手动下拉进度条，确认每个数据源中的字段情况。
        </p>
      </div>
    </div>
    <!-- <el-steps :active="active" finish-status="success">
      <el-step title="选择数据源"></el-step>
      <el-step title="预览数据列"></el-step>
      <el-step title="预览原子指标"></el-step>
    </el-steps> -->
    <div class="steps">
      <div
        class="step-item step1"
        :class="{
          active: active == 1,
          success: active > 1
        }"
      >
        <svg-icon
          v-if="active > 1"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">1</span>

        <span class="step-text">选择数据源</span>
        <span class="step-line"></span>
      </div>
      <div
        class="step-item"
        :class="{
          active: active == 2,
          success: active > 2
        }"
      >
        <svg-icon
          v-if="active > 2"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">2</span>
        <span class="step-text">预览数据列</span>
        <span class="step-line"></span>
      </div>
      <div
        class="step-item"
        :class="{
          active: active == 3,
          success: active > 3
        }"
      >
        <svg-icon
          v-if="active > 3"
          icon-class="success"
          class="step-icon-success"
        />
        <span v-else class="step-icon">3</span>
        <span class="step-text">预览原子指标</span>
      </div>
    </div>
    <div class="one" v-if="active == 1">
      <el-input
        placeholder="输入关键字搜索"
        prefix-icon="el-icon-search"
        v-model="key"
        class="search-input"
        @input="getTableList"
      ></el-input>
      <div class="checkbox-warp" v-loading="loading1">
        <el-checkbox-group v-model="ids">
          <el-checkbox
            v-for="item in dataDomainList"
            :label="item.id"
            :key="item.id"
          >
            <svg-icon icon-class="sjy" />
            {{ item.a }}/{{ item.c }}/{{ item.id }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div class="two" v-if="active == 2">
      <div
        class="data-column-wrapper"
        v-for="item in tableInfoList"
        :key="item.id"
      >
        <div class="data-column-title">{{ item.bm }}</div>
        <el-table :data="item.info" style="width: 100%" border>
          <!-- <el-table-column type="index" label="序号"></el-table-column> -->
          <el-table-column prop="zddm" label="字段代码"></el-table-column>
          <el-table-column prop="bt" label="标题"></el-table-column>
          <!-- <el-table-column prop="sm" label="说明"></el-table-column> -->
          <el-table-column prop="sjlx" label="数据类型"></el-table-column>
          <!-- <el-table-column prop="cd" label="长度"></el-table-column>
          <el-table-column prop="xsws" label="小数位数"></el-table-column> -->
          <!-- <el-table-column prop="kwk" label="可为空"></el-table-column> -->
          <!-- <el-table-column prop="sfwy" label="是否唯一"></el-table-column> -->
          <el-table-column prop="zdlx" label="字段类型">
            <template #default="{ row }">
              <el-select v-model="row.zdlx" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="wb" label="对应维表" width="150">
            <template #default="{ row }">
              <el-select
                v-if="row.zdlx == 'ps'"
                v-model="row.wb"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in wdList"
                  :key="item.id"
                  :label="item.wdmc"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
    <div class="three" v-if="active == 3">
      <!--  -->
      <div class="allselect">
        <el-button @click="toggleSelection">全选</el-button>
        <span style="margin-left: 12px">
          已选中{{ multipleSelection.length }}条
        </span>
      </div>
      <el-form
        status-icon
        :rules="rules"
        :model="form"
        ref="ruleForm"
        label-width="0"
        class="ruleForm"
      >
        <el-table
          ref="multipleTable"
          :data="form.tableData"
          tooltip-effect="dark"
          style="width: 100%"
          :max-height="320"
          :row-class-name="tableRowClassName"
          @cell-click="tabClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="zbmc" label="指标名称">
            <template slot-scope="scope">
              <el-form-item
                :ref="'zdmc' + scope.row.index"
                class="is-required"
                :prop="'tableData.' + scope.$index + '.zbmc'"
                :rules="scope.row.openRule?rules.zbmc:{}"
                style="margin-top: 18px"
              >
                <span
                  v-if="
                    scope.row.index === tabClickIndex &&
                    tabClickLabel === '指标名称'
                  "
                >
                  <el-input
                    v-focus
                    v-model.trim="scope.row.zbmc"
                    maxlength="300"
                    placeholder="请输入标签"
                    size="mini"
                  />
                </span>
                <span v-else>
                  {{ scope.row.zbmc }}
                  <svg-icon style="color: #5b8ff9" icon-class="edit" />
                </span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="zbymc" label="原数据列标题"></el-table-column>
          <el-table-column label="所属数据域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-model="row.sysjy"
                placeholder="请选择数据域"
                :dic="viewGroup"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="描述">
            <template slot-scope="scope">
              <span
                v-if="
                  scope.row.index === tabClickIndex && tabClickLabel === '描述'
                "
              >
                <el-input
                  v-focus
                  v-model="scope.row.ms"
                  maxlength="300"
                  placeholder="请输入描述"
                  size="mini"
                  @blur="inputBlur"
                />
              </span>
              <span v-else>
                {{ scope.row.ms }}
                <el-button type="text">添加</el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="bq" label="标签">
            <template slot-scope="scope">
              <!-- <span
                v-if="
                  scope.row.index === tabClickIndex && tabClickLabel === '标签'
                "
              >
                <el-input
                  v-focus
                  v-model="scope.row.bq"
                  maxlength="300"
                  placeholder="请输入标签"
                  size="mini"
                  @blur="inputBlur"
                />
              </span>
              <span v-else>
              </span> -->
              {{ scope.row.bq && scope.row.bq.join(",") }}
              <el-button type="text">添加</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="派生维度"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <!-- <span v-for="(item, index) in row.pswd" :key="index">
              {{ item.wdbm?item.wdbm:item.zdmc }}{{ index ? "," : "" }}
            </span> -->
              {{ foramtPswd(row.pswd) }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="创建人">
            <template>
              {{ $store.state.user.username }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <div class="fotter-btn">
      <el-button v-if="active == 1" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="active > 1" @click="active--">上一步</el-button>
      <el-button v-if="active < 3" @click="handleAddStep">下一步</el-button>
      <el-button v-if="active > 2" type="primary" @click="handleSave">
        保存({{ multipleSelection.length }})
      </el-button>
    </div>
    <MakeTag
      v-model="tabs"
      :dialog-visible.sync="dialogVisible"
      @handleSaveTags="handleSaveTags"
    />
  </DT-View>
</template>
<script>
import _ from "lodash"
import MakeTag from "./MakeTag"
export default {
  components: { MakeTag },
  // 自定义指定
  directives: {
    focus: {
      // 指令的定义
      inserted(el) {
        el.querySelector(".el-input__inner").focus()
      }
    }
  },
  props: {},
  data() {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode:""
        })
      const arr = this.form.tableData.filter(item => item.zbmc === value)
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else if (arr && arr.length > 1) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        this.tabClickIndex = null
        this.tabClickLabel = ""
      }
    }
    return {
      sysjy: "0",
      rules: {
        zbmc: [{ validator: changeZdmc, trigger: "blur", required: true }]
      },
      dialogVisible: false, // 打标签弹窗
      tabs: [],
      loading1: false,
      value: [],
      key: "",
      ids: [], // 选中的id
      dataDomainList: [], // 数据列表
      tableInfoList: [],
      multipleSelection: [],
      active: 1,
      isSuccess: false,
      tabClickIndex: "",
      tabClickLabel: "",
      checkList: [],
      form: {
        tableData: []
      },
      options: [
        {
          value: "ps",
          label: "维度"
        },
        {
          value: "INT",
          label: "度量(int)"
        },
        {
          value: "DECIMAL",
          label: "度量(decimal)"
        },
        {
          value: "VARCHAR",
          label: "度量(varchar)"
        },
        {
          value: "time",
          label: "时间维度"
        },
        {
          value: "NULL",
          label: "无"
        }
      ],
      wdList: [],
      viewGroup: [{ id: 0, name: "根目录", children: [] }] // 数据域分组
    }
  },
  computed: {},
  created() {
    this.sysjy = Number(this.$route.query.sysjy) || "0"
    this.getTableList()
    this.getWdTableList()
    this.getAllViewGroup()
  },
  mounted() {},
  watch: {
    active(val) {
      if (val === 3) {
        this.$nextTick(() => {
          this.toggleSelection()
        })
      }
    }
  },
  methods: {
    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    tableRowClassName({ row, rowIndex }) {
      // 把每一行的索引放进row
      row.index = rowIndex
    },
    // 添加明细原因 row 当前行 column 当前列
    tabClick(row, column) {
      switch (column.label) {
        case "描述":
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          break
        case "标签":
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          this.dialogVisible = true
          this.tabs = row.bq || []
          break
        case "指标名称":
          this.tabClickIndex = row.index
          this.tabClickLabel = column.label
          break

        default:
          return
      }
    },
    // 失去焦点初始化
    inputBlur() {
      this.tabClickIndex = null
      this.tabClickLabel = ""
    },
    // 获取列表
    getTableList: _.debounce(function () {
      this.loading1 = true
      this.$httpBi.indicatorAnagement
        .getTableList({
          key: this.key
        })
        .then(res => {
          this.dataDomainList = res.data
          this.loading1 = false
        })
    }, 300),
    // 下一步
    handleAddStep() {
      if (this.active === 1) {
        this.getTableInfo()
      }
      if (this.active === 2) {
        const arr = []
        const dywdMap = {} // 选对应维度的派生维度

        // 过滤为派生维度且选择对应维度的数据
        this.tableInfoList.forEach(item => {
          dywdMap[item.id] = []
          item.info.forEach(i => {
            if (i.zdlx === "ps" || i.zdlx === "time") {
              dywdMap[item.id].push({
                wdbm: this.filterWdName(i.wb) || "",
                wdid: i.wb || "0",
                wdzd: i.zddm,
                zdmc: i.bt,
                wdlx: i.zdlx,
                sjgs: "", // 时间维度格式
                gldm: "" // 关联代码
              })
            }
          })
          // 去重
          dywdMap[item.id] = Array.from(new Set(dywdMap[item.id]))
        })
        this.tableInfoList.forEach(item => {
          item.info.forEach(i => {
            if (
              i.zdlx === "INT" ||
              i.zdlx === "DECIMAL" ||
              i.zdlx === "VARCHAR"
            ) {
              const object = {
                zddm: i.zddm,
                cd: i.cd,
                kwk: i.kwk,
                sfwy: i.sfwy,
                sjlx: i.sjlx,
                ms: i.sm,
                bq: i.bq,
                tabmc: item.bm,
                pswd: dywdMap[item.id],
                tabid: item.id,
                zbmc: i.bt,
                zbymc: i.bt,
                sysjy: this.sysjy, // 所选指标域id
                zblx: "原子指标",
                lxbm: "yz",
                lxbms:["yz"],
                zdlx: i.zdlx,
                cjr: this.$store.state.user.username,
                openRule: true
              }

              arr.push(object)
            }
          })
        })
        this.form.tableData = arr
      }
      this.active++
    },
    // 原子指标详情
    async getTableInfo() {
      const { data } = await this.$httpBi.indicatorAnagement.getTableInfo({
        id: this.ids.join()
      })
      // 将data每项的info数组对象中的id转化为1
      data.forEach(item => {
        item.info.forEach(i => {
          i.zdlx = this.formatZdlx(i.sjlx)
        })
      })
      this.tableInfoList = data
    },
    // 获取维度列表
    async getWdTableList() {
      const { data } = await this.$httpBi.indicatorAnagement.getWdTableList({})
      this.wdList = data
    },
    handleSelectionChange(val) {
      console.log(val,'val')
      this.multipleSelection = val
      let newArr = [...val]
      console.log(newArr)
      this.form.tableData.forEach((item,index) => {
        const has = this.multipleSelection.find(i => i.zbymc === item.zbymc)
        if (has) {
          this.$set(this.form.tableData[index], 'openRule', true)
        } else {
          this.$set(this.form.tableData[index], 'openRule', false)
        }
        
      })
      console.log(  this.form.tableData,'this.form.tableData')
    },
    // 全选
    toggleSelection() {
      this.$refs.multipleTable.toggleAllSelection()
    },
    async handleSave() {
      console.log(this.multipleSelection)
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          if (!this.multipleSelection.length) {
            this.$message.error("请选择指标")
            return
          }
          const { code } = await this.$httpBi.indicatorAnagement.saveYzzb(
            this.multipleSelection
          )
          if (code === 200) {
            this.$router.go(-1)
            this.$message.success("保存成功")
          }
        }
      })
    },
    handleSaveTags(tags) {
      this.form.tableData[this.tabClickIndex].bq = tags
      this.dialogVisible = false
      this.inputBlur()
    },
    // 过滤维度名称
    filterWdName(id) {
      return this.wdList.filter(item => item.id === id)[0]?.wdmc || ""
    },
    foramtPswd(arr) {
      return arr.map(item => item.zdbh || item.zdmc).join(",")
    },

    formatZdlx(sjlx) {
      switch (sjlx) {
        case "varchar":
          return "ps"
        case "int":
          return "INT"
        case "decimal":
          return "DECIMAL"
        case "date":
          return "time"
        default:
          return "NULL"
      }
    }
  }
}
</script>

<style scoped lang="scss">
#project_frame .el-alert {
  margin-top: 0;
  margin-bottom: 24px;
}
.warm-reminder {
  position: relative;
  width: 100%;
  height: 48px;
  background: #fffbe6;
  border-radius: 2px;
  border: 1px solid #fff1b8;
  margin: 0 auto 24px;
  display: flex;
  padding: 12px 17px 0;
  box-sizing: border-box;
  .warm-reminder-content {
    margin-left: 8px;
    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      line-height: 22px;
      height: 22px;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
  }
}
.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  .step-item {
    display: flex;
    align-items: center;
    &.active {
      .step-icon {
        background: #5b8ff9;
        border: 1px solid #5b8ff9;
        color: #fff;
      }
      .step-text {
        font-weight: 500;
        color: #222222;
      }
    }
    &.success {
      .step-text {
        height: 16px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 16px;
        margin-left: 8px;
        white-space: nowrap;
      }
      .step-line {
        background: #5b8ff9;
      }
    }
    .step-icon {
      width: 32px;
      height: 32px;
      background: #fff;
      border: 1px solid #dddddd;
      border-radius: 50%;
      line-height: 32px;
      text-align: center;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
    }
    .step-icon-success {
      font-size: 32px;
    }
    .step-text {
      flex: 1;
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
      margin-left: 8px;
    }
    .step-line {
      margin: 0 16px;
      width: 192px;
      height: 1px;
      background: #dddddd;
    }
  }
}
::v-deep .search-input.el-input--small .el-input__inner {
  border: none !important;
  border-bottom: 1px solid #ccc !important;
  border-radius: 0 !important;
}
.one {
  width: 640px;
  margin: 20px auto;
  .checkbox-warp {
    height: calc(100vh - 420px);
    overflow: auto;
    margin-top: 24px;
  }
  ::v-deep .el-checkbox {
    margin-bottom: 24px;
  }
  //滚动条样式
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #cbced1;
  }
  ::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: transparent;
  }
}

.two {
  height: calc(100vh - 350px);

  overflow: auto;
  //滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
  &::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }

  .data-column-wrapper {
    margin: 20px auto;

    .data-column-title {
      font-size: 18px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #222222;
      margin-bottom: 16px;
    }
  }
}
.three {
  margin-top: 32px;
  padding-bottom: 40px;
  .allselect {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
  }
}

::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
}

.fotter-btn {
  position: absolute;
  bottom: 0px;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64px;
  background: #ffffff;
  box-sizing: border-box;
  box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
  z-index: 55;
}
.el-table {
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /*定义滚动条轨道 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: #ffffff;
  }
  /*定义滑块 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #cbced1;
  }
}
</style>
