<template>
  <div>
    <el-dialog
      title="分享列表"
      :visible.sync="shareVisible"
      width="680px"
      :close-on-click-modal="false"
      @close="$emit('update:shareVisible', false)"
    >
      <el-button
        type="primary"
        @click="addLink"
      >
        新建分享
      </el-button>
      <el-table
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          label="链接"
          width="220px"
        >
          <template slot-scope="scope">
            <p class="url">
              {{ shareBaseUrl }}shareType={{ scope.row.shareType }}&xx={{
                scope.row.sign
              }}
              <span v-show="scope.row.shareType == 2"> 密码：{{ scope.row.password }}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="类型"
          width="100px"
          style="margin-top: 20px"
        >
          <template slot-scope="scope">
            <span v-show="scope.row.shareType == 1">普通分享</span>
            <span v-show="scope.row.shareType == 2">私密分享</span>
            <span v-show="scope.row.shareType == 3">权限分享</span>
          </template>
        </el-table-column>
        <el-table-column
          label="有效期"
          width="150px"
          style="margin: 20px 0"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.startTime }}</span>
            至
            <span>{{ scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          style="margin-top: 20px"
        >
          <template #default="{ row }">
            <el-button
              type="text"
              @click="copyLink(row.sign, row.shareType, row.password)"
            >
              复制链接
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button
              type="text"
              @click="editLink(row)"
            >
              编辑
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定要删除吗？"
              @onConfirm="delLink(row.id)"
            >
              <el-button
                type="text"
                slot="reference"
              >
                删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="$emit('update:shareVisible', false)">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="分享"
      :visible.sync="dialogVisible"
      width="680px"
      :close-on-click-modal="false"
      @close="dialogVisible = false"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="100px"
      >
        <el-form-item label="分享类型">
          <el-radio-group
            v-model="form.shareType"
            @change="changeShareType"
          >
            <el-radio :label="1">公开分享</el-radio>
            <el-radio :label="2">私密分享</el-radio>
            <el-radio :label="3">权限分享</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="有效期"
          prop="enableTimeRange"
          style="margin-top: 20px"
          :rules="{ required: true, message: '请选择时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="form.enableTimeRange"
            range-separator="至"
            type="datetimerange"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
            @input="changeShareTime()"
            :default-time="['00:00:00', '23:59:59']"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>

        <div v-if="form.shareType == 2">
          <el-form-item
            prop="password"
            label="密码"
            style="margin-top: 20px"
            :rules="{
              required: true,
              message: '请输入密码',
              trigger: 'change',
            }"
          >
            <el-input
              v-model.trim="form.password"
              maxlength="4"
              style="width: 80px"
            ></el-input>
            <el-link
              :underline="false"
              @click="createdCode"
              type="primary"
              style="margin-left: 20px"
            >
              更新密码
            </el-link>
          </el-form-item>
        </div>

        <div
          v-if="form.shareType == 3"
          style="margin-top: 20px"
        >
          <el-form-item label="数据权限">
            <el-radio-group v-model="form.dataAuth">
              <el-radio :label="1">登录</el-radio>
              <el-radio :label="2">分享者</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="指定角色/成员"
            style="margin-top: 20px"
          >
            <div style="display: flex">
              <el-select
                v-model="form.roles"
                multiple
                collapse-tags
                placeholder="请选择"
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>

              <el-select
                v-model="form.users"
                multiple
                collapse-tags
                style="margin-left: 20px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>

        <!-- <el-row
          style="text-align: center; margin-top: 20px"
          v-show="isCreatLink"
        >
          <el-link>
            {{ createShareUrl }}
            <span v-show="form.shareType == 2">
              密码：{{ form.password }}
            </span>
          </el-link>
        </el-row> -->
        <el-form-item
          label="分享链接"
          style="margin-top: 20px"
        >
          <el-input
            disabled
            type="textarea"
            :rows="2"
            placeholder="请更新或生成分享链接"
            v-model="shareUrl"
          >
          </el-input>
        </el-form-item>

        <el-row style="text-align: center; margin-top: 20px">
          <el-button
            type="primary"
            @click="submitEdit"
          >
            {{ form.id === "" ? "生成" : "更新" }}分享链接并复制
          </el-button>

          <el-button
            type="primary"
            style="margin-left: 30px"
            @click="getAllLink"
          >
            关闭
          </el-button>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Request from "@/service"

export default {
  components: {},
  props: {
    shareVisible: {
      type: Boolean,
      default: false,
    },
    shareId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      shareBaseUrl: window.location.origin+"/ddsBi/share?isFullPage=true&",
      createShareUrl: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime()<Date.now()-8.64e7
        },
      },
      dialogVisible: false,
      form: {
        id: "",
        type: 1,
        shareId: this.shareId,
        enableTimeRange: [],
        startTime: "",
        endTime: "",
        shareType: 1,
        dataAuth: 1,
        roles: [],
        users: [],
        password: "",
      },
      roleList: [
        {
          value: 1,
          label: "超级管理员",
        },
        {
          value: 2,
          label: "观察者",
        },
        {
          value: 3,
          label: "管理员",
        },
      ],
      userList: [
        {
          value: 1,
          label: "张三",
        },
        {
          value: 2,
          label: "李四",
        },
        {
          value: 3,
          label: "王五",
        },
      ],
      tableData: [],
      isCreatLink: false,
    }
  },
  computed: {
    shareUrl() {
      if (this.form.shareType===2) {
        return `${this.createShareUrl}密码${this.form.password}`
      } else {
        return this.createShareUrl
      }
    },
  },
  created() {
    this.getAllLink()
  },
  mounted() { },
  watch: {
    form: {
      handler() {
        this.isCreatLink=false
      },
      deep: true,
    },
  },
  methods: {
    // 当前看板的所有分享链接
    async getAllLink() {
      let params={
        type: 1,
        shareId: this.shareId,
      }
      const { data }=await Request.dashboard.getAllShareLink(params)
      this.tableData=data
      this.dialogVisible=false
    },

    // 添加链接
    addLink() {
      this.dialogVisible=true
      this.form.id=""
      this.form.shareType=1
      this.$set(this.form,"enableTimeRange",[])
    },
    // 复制链接
    copyLink(sign,shareType,password) {
      const sign1=encodeURIComponent(sign)
      let oInput=document.createElement("input")
      let cpv=`${this.shareBaseUrl}shareType=${shareType}&xx=${sign1}`

      if (shareType===2) {
        cpv=cpv+"&密码："+password
      }
      
      console.log(shareType)
      oInput.value=cpv
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      document.execCommand("Copy") // 执行浏览器复制命令
      this.$message.success("复制成功！")
      oInput.remove()
    },
    // 编辑链接
    async editLink(row) {
      this.dialogVisible=true
      this.form=row
      this.$set(this.form,"enableTimeRange",[ row.startTime,row.endTime ])
      if (row.shareType===3) {
        const { data }=await Request.dashboard.getShareAuth({ id: row.id })
        this.$set(this.form,"users",data.users)
        this.$set(this.form,"roles",data.roles)
        this.$set(this.form,"dataAuth",data.dataAuth)
      }
      this.isCreatLink=true
    },
    // 删除链接
    async delLink(id) {
      const { code }=await Request.dashboard.delShareLink({ id: id })
      if (code===200) {
        this.$message.success("删除成功")
        this.getAllLink()
      }
    },
    // 生成4个随机数
    createdCode() {
      const len=4
      const codeList=[]
      const chars="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz0123456789"
      const charsLen=chars.length
      for (let i=0;i<len;i++) {
        codeList.push(chars.charAt(Math.floor(Math.random()*charsLen)))
      }
      this.form.password=codeList.join("")
    },
    // 提交保存
    submitEdit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },

    async handleSave() {
      // 新增
      if (this.form.shareType===1) {
        this.form.password=""
      }
      this.form.startTime=this.form.enableTimeRange[0]
      this.form.endTime=this.form.enableTimeRange[1]
      this.form.type=1 // 页面分享
      this.form.shareId=this.shareId
      if (this.form.id) {
        // 设置
        const { code,data }=await Request.dashboard.updShareLink(this.form)
        if (code===200) {
          // this.$message.success("修改成功");
          this.createShareUrl=`${this.shareBaseUrl}shareType=${this.form.shareType}&xx=${data}`
          if (this.form.shareType===2) {
            this.copyLink(data,this.form.shareType,this.form.password)
          } else {
            this.copyLink(data,this.form.shareType,"")
          }
        }
      } else {
        const { code,data }=await Request.dashboard.addShareLink(this.form)
        if (code===200) {
          // this.$message.success("新增成功");
          this.createShareUrl=`${this.shareBaseUrl}shareType=${this.form.shareType}&xx=${data}`
          if (this.form.shareType===2) {
            this.copyLink(data,this.form.shareType,this.form.password)
          } else {
            this.copyLink(data,this.form.shareType,"")
          }
        }
      }
      this.isCreatLink=true
    },
    changeShareType(shareType) {
      if (shareType===3) {
        this.$set(this.form,"dataAuth",1)
      } else if (shareType===2) {
        if (!this.form.password) {
          this.createdCode()
        }
      }
      this.isCreatLink=false
    },
    changeShareTime() {
      this.isCreatLink=false
    },
  },
}
</script>

<style scoped lang="scss">
.url {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2; //行数
  -webkit-box-orient: vertical;
}
.de-link {
  justify-content: left !important;
}

.de-span {
  margin: 0 15px;
}
::v-deep .el-dialog__header {
  border-bottom: 1px solid #ebeef5;
}
::v-deep .el-dialog__body {
  padding: 16px 20px 30px;
}
</style>
