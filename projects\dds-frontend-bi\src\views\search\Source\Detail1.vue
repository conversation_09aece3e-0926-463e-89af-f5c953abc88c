<template>
  <div>
    <h2 style="margin-bottom: 16px">{{ name }}</h2>
    <vxe-table
      border
      :data="tableData"
      v-loading="loading"
      style="width: 100%"
      height="480px"
    >
      <vxe-column
        :field="item.name"
        :title="item.alias ? item.alias : item.name"
        v-for="item in tablecolumn"
        :key="item.name"
      ></vxe-column>
    </vxe-table>
    <el-pagination
      style="margin-top: 16px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="params.currentPage"
      :page-sizes="[10, 20, 40, 80]"
      :page-size="params.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      name: "",
      lxbm: "",
      total: 0,
      id: 0,
      params: {
        currentPage: 1,
        pageSize: 10
      },
      tableData: [],
      tablecolumn: [],
      loading: false
    }
  },
  computed: {},
  created() {
    const { id, lxbm, name } = this.$route.query
    this.id = id
    this.name = name
    this.lxbm = lxbm
  },
  mounted() {},
  watch: {
    id: {
      handler() {
        this.getData()
      }
    }
  },
  methods: {
    async getData() {
      this.loading = true
      // 表头
      const { data } = await this.$httpBi.search.getTableColumn({
        id: this.id,
        lxbm: this.lxbm
      })
      const { data: tableData } = await this.$httpBi.search.getTableData({
        id: this.id,
        lxbm: this.lxbm,
        ...this.params
      })
      this.tablecolumn = data
      this.total = tableData?.totalCount
      this.tableData = tableData.list
      this.loading = false
    },
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.params.currentPage = val
      this.getData()
    }
  }
}
</script>

<style scoped lang="less"></style>
