<template>
  <div>
    <h3>
      <!-- <i :class="status ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i
      > -->
      {{ name }}
    </h3>
    <el-collapse-transition name="el-fade-in">
      <slot></slot>
      >
    </el-collapse-transition>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      status: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    toggle() {
      this.status = !this.status
    }
  }
}
</script>

<style scoped lang="scss">
h3 {
  position: relative;
  background-color: #fff;
  cursor: pointer;
  font-size: 18px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 20px;
  margin-top: 24px;
  padding-left: 12px;
  &::before {
    position: absolute;
    content: "";
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #1563ff;
  }
}
</style>
