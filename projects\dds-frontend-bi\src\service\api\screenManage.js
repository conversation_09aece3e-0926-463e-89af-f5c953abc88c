import service from "../base"
import config from "../config"

/**
 * 数据源
 */
export default {
  // 查询
  getAll(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/getPage",
      method: "post",
      data: params
    })
  },
  // 查询
  getOne(params) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + "manage/get",
      method: "get",
      params: params
    })
  },
  // 更新-定制大屏-display
  updDisplay(data) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + "manage/upd",
      method: "post",
      data
    })
  },
  // 添加-定制大屏-display
  addDisplay(data) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + "manage/add",
      method: "post",
      data
    })
  },
  // 删除-定制大屏-display
  delDisplay(params) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + "manage/del",
      method: "get",
      params
    })
  },
  // 获取大屏角色权限
  getRolePermission(params) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + "manage/displayRoles",
      method: "get",
      params
    })
  }
}
