<template>
  <div class="chart-config">
    <el-tabs v-model="activeName" stretch @tab-click="handleClick">
      <el-tab-pane label="数据" name="data">
        <ChartDataConfigPanel v-bind="$attrs" v-on="$listeners" />
      </el-tab-pane>
      <el-tab-pane label="样式" name="style">
        <ChartStyleConfigPanel />
      </el-tab-pane>
      <el-tab-pane label="配置" name="setting">
        <ChartSettingConfigPanel />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ChartDataConfigPanel from "./ChartDataConfigPanel.vue"
import ChartStyleConfigPanel from "./ChartStyleConfigPanel.vue"
import ChartSettingConfigPanel from "./ChartSettingConfigPanel.vue"
export default {
  components: {
    ChartDataConfigPanel,
    ChartStyleConfigPanel,
    ChartSettingConfigPanel,
  },
  props: {},
  data() {
    return {
      activeName: "data",
    }
  },
  computed: {},
  created() {
  },
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.chart-config {
  width: 100%;
  height: calc(100% - 150px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
::v-deep .el-tabs {
  height: 100%;
}
::v-deep .el-tabs__header {
  margin: 0;
}
::v-deep .el-tabs__content {
  height: calc(100% - 40px);
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: skyblue;
    background-image: -webkit-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      transparent 75%,
      transparent
    );
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
  }
}
</style>
