<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative'
    }"
  >
    <div class="create-head">
      <i class="el-icon-back" @click="$router.go(-1)"></i>
      <div class="create-text">创建SQL指标</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          表视图
        </div>
        <div class="line" :class="{ active: activeId == 2 }"></div>
        <div class="step-item" :class="{ active: activeId == 2 }">
          <i class="el-icon-data-line"></i>
          模型与权限
        </div>
      </div>
    </div>

    <div class="editor-content" v-if="editStep === 1">
      <LeftSide
        :form="form"
        ref="leftSide"
        :drag-tables="dragTables"
        @changeSourceDb="changeSourceDb"
        @onDragEnd="handleDragEnd"
        @onDragStart="handleDragstart"
      />
      <div class="right" @drop="drop" @dragover="dragover" ref="sqlView">
        <div class="table-links-wrapper" ref="dragContainer">
          <TableWrapper
            @fieldInfo="fieldInfo"
            @handleRelate="handleRelate"
            @drop="drop"
            @dragover="dragover"
            @delDropBox="delDropBox"
            root
            :dargging="dargging"
            :tree="tree"
            v-for="(item, index) in tree"
            :item="item"
            :index="index"
            :key="index"
          />
          <div
            v-if="!dragTables.length"
            class="empty-table-tips-wrapper"
            :class="{ dargging: dargging }"
          >
            <div class="empty-table-tips-wrapper">
              <div class="empty-table-tips">请从左侧拖拽数据表开始创建</div>
            </div>
          </div>
          <div class="table-link-help-wrapper" v-if="dragTables.length == 1">
            <div class="table-link-help-title">
              继续从左侧拖拽数据表进行表关联
            </div>
          </div>
        </div>
        <div class="tables" v-if="tableData.length || tableColumns.length">
          <CommonTable
            :page.sync="page"
            id="xh"
            :table-data="tableData"
            :show-batch-tag="false"
            :table-columns.sync="tableColumns"
            @onload="getTableData"
            @handleSortChange="sortChange"
            @handleExport="handleExportExcel"
            height="240px"
          >
            <template #yjxfjeSlot="{ row }">
              <span :style="{ color: row.xfjeys }">{{ row.yjxfje }}</span>
            </template>
            <template #yjcyxfjeSlot="{ row }">
              <span :style="{ color: row.cyxfjeys }">{{ row.yjcyxfje }}</span>
            </template>
          </CommonTable>
        </div>
        <div class="empty" v-else>
          <div class="empty-icon"></div>
          <div class="empty-text">暂无数据</div>
        </div>
        <div class="footer-btn">
          <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
            取消
          </el-button>
          <el-button @click="runSql" type="success">执行</el-button>
          <el-button
            @click="nextStep"
            :disabled="!isRunOver"
            :type="isRunOver ? 'primary' : 'info'"
          >
            下一步
          </el-button>
        </div>
      </div>

      <el-drawer title="我是标题" :visible.sync="drawer" :with-header="false">
        <div class="drawer-content">
          <el-table
            height="500"
            ref="multipleTable"
            :data="fieildData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection"></el-table-column>
            <el-table-column prop="name" label="字段"></el-table-column>
            <el-table-column prop="comment" label="备注"></el-table-column>
          </el-table>
        </div>
      </el-drawer>

      <el-drawer
        title="我是标题"
        size="40%"
        :visible.sync="relateDrawer"
        :with-header="false"
        :wrapper-closable="false"
      >
        <RelateDrawer
          ref="relateDrawer"
          @handleSelectionChangeRight="handleSelectionChangeRight"
          @handleSelectionChangeLeft="handleSelectionChangeLeft"
          :source-table-info="sourceTableInfo"
          :target-table-info="targetTableInfo"
          @changeRelate="changeRelate"
          @addRelate="addRelate"
          @removeRelate="removeRelate"
          @changeRelateType="changeRelateType"
          @handleSave="handleSave"
          @cancelForm="cancelForm"
        />
      </el-drawer>
    </div>
    <template v-if="editStep == 2">
      <el-table
        :data="tableData1"
        style="width: calc(100% - 48px); margin: 24px"
        height="calc(100vh - 300px)"
      >
        <el-table-column prop="val1" label="指标名称">
          <template #default="{ row }">
            <el-input placeholder="" v-model="row.zbmc"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="val1" label="类型">
          <template #default="{ row }">
            <el-select v-model="row.zdlx" placeholder="请选择">
              <el-option label="维度" :value="1"></el-option>
              <el-option label="指标" :value="2"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="val8" label="时间维度">
          <template #default="{ row }">
            <el-select
              v-model="row.sjwd"
              placeholder="请选择"
              v-if="row.zdlx == 2"
            >
              <el-option
                v-for="item in sjdwList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="val10" label="计算周期">
          <template #default="{ row }">
            <el-select
              v-model="row.jszq"
              placeholder="请选择"
              v-if="row.zdlx == 2"
            >
              <el-option
                v-for="item in jszqList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="val9" label="所属指标域">
          <template #default="{ row }">
            <avue-input-tree
              v-if="row.zdlx == 2"
              default-expand-all
              v-model="row.zby"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择归属域"
              :dic="viewGroup"
            ></avue-input-tree>
          </template>
        </el-table-column>
        <el-table-column>
          <template #header>
            精度
            <el-tooltip
              class="item"
              effect="dark"
              content="精度的数值代表小数点的位数，此处仅支持输入整"
              placement="top"
            >
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            <div
              style="display: flex; align-items: center"
              v-if="row.zdlx == 2"
            >
              <el-input placeholder="" v-model="row.jd"></el-input>

              <el-checkbox
                true-label="1"
                false-label="0"
                style="margin-left: 16px"
                v-model="row.sswr"
              >
                四舍五入
              </el-checkbox>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数仓分层">
          <template #default="{ row }">
            <el-select
              v-model="row.sjfc"
              placeholder="请选择数仓分层"
              v-if="row.zdlx == 2"
            >
              <el-option
                v-for="(item, index) in sjfcList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="阈值">
          <template #default="{ row }">
            <div
              style="display: flex; align-items: center"
              v-if="row.zdlx == 2"
            >
              <el-input placeholder="" v-model="row.tvmin"></el-input>
              -
              <el-input placeholder="" v-model="row.tvmax"></el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="val11" label="单位">
          <template #default="{ row }">
            <div style="display: flex" v-if="row.zdlx == 2">
              <el-select
                v-model="row.dw"
                placeholder="请选择单位"
                :style="{ width: form.dw === '其他' ? '122px' : '250px' }"
                class="myselect"
              >
                <el-option
                  v-for="(item, index) in dwList"
                  :key="index"
                  :label="item.name"
                  :value="item.bm"
                ></el-option>
              </el-select>
              <el-input
                v-if="form.dw === '其他'"
                v-model="form.diydw"
                style="width: 122px; margin-left: 6px"
                placeholder="请输入单位"
              ></el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="val11" label="标签">
          <template #default="{ row }">
            <el-select
              v-if="row.zdlx == 2"
              v-model="row.bq"
              filterable
              multiple
              remote
              allow-create
              default-first-option
              @change="changeTag"
              @remove-tag="removeTag"
              :remote-method="remoteMethod"
              placeholder="请创建或者选择标签"
            >
              <el-option
                v-for="item in formatLabels"
                :key="item.bqmc"
                :label="item.bqmc"
                :value="item.bqmc"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <div class="step-btn">
        <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
          取消
        </el-button>
        <el-button size="small" @click="editStep--">上一步</el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="false"
          @click="handleSave1"
        >
          保存
        </el-button>
      </div>
    </template>
  </DT-View>
</template>

<script>
import TableWrapper from "./TableWrapper"
import LeftSide from "./LeftSide.vue"
import RelateDrawer from "./RelateDrawer.vue"
import CommonTable from "@/components/CommonTable.vue"
import options from "../mixins/options"

import encryptSql from "@/utils/encryptSql.js"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
export default {
  components: {
    TableWrapper,
    LeftSide,
    RelateDrawer,
    CommonTable
  },
  mixins: [options],

  props: {},
  data() {
    return {
      dragTables: [],

      title: "新增",
      form: {
        id: null,
        name: "",
        description: "",
        sourceId: "",
        isVisc: 1,
        code: "",
        sql: "",
        variable: "",
        model: "",
        config: "",
        groupId: 0,
        roles: []
      },
      variable: [],
      model: "",
      activeName: "model",
      authorVariable: [],
      roles: [],
      modelList: [],
      editStep: 1,
      pageSize: 10,
      isSimple: 0,
      isRunOver: false,
      sqlResult: [],
      sqlColumns: [],
      isRunning: false,
      drawer: false,
      relateDrawer: false,
      sourceName: "",
      sourceId: "",
      dragged: null,
      dargging: false,
      tree: [],
      fieildData: [],
      curTableInfo: null,
      sourceTableInfo: null,
      targetTableInfo: null,
      targetTableInfoCopy: null,
      sourceTableInfoCopy: null,
      tableData: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableColumns: []
    }
  },
  computed: {},
  created() {
    this.id = this.$route.query.id || null
    if (this.id) {
      this.initForm()
      this.title = "编辑"
    } else {
      this.form.groupId = this.$route.query.group
      this.title = "新增"
    }
  },
  mounted() {},
  watch: {
    dragTables: {
      handler(val) {
        this.tree = this.arrayToTree(val)
        console.log(this.tree, "tree")
      },
      deep: true
    },
    "targetTableInfo.fields": {
      handler(val) {
        this.$nextTick(() => {
          this.$refs.relateDrawer.toggleSelection(val, "targetTable")
        })
      },
      deep: true
    },
    "sourceTableInfo.fields": {
      handler(val) {
        this.$nextTick(() => {
          this.$refs.relateDrawer.toggleSelection(val, "sourceTable")
        })
      },
      deep: true
    }
  },
  methods: {
    async handleSave1() {
      await this.$httpBi.indicatorAnagement.saveTempSqlIndicator({
        type: "2"
      })
      this.$message.success("保存成功")
      this.$router.push({
        path: "/ddsBi/indicatorAnagement",
        query: {}
      })
    },
    // 删除
    delDropBox(item) {
      // 从dragTables删除item
      const index = this.dragTables.findIndex(table => table.name === item.name)
      this.dragTables.splice(index, 1)
      if (item.children.length) {
        item.children.forEach(child => {
          this.delDropBox(child)
        })
      }
      this.handleSave()
    },
    // 表单组确认
    async submitFrom() {
      this.form["variable"] = JSON.stringify(this.variable)
      this.form["config"] = ""
      let model = {}
      for (let i = 0; i < this.modelList.length; i++) {
        model[this.modelList[i].name] = {
          alias: this.modelList[i].alias,
          sqlType: this.modelList[i].sqlType,
          visualType: this.modelList[i].visualType,
          modelType: this.modelList[i].modelType
        }
      }
      this.form["model"] = JSON.stringify(model)
      this.form["source"] = {}
      this.form["roles"] = []

      for (let i = 0; i < this.roles.length; i++) {
        this.form["roles"].push({
          roleId: this.roles[i].roleId,
          columnAuth: JSON.stringify(this.roles[i].columnAuth),
          rowAuth: JSON.stringify(this.roles[i].rowAuth)
        })
      }

      // 加密sql
      this.form.sql = encryptSql.encrypt(this.form.sql)
      this.form.config = JSON.stringify(this.tree)
      console.log(this.form, "form")
      if (this.form.id) {
        // 修改
        await this.$httpBi.view.update(this.form)
        this.$message.success("操作成功")
        this.$router.go(-1)
      } else {
        // 新增
        await this.$httpBi.view.create(this.form)
        this.$message.success("操作成功")
        this.$router.go(-1)
      }
    },
    // 上一步
    lastStep() {
      this.editStep = 1
      this.modelList = []
    },
    // 返回列表页
    goBack() {
      this.$router.go(-1)
    },
    // 请求表单数据
    initForm() {
      this.$httpBi.view
        .getOne({ id: this.id })
        .then(res => {
          this.form = { ...res.data, groupId: res.data.groupId.toString() }
          this.dragTables = this.treeToArray(JSON.parse(res.data.config))
          console.log(this.form, "  this.form")
          this.model = res.data.model
          this.variable = JSON.parse(res.data.variable)
          this.roles = []
          for (let i = 0; i < res.data.roles.length; i++) {
            this.roles.push({
              roleId: res.data.roles[i].roleId,
              columnAuth: JSON.parse(res.data.roles[i].columnAuth),
              rowAuth: JSON.parse(res.data.roles[i].rowAuth)
            })
          }
          this.isRunOver = true
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    cancelForm() {
      if (this.targetTableInfoCopy && this.sourceTableInfoCopy) {
        this.relateDrawer = false
        // 取消还原
        this.targetTableInfo = { ...this.targetTableInfoCopy }
        this.sourceTableInfo = { ...this.sourceTableInfoCopy }
        this.dragTables.find(item => {
          if (item.id === this.targetTableInfo.id) {
            item = this.targetTableInfo
          } else if (item.id === this.sourceTableInfo.id) {
            item = this.sourceTableInfo
          }
        })
      } else {
        this.relateDrawer = false
        this.delDropBox(this.targetTableInfo)
        // 删除
      }
    },
    async handleSave() {
      this.$nextTick(async () => {
        const params = {
          model: "visc",
          tables: this.tree[0]
        }
        const { data } = await this.$httpBi.view.getSql(params)
        console.log(data, "data")
        this.form.sql = data
        this.relateDrawer = false
        this.targetTableInfoCopy = null
        this.sourceTableInfoCopy = null
        console.log(this.dragTables, "保存this.dragTables")
      })
    },
    async runSql() {
      this.isRunning = true
      console.log(this.form.sql, "this.form.sql")
      const params = {
        sourceId: this.sourceId,
        sql: encryptSql.encrypt(this.form.sql),
        variables: [],
        pageNo: 1,
        pageSize: this.pageSize
      }
      const { data } = await this.$httpBi.view.runsql(params)
      this.tableColumns = data.columns.map(item => ({
        ...item,
        label: item.name,
        prop: item.name,
        visible: true,
        sortable: false
      }))
      this.tableData = data.resultList
      this.isRunning = false
      this.isRunOver = true
    },
    handleExportExcel(selection) {
      jsonToSheetXlsx({
        data: selection.length ? selection : this.tableData,
        filename: "表数据" + new Date().getTime()
      })
    },
    handleSelectionChangeLeft(val, tableInfo) {
      this.dragTables.find(item => item.name === tableInfo.name).fields = val
      this.sourceTableInfo.fields = val
    },
    handleSelectionChangeRight(val, tableInfo) {
      this.dragTables.find(item => item.name === tableInfo.name).fields = val
      this.targetTableInfo.fields = val
    },
    // 关联字段的更改
    changeRelate(tableInfo, val, key, index) {
      this.dragTables.find(item => item.name === tableInfo.name).ref[index][
        key
      ] = val
      this.targetTableInfo.ref[index][key] = val
    },
    // 改变关联类型
    changeRelateType(val) {
      this.dragTables.find(item => item.name === this.targetTableInfo.name).c =
        val
      this.targetTableInfo.c = val
    },
    // 添加关联字段
    addRelate() {
      this.dragTables
        .find(item => item.name === this.targetTableInfo.name)
        .ref.push({
          fy: null, // --自身字段
          fr: null // --父级字段
        })
    },
    // 删除关联字段
    removeRelate(item) {
      const index = this.targetTableInfo.ref.indexOf(item)
      console.log(this.targetTableInfo.ref, "this.targetTableInfo.ref")

      console.log(index, "index")
      if (index !== -1 && this.targetTableInfo.ref.length > 1) {
        this.dragTables
          .find(item => item.name === this.targetTableInfo.name)
          .ref.splice(index, 1)
      }
    },
    // 点击关系icon
    handleRelate(item) {
      this.targetTableInfoCopy = { ...item }
      console.log(this.targetTableInfoCopy, "   this.targetTableInfoCopy ")
      this.relateDrawer = true
      this.targetTableInfo = item
      const sourceName = item.source
      this.sourceTableInfo = this.dragTables.find(
        item => item.name === sourceName
      )
      this.sourceTableInfoCopy = { ...this.sourceTableInfo }
    },
    //
    handleSelectionChange(val) {
      console.log(val, "val")
      this.dragTables.find(
        item => item.name === this.curTableInfo.name
      ).fields = val
      console.log(this.dragTables, "this.dragTables")
      this.curTableInfo.fields = val
    },
    // 数据源选择
    changeSourceDb(sourceId, sourceName) {
      this.sourceId = sourceId
      this.sourceName = sourceName
    },
    // 点击查看字段详情
    fieldInfo(item) {
      console.log("查看字段详情", item)
      this.curTableInfo = item
      this.fieildData = item.allFields
      this.drawer = true
      this.$nextTick(() => {
        this.toggleSelection(item.fields)
      })
    },
    // 默认选中
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },

    // 获取表字段
    async getTableFields(tableName) {
      const { data } = await this.$httpBi.view.getColumns({
        id: this.sourceId,
        dbName: this.sourceName,
        tableName: tableName
      })
      const allFields = data.columns ?? []
      const tableInfo = this.dragTables.find(item => item.name === tableName)
      tableInfo.allFields = allFields

      // 如果是根节点，直接赋值
      if (this.dragTables.length === 1) {
        console.log(this.dragTables, "dragTables")
        tableInfo.fields = allFields
        this.handleSave()
        return
      } else {
        console.log("//////////////////////")
        tableInfo.fields = allFields
        const sourceName = tableInfo.source
        this.sourceTableInfo = this.dragTables.find(
          item => item.name === sourceName
        )
        console.log(tableInfo, "tableInfo")
        this.targetTableInfo = tableInfo
        this.relateDrawer = true
      }
    },
    // 拖拽开始
    handleDragstart(data) {
      const index = this.dragTables.findIndex(item => item.name === data.name)
      if (index > -1) {
        this.dargging = false
        return this.$message.warning("当前数据表已经存在，请不要重复关联哦")
      }
      this.dragged = data
      this.dargging = true
      console.log(this.dragged, "拖拽开始")
    },
    // 拖拽结束
    handleDragEnd() {
      console.log("拖拽结束")
      if (this.dragged) {
        this.dragged = null
      }
      this.dargging = false
    },
    // 允许放下拖拽
    dragover(e) {
      // const width = this.$refs.dragContainer.clientWidth;
      // if (e.clientY > width - 20) {
      //   this.$refs.dragContainer.scrollLeft -= 10;
      // }
      e.preventDefault()
    },
    // 放下事件
    async drop(e, source) {
      if (!this.dargging) return
      e.preventDefault()
      // 如果已经拖拽了根元素就不能再拖拽了
      if (this.dragTables.length && !source) {
        return
      }

      this.dragTables.push({
        name: this.dragged.name,
        source,
        target: this.dragged.name,
        allFields: [], // --所有字段
        fields: [], // --已选表字段
        c: "left", // --连接方式
        ref: [
          {
            fy: null, // --自身字段
            fr: null // --父级字段
          }
        ] // --关联字段
      })
      this.getTableFields(this.dragged.name)
    },
    // 数组转树
    arrayToTree(
      items,
      idKey = "target",
      pidKey = "source",
      childKey = "children"
    ) {
      const result = [] // 存放结果集
      const itemMap = {} //
      for (const item of items) {
        itemMap[item[idKey]] = { ...item, [childKey]: [] }
      }
      console.log(itemMap, "itemMap")
      for (const item of items) {
        const id = item[idKey]
        const pid = item[pidKey]
        const treeItem = itemMap[id]

        if (!itemMap[pid]) {
          result.push(treeItem)
        } else {
          itemMap[pid][childKey].push(treeItem)
        }
      }
      return this.renderTree(result, 0)
    },
    // 树转数组
    treeToArray(data) {
      return data.reduce((pre, item) => {
        if (item.children) {
          pre.push(item)
          pre.push(...this.treeToArray(item.children))
        } else {
          pre.push(item)
        }
        return pre
      }, [])
    },
    exportExcel() {
      jsonToSheetXlsx({
        data: this.sqlResult,
        filename: "表数据" + new Date().getTime()
      })
    },
    submitForm() {
      console.log(this.tree, "this.tree")
      const sql = this.renderSql(this.tree)
      console.log(sql, "sql")
    },
    renderTree(data, level) {
      return data.map(item => {
        if (item.children) {
          this.renderTree(item.children, level + 1)
        }
        item.level = level
        return item
      })
    },
    // 下一步
    nextStep() {
      this.editStep = 2
      this.tableData1 = this.tableColumns.map(item => ({
        zbmc: item.name,
        sjfc: 5,
        sjwd: "",
        zdlx: "",
        jszq: "",
        zby: "",
        jd: "",
        sswr: "",
        tvmin: "",
        tvmax: "",
        dw: "",
        bq: []
      }))
      console.log(this.tableData1, "this.tableData1")
    }
  }
}
</script>

<style scoped lang="scss">
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }
  .steps {
    width: 406px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;
    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;
      &.active {
        background: #1563ff;
      }
    }
    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;
      white-space: nowrap;
      &.active {
        color: #1563ff;
      }
      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}

.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100vh - 230px);
  background: #f0f2f5;
  padding-top: 20px;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    min-height: calc(100vh - 230px);
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;
    .custom-tree-node {
      width: 100%;
    }
  }
  .right {
    position: relative;
    width: calc(100% - 320px);
    padding: 20px;
    min-height: calc(100vh - 230px);

    background: #fff;
    padding-bottom: 52px;

    .table-links-wrapper {
      position: relative;
      width: 100%;
      overflow: auto;
      height: 300px;
      padding: 24px;
      box-sizing: border-box;
      background-color: #f2f2f2;

      .table-wrapper {
        .table-link-tip {
          position: absolute;
          align-items: center;
          display: flex;
          justify-content: center;
          cursor: pointer;

          .table-link-line {
            position: absolute;
            z-index: 2;
            background: #c1c1c1;
          }

          .table-link-line.vertical {
            width: 1px;
            left: 18px;
          }

          .table-link-line.horizontal {
            right: 0;
            height: 1px;
          }
        }

        .table-info {
          height: 28px;
          width: 180px;
          background: #fff;
          position: absolute;
          color: #000;
          cursor: pointer;
          padding: 1px 1px 1px 0;
          line-height: 28px;
          border-left: 2px solid #2153d4;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .empty-table-tips-wrapper {
        text-align: center;
        height: 100%;
        border: 1px solid transparent;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        justify-content: center;
        align-items: center;
        &.dargging {
          border: 1px dashed #1563ff;
          background-color: #f8f8f8;
        }
      }

      .table-link-help-wrapper {
        text-align: center;
        height: 100%;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .tables {
      height: calc(100% - 352px);
      width: 100%;
      margin-top: 20px;
    }
    .empty {
      height: calc(100% - 352px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .empty-icon {
        width: 120px;
        height: 120px;
        background: url("~@/assets/images/empty1.png") no-repeat;
      }
      .empty-text {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #5c646e;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.list {
  width: 300px;
  padding: 50px;
  border: 1px solid #ccc;

  .list-item-custom {
    width: 200px;
    height: 30px;
    background-color: skyblue;
    margin: 10px 0;
    line-height: 30px;
    cursor: move;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.sql-view {
  flex: 1;
  width: calc(100% - 300px);
  height: calc(100vh - 400px);
  box-sizing: border-box;
  border-left: none;
  text-align: center;
  background: #fff;

  .table-links-wrapper {
    position: relative;
    width: 100%;
    overflow: auto;
    height: 300px;
    padding: 24px;
    box-sizing: border-box;
    background-color: #f2f2f2;

    .table-wrapper {
      .table-link-tip {
        position: absolute;
        align-items: center;
        display: flex;
        justify-content: center;
        cursor: pointer;

        .table-link-line {
          position: absolute;
          z-index: 2;
          background: #c1c1c1;
        }

        .table-link-line.vertical {
          width: 1px;
          left: 18px;
        }

        .table-link-line.horizontal {
          right: 0;
          height: 1px;
        }
      }

      .table-info {
        height: 28px;
        width: 180px;
        background: #fff;
        position: absolute;
        color: #000;
        cursor: pointer;
        padding: 1px 1px 1px 0;
        line-height: 28px;
        border-left: 2px solid #2153d4;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .empty-table-tips-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      border: 1px solid transparent;
      color: rgba(0, 0, 0, 0.65);

      &.dargging {
        border: 1px dashed #c6c6c6;
        background-color: #f8f8f8;
      }
    }

    .table-link-help-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.step-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
</style>
