<template>
  <div class="data-analysis-page">
    <!-- 顶部时间筛选区 -->
    <div class="top-time-filter">
      <el-row type="flex">
        <div class="select-wrap">
          <div class="select-item">
            <div class="select-label">分析范围</div>
            <el-date-picker
              v-model="date"
              type="daterange"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>
        </div>
        <el-button
          type="primary"
          style="margin-left: auto"
          @click="fetchChartData"
        >
          分析
        </el-button>
      </el-row>
    </div>

    <!-- 主要数据展示区 -->
    <div class="main-data-display">
      <div class="chart-container">
        <ChartLine
          :chart-data="indChangeInfo.indicatorTrends"
          x-field="label"
          y-field="value"
          series-name=""
          :y-axis-name="yAxisName"
          :bar-width="16"
          show-data-zoom
          :show-num="8"
          :unit="unit"
        />
      </div>
      <!-- 关键指标展示 -->
      <div class="key-indicators">
        <div class="indicator-card">
          <div class="card-title">指标变化</div>
          <div class="card-value">
            <span class="old">
              {{ indChangeInfo.indStartValue }}
              <br />
              <div class="old-date">{{ indChangeInfo.indStartTime }}</div>
            </span>
            <span style="margin: 0 6px">~</span>
            <span class="new">
              {{ indChangeInfo.indEndValue }}

              <br />
              <div class="new-date">{{ indChangeInfo.indEndTime }}</div>
            </span>
          </div>
        </div>
        <div class="indicator-card">
          <div class="card-title">差值</div>
          <div class="card-value">
            {{ indChangeInfo.changeValue }}
          </div>
        </div>
        <div class="indicator-card">
          <div class="card-title">变化率</div>
          <div
            class="card-value"
            :class="{
              negative: indChangeInfo.changeRate < 0,
              positive: indChangeInfo.changeRate >= 0,
            }"
          >
            {{ indChangeInfo.changeRate }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 底部维度分析区 -->
    <div class="bottom-dimension-analysis">
      <div class="dimension-filter">
        <div class="title">内部贡献度分析：</div>
        <div class="select-wrap">
          <!-- <div class="select-item">
            <div class="select-label">下级指标</div>
            <el-select
              v-model="nextInd"
              placeholder="请选择下级指标"
              value-key="currentCode"
              @change="fetchChartData1"
            >
              <el-option
                v-for="item in nextIndicators"
                :key="item.currentCode"
                :label="item.currentName"
                :value="item"
              ></el-option>
            </el-select>
          </div> -->
          <div class="select-item">
            <div class="select-label">分析维度</div>
            <el-select
              placeholder="请选择分析维度"
              @change="changeDim"
              v-model="dim"
              filterable
              value-key="levelCode"
            >
              <el-option
                v-for="dim in dimList"
                :key="dim.levelCode"
                :label="dim.dimName"
                :value="dim"
              ></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="chart-container" v-loading="leftLoading">
          <ChartLine
            :chart-data="chartData1"
            x-field="label"
            y-field="value"
            series-field="dimVlue"
            :y-axis-name="yAxisName"
            :bar-width="16"
            show-data-zoom
            :show-num="8"
            :unit="unit"
            ref="ChartLine2"
          />
        </div>
        <div class="contribution-ranking">
          <h3>贡献度排名</h3>
          <div class="contribution" v-loading="rightLoading">
            <div class="positive-contribution">
              <h4>正向贡献</h4>
              <ul>
                <template v-if="jointContribution.length > 0">
                  <li v-for="(item, index) in jointContribution" :key="index">
                    <div class="li-info">
                      <div class="li-name">{{ item.dimValue }}</div>
                      <div class="li-value">+{{ item.contribution }}%</div>
                    </div>

                    <div
                      class="bar"
                      :style="{
                        width:
                          (item.contribution / maxPositiveContribution) * 100 +
                          '%',
                      }"
                    ></div>
                  </li>
                </template>
                <Empty description="暂无数据" size="80" v-else />
              </ul>
            </div>
            <div class="negative-contribution">
              <h4>反向贡献</h4>
              <ul>
                <template v-if="reverseContribution.length > 0">
                  <li v-for="(item, index) in reverseContribution" :key="index">
                    <div class="li-info">
                      <div class="li-name">{{ item.dimValue }}</div>
                      <div class="li-value">
                        -{{ Math.abs(item.contribution) }}%
                      </div>
                    </div>

                    <div
                      class="bar"
                      :style="{
                        width:
                          (item.contribution / maxNegativeContribution) * 100 +
                          '%',
                      }"
                    ></div>
                  </li>
                </template>
                <Empty description="暂无数据" size="80" v-else />
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChartLine from "@/components/Charts/ChartLine"
import Empty from "@/components/Empty.vue"

export default {
  name: "IndicatorAttribution",
  components: { ChartLine, Empty },
  props: {},
  data() {
    return {
      date: [], // 日期
      unit: "", // 单位
      // 指标变化信息
      indChangeInfo: {
        indStartValue: "",
        indEndValue: "",
        indStartTime: "",
        indEndTime: "",
        changeValue: "",
        changeRate: "",
      },
      analyzeDim: {
        dimCol: "",
        dimValList: [],
      },
      // 下级指标
      nextInd: "",
      dim: "",
      dimList: [],

      chartData: [],
      chartData1: [],
      leftLoading: false,
      rightLoading: false,
      jointContribution: [],
      reverseContribution: [],
      availableDates: [],
      pickerOptions: {
        disabledDate: (time) => {
          if (!this.availableDates.length) return
          // 将时间戳转换为日期字符串
          const dateStr = this.formatDate(time)
          // 如果日期不在可用日期数组中，则禁用
          return !this.availableDates.includes(dateStr)
        },
      },
    }
  },

  created() {
    this.fetchAvailableDates()
    this.getIndDim()
  },
  computed: {
    maxPositiveContribution() {
      return Math.max(
        ...this.jointContribution.map((item) => item.contribution)
      )
    },
    maxNegativeContribution() {
      return Math.min(
        ...this.reverseContribution.map((item) => item.contribution)
      )
    },
    yAxisName() {
      return this.unit ? `单位:${this.unit}` : ""
    },
  },
  watch: {},
  inject: ["parent"],

  methods: {
    // 格式化日期为 yyyy-MM-dd 格式
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, "0")
      const day = String(date.getDate()).padStart(2, "0")
      return `${year}-${month}-${day}`
    },
    async fetchAvailableDates() {
      try {
        const { code, data } = await this.$httpBi.api.paramGet(
          "/indicator/trends/indicatorCornData",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm,
          }
        )
        if (code === 200) {
          this.availableDates = data.map((item) => item.rq)
          if (this.availableDates.length) {
            this.date = [
              this.availableDates[0],
              this.availableDates[this.availableDates.length - 1],
            ]
          }

          console.log(this.date, "data")
          this.fetchChartData()
        }
      } catch (error) {
        console.error("获取可用日期失败:", error)
      }
    },

    async fetchChartData() {
      try {
        const { code, data } = await this.$httpBi.api.paramPost(
          "/indicator/attribution/indAttributions",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm,
            indName: this.parent.indicatorData.zbmc,
            startTime: this.date[0],
            endTime: this.date[1],
          }
        )
        if (code === 200) {
          if (data.length > 0) {
            this.chartData = data[0].indicatorTrends || []
            this.unit = this.chartData[0].dw
            this.indChangeInfo = data[0]
          } else {
            this.indChangeInfo = {}
            this.unit = ""
            this.chartData = []
          }
        }
      } catch (error) {
        console.error("获取趋势数据失败:", error)
      }
    },
    async fetchChartData1() {
      try {
        this.leftLoading = true
        const { code, data } = await this.$httpBi.api.paramPost(
          "/indicator/attribution/indAttributions",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm,
            indName: this.parent.indicatorData.zbmc,
            startTime: this.date[0],
            endTime: this.date[1],
            analyzeDim: this.analyzeDim,
          }
        )
        if (code === 200) {
          this.chartData1 = data.reduce((acc, cur) => {
            return (acc = [...acc, ...cur.indicatorTrends])
          }, [])
          this.leftLoading = false
        }
      } catch (error) {
        console.error("获取趋势数据失败:", error)
      }
    },

    // 获取维度
    async getIndDim() {
      try {
        const { code, data } = await this.$httpBi.api.paramGet(
          "/DimManage/getDimLevelByIndCode",
          {
            indCode: this.parent.indCode,
          }
        )
        if (code === 200) {
          this.dimList = data
          this.dim = data[0]
          this.changeDim(this.dim)
        }
      } catch (error) {
        console.error("获取趋势数据失败:", error)
      }
    },
    async changeDim(levelItem) {
      let dimValList = []

      if (levelItem.enableClustering) {
        const clusterData = await this.$httpBi.api.paramPostQuery(
          "/DimManage/getDimClusterByLevelCode",
          {
            levelCode: levelItem.levelCode,
          }
        )

        await Promise.all(
          clusterData.data.map(async (item, index) => {
            const { data } = await this.$httpBi.api.paramPost(
              "/DimManage/getDimValueByClusterCodes",
              [item.clusterCode]
            )
            this.$set(dimValList, index, {
              dimVal: item.cluName,
              clusterDimValList: data.map((item) => ({
                dimVal: item.value,
                dimCol: levelItem.dimCol,
                dimValCode: item.valueCode,
              })),
            })
          })
        )
        console.log(dimValList, "dimValList")
      } else {
        // 获取维度值
        const { data } = await this.$httpBi.api.paramPostQuery(
          "/DimManage/getDimValueByLevelCode",
          {
            levelCode: levelItem.levelCode,
            size: -1,
            page: 1,
          }
        )
        dimValList = data.list.map((item) => ({
          dimVal: item.value,
          dimCol: levelItem.dimCol,
        }))
      }

      this.analyzeDim = {
        ...levelItem,
        dimType: 1,
        dimValList: dimValList,
      }

      this.fetchChartData1()
      this.getContribution()
    },
    // 获取贡献度分析
    async getContribution() {
      try {
        this.rightLoading = true
        const { data } = await this.$httpBi.api.paramPost(
          "/indicator/attribution/analyse",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm,
            indName: this.parent.indicatorData.zbmc,
            startTime: this.date[0],
            endTime: this.date[1],
            analyzeDim: this.analyzeDim,
          }
        )
        const res = data.filter(
          (item) =>
            item.dimName === this.dim.dimName &&
            item.indCode === this.nextInd.currentCode
        )
        console.log(this.dim.dimName, "dimName")
        console.log(this.nextInd.currentCode, "currentCode")

        if (res.length > 0) {
          this.jointContribution = res[0].data.jointContribution
          this.reverseContribution = res[0].data.reverseContribution
        }

        // if (code === 200) {
        //   console.log(data, "data")
        //   this.jointContribution = data.jointContribution
        //   this.reverseContribution = data.reverseContribution
        // }
        this.rightLoading = false
      } catch (error) {
        console.error("获取趋势数据失败:", error)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.data-analysis-page {
  .top-time-filter {
    margin-bottom: 16px;
  }

  .main-data-display {
    display: flex;
    justify-content: space-between;

    .chart-container {
      flex: 1;
      height: 248px;
      padding: 0 24px 24px 0;
      box-sizing: border-box;
    }

    .key-indicators {
      .indicator-card {
        width: 360px;
        height: 64px;
        background: #f5f7fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 10px 20px;
        box-sizing: border-box;
        margin-bottom: 16px;

        .card-title {
          height: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          line-height: 14px;
          text-align: left;
          font-style: normal;
          width: 96px;
        }

        .card-value {
          font-size: 18px;
          font-weight: bold;
          display: flex;

          &.negative {
            color: #ff5256;
          }

          &.positive {
            color: #00cc88;
          }

          .old {
            text-align: center;
            white-space: nowrap;

            .old-date {
              height: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #999999;
              line-height: 12px;
              font-style: normal;
            }
          }

          .new {
            text-align: center;
            white-space: nowrap;

            .new-date {
              height: 12px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #999999;
              line-height: 12px;
              font-style: normal;
            }
          }
        }
      }
    }
  }

  .bottom-dimension-analysis {
    .dimension-filter {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #222222;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }

      .select-wrap {
        display: flex;

        .select-item {
          display: flex;
          align-items: center;
          margin-right: 32px;

          &:last-child {
            margin-right: 0;
          }

          .select-label {
            min-width: max-content;
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: center;
            font-style: normal;
            margin-right: 12px;
          }
        }
      }
    }

    .content {
      display: flex;
      justify-content: space-between;
      padding-top: 16px;
      box-sizing: border-box;

      .chart-container {
        flex: 1;
        height: 243px;
        padding: 0 24px 20px 0;
        box-sizing: border-box;
      }

      .contribution-ranking {
        width: 360px;

        h3 {
          height: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #222222;
          line-height: 14px;
          text-align: left;
          font-style: normal;
        }

        .contribution {
          display: flex;
          justify-content: space-between;
          margin-top: 16px;
        }

        .positive-contribution {
          width: 172px;
          height: 192px;
          background: #f5f7fa;
          border-radius: 8px;
          padding: 16px;
          box-sizing: border-box;

          h4 {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: left;
            font-style: normal;
          }

          ul {
            li {
              .li-info {
                display: flex;
                justify-content: space-between;
                margin: 20px 0 12px;

                .li-name {
                  height: 12px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #222222;
                  line-height: 12px;
                  text-align: left;
                  font-style: normal;
                }

                .li-value {
                  height: 12px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #1563ff;
                  line-height: 12px;
                  text-align: left;
                  font-style: normal;
                }
              }

              .bar {
                height: 2px;
                background: linear-gradient(
                  90deg,
                  rgba(21, 99, 255, 0.4) 0%,
                  #1563ff 100%
                );
                border-radius: 2px;
              }
            }
          }
        }

        .negative-contribution {
          width: 172px;
          height: 192px;
          background: #f5f7fa;
          border-radius: 8px;
          padding: 16px;
          box-sizing: border-box;

          h4 {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: left;
            font-style: normal;
          }

          ul {
            li {
              .li-info {
                display: flex;
                justify-content: space-between;
                margin: 20px 0 12px;

                .li-name {
                  height: 12px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #222222;
                  line-height: 12px;
                  text-align: left;
                  font-style: normal;
                }

                .li-value {
                  height: 12px;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #ff800e;
                  line-height: 12px;
                  text-align: left;
                  font-style: normal;
                }
              }

              .bar {
                height: 2px;
                background: linear-gradient(
                  90deg,
                  rgba(255, 128, 14, 0.4) 0%,
                  #ff800e 100%
                );
                border-radius: 2px;
              }
            }
          }
        }
      }
    }
  }
}

.select-wrap {
  display: flex;

  .select-item {
    display: flex;
    align-items: center;
    margin-right: 32px;

    &:last-child {
      margin-right: 0;
    }

    .select-label {
      min-width: max-content;
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 14px;
      text-align: center;
      font-style: normal;
      margin-right: 12px;
    }
  }
}

::v-deep .el-row {
  margin-bottom: 0;
}
</style>
