<template>
  <div>
    <!-- 主视图 -->
    <main-view
      v-if="mainView.show"
      :data="mainView.data"
      :search.sync="mainView.search"
      :pagination.sync="mainView.pagination"
      :channels="channels"
      :scene-type="sceneType"
      @search="getTableData"
      @handleAdd="handleAdd"
      @handleEdit="handleEdit"
      @handleDelete="handleDelete"
      @handleHistory="handleHistory"
      @handleClose="handleClose"
      @handleImport="handleImport"
      @paginationChange="getTableData"
    />
    <HistoryDialog ref="HistoryDialog" />
    <!-- 创建工具类算法 -->
    <create-algorithm
      v-if="createAlgorithm.show"
      :title="createAlgorithm.title"
      :data="createAlgorithm.data"
      @lookVersion="lookVersion"
      @backMain="backMain"
    />
    <!-- 历史版本 -->
    <HistoryVersion v-if="historyVersion.show" @handleBack="handleLstPage" />
  </div>
</template>

<script>
import Request from "@/service"
import MainView from "./components/mainView"
import CreateAlgorithm from "./components/createAlgorithm"

import HistoryVersion from "../common/HistoryVersion.vue"
import HistoryDialog from "./components/HistoryDialog.vue"
export default {
  name: "role",
  components: {
    MainView,
    HistoryVersion,
    HistoryDialog,
    CreateAlgorithm
  },
  props: {
    showDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      sceneType: 0,
      importExcelDialogVisible: false,
      pageShow: true,
      warningType: [], // 预警类型
      stuWarningResult: [], // 学生预警指标
      channels: [],
      // 用户id
      roleId: null,

      // 主视图
      mainView: {
        show: true,
        // 数据
        data: [
          {
            col1: "学生困难度评估",
            col2: "基于学生消费聚类、层次分析权重得出的",
            col3: "每天",
            col4: "2025-04-28 14:43:12",
            col5: 14,
            col6: "82%",
            col7: "正常"
          }
        ],
        // 搜索
        search: {
          keywords: ""
        },
        // 分页配置
        pagination: {
          total: 1,
          pageSize: 10,
          currentPage: 1
        }
      },
      // 表单视图
      createAlgorithm: {
        show: false,
        // 标题
        title: "",
        data: {}
      },
      // 历史版本:
      historyVersion: {
        show: false
      }
    }
  },
  mounted() {
    console.log(this, "xxxxxxxxxxxxxxxxxx")

    // this.getTableData()
  },
  methods: {
    // ------------------------ 主视图 ------------------------
    // 拉取表格数据
    getTableData() {
      let param = {
        pageSize: this.mainView.pagination.pageSize,
        currentPage: this.mainView.pagination.currentPage,
        query: ""
      }
      this.$dt_loading.show()
      Request.api
        .paramGet("/pushSetting/scene/list", param)
        .then(res => {
          this.mainView.pagination.total = res.data.total
          this.mainView.data = res.data.records
          this.mainView.show = true
          this.pageShow = true
          this.$dt_loading.hide()
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 新增
    handleAdd() {
      this.formView.data = {
        status: 1,
        stuWarningResultIds: [],
        stuWarningTypeIds: []
      }
      this.formView.title = this.$t("button.add_")
      this.formView.show = true
      this.mainView.show = false
    },

    // 编辑
    async handleEdit() {
      // this.createAlgorithm.show = true
      // this.mainView.show = false
      // this.$emit("update:showDetail", true)
      this.$router.push('/ddsBi/createAlgorithmExecution')

    },
    async handleHistory() {
      this.$refs.HistoryDialog.open()
    },
    lookVersion() {
      this.createAlgorithm.show = false
      this.mainView.show = false
      this.historyVersion.show = true
    },
    handleLstPage() {
      this.handleBack()
      this.createAlgorithm.show = true
    },
    // 删除
    handleDelete(row) {
      this.$confirm("是否删除选中数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()

          Request.api
            .paramGet("system/specialPersonnel/delNameGroup", { id: row.id })
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 开启
    handleOpen(row) {
      this.$confirm("该场景一旦开启后，推送该场景", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()

          Request.api
            .paramPost("/pushSetting/scene/changeState", {
              id: row.id,
              isEnable: 1
            })
            .then(() => {
              this.$message.success("启用成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 关闭
    handleClose(row) {
      this.$confirm("该场景一旦禁用后，无法推送该场景，直到再次开启", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()
          Request.api
            .paramPost("/pushSetting/scene/changeState", {
              id: row.id,
              isEnable: 0
            })
            .then(() => {
              this.$message.success("禁用成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 返回
    handleBack() {
      this.createAlgorithm.show = false
      this.mainView.show = false
      this.historyVersion.show = false
    },
    backMain() {
      this.handleBack()
      this.mainView.show = true
      this.$emit("update:showDetail", false)
    }
  }
}
</script>
<style scoped lang="scss">
.btns {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
</style>
