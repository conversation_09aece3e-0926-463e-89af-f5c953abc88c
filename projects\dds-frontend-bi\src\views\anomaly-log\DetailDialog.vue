<template>
  <el-dialog
    title="处理详情"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    append-to-body
    width="650px"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      status-icon
      :rules="rules"
      ref="ruleForm"
      label-width="120px"
      class="ruleForm"
    >
      <el-form-item label="指标名称:" prop="djdx">
        {{ ruleForm.indName }}
      </el-form-item>
      <el-form-item label="异常状况:" prop="ycsj">
        {{ ruleForm.alertStatus }}
      </el-form-item>
      <el-form-item label="异常时间:" prop="ycsj">
        {{ ruleForm.alertTime }}
      </el-form-item>
      <el-form-item label="处理状态:" prop="clzt">
        {{ ruleForm.processStatus }}
      </el-form-item>
      <el-form-item label="异常原因:" prop="ycyy">
        {{ ruleForm.alertReason }}
      </el-form-item>
      <el-form-item label="备注:">
        {{ ruleForm.remark }}
      </el-form-item>
      <el-form-item label="附件:">
        <el-image
          v-if="ruleForm.attachment"
          style="width: 250px"
          :src="ruleForm.attachment"
          :preview-src-list="srcList"
        ></el-image>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import Request from "@/service"

export default {
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      srcList: [],
      ruleForm: {},
      // 处理状态选项
      statusOptions: [
        { label: "暂未处理", value: "暂未处理" },
        { label: "已修复", value: "已修复" },
        {
          label: "环境、数据链路等因素正常后自动恢复",
          value: "环境、数据链路等因素正常后自动恢复"
        },
        { label: "无需处理", value: "无需处理" },
        { label: "排查中,问题尚不明确", value: "排查中,问题尚不明确" }
      ],
      // 异常原因选项
      reasonOptions: [
        { label: "计算规则错误", value: "计算规则错误" },
        { label: "数据源数据缺失", value: "数据源数据缺失" },
        { label: "指标正常波动", value: "指标正常波动" },
        { label: "服务器磁盘已满", value: "服务器磁盘已满" },
        { label: "服务器故障", value: "服务器故障" },
        { label: "停电", value: "停电" },
        { label: "服务器重启", value: "服务器重启" }
      ],
      rules: {
        clzt: [{ required: true, message: "请输入登记对象", trigger: "blur" }],
        ycyy: [{ required: true, message: "请输入异常时间", trigger: "blur" }]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    async open(row) {
      this.dialogVisible = true
      const { data } = await Request.api.paramPost(
        "zeroWarn/getProcessingDetails",
        {
          id: row.id
        }
      )
      console.log(data)
      this.ruleForm = data
      this.srcList = [data.attachment]
    },
    handleChange() {
      console.log(this.ruleForm)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
