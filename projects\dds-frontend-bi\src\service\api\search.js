import service from "../base"
// import config from "../config"
const apiPrefix = process.env.VUE_APP_SEARCH_API

/**
 * 全局搜索
 */
export default {
  // 新增
  globalSearch(data) {
    return service(
      {
        url: apiPrefix + "send",
        method: "post",
        data
      },
      "/send"
    )
  },
  globalSearchRecord(data) {
    return service({
      url: apiPrefix + "record",
      method: "post",
      data
    })
  },
  // 清除历史记录
  clearHistory(data) {
    return service({
      url: apiPrefix + "clear",
      method: "post",
      data
    })
  },
  // 查表字段
  getTableColumn(data) {
    return service(
      {
        url: apiPrefix + "getIndicatorTableColumn",
        method: "post",
        data
      },
      "/getIndicatorTableColumn"
    )
  },
  getTableData(data) {
    return service(
      {
        url: apiPrefix + "getIndicatorTableData",
        method: "post",
        data
      },
      "/getIndicatorTableData"
    )
  },
  // getTableColumn(data) {
  //   return service({
  //     url: apiPrefix + "getTableColumn",
  //     method: "post",
  //     data
  //   })
  // },
  // getTableData(data) {
  //   return service({
  //     url: apiPrefix + "getTableData",
  //     method: "post",
  //     data
  //   })
  // },
  getColumnAndParam(params) {
    return service({
      url: apiPrefix + "bi/view/getColumnAndParam",
      method: "get",
      params
    })
  },
  getViewData(data) {
    return service({
      url: apiPrefix + "bi/view/getViewData",
      method: "post",
      data
    })
  },
  getIndicatorLatestData(data) {
    return service(
      {
        url: apiPrefix + "getIndicatorLatestData",
        method: "post",
        data
      },
      "/getIndicatorLatestData"
    )
  },
  getIndicatorSearchData(data) {
    return service(
      {
        url: apiPrefix + "getIndicatorSearchData",
        method: "post",
        data
      },
      "/getIndicatorSearchData"
    )
  },
  getIndicatorDimensionSearchCascade(data) {
    return service(
      {
        url: apiPrefix + "getIndicatorDimensionSearchCascade",
        method: "post",
        data
      },
      "/getIndicatorDimensionSearchCascade"
    )
  },
  getAtomIndicatorInfo(data) {
    return service(
      {
        url: apiPrefix + "getAtomIndicatorInfo",
        method: "post",
        data
      },
      "/getAtomIndicatorInfo"
    )
  }
}
