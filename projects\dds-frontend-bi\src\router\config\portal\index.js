import { prefix } from "../../prefix"
import portal from "@/views/portal"
import PortalDetail from "@/views/portal/portal-detail"
import PortalDetailView from "@/views/portal/Portalview"
export default [
  {
    name: "portal",
    component: portal,
    path: prefix + "/portal",
    meta: {
      title: "数据看板", 
    }
  },
  {
    name: "PortalDetail",
    component:PortalDetail,
    path: prefix + "/PortalDetail",
    meta: {
      title: "数据看板详情", 
    },
    children: [
      {
        name: "PortalDetailView",
        component: PortalDetailView,
        path: prefix + "/PortalDetail/Portalview/:id",
        meta: {
          isEdit: true,
        },
      },
    ],
  },
]
