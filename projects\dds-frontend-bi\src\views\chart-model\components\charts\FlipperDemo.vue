<template>
  <div class="flipper-demo">
    <h2>数字翻牌器演示</h2>
    <p class="description">参考蓝色数字显示效果，支持动画翻转</p>
    
    <div class="demo-container">
      <!-- 主要演示 -->
      <div class="main-demo">
        <h3>主要演示 - 数字: {{ currentNumber }}</h3>
        <ChartFlipper
          :value="currentNumber"
          :animation="true"
          width="100%"
          height="140px"
        />
        
        <div class="controls">
          <el-button type="primary" @click="setNumber('123450')">设置为 123450</el-button>
          <el-button type="success" @click="setNumber('9876543')">设置为 9876543</el-button>
          <el-button type="warning" @click="generateRandom">随机数字</el-button>
          <el-button type="info" @click="setNumber('0')">重置为 0</el-button>
        </div>
      </div>
      
      <!-- 多个尺寸演示 -->
      <div class="size-demos">
        <div class="size-demo">
          <h4>大尺寸 (160px)</h4>
          <ChartFlipper
            :value="currentNumber"
            :animation="true"
            width="100%"
            height="160px"
          />
        </div>
        
        <div class="size-demo">
          <h4>标准尺寸 (120px)</h4>
          <ChartFlipper
            :value="currentNumber"
            :animation="true"
            width="100%"
            height="120px"
          />
        </div>
        
        <div class="size-demo">
          <h4>小尺寸 (80px)</h4>
          <ChartFlipper
            :value="currentNumber"
            :animation="true"
            width="100%"
            height="80px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChartFlipper from './ChartFlipper.vue'

export default {
  name: 'FlipperDemo',
  components: {
    ChartFlipper
  },
  data() {
    return {
      currentNumber: '123450'
    }
  },
  methods: {
    setNumber(value) {
      this.currentNumber = value
    },
    generateRandom() {
      const randomValue = Math.floor(Math.random() * 100000000).toString()
      this.currentNumber = randomValue
    }
  }
}
</script>

<style scoped>
.flipper-demo {
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

h2 {
  text-align: center;
  margin-bottom: 10px;
  font-size: 32px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.description {
  text-align: center;
  margin-bottom: 40px;
  font-size: 16px;
  opacity: 0.9;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

.main-demo {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-demo h3 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.controls {
  margin-top: 30px;
  text-align: center;
}

.controls .el-button {
  margin: 5px 10px;
}

.size-demos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.size-demo {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.size-demo h4 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 18px;
  color: #00d4ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flipper-demo {
    padding: 20px;
  }
  
  h2 {
    font-size: 24px;
  }
  
  .main-demo {
    padding: 20px;
  }
  
  .main-demo h3 {
    font-size: 20px;
  }
  
  .size-demos {
    grid-template-columns: 1fr;
  }
  
  .controls .el-button {
    margin: 5px;
    font-size: 12px;
  }
}
</style>
