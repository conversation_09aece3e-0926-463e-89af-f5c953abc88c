<template>
  <div class="chart-data-config-panel">
    <template v-for="item in chartDataList">
      <components
        :is="item.key"
        :key="item.label"
        v-bind="{ ...$attrs, ...item }"
        :widget-params="widgetParams"
        v-on="$listeners"
      />
    </template>
  </div>
</template>

<script>
import { widgetlibs } from "@/components/ChartGraph/index.js"
import DimensionTypeSection from "../ChartDataConfigSection/DimensionTypeSection.vue"
import MetricsTypeSection from "../ChartDataConfigSection/MetricsTypeSection.vue"
import FiltersTypeSection from "../ChartDataConfigSection/FiltersTypeSection.vue"
export default {
  components: {
    dimension: DimensionTypeSection,
    metrics: MetricsTypeSection,
    filters: FiltersTypeSection,
  },
  props: {
    widgetParams: {
      type: Object,
      default: () => ({}),
    },
    
  },
  data() {
    return {}
  },
  computed: {
    // 获取图表配置数据列表
    chartDataList() {
      return (
        widgetlibs.find((item) => {
          return item.id === this.widgetParams.selectedChart
        }).data || []
      )
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.chart-data-config-panel {
  padding: 0 10px;
  box-sizing: border-box;
}
</style>
