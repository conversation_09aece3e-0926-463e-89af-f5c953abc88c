import Request from "@/service"
import Vue from "vue"
export default {
  data() {
    return {
      name: "1",
      variable: "",
      isAddOpt: true,
      columnsValues: [],
      optionDialogVisible: false,
      currentOption: {},
      globalVariable: {},
      views: [],
      viewCols: [],
      viewTextCols: [],

      // 默认值绑定
    }
  },
  props: {
    id: String | Number,
    variableList: Array,
    operators: Array,
    view: Array,
    data: Object,
    type: String,
    model: String, // 全局控制器&普通控制器模式
  },
  created() {
    this.data.valueViewId && this.getOne(this.data.valueViewId)
    this.data.optionType && this.getAll()
    typeof this.variableList === "string" &&
      (this.variableList = JSON.parse(this.variableList))
  },
  methods: {
    changeDefaultType(val) {
      if (val === "dynamic") {
        if (this.data.type === "dateRange") {
          this.$set(this.data, "defaultValue", [
            { type: "day", value: 0, valueType: "current" },
            { type: "day", value: 0, valueType: "current" },
          ])
        } else if (this.data.type === "date") {
          this.$set(this.data, "defaultValue", {
            type: "day",
            value: 0,
            valueType: "current",
          })
        }
      } else {
        // 固定
        if (this.data.type === "dateRange")
          this.$set(this.data, "defaultValue", [])
        else if (this.data.type === "date")
          this.$set(this.data, "defaultValue", "")
      }
    },
    getAll() {
      const recursionFn = (data) => {
        data.forEach((item) => {
          if (item.children) recursionFn(item.children)
          item.value = item.id
          item.label = item.name
        })
      }
      Request.view.getGroupAndView().then((res) => {
        this.views = res.data
        // jsx传props识别不了，所以递归为数据添加属性
        recursionFn(this.views)
      })
      // Request.view.getAll().then((res) => {
      //     this.views = res.data
      // }).catch((error) => this.$message.error('加载view失败'))
    },
    getOne(id) {
      Request.view
        .getOne({ id })
        .then((res) => {
          let cols = Object.keys(JSON.parse(res.data.model))
          this.viewCols = cols
          this.viewTextCols = cols
        })
        .catch(() => this.$message.error("加载view字段失败"))
    },
    // 添加自定义选项
    addOpt() {
      this.isAddOpt = true
      this.optionDialogVisible = true
      this.variable = null
      this.currentOption = {
        text: "",
        value: "",
        variables: {},
      }
      this.columnsValues.push(this.currentOption)
    },
    // 添加选项
    saveOpt() {
      if (this.isAddOpt) {
        // console.log(this.currentOption, this.data. 'this.currentOption')
        this.data.customOptions.push(this.currentOption)
        console.log(this.data.customOptions, "this.data.customOptions")
      }
      this.optionDialogVisible = false
    },
    // 编辑选项
    editOpt(index, row) {
      console.log(index, row.variables, this.globalVariable, "row")
      this.isAddOpt = false
      this.optionDialogVisible = true
      if (this.model === "global") {
        this.globalVariable = row.variables
      }
      console.log(
        this.data.customOptions[index],
        "this.data.customOptions[index]"
      )
      console.log(
        this.data.relatedViews,
        "this.data.relatedViews[item].variable"
      )
      this.currentOption = this.data.customOptions[index]
      this.variable = this.currentOption.variables
    },
    // 删除选项
    delOpt(index) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.data.defaultValue = ""
          this.columnsValues.splice(index, 1)
          this.data.customOptions.splice(index, 1)
        })
        .catch((err) => console.log(err))
    },
  },
  render() {
    // 日期动态值 今、前、后
    const changeDynamicValueType = (type, flag) => {
      if (type === "current") {
        if (flag === 2) this.data.defaultValue.value = 0
        else this.data.defaultValue[flag].value = 0
      }
    }
    const getColumnsValues = () => {
      this.columnsValues = []
      let param = { cache: false, expired: 0 }
      if (this.data.optionType === "auto") {
        if (Object.keys(this.data.relatedViews).length === 0)
          return this.$message.error("请先选择关联图标和关联数据视图")
        Object.keys(this.data.relatedViews).forEach((item) => {
          param["columns"] = this.data.relatedViews[item]["fields"]
          param["viewId"] = item
          if (!param["columns"][0] && this.model === "global") {
            return this.$notify({
              title: "警告",
              message: `请先选择 ${this.data.relatedViews[item].name} 关联字段或变量！`,
              type: "warning",
            })
          }
          Request.view
            .getdistinctvalue(param)
            .then((res) => {
              this.columnsValues = this.columnsValues.concat(res.data)
              this.$message.success("加载成功")
            })
            .catch(() => Vue.$message.error("加载失败"))
        })
      } else if (this.data.optionType === "manual") {
        if (!this.data.valueField || !this.data.valueViewId)
          return this.$emit("manualVaildateField")
        param["columns"] = [ this.data.valueField ]
        this.data.textField && param["columns"].push(this.data.textField)
        param["viewId"] = this.data.valueViewId
        Request.view
          .getdistinctvalue(param)
          .then((res) => {
            this.columnsValues = this.columnsValues.concat(res.data)
            this.$message.success("加载成功")
          })
          .catch(() => Vue.$message.error("加载失败"))
      }
    }
    const changeOptionType = (val) => {
      this.data.defaultValue = ""
      this.columnsValues = []
      this.data.valueViewId = ""
      this.data.valueField = ""
      this.data.textField = ""
      // 自定义
      if (val === "custom") return (this.data.customOptions = [])
      // else delete this.data.customOptions
      // 手动
      if (val === "manual") this.getAll()
    }
    let btnLoad = (
      <el-button
        onClick={() => getColumnsValues()}
        type="text"
        style="margin-left: 10px"
      >
        点击加载
      </el-button>
    )
    let elCommon = (
      <el-form-item label="对应关系：" prop="operator">
        <el-select
          value={this.data.operator}
          onChange={(val) => (this.data.operator = val)}
        >
          {this.operators.map((item) => (
            <el-option label={item.label} key={item.value} value={item.value} />
          ))}
        </el-select>
      </el-form-item>
    )
    let elItemDataView = (
      <el-form-item label="数据视图：" prop="valueViewId">
        <el-cascader
          ref="cascader"
          value={this.data.valueViewId}
          options={this.views}
          onChange={(val) => {
            console.log(this.$refs.cascader)
            this.data.valueViewId = val[val.length - 1]
            this.data.valueField = ""
            this.data.textField = ""
            this.data.defaultValue = ""
            if (this.data.valueViewId) this.getOne(this.data.valueViewId)
          }}
          show-all-levels={false}
          filterable
        />
        {/* <el-select
          value={this.data.valueViewId}
          
        >
          {this.views.map((item) => (
            <el-option key={item.id} label={item.name} value={item.id} />
          ))}
        </el-select> */}
      </el-form-item>
    )
    let elItemField = (
      <el-form-item label="取值字段：" prop="valueField">
        <el-select
          value={this.data.valueField}
          onInput={(val) => {
            this.data.valueField = val
            this.columnsValues = []
            this.data.defaultValue = ""
          }}
        >
          {this.viewCols.map((item, index) => (
            <el-option key={index} label={item} value={item}></el-option>
          ))}
        </el-select>
      </el-form-item>
    )
    let elItemParentIdField = (
      <el-form-item label="父ID字段：" prop="parentField">
        <el-select
          value={this.data.parentField}
          onInput={(val) => (this.data.parentField = val)}
        >
          {this.viewCols.map((item, index) => (
            <el-option key={index} label={item} value={item}></el-option>
          ))}
        </el-select>
      </el-form-item>
    )
    let elItemTextField = (
      <el-form-item label="文本字段：" prop="textField">
        <el-select
          value={this.data.textField}
          onInput={(val) => {
            this.data.textField = val
            this.columnsValues = []
            this.data.defaultValue = ""
          }}
        >
          {this.viewTextCols.map((item, index) => (
            <el-option key={index} label={item} value={item} />
          ))}
        </el-select>
        <p style="color: #999;font-size: 12px;">
          如不设置文本字段，下拉菜单项则默认显示取值字段的值
        </p>
      </el-form-item>
    )
    let elDefaultType = (
      <el-form-item label="默认值类型：" prop="defaultValueType">
        <el-radio-group
          value={this.data.defaultValueType}
          onInput={(val) => {
            this.data.defaultValueType = val
            this.changeDefaultType(val)
            console.log(this.data.defaultValue, "this.data.defaultValue")
          }}
        >
          <el-radio-button label="fixed">固定值</el-radio-button>
          <el-radio-button label="dynamic">动态值</el-radio-button>
        </el-radio-group>
      </el-form-item>
    )
    const scopedSlots = {
      // defaul 默认具名插槽
      default: (scope) => {
        return (
          <span>
            <el-button
              style="margin-right: 5px"
              size="mini"
              class="el-icon-edit"
              circle
              onClick={() => this.editOpt(scope.$index, scope.row)}
            />
            <el-button
              onClick={() => this.delOpt(scope.$index)}
              size="mini"
              circle
              type="danger"
              class="el-icon-delete"
            />
          </span>
        )
      },
    }
    const variableScope = {
      // defaul 默认具名插槽
      default: (scope) => {
        return (
          <span>
            {Object.values(scope.row.variables)
              .map((item) => item)
              .join(",")}
          </span>
        )
      },
    }
    let elItemCustom = (
      <div>
        <el-form-item label="选项关联变量：">
          <el-switch
            value={this.data.optionWithVariable}
            onInput={(val) => (this.data.optionWithVariable = val)}
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="text"
            class="el-icon-plus"
            onClick={() => this.addOpt()}
          >
            点击添加
          </el-button>
          <el-table data={this.data.customOptions}>
            <el-table-column label="文本" prop="text" />
            <el-table-column label="值" prop="value" />
            {this.data.optionWithVariable ? (
              <el-table-column
                label="关联变量"
                {...{ scopedSlots: variableScope }}
              ></el-table-column>
            ) : (
              ""
            )}
            <el-table-column
              label="操作"
              {...{ scopedSlots }}
            ></el-table-column>
          </el-table>
        </el-form-item>
        <el-dialog
          title={this.isAddOpt ? "新增" : "编辑"}
          visible={this.optionDialogVisible}
          width="30%"
          onClose={() => (this.optionDialogVisible = false)}
          append-to-body
        >
          <el-form
            model={this.currentOption}
            label-width="80px"
            style="width: 80%; text-align: center"
          >
            <el-form-item label="文本:">
              <el-input
                value={this.currentOption.text}
                onInput={(val) => (this.currentOption.text = val)}
              />
            </el-form-item>
            <el-form-item label="值:">
              <el-input
                value={this.currentOption.value}
                onInput={(val) => (this.currentOption.value = val)}
              />
            </el-form-item>
            {this.data.optionWithVariable ? (
              <div style="margin-bottom: 10px;">关联变量</div>
            ) : (
              ""
            )}
            {this.model === "global" && this.data.optionWithVariable
              ? Object.keys(this.data.relatedViews).map((item) => {
                return (
                  <el-form-item
                    label-width="120px"
                    label={this.data.relatedViews[item].name}
                  >
                    <el-select
                      style="display: block"
                      value={this.globalVariable[item]}
                      value-key="name"
                      onChange={(val) => {
                        this.$set(this.globalVariable, item, val)
                        this.$set(
                          this.data.relatedViews[item],
                          "variable",
                          val
                        )
                        this.$set(this.currentOption.variables, item, val)
                        console.log(this.currentOption.variables, item, "--")
                      }}
                    >
                      {this.data.relatedViews[item].variables.map((el) => (
                        <el-option label={el} key={el} value={el} />
                      ))}
                    </el-select>
                  </el-form-item>
                )
              })
              : ""}
            {this.model !== "global" && this.data.optionWithVariable ? (
              <el-form-item label="关联变量:">
                <el-select
                  style="display: block"
                  value={this.variable}
                  value-key="name"
                  onChange={(val) => {
                    this.variable = val
                    this.currentOption.variables = {
                      [this.id]: val,
                    }
                  }}
                >
                  {this.variableList.map((item) => (
                    <el-option
                      label={item.name}
                      key={item.name}
                      value={item.name}
                    />
                  ))}
                </el-select>
              </el-form-item>
            ) : (
              ""
            )}
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button onClick={() => (this.optionDialogVisible = false)}>
              取 消
            </el-button>
            <el-button type="primary" onClick={() => this.saveOpt()}>
              确 定
            </el-button>
          </span>
        </el-dialog>
      </div>
    )
    let elItemModel = (
      <span>
        <el-form-item label="选项适配：">
          <el-radio-group
            value={this.data.optionType}
            onInput={(val) => (this.data.optionType = val)}
            onChange={(val) => {
              changeOptionType(val)
            }}
          >
            <el-radio label="auto">自动关联</el-radio>
            <el-radio label="manual">手动</el-radio>
            <el-radio label="custom">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        {this.data.optionType === "manual" ? elItemDataView : ""}
        {this.data.optionType === "manual" ? elItemField : ""}
        {this.data.optionType === "manual" ? elItemTextField : ""}
        {this.data.optionType === "custom" ? elItemCustom : ""}
      </span>
    )
    let elInputTextDefault = (
      <el-form-item label="默认值：">
        <el-input
          clearable
          style="width: 200px"
          value={this.data.defaultValue}
          onInput={(val) => (this.data.defaultValue = val)}
        />
      </el-form-item>
    )

    // select默认值
    let elSelectDefault = (
      <el-form-item label="默认值：">
        <el-select
          clearable
          multiple={!!this.data.multiple}
          value={this.data.defaultValue}
          onInput={(val) => (this.data.defaultValue = val)}
        >
          {this.data.optionType === "custom"
            ? this.data.customOptions.map((item, index) => {
              let label = item.text
              let value = item.value
              return <el-option key={index} label={label} value={value} />
            })
            : this.columnsValues.map((item, index) => {
              let label = ""
              let value = ""
              if (this.data.optionType === "auto") {
                let relatedViewList = Object.values(this.data.relatedViews)
                value = label =
                    item[
                      relatedViewList[relatedViewList.length - 1]["fields"][0]
                    ]
              } else if (this.data.optionType === "manual") {
                value = item[this.data.valueField]
                label = item[this.data.textField]
              } else if (this.data.optionType === "custom") {
                label = item.text
                value = item.value
              }
              return <el-option key={index} label={label} value={value} />
            })}
        </el-select>
        {"auto,manual".includes(this.data.optionType) && btnLoad}
      </el-form-item>
    )
    // radio默认值
    let elRadioDefault = (
      <el-form-item label="默认值：">
        {this.data.optionType === "custom" ? (
          <el-radio-group
            value={this.data.defaultValue}
            onInput={(val) => (this.data.defaultValue = val)}
          >
            {this.data.customOptions.map((item, index) => {
              let label = item.text
              let value = item.value
              return this.data.radioType === "normal" ? (
                <el-radio key={index} label={value}>
                  {label}
                </el-radio>
              ) : (
                <el-radio-button key={index} label={value}>
                  {label}
                </el-radio-button>
              )
            })}
          </el-radio-group>
        ) : this.columnsValues ? (
          <el-radio-group
            value={this.data.defaultValue}
            onInput={(val) => (this.data.defaultValue = val)}
          >
            {this.columnsValues.map((item, index) => {
              let label = ""
              let value = ""
              if (this.data.optionType === "auto") {
                value = label =
                  item[Object.values(this.data.relatedViews)[0]["fields"][0]]
              } else if (this.data.optionType === "manual") {
                value = item[this.data.valueField]
                label = item[this.data.textField]
              }
              return this.data.radioType === "normal" ? (
                <el-radio key={index} label={value}>
                  {label}
                </el-radio>
              ) : (
                <el-radio-button key={index} label={value}>
                  {label}
                </el-radio-button>
              )
            })}
          </el-radio-group>
        ) : (
          ""
        )}
        {"auto,manual".includes(this.data.optionType) && btnLoad}
      </el-form-item>
    )
    let elDateDefault = this.data.type === "date" && (
      <el-form-item label="默认值">
        {this.data.defaultValueType === "fixed" ? (
          <el-date-picker
            value={this.data.defaultValue}
            onInput={(val) => {
              this.data.defaultValue = val
              console.log(this.data, "111")
            }}
            type={this.data.multiple ? "dates" : "date"}
            value-format={this.data.dateFormat}
            format={this.data.dateFormat}
            clearable
            placeholder="选择日期"
          ></el-date-picker>
        ) : (
          <span>
            <el-select
              style="width: 70px"
              onChange={($event) => changeDynamicValueType($event, 2)}
              value={this.data.defaultValue.valueType}
              onInput={(val) => (this.data.defaultValue.valueType = val)}
            >
              <el-option label="今" value="current" />
              <el-option label="前" value="prev" />
              <el-option label="后" value="next" />
            </el-select>
            {this.data.defaultValue.valueType !== "current" && (
              <el-input-number
                value={this.data.defaultValue.value}
                onInput={(val) => (this.data.defaultValue.value = val)}
                min={1}
                max={365}
                controls-position="right"
              />
            )}
            <el-select
              style="width: 100px"
              value={this.data.defaultValue.type}
              onInput={(val) => (this.data.defaultValue.type = val)}
            >
              <el-option label="天" value="day" />
              <el-option label="周" value="isoWeek" />
              <el-option label="月" value="month" />
              <el-option label="季度" value="quarter" />
              <el-option label="年" value="year" />
            </el-select>
          </span>
        )}
      </el-form-item>
    )
    let elDateRangeDefault = this.data.type === "dateRange" && (
      <span>
        {this.data.defaultValueType === "fixed" ? (
          <el-form-item label="默认值：">
            <el-date-picker
              value={this.data.defaultValue}
              onInput={(val) => {
                this.data.defaultValue = val
                if (!val) this.data.defaultValue = []
                console.log(this.data, "this.data.defaultValue = val")
              }}
              type="daterange"
              value-format={this.data.dateFormat}
              format={this.data.dateFormat}
              range-separator="至"
              start-placeholder="从"
              end-placeholder="到"
            ></el-date-picker>
          </el-form-item>
        ) : (
          <span>
            <el-form-item label="默认值-起始：">
              <el-select
                style="width: 70px"
                onChange={($event) => changeDynamicValueType($event, 0)}
                value={this.data.defaultValue[0].valueType}
                onInput={(val) => (this.data.defaultValue[0].valueType = val)}
              >
                <el-option label="今" value="current" />
                <el-option label="前" value="prev" />
                <el-option label="后" value="next" />
              </el-select>
              {this.data.defaultValue[0].valueType !== "current" && (
                <el-input-number
                  controls-position="right"
                  value={this.data.defaultValue[0].value}
                  onInput={(val) => (this.data.defaultValue[0].value = val)}
                  min={1}
                  max={365}
                />
              )}
              <el-select
                style="width: 100px"
                value={this.data.defaultValue[0].type}
                onInput={(val) => (this.data.defaultValue[0].type = val)}
              >
                <el-option label="天" value="day" />
                <el-option label="周" value="week" />
                <el-option label="月" value="month" />
                <el-option label="季度" value="quarter" />
                <el-option label="年" value="year" />
              </el-select>
            </el-form-item>
            <el-form-item label="默认值-结束：">
              <el-select
                style="width: 70px"
                onChange={($event) => changeDynamicValueType($event, 2)}
                value={this.data.defaultValue[1].valueType}
                onInput={(val) => (this.data.defaultValue[1].valueType = val)}
              >
                <el-option label="今" value="current" />
                <el-option label="前" value="prev" />
                <el-option label="后" value="next" />
              </el-select>
              {this.data.defaultValue[1].valueType !== "current" && (
                <el-input-number
                  controls-position="right"
                  value={this.data.defaultValue[1].value}
                  onInput={(val) => (this.data.defaultValue[1].value = val)}
                  min={1}
                  max={365}
                />
              )}
              <el-select
                style="width: 100px"
                value={this.data.defaultValue[1].type}
                onInput={(val) => (this.data.defaultValue[1].type = val)}
              >
                <el-option label="天" value="day" />
                <el-option label="周" value="week" />
                <el-option label="月" value="month" />
                <el-option label="季度" value="quarter" />
                <el-option label="年" value="year" />
              </el-select>
            </el-form-item>
          </span>
        )}
      </span>
    )
    let elNumberRangeDefault = this.data.type === "numberRange" && (
      <el-form-item label="默认值：" key="number-range">
        从
        <el-input-number
          value={this.data.defaultValue[0]}
          step={1}
          key="number-range-0"
          onInput={(val) => {
            this.data.defaultValue[0] = val
            this.$forceUpdate()
          }}
        />
        到
        <el-input-number
          key="number-range-1"
          value={this.data.defaultValue[1]}
          step={1}
          onInput={(val) => {
            this.data.defaultValue[1] = val
            this.$forceUpdate()
          }}
        />
      </el-form-item>
    )
    let elSliderDefault = (
      <el-form-item label="默认值：">
        <el-slider
          value={this.data.defaultValue}
          onInput={(val) => (this.data.defaultValue = val)}
          step={this.data.step || 1}
          min={this.data.min}
          max={this.data.max}
        />
      </el-form-item>
    )

    let select = (
      <span>
        {elCommon}
        {elItemModel}
        {elSelectDefault}
      </span>
    )
    let inputText = (
      <span>
        {elCommon}
        {elInputTextDefault}
      </span>
    )
    let radio = (
      <span>
        {elCommon}
        {elItemModel}
        {elRadioDefault}
      </span>
    )
    let date = (
      <span>
        {elCommon}
        {elDefaultType}
        {elDateDefault}
      </span>
    )
    let dateRange = (
      <span>
        {elCommon}
        {elDefaultType}
        {elDateRangeDefault}
      </span>
    )
    let numberRange = <span>{elNumberRangeDefault}</span>
    let slider = <span>{elSliderDefault}</span>
    let treeSelect = (
      <span>
        {elCommon}
        {elItemDataView}
        {elItemField}
        {elItemParentIdField}
        {elItemTextField}
      </span>
    )
    const res = {
      select,
      inputText,
      radio,
      date,
      dateRange,
      numberRange,
      slider,
      treeSelect,
    }
    console.log(this.type, "this.type")
    return res[this.type]
  },
}
