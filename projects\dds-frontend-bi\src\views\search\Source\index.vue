<template>
  <div>
    <template v-if="sourceList.length">
      <p class="result">{{ sourceList.length }} 条相关结果</p>
      <div class="list">
        <div
          class="list-item-custom"
          v-for="(item, i) in sourceList.slice(
            (currentPage - 1) * pagesize,
            currentPage * pagesize
          )"
          :key="i"
          @click="handleDetail(item)"
        >
          <div
            class="title"
            v-html="brightenKeyword(item.zbmc, searchContent)"
          ></div>
          <!-- 数据源表 -->
          <div class="type">
            <span class="field">{{ item.name }}</span>
            所属指标域：{{ item.sysjy }}
          </div>
          <div class="desc">
            {{ item.info }}
          </div>
        </div>
      </div>
      <div class="page">
        <el-pagination
          v-if="sourceList.length > 10"
          small
          layout="prev, pager, next"
          :current-page="currentPage"
          :total="sourceList.length"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </template>
    <Empty v-else />
  </div>
</template>

<script>
import { brightenKeyword } from "@/utils"
import Empty from "../Empty"
export default {
  components: { Empty },
  props: {
    sourceList: {
      type: Array,
      default: () => []
    },
    searchContent: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      listData: [],
      currentPage: 1,
      pagesize: 10
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    brightenKeyword,
    // 查看详情
    handleDetail({ lxbm, id,ind_code, zbmc }) {
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path: "/ddsBi/atomDetail",
        query: {
          id,
          name: zbmc,
          indCode:ind_code,
          lxbm
        }
      })
      window.open(routerUrl.href, "_blank")
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped lang="scss">
.result {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
  margin-bottom: 16px;
}
.list {
  .list-item-custom {
    margin-bottom: 24px;
    .title {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 28px;
      color: #323233;
      cursor: pointer;
    }
    .type {
      font-size: 14px;

      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
      margin-top: 10px;
      .field {
        margin-right: 10px;
      }
    }
    .desc {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #86909c;
      line-height: 22px;
      margin-top: 8px;
    }
  }
}
.page {
  display: flex;
}
</style>
