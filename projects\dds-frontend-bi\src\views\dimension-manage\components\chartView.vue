<template>
  <div class="dimension-relation">
    <el-form :inline="true" :model="formInline" class="form-inline">
      <el-form-item label="维度定位">
        <el-input v-model="formInline.user" placeholder="审批人"></el-input>
      </el-form-item>
      <el-form-item label="维度类型">
        <el-select v-model="formInline.region" placeholder="活动区域">
          <el-option label="区域一" value="shanghai"></el-option>
          <el-option label="区域二" value="beijing"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度版本">
        <el-select v-model="formInline.region" placeholder="活动区域">
          <el-option label="区域一" value="shanghai"></el-option>
          <el-option label="区域二" value="beijing"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="$emit('goBack')">返回表视图</el-button>
      </el-form-item>
    </el-form>
    <div class="chart-content">
      <ChartsGraph :chart-data="chartData" />
    </div>
    <div class="dimension-detail"></div>
  </div>
</template>

<script>
import ChartsGraph from "./ChartsGraph.vue"
export default {
  components: { ChartsGraph },
  props: {},
  data() {
    return {
      // 维度
      formInline: {
        user: "",
        region: ""
      },
      chartData: {
        nodes: [],
        links: []
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    setTimeout(() => {
      this.getChartData()
    }, 1000)
  },
  watch: {},
  methods: {
    getChartData() {
      const chartData = {
        nodes: [
          {
            id: 1398,
            code: "1",
            category: 1,
            dataName: "地区"
          },
          {
            id: 1400,
            code: "2",
            category: 1,
            dataName: "省份"
          },
          {
            id: 1400,
            code: "3",
            category: 1,
            dataName: "城市"
          },
          {
            id: 1400,
            code: "4",
            category: 1,
            dataName: "区"
          },
          {
            id: 1400,
            code: "5",
            category: 1,
            dataName: "销售区"
          },
          {
            id: null,
            code: "6",
            category: 2,
            dataName: "年龄",
            x: 0
          },
          {
            id: null,
            code: "7",
            category: 2,
            dataName: "专业",
            x: 0
          },
          {
            id: null,
            code: "8",
            category: 2,
            dataName: "学号",
            x: 0
          },
          {
            id: null,
            code: "9",
            category: 2,
            dataName: "性别",
            x: 0
          },
          {
            id: null,
            code: "10",
            category: 2,
            dataName: "学号",
            x: 0
          }
        ],
        links: [
          {
            source: "1",
            target: "2",
            name: "",
            type: 1
          },
          {
            source: "2",
            target: "3",
            name: "行政区划",
            type: 1
          },
          {
            source: "3",
            target: "4",
            name: "",
            type: 1
          },
          {
            source: "3",
            target: "5",
            name: "",
            type: 1
          }
        ]
      }
      this.chartData.links = chartData.links.map(item => {
        return {
          source: item.source,
          target: item.target,
          name: item.name
        }
      })
      this.chartData.nodes = chartData.nodes.map(item => {
        return {
          name: item.code,
          category: item.category,
          dataName: item.dataName,
          tempId: item.id,
          x: item.x
        }
      })
      // 调用接口获取图表数据
    }
  }
}
</script>

<style scoped lang="scss">
.dimension-relation {
  position: relative;
  width: 100%;
  .chart-content {
    width: 100%;
    height: calc(100vh - 188px);
  }
  .form-inline {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    height: 51px;
    background-color: #fff;
  }
  .dimension-detail {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: calc(100vh - 190px);
    padding: 20px;
    overflow-y: auto;
    background: #fff;
    border-radius: 6px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    //滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }
  }
}
</style>
