<template>
  <div class="visualize-card">
    <div class="vue-draggable-handle">
      <div
        class="name"
        :style="{
          opacity: chartData.name == 'scorecard' ? '0' : '1'
        }"
      >
        <span>{{ item.alias != "" ? item.alias : item.name }}</span>
        <el-tooltip v-if="showWarning" :content="warningMsg" placement="top">
          <i class="el-icon-warning" style="color: red"></i>
        </el-tooltip>
      </div>
      <div class="tool">
        <!-- <el-tooltip
          v-if="chartData.controls.length"
          content="控制器"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i
            class="el-icon-s-operation"
            @click="isShowControl = !isShowControl"
          />
        </el-tooltip> -->
        <el-tooltip
          content="同步数据"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-refresh-right" @click="reload"></i>
        </el-tooltip>
        <!-- <el-tooltip
          content="下载"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-download" @click="handleDownload" />
        </el-tooltip>

        <el-tooltip
          content="编辑"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-edit" @click="handleEdit(item.id)" />
        </el-tooltip>

        <el-tooltip
          :content="item.description"
          class="item"
          effect="dark"
          placement="top-end"
        >
          <i class="el-icon-info" style="color: #409eff; cursor: pointer" />
        </el-tooltip>
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="editInfo">基本信息</el-dropdown-item>
            <el-dropdown-item v-if="item.isSimple" command="setWarning"
              >设置预警</el-dropdown-item
            >
            <el-dropdown-item
              v-if="chartData.name != 'scorecard'"
              command="linkageJump"
              >联动跳转</el-dropdown-item
            >
            <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定删除这个图表吗？"
              @onConfirm="handleDel(item.relationsId)"
            >
              <el-dropdown-item slot="reference">删除</el-dropdown-item>
            </el-popconfirm>
          </el-dropdown-menu>
        </el-dropdown> -->
      </div>
    </div>
    <div
      class="no-drag"
      v-if="item.config"
      :style="{
        padding: chartData.name == 'scorecard' ? 0 : '35px 20px 0px'
      }"
    >
      <ControlPanelComponent
        v-if="isShowControl"
        :controls.sync="chartData.controls"
        :select-options="selectOptions"
        @search="getData"
        type="local"
      />
      <Widgets
        ref="WidgetsRefs"
        v-if="chartData.name"
        :widget-props="chartData"
        @onPageChange="onPageChange"
        :loading="loading"
        @handleChartClick="handleChartClick"
      />
    </div>
  </div>
</template>

<script>
import { widgetParamsFormat } from "@/views/widgets/component/util"
import ControlPanelComponent from "@/components/Control/Control"
import Widgets from "@/views/widgets/component/Widget"
import Request from "@/service"
import dayjs from "dayjs"
import { mapGetters } from "vuex"
import axios from "axios"
let quarterOfYear = require("dayjs/plugin/quarterOfYear") // import plugin
dayjs.extend(quarterOfYear)
export default {
  components: {
    Widgets,
    ControlPanelComponent
  },
  props: {
    token: {
      type: String
    },
    index: {
      type: Number
    },
    onlyView: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {}
    },
    isAuthorized: {
      type: Boolean,
      default: false
    },
    dashboardId: {
      type: Number
    },
    globalControls: {
      type: Array
    },
    originalFilters: {
      type: Array,
      default: () => []
    },
    queryMode: {
      type: Number,
      default: 0
    },
    FormValuesRelatedItemsAll: {
      type: Array,
      default: () => []
    },
    FormValuesRelatedItems: {
      type: Array
    }
  },
  data() {
    return {
      warningMsg: "",
      showWarning: false,
      drillCon: null, // 下钻组件名称
      warningVisible: false,
      chartParams: "",
      Drillconfig: {},
      drillVisible: false,
      loading: false,
      pollingTimer: null,
      controlConfigVisible: false,
      isShowControl: false,
      isRouterAlive: true,
      updatedPagination: {
        pageNo: 0,
        pageSize: 0
      },
      selectOptions: {},
      page: {
        pageNo: 0,
        pageSize: 0
      },
      chartData: {
        name: null,
        controls: []
      }
    }
  },
  computed: {
    ...mapGetters({
      currentTheme: "currentTheme"
    })
  },
  created() {},
  mounted() {},
  watch: {
    item: {
      immediate: true,
      handler(val) {
        this.chartData = val.config
          ? JSON.parse(val.config)
          : {
              name: null,
              controls: []
            }
        if (this.chartData.name) {
          this.getData()
        }
      }
    },
    // FormValuesRelatedItemsAll: {
    //   deep: true,
    //   handler(val) {
    //     const isReload = val.some((id) => id == this.item.relationsId);
    //     if (isReload) {
    //       console.log(val, "FormValuesRelatedItemsAll");
    //       this.getData();
    //     }
    //   },
    // },

    FormValuesRelatedItems: {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val) return
        const isReload = val.some(id => id === this.item.relationsId)
        if (isReload && !this.queryMode) {
          this.getData()
        }
      }
    }
  },
  methods: {
    handleChartClick(params) {
      console.log(params, "params")
      const drillConfig = JSON.parse(this.item.drillConfig)
      // 针对表格单元格点击处理
      if (this.chartData.selectedChartId === 1) {
        const fieldArr = drillConfig.relation.filter(
          item => item.zd === params.field
        )
        if (!fieldArr.length) return
      }
      // 外链打开
      if (drillConfig.drillType === 3) {
        let url = drillConfig.url + "?"
        if (this.chartData.selectedChartId !== 1) {
          if (drillConfig.xAxis !== "") {
            url += drillConfig.xAxis + "=" + params.name
          }
          if (drillConfig.series !== "") {
            url += drillConfig.series + "=" + params.seriesName
          }
        } else {
          drillConfig.relation.forEach(({ zd, kzq }) => {
            url += kzq + "=" + params[zd]
          })
        }
        window.open(url, "_blank")
        return
      }
      // 当前页窗口打开
      if (drillConfig.revealType === 1) {
        if (!drillConfig.target || !drillConfig.isOpen) return

        if (drillConfig.drillType === 1) {
          this.drillCon = "DrillWidget"
        }
        if (drillConfig.drillType === 2) {
          this.drillCon = "DrillPortal"
        }
        this.chartParams = params
        this.drillVisible = true
        this.Drillconfig = drillConfig
      }
      // 新窗口打开
      if (drillConfig.revealType === 2) {
        let obj = {}
        if (this.chartData.selectedChartId !== 1) {
          obj = {
            selectedChartId: this.chartData.selectedChartId,
            Drillconfig: drillConfig,
            chartParams: { name: params.name, seriesName: params.seriesName }
          }
        } else {
          obj = {
            selectedChartId: this.chartData.selectedChartId,
            Drillconfig: drillConfig,
            chartParams: params
          }
        }
        const strConfig = JSON.stringify(obj)
        let routeUrl = null
        // 图表下钻
        if (drillConfig.drillType === 1) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillWidget?isFullPage=true&config=${strConfig}`
          })
        }
        // 看板下钻
        if (drillConfig.drillType === 2) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillPortal?isFullPage=true&config=${strConfig}`
          })
        }
        window.open(routeUrl.href, "_blank")
      }
    },
    reload() {
      this.getData()
    },
    // 下载
    handleDownload() {
      const name = this.item.alias || this.item.name
      this.$refs.WidgetsRefs.download(name, this.item.viewId)
    },
    async handleCommand(command) {
      if (!command) return
      if (command === "editInfo") {
        this.$emit("handleEditInfo", this.item)
      } else if (command === "linkageJump") {
        const { data } = await this.$httpBi.widget.getAll()
        this.$store.commit("widget/SET_WIDGETS", data)
        this.$emit("handleDrillDown", this.item)
      } else {
        this.warningVisible = true
        this.$refs.WarningDialog.init()
      }
    },
    async getOptions(name, item) {
      let param = {}
      // 手动
      if (item.optionType === "manual") {
        param = {
          cache: false,
          expired: 0,
          columns: [item.valueField, item.textField],
          viewId: this.item.viewId
        }
        // 自动关联
      } else if (item.optionType === "auto") {
        param = {
          cache: false,
          expired: 0,
          columns: [name],
          viewId: this.item.viewId
        }
      }
      // 自定义
      if (item.optionType === "custom") {
        this.selectOptions[item.key] = item.customOptions
      } else {
        const { data } = await Request.view.getdistinctvalue(param)
        this.selectOptions[item.key] = data
      }
    },
    handleEdit(id) {
      this.$router.push(`Workbench?isFullPage=true&id=${id}`)
    },
    async handleDel(id) {
      const { code } = await Request.dashboard.delMemDashboardWidget({
        relationId: id
      })
      if (code === 200) {
        this.$message.success("删除成功")
        this.$emit("onReload")
      }
    },
    onPageChange(page) {
      this.updatedPagination.pageNo = page.currentPage
      this.updatedPagination.pageSize = page.pageSize
      this.getData()
      // this.page = page;
    },
    getData() {
      const filters = [] // 字段
      const params = [] // 变量
      const [lf, lp] = this.getControlsPrams(this.chartData.controls, "local")
      // 过滤出关联自己的全局控制器
      const filterGlobalControls =
        this.globalControls.length &&
        this.globalControls.filter(item => {
          return Object.entries(item.relatedItems).some(([k, v]) => {
            return k === this.item.relationsId && v.checked
          })
        })

      const [gf, gp] = this.getControlsPrams(filterGlobalControls, "global")
      filters.push(...lf, ...gf)
      params.push(...lp, ...gp)
      let parameter = {
        ...widgetParamsFormat(
          this.chartData,
          this.item.viewId,
          this.updatedPagination
        ),
        filters,
        params
      }
      this.onLoadData(parameter)
      clearInterval(this.pollingTimer)
      if (this.item.polling) {
        this.pollingTimer = setInterval(() => {
          this.onLoadData(parameter)
        }, Number(this.item.frequency) * 1000)
      }
    },
    async onLoadData(parameter) {
      this.loading = true
      // Request.dashboard.shareData(this.token, parameter);

      axios
        .post("/dds-server-bi/shareClient/data", parameter, {
          headers: {
            token: this.token
          }
        })
        .then(async res => {
          this.$emit(
            "setExceldata",
            res.data.data.resultList,
            this.index,
            this.item.name
          )
          this.chartData.data = res.data.data.resultList
          if (this.chartData.name === "table") {
            this.chartData.pagination = {
              pageNo: res.data.data.pageNo,
              pageSize: res.data.data.pageSize,
              totalCount: res.data.data.totalCount,
              withPaging: this.chartData.chartStyles.table.withPaging
            }
            const innerRes = await axios({
              method: "post",
              url: "/dds-server-bi/shareClient/data",
              headers: {
                token: this.token
              },
              data: {
                ...parameter,
                pageNo: 1,
                pageSize: res.data.data.totalCount
              }
            })
            // const { data } = await this.$httpBi.dashboard.shareData(this.token, {
            //   ...parameter,
            //   pageNo: 1,
            //   pageSize: res.data.totalCount,
            // });
            const data = innerRes.data.data
            this.$emit(
              "setExceldata",
              data.resultList,
              this.index,
              this.item.name
            )
          }
          // 获取表格导出导出数据
          this.loading = false
        })
        .catch(err => {
          this.warningMsg = err.response.data.message
          this.showWarning = true
        })
    },
    getControlsPrams(controls, ControlType) {
      let filters = []
      let params = []

      controls.length &&
        controls.forEach((item, index) => {
          const isVisibility =
            item.visibility === "conditional"
              ? this.isMeet(controls, item.conditions[0])
              : true
          console.log(isVisibility, "isVisibility")
          if (JSON.stringify(item.relatedViews) !== "{}") {
            let name = item.relatedViews[this.item.viewId].fields[0]
            let fieldType = item.relatedViews[this.item.viewId].fieldType
            let model = JSON.parse(this.chartData.model)
            let _defaultValue = isVisibility
              ? item.defaultValue
              : this.originalFilters[index].defaultValue
            console.log(_defaultValue, "_defaultValue")
            if (_defaultValue !== "" && _defaultValue) {
              // 如果是字段
              if (fieldType === "column" && item.optionType !== "custom") {
                let sqlType = model[name].sqlType
                // 如果是动态值
                if (item.defaultValueType === "dynamic") {
                  if (item.type === "dateRange") {
                    filters.push(
                      JSON.stringify({
                        value: this.transformRelativeDateValue(
                          _defaultValue[0]
                        ),
                        name,
                        operator: ">=",
                        sqlType: sqlType,
                        type: "filter"
                      }),
                      JSON.stringify({
                        value: this.transformRelativeDateValue(
                          _defaultValue[1]
                        ),
                        name,
                        operator: "<=",
                        sqlType: sqlType,
                        type: "filter"
                      })
                    )
                  } else {
                    filters.push(
                      JSON.stringify({
                        value: this.transformRelativeDateValue(_defaultValue),
                        name,
                        operator: item.operator ?? "=",
                        sqlType: sqlType,
                        type: "filter"
                      })
                    )
                  }
                } else if (
                  ["numberRange", "dateRange", "slider"].includes(item.type)
                ) {
                  filters.push(
                    JSON.stringify({
                      value: _defaultValue[0],
                      name,
                      operator: ">=",
                      sqlType: sqlType,
                      type: "filter"
                    }),
                    JSON.stringify({
                      value: _defaultValue[1],
                      name,
                      operator: "<=",
                      sqlType: sqlType,
                      type: "filter"
                    })
                  )
                } else {
                  filters.push(
                    JSON.stringify({
                      value: _defaultValue,
                      name,
                      operator: item.operator ?? "=",
                      sqlType: sqlType,
                      type: "filter"
                    })
                  )
                }
              } else {
                // 动态值
                if (item.defaultValueType === "dynamic") {
                  if (item.type === "dateRange") {
                    name.forEach((n, i) => {
                      params.push({
                        value: this.transformRelativeDateValue(
                          _defaultValue[i]
                        ),
                        name: n
                      })
                    })
                  } else {
                    params.push({
                      value: this.transformRelativeDateValue(_defaultValue),
                      name
                    })
                  }
                  // 固定值
                } else if (
                  ["numberRange", "dateRange", "slider"].includes(item.type)
                ) {
                  if (name instanceof Array) {
                    name.forEach((n, i) => {
                      params.push({
                        value: _defaultValue[i],
                        name: n
                      })
                    })
                  } else {
                    params.push({
                      value: _defaultValue[0],
                      name
                    })
                  }
                } else if (
                  ["radio", "select"].includes(item.type) &&
                  item.optionType === "custom"
                ) {
                  const optionsOBJ = item.customOptions.find(
                    e => e.value === _defaultValue
                  )
                  const n = optionsOBJ.variables[this.item.viewId]
                  if (n) {
                    params.push({
                      value: _defaultValue,
                      name: n
                    })
                  }
                } else {
                  params.push({
                    value: _defaultValue,
                    name
                  })
                }
              }
            }
            // 如果下拉选择控制器就请求 options   如果已经存在不需要二次请求
            if (
              ["select", "radio"].includes(item.type) &&
              !this.selectOptions[item.key] &&
              ControlType === "local"
            ) {
              this.getOptions(name, item)
            }
          }
        })
      return [filters, params]
    },
    transformRelativeDateValue(val) {
      const { type, value, valueType } = val
      return valueType === "prev"
        ? dayjs().subtract(value, `${type}`).startOf(type).format("YYYY-MM-DD")
        : dayjs().add(value, `${type}`).startOf(type).format("YYYY-MM-DD")
    },
    isMeet(controls, { control, value, operator }) {
      const itemControl = controls.find(item => item.key === control)
      if (operator === "=") {
        return itemControl.defaultValue === value
      } else if (operator === "!=") {
        return itemControl.defaultValue !== value
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.pollingTimer)
    this.pollingTimer = null
  }
}
</script>

<style scoped lang="scss">
.visualize-card {
  position: relative;
  border-radius: 5px;
  height: 100%;
  background-color: var(--theme-color);
  color: var(--theme-text-color);
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;

  .vue-draggable-handle {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    padding: 0 20px;
    width: 100%;
    z-index: 99;
    .name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .tool {
      opacity: 0;
    }

    i {
      margin-right: 10px;
      color: #409eff;
      cursor: pointer;
    }
  }
  .no-drag {
    position: absolute;
    top: 0;
    left: 0;
    touch-action: none;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .vue-draggable-handle:hover .tool {
    opacity: 1 !important;
  }
}
</style>
