<template>
  <div class="date-box" :style="styleColor">
    {{ date }}
  </div>
</template>

<script>
import dayjs from "dayjs"
export default {
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      date: "",
    }
  },
  computed: {
    styleColor() {
      const { color, fontSize ,letterSpacing,background,fontWeight,textAlign } = this.layer.params
      return {
        color,
        fontSize: fontSize + "px",
        letterSpacing: letterSpacing + "em",
        background,
        fontWeight,
        textAlign,
      }
    },
  },
  created() {
    const timer = setInterval(() => {
      this.formatDate()
    }, 1000)
    this.$once("hook:beforeDestroy", () => {
      clearInterval(timer)
    })
  },
  mounted() {},
  watch: {},
  methods: {
    formatDate() {
      switch (this.layer.params.timeFormat) {
      case "YYYY-MM-DD-week":
        this.date = dayjs().format("YYYY-MM-DD") + this.getWeek()
        break
      case "YYYY-MM-DD-HH:mm-week":
        this.date = dayjs().format("YYYY-MM-DD-HH:mm") + this.getWeek()
        break
      case "YYYY-MM-DD-HH:mm:ss-week":
        this.date = dayjs().format("YYYY-MM-DD-HH:mm:ss") + this.getWeek()
        break
      case "week":
        this.date = this.getWeek()
        break
      default:
        this.date = dayjs().format(this.layer.params.timeFormat)
      }
    },
    getWeek() {
      const week = "日一二三四五六".charAt(dayjs().day())
      return " 星期" + week
    },
  },
}
</script>

<style scoped lang="scss">
.date-box {
  width: 100%;
  height: 100%;
  display: flex;
  font-family: PingFangSC-Regular, PingFang SC;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
