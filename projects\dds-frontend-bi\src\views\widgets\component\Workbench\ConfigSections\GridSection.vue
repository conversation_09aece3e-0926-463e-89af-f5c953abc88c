<template>
  <div class="paneBlock">
    <h4>坐标轴边距</h4>
    <div class="blockBody">
      <el-row class="blockRow" style="margin-top: 10px">
        <el-col span="24">左边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0px 10px"
            v-model="gridFrom.left"
            :format-tooltip="(val) => val + 'px'"
            @change="changeGridStyle"
            :max="400"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="400"
            controls-position="right"
            placeholder=""
            v-model="gridFrom.left"
            @change="changeGridStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row class="blockRow">
        <el-col span="24">右边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0 10px"
            v-model="gridFrom.right"
            :format-tooltip="(val) => val + 'px'"
            @change="changeGridStyle"
            :max="400"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="400"
            controls-position="right"
            placeholder=""
            v-model="gridFrom.right"
            @change="changeGridStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row class="blockRow">
        <el-col span="24">顶边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0 10px"
            v-model="gridFrom.top"
            :format-tooltip="(val) => val + 'px'"
            @change="changeGridStyle"
            :max="400"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="400"
            controls-position="right"
            placeholder=""
            v-model="gridFrom.top"
            @change="changeGridStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row class="blockRow" style="margin-bottom: 60px">
        <el-col span="24">底边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0 10px"
            v-model="gridFrom.bottom"
            @change="changeGridStyle"
            :format-tooltip="(val) => val + 'px'"
            :max="400"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="400"
            controls-position="right"
            placeholder=""
            v-model="gridFrom.bottom"
            @change="changeGridStyle"
          ></el-input-number>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      gridFrom: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      if (this.chartData.chartStyles.grid) {
        this.gridFrom = this._.cloneDeep(this.chartData.chartStyles.grid)
      }
    },
    changeGridStyle() {
      this.$emit("changeStyle", "grid", this.gridFrom)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
