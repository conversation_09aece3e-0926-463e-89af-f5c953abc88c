<template>
  <el-dialog
    title="添加维度类型"
    :visible.sync="dialogVisible"
    width="780px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="维度类型名称" prop="categoryName">
        <el-input v-model="form.categoryName"></el-input>
      </el-form-item>
      <el-form-item label="维度类型描述">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入维度类型描述"
          v-model="form.description"
        ></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleAdd">新增维度类型</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {},
  data() {
    return {
      dialogTitle: "新建图表",
      dialogVisible: false,
      form: {
        categoryName: "",
        description: ""
      },
      rules: {
        categoryName: [
          { required: true, message: "请输入维度类型名称", trigger: "blur" }
        ]
      }
    }
  },
  inject: ["parent"],

  methods: {
    openDialog() {
      this.dialogVisible = true
      this.form = {
        categoryName: "",
        description: ""
      }
    },
    // 创建维度
    handleAdd() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/DimManage/addDimCategory",
            this.form
          )
          this.$message.success(data)
          this.dialogVisible = false
          this.$emit("refresh")
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.step2 {
  .dimension-table {
    .dimension-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .dimension-head-text {
      }
    }
    .dimension-body {
      display: flex;
      height: 300px;
      background-color: rgba(250, 250, 250, 1);
      .el-tree {
        width: 50%;
        background-color: rgba(250, 250, 250, 1);
      }
      .dimension-value-preview {
        width: 50%;
      }
    }
  }
  .drop-zone {
    width: 300px;
    min-height: 200px;
    border: 2px dashed #ccc;
    padding: 10px;
    margin-bottom: 20px;
  }
  .drop-zone.drag-over {
    border-color: #409eff;
    background: #f0f7ff;
  }
}
.dialog-footer {
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent !important;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

.el-tree-node.dragging > .el-tree-node__content {
  opacity: 0.2;
}
.el-tree-node > .el-tree-node__content {
  &.is-current {
    background: #f5f7fa !important;
  }
  &.is-focusable {
    background: #f5f7fa !important;
  }
  &:hover {
    background: #f5f7fa !important;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
    .custom-tree-btns {
      opacity: 1;
    }
  }
}

#project_frame .el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa !important;
  .el-tree-node__label {
    background-color: transparent;
  }
  .custom-tree-btns {
    opacity: 1;
  }
}
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame
  .el-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__label {
  background-color: transparent !important;
}
.custom-tree-node {
  padding: 0 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .custom-tree-btns {
    opacity: 0;
  }
}
</style>
