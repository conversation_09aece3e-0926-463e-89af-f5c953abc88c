
<script>
export default {
  functional: true,
  props: {
    width: {
      type: String,
    },
    type: {
      type: String,
    },
  },
  render(h, context) {
    const { props, children } = context

    return props.width === 0 ? (
      props.type === "local" ? (
        <el-col xs={24} sm={12} md={12} lg={12} xl={12}>
          {children}
        </el-col>
      ) : (
        <el-col xs={24} sm={10} md={8} lg={4} xl={2}>
          {children}
        </el-col>
      )
    ) : (
      <el-col span={props.width}>{children}</el-col>
    )
  },
}
</script>

