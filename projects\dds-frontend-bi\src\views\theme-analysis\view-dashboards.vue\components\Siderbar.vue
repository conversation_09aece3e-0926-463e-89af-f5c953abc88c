<template>
  <el-menu
    class="el-menu-vertical"
    @select="handleSelect"
    :collapse="$store.state.dashboard.isCollapse"
    :default-active="id"
    :collapse-transition="false"
  >
    <el-menu-item :index="path.id" v-for="path in dashboards" :key="path.id">
      <i v-show="path.type == 0" class="el-icon-folder-opened" />
      <DT-Icon
        class="svg_icon"
        v-show="path.type !== 0"
        :icon-class="path.icon"
      />
      <span slot="title">{{ path.name }} </span>
    </el-menu-item>
  </el-menu>
</template>

<script>
export default {
  components: {},
  props: {
    dashboards: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      id: null,
    }
  },
  computed: {},
  created() {
    this.id = this.$route.params.id
  },
  mounted() {},
  watch: {},
  methods: {
    handleSelect(id) {
      const type = this.dashboards.find((item) => item.id === id).type
      if (type !== 0) {
        this.$router.push({
          path: `/ddsBi/viewDashboards/Portalview/${id}${window.location.search}`,
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.el-menu-vertical {
  height: 100%;
  overflow: auto;
}
</style>
