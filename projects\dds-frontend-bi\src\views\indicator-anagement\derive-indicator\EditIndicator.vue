<template>
  <el-dialog
    title="派生指标编辑"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="700px"
    :before-close="handleClose"
  >
    <el-scrollbar>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
        class="form"
        hide-required-asterisk
      >
        <div class="base-info">
          <div class="sub-title">基础信息</div>
          <div class="base-content">
            <div class="base-row">
              <el-form-item label="指标名称" prop="zbmc">
                <el-input
                  v-model="form.zbmc"
                  placeholder="请输入指标名称，不超过20个汉字"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item label="所属指标域" prop="sysjy">
                <avue-input-tree
                  default-expand-all
                  v-model="form.sysjy"
                  :props="{
                    label: 'name',
                    value: 'id'
                  }"
                  placeholder="请选择指标域"
                  :dic="viewGroup"
                ></avue-input-tree>
              </el-form-item>
            </div>
            <div class="base-row">
              <el-form-item label="数据格式" prop="dataFormat">
                <el-select
                  v-model="form.dataFormat"
                  placeholder="请选择数据格式"
                >
                  <el-option
                    v-for="item in sjgs"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="单位"
                prop="jldw"
                v-if="form.dataFormat == 0"
              >
                <div style="display: flex">
                  <el-select
                    v-model="form.jldw"
                    placeholder="请选择单位"
                    :style="{
                      width: form.jldw === '其他' ? '100px' : '240px'
                    }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in dwList"
                      :key="index"
                      :label="item.name"
                      :value="item.bm"
                    ></el-option>
                    <el-option label="无单位" value="无单位"></el-option>
                  </el-select>
                  <el-input
                    v-if="form.jldw === '其他'"
                    v-model="form.diydw"
                    style="width: 134px; margin-left: 6px"
                    placeholder="请输入单位"
                  ></el-input>
                </div>
              </el-form-item>
            </div>
            <div class="base-row">
              <el-form-item label="精度">
                <el-input
                  v-model.number="form.jd"
                  placeholder="仅支持输入整数，数值则代表小数点的位数"
                  style="width: 240px"
                ></el-input>
                <el-checkbox
                  v-model="form.sswr"
                  :true-label="1"
                  :false-label="0"
                  style="margin-left: 20px"
                >
                  四舍五入
                </el-checkbox>
              </el-form-item>
            </div>
            <div class="base-row">
              <el-form-item label="标签" prop="bq">
                <el-select
                  v-model="form.bq"
                  filterable
                  multiple
                  remote
                  allow-create
                  default-first-option
                  @visible-change="handleSelectVisibleChange"
                  @change="changeTag"
                  @remove-tag="removeTag"
                  :remote-method="remoteMethod"
                  placeholder="请创建或者选择标签"
                >
                  <el-option
                    v-for="item in formatLabels"
                    :key="item.bqmc"
                    :label="item.bqmc"
                    :value="item.bqmc"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="归属部门">
                <el-cascader
                  v-model="form.deptAllCode"
                  style="width: 100%"
                  clearable
                  placeholder="请选择归属部门"
                  :props="cascaderProps"
                  @change="handleChange"
                ></el-cascader>
              </el-form-item>
            </div>
            <div class="base-row">
              <el-form-item label="描述">
                <el-input v-model="form.ms" placeholder="请输入描述"></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="model-info">
          <div class="sub-title">模型信息</div>
          <div class="model-content">
            <div class="model-row">
              <el-form-item label="基础指标" prop="baseIndCode">
                <el-select
                  v-model="form.baseIndCode"
                  placeholder="选择基础指标"
                  filterable
                  clearable
                  @change="changeAtom"
                >
                  <el-option
                    v-for="(item, index) in yzzbList"
                    :key="index"
                    :label="item.zbmc"
                    :value="item.indCode"
                    :disabled="downStreamIndList.some(ind => ind.currentCode === item.indCode)||item.indCode===form.indCode"
                  >
                    <span style="float: left">{{ item.zbmc }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ item.zblx }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="派生维度" style="margin-bottom: 0">
                <div
                  class="form-item"
                  v-for="(item, index) in form.xsc"
                  :key="index"
                >
                  <el-row type="flex" gutter="10">
                    <el-col>
                      <!-- :prop="'xsc.' + index + '.adid'"
                  :rules="{
                    required: true,
                    message: '请选择派生维度',
                    trigger: 'blur'
                  }" -->
                      <el-form-item>
                        <div style="display: flex">
                          <el-select
                            v-model="item.adid"
                            clearable
                            placeholder="请选择派生维度"
                            @change="change($event, index)"
                            @clear="clearDerive"
                            style="width: 205px"
                            :disabled="item.isDisabled"
                            filterable
                            class="myselect"
                          >
                            <el-option
                              v-for="ele in currentDerive.pswd"
                              :disabled="
                                form.xsc.some(e => e.adid === ele.adid)
                              "
                              :key="ele.adid"
                              :label="ele.dimAndLevelName || ele.zdmc"
                              :value="ele.adid"
                            ></el-option>
                          </el-select>

                          <!-- 使用维度维度值 -->
                          <LevelMultipleSelect
                            v-model="item.wdzval"
                            v-if="item.levelCode && !item.enableClustering"
                            :is-selected-all="item.isSelectedAll"
                            :is-selected-all-name="item.isSelectedAllName"
                            :disabled="item.isDisabled"
                            style="width: 205px; margin-left: 6px"
                            :level-code="item.levelCode"
                          />
                          <!-- 使用聚类 -->
                          <ClusterMultipleSelect
                            v-if="item.levelCode && item.enableClustering"
                            :dim-values.sync="item.wdzval"
                            v-model="item.clusterCodes"
                            :is-selected-all="item.isSelectedAll"
                            :is-selected-all-name="item.isSelectedAllName"
                            :disabled="item.isDisabled"
                            :level-code="item.levelCode"
                            style="width: 205px; margin-left: 6px"
                          />
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-button
                        type="danger"
                        icon="el-icon-minus"
                        circle
                        v-if="form.xsc.length > 1"
                        @click="removeDerive(item)"
                        :disabled="item.isDisabled"
                      ></el-button>
                      <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="addDerive"
                        circle
                      ></el-button>
                    </el-col>
                  </el-row>
                </div>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="可扩展维度">
                <el-select
                  v-model="form.extDim"
                  placeholder="请选择可扩展维度"
                  value-key="adid"
                  multiple
                >
                  <template v-if="this.extendDimensionList.length">
                    <el-option
                      v-for="(item, index) in this.extendDimensionList"
                      :key="index"
                      :label="item.dimAndLevelName || item.zdmc"
                      :value="item"
                    ></el-option>
                  </template>
                </el-select>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="计算方式" prop="jsfs">
                <div style="display: flex">
                  <el-select
                    v-model="form.jsfs"
                    placeholder="请选择计算方式"
                    :style="{
                      width: form.jsfs === 'sort' ? '76px' : '240px'
                    }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in jsfsList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <template v-if="form.jsfs === 'sort'">
                    <el-select
                      style="margin-left: 10px"
                      v-model="form.sorttype"
                      placeholder="排序方式"
                      :style="{ width: '76px' }"
                      class="myselect"
                    >
                      <el-option
                        v-for="(item, index) in sortTypeList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                    <el-select
                      style="margin-left: 10px"
                      v-model="form.sortrange"
                      v-if="form.jsfs === 'sort'"
                      :style="{ width: '76px' }"
                      class="myselect"
                    >
                      <el-option
                        v-for="(item, index) in sortDefineList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                    <el-input
                      :style="{ width: '76px' }"
                      style="margin-left: 10px"
                      placeholder="数值"
                      v-if="form.sortrange === 'top'"
                      v-model.number="form.sortlimit"
                      clearable
                    ></el-input>
                  </template>
                </div>
              </el-form-item>
              <el-form-item label="计算周期" prop="jszq">
                <el-select v-model="form.jszq" placeholder="请选择计算周期">
                  <el-option
                    v-for="(item, index) in jszqList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="时间范围">
                <el-select v-model="form.sjwd" placeholder="请选择时间范围">
                  <el-option
                    v-for="(item, index) in sjdwList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="0值预警">
                <el-select
                  v-model="form.zeroWarnTime"
                  placeholder="请选择0值预警"
                >
                  <el-option
                    v-for="(item, index) in zeroList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="设置阈值">
                <template #label>
                  <span
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: flex-end;
                    "
                  >
                    设置阈值
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content=""
                      placement="top"
                    >
                      <div slot="content">
                        1.
                        如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                        <br />
                        2.
                        如果不填写最大值，仅填写最小值，则表示大于等于最小值。
                      </div>
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <div style="display: flex; align-items: center">
                  <el-form-item
                    prop="tvmin"
                    style="margin-bottom: 0; width: 107px"
                  >
                    <el-input
                      v-model.number="form.tvmin"
                      placeholder="请输入最小值"
                      style="width: 107px"
                    ></el-input>
                  </el-form-item>
                  <div style="flex: 0 0 26px; text-align: center">-</div>
                  <el-form-item
                    style="margin-bottom: 0; width: 107px"
                    prop="tvmax"
                  >
                    <el-input
                      v-model.number="form.tvmax"
                      placeholder="请输入最大值"
                      style="width: 107px"
                    ></el-input>
                  </el-form-item>

                  <!--  -->

                  <p
                    style="margin-left: 20px; line-height: 24px"
                    v-if="form.initWarn"
                  >
                    如果已设置预警规则，请前往指标预警管理联动修改
                  </p>
                  <el-checkbox
                    v-else
                    v-model="form.isWarnThreshold"
                    :true-label="1"
                    :false-label="0"
                    style="margin-left: 20px"
                  >
                    开启阈值外预警
                  </el-checkbox>
                </div>
              </el-form-item>
            </div>
            <div class="model-row">
              <el-form-item label="数据过滤">
                <el-input
                  v-model="form.dataFilters"
                  placeholder="请输入过滤数值，多个数值间使用&quot;,&quot;号隔断"
                  style="width: 240px"
                ></el-input>
                <el-checkbox
                  v-model="form.nullFilter"
                  :true-label="1"
                  :false-label="0"
                  style="margin-left: 20px"
                >
                  空数值过滤
                </el-checkbox>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm('form')" :loading="loading">
        确 定
      </el-button>
    </span>
    <AffectScopeDialog ref="AffectScopeDialog" />
  </el-dialog>
</template>

<script>
import options from "../mixins/options"
import debounce from "lodash/debounce"
import cloneDeep from "lodash/cloneDeep"
import LevelMultipleSelect from "../components/LevelMultipleSelect.vue"
import ClusterMultipleSelect from "../components/ClusterMultipleSelect.vue"
import AffectScopeDialog from "../components/AffectScopeDialog.vue"

export default {
  components: { LevelMultipleSelect, ClusterMultipleSelect, AffectScopeDialog },
  mixins: [options],
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    yzzbList: {
      type: Array,
      default: () => []
    },
    viewGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          indCode: this.form.indCode,
          zbmc: value
        })
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === "" || this.form.tvmax == null || value === "") {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        console.log(value, "this.form.tvmax")
        console.log(this.form.tvmax, "/////////")

        callback(new Error("最小值不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === "" || this.form.tvmin == null || value === "") {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error("最大值不能小于最小值"))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.form.jldw === "") {
        callback(new Error("请选择单位"))
      } else if (this.form.jldw === "其他" && this.form.diydw === "") {
        callback(new Error("请自定义单位"))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      baseIndType: "ps",
      visible: false,
      rules: {
        zbmc: [
          { required: true, message: "请输入指标名称", trigger: "change" },
          { max: 40, message: "最大为40个字符", trigger: "change" },
          {
            validator: debounce(changeZdmc, 400, { leading: true })
          }
        ],
        tvmin: { validator: changeMin, trigger: "blur" },
        tvmax: { validator: changeMax, trigger: "blur" },
        sysjy: { required: true, message: "请选择数据域", trigger: "change" },
        dataFormat: {
          required: true,
          message: "请选择数据格式",
          trigger: "change"
        },
        baseIndCode: {
          required: true,
          message: "请选择基础指标",
          trigger: "change"
        },
        jsfs: { required: true, message: "请选择计算方式", trigger: "change" },
        sjwd: { required: true, message: "请选择时间维度", trigger: "change" },
        jszq: { required: true, message: "请选择计算周期", trigger: "change" },
        jd: { required: true, message: "请输入精度", trigger: "change" },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: "change"
        }
      },
      labels: [],
      newTag: "", // 新建标签
      tempTag: "", // 临时存储标签
      pswdOptionsMap: {},
      parentIds: "",
      // 可扩展维度
      extendDimensionList: [],
      currentDerive: {
        pswd: []
      },
      // 下游指标
      downStreamIndList: []
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    open() {
      this.getLabelSelectList()
      this.getBaseUnit()
      this.getDeriveLineage()
      this.$nextTick(async () => {
        this.currentDerive = {
          ...this.form,
          pswd: [...this.form.allDim]
        }
        this.form.temp_atomIndCode = this.form.atomIndCode
        let preIndInfo = {
          xsc: []
        }
        let preAllAdid = []
        if (this.form.baseIndType === "ps") {
          const { data } =
            await this.$httpBi.indicatorAnagement.getDeriveIndicatorInfo({
              indCode: this.form.baseIndCode
            })
          preIndInfo = data
          preAllAdid = data.xsc.map(item => item.adid)
        }
        console.log(
          ...this.form.xsc.filter(item => !preAllAdid.includes(item.adid)),
          "preIndInfo.xsc"
        )

        // 上级派生指标详情
        this.$nextTick(() => {
          this.form.extDim = cloneDeep(this.form.extDim)
          this.form.xsc = this.form.xsc.length
            ? [
                ...this.form.xsc.filter(
                  item => !preAllAdid.includes(item.adid)
                ),
                ...preIndInfo.xsc.map(item => ({
                  ...item,
                  isDisabled: true
                }))
              ]
            : [
                {
                  adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
                  atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzd: "", // getAtomIndicatorList接口返回的zddm字段
                  zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
                  wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzval: [] // 用户选择的维度值
                }
              ]
          this.form.extDim = this.form.extDim.filter(
            item => !this.form.xsc.some(e => e.adid === item.adid)
          )

          this.extendDimensionList = this.form.allDim.filter(
            item => !this.form.xsc.some(e => e.adid === item.adid)
          )
          const allDim = cloneDeep(this.extendDimensionList) || []
          const extDim = cloneDeep(this.form.extDim) || []
          const extDimIds = extDim.map(item => item.adid)
          const extDimList = extDimIds
            .map(adid => allDim.find(item => item.adid === adid))
            .filter(Boolean)
          const restList = allDim.filter(item => !extDimIds.includes(item.adid))
          this.extendDimensionList = [...restList, ...extDimList]
          console.log(this.extendDimensionList, "this.extendDimensionList")
          console.log(this.form.xsc, "this.form.xsc")
        })

        // this.changeAtom(this.form.atomIndCode)
        this.visible = true
      })
      
    },
    // 获取指标血缘
    async getDeriveLineage() {
      const { data } = await this.$httpBi.api.paramGet("/indicator/lineage/", {
        indCode: this.form.indCode,
        indType: this.form.lxbm
      })
      this.downStreamIndList = data.nodes.filter(item => ['yz', 'ps','sq','ys'].includes(item.currentType)).filter(item => data.links.some(link => link.targetCode === item.currentCode&&link.currentCode === this.form.indCode))  
    },

    // 选择原子指标获取当前指标派生维度
    async changeAtom(val) {
      if (!val) return
      const lxbm = val.slice(0, 2)
      this.baseIndType = lxbm
      if (lxbm === "yz") {
        const { data } =
          await this.$httpBi.indicatorAnagement.getAtomIndicatorInfo({
            indCode: val
          })
        this.currentDerive = data
        this.form.temp_atomIndCode = val

        this.$nextTick(() => {
          this.form.extDim = cloneDeep(data.pswd)
          this.extendDimensionList = cloneDeep(this.currentDerive.pswd) || []

          this.form.xsc = [
            {
              adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
              atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              wdzd: "", // getAtomIndicatorList接口返回的zddm字段
              zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
              wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
              wdzval: [] // 用户选择的维度值
            }
          ]
        })
      }
      if (lxbm === "ps") {
        const { data } =
          await this.$httpBi.indicatorAnagement.getDeriveIndicatorInfo({
            indCode: val
          })
        this.currentDerive = {
          ...data,
          pswd: [...data.allDim]
        }
        this.form.temp_atomIndCode = data.atomIndCode
        this.$nextTick(() => {
          this.extendDimensionList = cloneDeep(data.allDim) || []

          this.form.extDim = cloneDeep(data.extDim)
          console.log(this.extendDimensionList, " this.extendDimensionList ")
          console.log(this.extendDimensionList, " this.extendDimensionList ")

          this.form.xsc = data.xsc.length
            ? data.xsc.map(item => {
                return {
                  ...item,
                  wdzval: item.wdzval?.length ? item.wdzval : [],
                  isDisabled: true
                }
              })
            : [
                {
                  adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
                  atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzd: "", // getAtomIndicatorList接口返回的zddm字段
                  zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
                  wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
                  wdzval: [] // 用户选择的维度值
                }
              ]

          this.extendDimensionList = this.currentDerive.pswd.filter(
            item => !this.form.xsc.some(e => e.adid === item.adid)
          )
        })
      }
    },

    findParentIds(data, targetId, parentIds = []) {
      parentIds.push(data.id) // 将当前节点的 id 加入父级数组

      if (data.id === targetId) {
        return parentIds
      }

      if (data.children) {
        for (const child of data.children) {
          console.log(child, "child")
          const result = this.findParentIds(child, targetId, [...parentIds])
          if (result) {
            return result
          }
        }
      }

      return null // 如果找不到目标 id
    },

    inputThree({ dic, value }) {
      this.parentIds = this.findParentIds(dic[0], value)
    },
    // 获取原子指标
    async getYzList() {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: ""
      })
      this.yzzbList = data
    },
    // 获取派生维度
    async getPsOption(wdzd, adid) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionValueById({
          wdzd: wdzd,
          tabid: this.currentDerive.tabid
        })
      // this.pswdOptionsMap[id] = data
      this.$set(this.pswdOptionsMap, adid, data)
    },

    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 删除
    clearDerive(val) {
      console.log(val, "val")
      this.extendDimensionList = this.currentDerive.pswd.filter(
        item => !this.form.xsc.some(e => e.adid === item.adid)
      )
      this.form.extDim = this.extendDimensionList
    },
    // 选择派生维度
    // 选择派生维度
    async change(adid, index) {
      if (!adid) return
      console.log(index, "index")
      console.log(this.form.xsc, "this.form.xsc")

      const item = {
        ...this.currentDerive.pswd.find(item => item.adid === adid)
      }

      // const { data } =
      //   await this.$httpBi.indicatorAnagement.getDimensionValueById({
      //     wdzd: item.wdzd,
      //     tabid: this.currentDerive.tabid
      //   })
      // this.pswdOptionsMap[adid] = data

      delete item.adid
      this.$set(this.form.xsc, index, {
        ...item,
        adid: adid,
        wdzval: []
      })
      console.log(this.form.xsc)
      // 过滤出this.currentDerive.pswd中不含在this.form.xsc中的项
      this.extendDimensionList = this.currentDerive.pswd.filter(
        item => !this.form.xsc.some(e => e.adid === item.adid)
      )
      this.form.extDim = this.extendDimensionList
    },

    // 添加派生维度
    addDerive() {
      if (this.form.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: "暂无派生维度，请先选择基础指标",
          type: "warning"
        })
      }
      this.form.xsc.push({
        adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: "", // getAtomIndicatorList接口返回的zddm字段
        zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
        wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive(item) {
      const index = this.form.xsc.indexOf(item)
      if (index !== -1 && this.form.xsc.length > 1) {
        // 删除派生维度
        this.form.xsc.splice(index, 1)

        // 重新计算 extendDimensionList
        this.extendDimensionList = this.currentDerive.pswd.filter(
          ele => !this.form.xsc.some(e => e.adid === ele.adid)
        )

        // 同步更新扩展维度
        this.form.extDim = this.extendDimensionList
      }
    },
    submitForm: debounce(
      function () {
        console.log(this.form)
        const jldwMapping = {
          其他: 1,
          无单位: 2
        }
        this.$refs.form.validate(async valid => {
          if (valid) {
            await this.$refs.AffectScopeDialog.isAffectSpace(
              {
                indCode: this.form.indCode,
                indType: this.form.lxbm
              },
              async () => {
                this.loading = true

                const { code } =
                  await this.$httpBi.indicatorAnagement.editDeriveIndicator({
                    optType: "update",
                    ind: {
                      ...this.form,
                      extDim: this.form.extDim.map(item => ({
                        ...item,
                        zdlx: item.wdlx
                      })),
                      xsc: this.form.xsc
                        .filter(
                          (item, index) => item.adid !== "全部" || index === 0
                        )
                        .map(item => ({
                          ...item,
                          zdlx: item.wdlx,
                          wdzval: item.wdzval.filter(e => e !== "全部")
                        })),
                      jldw:
                        this.form.jldw === "其他"
                          ? this.form.diydw
                          : this.form.jldw,
                      diydw: jldwMapping[this.form.jldw] || 0,
                      isZeroWarn: this.form.zeroWarnTime === "0" ? 1 : 0
                    }
                  })
                if (code === 200) {
                  this.$message({
                    message: "编辑成功",
                    type: "success"
                  })
                  this.visible = false
                  this.$emit("refresh")
                } else {
                  this.$message({
                    message: "编辑失败",
                    type: "warning"
                  })
                }
                this.loading = false
              }
            )
          } else {
            console.log("error submit!!")
            this.loading = false
            return false
          }
        })
      },
      400,
      { leading: true }
    )
  }
}
</script>

<style scoped lang="scss">
//关闭el-scrollbar横向滚动条
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

.base-info {
  margin-top: 24px;

  .sub-title {
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    padding-left: 12px;
    margin-bottom: 20px;

    &::after {
      position: absolute;
      left: 0;
      top: 2px;
      content: "";
      width: 4px;
      height: 12px;
      background: #1563ff;
      border-radius: 1px;
    }
  }

  .base-content {
    position: relative;
    padding-left: 12px;
    width: 500px;

    &::after {
      position: absolute;
      bottom: 0;
      left: 12px;
      content: "";
      width: 500px;
      height: 1px;
      background: #ebede0;
    }

    .base-row {
      width: 500px;
      display: flex;
      grid-gap: 20px;
      justify-content: space-between;

      .el-form-item {
        width: 100%;
      }
    }
  }
}

.model-info {
  margin-top: 24px;

  .sub-title {
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    padding-left: 12px;
    margin-bottom: 20px;

    &::after {
      position: absolute;
      left: 0;
      top: 2px;
      content: "";
      width: 4px;
      height: 12px;
      background: #1563ff;
      border-radius: 1px;
    }
  }

  .model-content {
    position: relative;
    padding-left: 12px;
    width: 500px;

    &::after {
      position: absolute;
      bottom: 0;
      left: 12px;
      content: "";
      width: 500px;
      height: 1px;
      background: #ebede0;
    }

    .model-row {
      width: 500px;
      display: flex;
      grid-gap: 20px;
      justify-content: space-between;

      .el-form-item {
        width: 100%;
      }
    }
  }
}

.footer-item {
  display: flex;
  margin: 24px 0 20px;
  padding-left: 12px;
  box-sizing: border-box;

  .sub-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: rgba(0, 0, 0, 0.88);
    text-align: left;
    font-style: normal;
    margin-right: 24px;
  }
}

.form {
  width: 512px;
  margin: 0 auto;
  padding-bottom: 20px;
  height: 55vh;
}

::v-deep .el-form--label-top .el-form-item__label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: rgba(0, 0, 0, 0.88);
  text-align: left;
  font-style: normal;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}

.el-select {
  width: 100%;
}

::v-deep .el-input--small {
  width: 100%;
}

::v-deep .is-required .el-form-item__label::after {
  content: "*";
  color: #ff0000;
  margin-left: 4px;
}

::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
  line-height: 32px;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
