<template>
  <div style="padding: 0 20px; width: 100%">
    <Step :steps="steps" :current-step="currentStep" style="margin-top: 32px" />
    <div class="warm-reminder">
      <svg-icon icon-class="warning" style="margin-top: 5px" />
      <div class="warm-reminder-content">
        <p>
          1.基于参照群体的特征，找到【分析群体里每个学生】 与【参照群体】
          在特征指标上的相似度；
        </p>
        <p>
          2.
          若本次相似度分析结果达到预期，请点击“下一步”，完成指标创建的最后一步。
        </p>
      </div>
    </div>
    <SectionTitle title="相似度明细" style="margin-bottom: 16px" />
    <template v-if="isResult">
      <CommonTable
        :page.sync="page"
        id="xh"
        :table-data="tableData"
        :show-batch-tag="false"
        :table-columns.sync="tableColumns"
        @onload="getTableData"
        @handleSortChange="sortChange"
        @handleExport="handleExportExcel"
        height="calc(100vh - 650px)"
      >
        <template #yjxfjeSlot="{ row }">
          <span :style="{ color: row.xfjeys }">{{ row.yjxfje }}</span>
        </template>
        <template #yjcyxfjeSlot="{ row }">
          <span :style="{ color: row.cyxfjeys }">{{ row.yjcyxfje }}</span>
        </template>
      </CommonTable>
      <div class="step-btn">
        <el-button
          size="small"
          @click="$emit('update:currentStep', currentStep - 1)"
        >
          上一步
        </el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="false"
          @click="$emit('update:currentStep', currentStep + 1)"
        >
          下一步
        </el-button>
      </div>
    </template>
    <template v-else>
      <div class="calcing">
        <img src="@/assets/images/wait.png" alt="" />
        <div class="title">请耐心等待</div>
        <div class="progress">
          <div
            class="progress-inner"
            :style="{
              width: '20%'
            }"
          ></div>
        </div>
        <div class="desc">
          抱歉，数据量较大，程序还在飞速计算中，等待过程请勿关闭当前页，否则结果丢失。
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import Step from "../Step"
import SectionTitle from "@/components/SectionTitle.vue"
import CommonTable from "@/components/CommonTable.vue"
export default {
  components: { Step, SectionTitle, CommonTable },
  props: {
    currentStep: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isResult: true,
      page: {
        total: 200,
        pageSize: 10,
        currentPage: 1
      },
      tableData: [
        {
          xh: "2019001",
          xm: "张三",
          bq: "标签1",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型1"
        },
        {
          xh: "2019002",
          xm: "李四",
          bq: "标签2",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型2"
        },
        {
          xh: "2019003",
          xm: "王五",
          bq: "标签3",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型3"
        },
        {
          xh: "2019004",
          xm: "赵六",
          bq: "标签4",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型4"
        },
        {
          xh: "2019005",
          xm: "钱七",
          bq: "标签5",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型5"
        }
      ],
      tableColumns: [
        {
          label: "学号",
          prop: "xh",
          visible: true,
          sortable: false
        },
        {
          label: "姓名",
          prop: "xm",
          visible: true,
          sortable: false
        },
        {
          label: "标签",
          prop: "bq",
          visible: false,
          sortable: false
        },
        {
          label: "性别",
          prop: "xb",
          visible: true,
          sortable: false
        },
        {
          label: "民族",
          prop: "mz",
          visible: true,
          sortable: false
        },
        {
          label: "年级",
          prop: "nj",
          visible: true,
          sortable: false
        },
        {
          label: "院系",
          prop: "yx",
          visible: true,
          sortable: false
        },
        {
          label: "专业",
          prop: "zy",
          visible: true,
          sortable: false
        },
        {
          label: "班级",
          prop: "bj",
          visible: true,
          sortable: false
        },
        {
          label: "统计周期",
          prop: "zq",
          visible: true,
          sortable: false
        },
        {
          label: "预警次数",
          prop: "yjcs",
          visible: true,
          sortable: false
        },
        {
          label: "预警类型",
          prop: "yjlx",
          visible: true,
          sortable: false
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="scss">
.warm-reminder {
  position: relative;
  width: 100%;
  height: 68px;
  background: #fffbe6;
  border-radius: 2px;
  border: 1px solid #fff1b8;
  margin: 16px auto 24px;
  display: flex;
  padding: 12px 17px 0;
  box-sizing: border-box;
  .warm-reminder-content {
    margin-left: 8px;
    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      line-height: 22px;
      height: 22px;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
  }
}
.step-btn {
  position: absolute;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  .el-button--primary {
    background: #1563ff;
    border-color: #1563ff;
    &.is-disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
      &:hover {
        color: #fff;
        background-color: #a0cfff;
        border-color: #a0cfff;
      }
    }
    &:hover {
      background-color: rgba(64, 128, 255, 1);
      border-color: rgba(64, 128, 255, 1);
    }
  }
}
.calcing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  .title {
    margin-top: 24px;
    height: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #2f3338;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    margin-bottom: 32px;
  }
  .progress {
    width: 440px;
    height: 12px;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 16px;
    .progress-inner {
      height: 100%;
      background-image: linear-gradient(
        -45deg,
        #51a9ff 0%,
        #51a9ff 25%,
        #0079ff 25%,
        #0079ff 50%,
        #51a9ff 50%,
        #51a9ff 75%,
        #0079ff 75%,
        #0079ff 100%
      );
      border-radius: 4px;

      background-size: 20px 20px;
    }
  }
  .desc {
    width: 360px;
    height: 44px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #5c646e;
    line-height: 22px;
    text-align: center;
    font-style: normal;
  }
}
</style>
