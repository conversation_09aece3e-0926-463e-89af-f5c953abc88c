<template>
  <dt-single-page-view
    class="cardList"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <DT-Form
      v-model="searchData"
      type="search"
      :show-button="true"
      :render="render"
      @confirm="handleSearch"
    />
    <el-button type="primary" icon="el-icon-plus" @click="handleOnAdd">
      新建
    </el-button>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="theme" label="主题场景">
        <template slot-scope="{ row }">
          {{ fromatTheme(row.theme) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="大屏名称"
        align="left"
      ></el-table-column>
      <el-table-column prop="type" label="大屏类型" align="center">
        <template slot-scope="{ row }">
          {{ getType(row) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="大屏封面" align="center">
        <template slot-scope="{ row }">
          <img
            :src="row.icon"
            @error="
              e => {
                e.target.src = def_pc_icon
              }
            "
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="info"
        label="大屏描述"
        align="left"
      ></el-table-column>

      <el-table-column prop="operate" label="操作" align="right">
        <template slot-scope="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="editLine(row.id)"
            v-if="row.type === 1"
          >
            配置页面
          </el-link>
          <div class="el-divider el-divider--vertical" v-if="row.type === 1" />
          <!-- <el-link :underline="false" type="primary" @click="shareLine(row)">
            分享
          </el-link>
          <div class="el-divider el-divider--vertical" /> -->
          <el-link :underline="false" type="primary" @click="setLine(row)">
            编辑
          </el-link>
          <div class="el-divider el-divider--vertical" />
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="copyLine(row)"
            v-if="row.type === 1"
          >
            复制
          </el-link>
          <div class="el-divider el-divider--vertical" v-if="row.type === 1" /> -->
          <el-link
            :underline="false"
            type="primary"
            @click="delLine(row.id, row.type)"
          >
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="searchData.currentPage"
      :page-size="searchData.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>
  </dt-single-page-view>
</template>
<script>
import Request from "@/service"
export default {
  name: "screen-manage-list",
  data() {
    return {
      show: false,
      loading: true,
      def_pc_icon: require("@/assets/imgs/bi/def_pc.png"),
      total: 0,
      tableData: [],
      searchData: {
        currentPage: 1,
        pageSize: 10,
        type: "0",
        name: ""
      },
      render: [
        {
          type: "input",
          key: "name",
          label: "大屏名称",
          props: {
            placeholder: "请输入大屏名称"
          }
        },
        {
          type: "select",
          key: "type",
          label: "大屏类型",
          props: {
            placeholder: ""
          },
          option: [
            {
              label: "全部",
              value: "0"
            },
            ...this.typeList
          ]
        }
      ]
    }
  },
  inject: ["typeList", "parent"],
  created() {
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
    this.getData()
  },
  computed: {
    getType() {
      return params => {
        const d = {
          1: "敏捷配置",
          2: "定制开发",
          3: "外链大屏"
        }
        return d[params.type]
      }
    }
  },
  methods: {
    // 点击“新增”
    handleOnAdd() {
      this.$emit("click", { com: "edit", opt: "add", data: { id: "" } })
    },
    // table-点击“编辑”
    editLine(id) {
      this.$router.push({
        name: "displayEditor",
        query: {
          isFullPage: true,
          displayId: id
        }
      })
    },
    // table-点击“分享” ---TODO
    shareLine(row) {
      // 修改
      this.$emit("click", { com: "edit", opt: "edit", data: { id: row.id } })
    },
    // table-点击“设置”
    setLine(row) {
      // 修改
      // if (row.type === 2) {
      this.loading = true
      console.log(this.searchData.type)
      Request.screenManage
        .getOne({
          id: row.id
        })
        .then(res => {
          row = { ...row, ...res.data }
          this.$emit("click", { com: "edit", opt: "edit", data: row })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
      // } else {
      //   Request.screenManage
      //     .getRolePermission({
      //       id: row.id
      //     })
      //     .then(res => {
      //       let roleList = res.data || []
      //       row = { ...row, roles: roleList }
      //       this.$emit("click", { com: "edit", opt: "edit", data: row })
      //     })
      // }
    },
    // table-点击“复制” ---TODO
    copyLine(row) {
      // 修改
      this.$emit("click", { com: "edit", opt: "edit", data: { id: row.id } })
    },
    // table-点击“删除”
    delLine(id, type) {
      this.$confirm("此操作将删除选中数据, 是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true
        let api = ""
        if (type === 1) {
          // 删除BI大屏【敏捷配置】
          api = "display"
        } else if (type === 2) {
          // 删除定制大屏
          api = "screenManage"
        } else {
          // 删除外链大屏
          api = "screenManage"
        }
        Request[api]
          .delDisplay({ id: id })
          .then(() => {
            this.$message.success("删除成功")
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
          .finally(() => {
            this.getData()
          })
      })
    },
    // 初始化表格数据
    getData() {
      this.loading = true
      console.log(this.searchData.type)
      Request.screenManage
        .getAll({
          currentPage: this.searchData.currentPage, // 页码
          pageSize: this.searchData.pageSize, // 页面大小
          name: this.searchData.name, //  大屏名称
          type: this.searchData.type // 0 全部 1 敏捷配置 2 定制开发 3 外链大屏
        })
        .then(res => {
          this.total = res.data.totalCount
          this.tableData = res.data.list
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 切换table页码
    handleCurrentChange(val) {
      this.searchData.currentPage = val
      this.getData()
    },
    // 点击顶部“搜索”
    handleSearch() {
      this.searchData.currentPage = 1
      this.getData()
    },
    fromatTheme(value) {
      const item = this.parent.themes.find(item => item.value === value)
      if (item) {
        return item.label
      } else {
        return value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cardList {
  .title {
    margin-top: 100px;
  }

  .buttons {
    margin-top: 50px;
  }
}
img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  border-radius: 5px;
}
</style>
