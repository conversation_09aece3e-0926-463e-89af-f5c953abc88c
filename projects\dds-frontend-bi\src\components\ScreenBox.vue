<template>
  <div class="ScreenAdapter" :style="style">
    <slot />
  </div>
</template>
<script>
export default {
  name: "",
  // 参数注入
  props: {
    width: {
      type: String,
      default: "1920",
    },
    height: {
      type: String,
      default: "1080",
    },
    bgUrl: {
      type: String,
    },
    padding: {
      type: String,
      default: "0",
    },
    isFull: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      style: {
        width: this.width + "px",
        height: this.height + "px",
        transform: "scale(1) translate(-50%, -50%)",
        background: `url(${this.bgUrl}) no-repeat center`,
        backgroundSize: "cover",
        padding: this.padding,
      },
    }
  },
  mounted() {
    this.setScale()
    window.onresize = this.Debounce(this.setScale, 1000)
  },
  methods: {
    Debounce: (fn, t) => {
      const delay = t || 500
      let timer
      return function() {
        const args = arguments
        if (timer) {
          clearTimeout(timer)
        }
        const context = this
        timer = setTimeout(() => {
          timer = null
          fn.apply(context, args)
        }, delay)
      }
    },
    // 获取放大缩小比例
    getScale() {
      const w = window.innerWidth / this.width
      const h = window.innerHeight / this.height
      if (this.isFull) {
        return [ w, h ]
      }
      return w < h ? w : h
    },
    // 设置比例
    setScale() {
      if (this.isFull) {
        var arr = this.getScale()
        console.log(arr)
        this.style.transform =
          "scale(" + arr[0] + "," + arr[1] + ") translate(-50%, 0%)"
      } else {
        this.style.transform =
          "scale(" + this.getScale() + ") translate(-50%, 0%)"
      }
    },
  },
}
</script>
<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
}
.ScreenAdapter {
  transform-origin: 0 0;
  box-sizing: border-box;
  color: #fff;
  position: absolute;
  left: 50%;
  top: 0%;
  transition: 0.3s;
}
</style>
