import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  CHART_LABEL_POSITIONS
} from '@/globalConstants'
import defaultTheme from '@/assets/json/echartsThemes/default.project.json'
const defaultThemeColors = defaultTheme.theme.color
// 参考线默认值
export function getDefaultReferenceLineData() {
  return {
    data: {
      metric: void 0,
      type: 'constant',
      value: 0,
      label: {
        visible: false,
        position: 'start',
        font: {
          size: '12',
          family: PIVOT_CHART_FONT_FAMILIES[0].value,
          color: PIVOT_DEFAULT_FONT_COLOR
        }
      },
      line: {
        width: 1,
        type: 'solid',
        color: defaultThemeColors[0]
      }
    }
  }
}
// 参考区间默认值
export function getDefaultReferenceBandData() {
  return {
    data: [
      {
        metric: void 0,
        type: "constant",
        value: 0
      },
      {
        metric: void 0,
        type: "constant",
        value: 0,
        label: {
          visible: false,
          position: CHART_LABEL_POSITIONS[0].value,
          font: {
            size: '12',
            family: PIVOT_CHART_FONT_FAMILIES[0].value,
            // style: string,
            // weight: string,
            color: PIVOT_DEFAULT_FONT_COLOR
          }
        },
        band: {
          color: defaultThemeColors[0],
          border: {
            width: 0,
            type: 'solid',
            color: '#000'
          }
        }
      }
    ]
  }
}
