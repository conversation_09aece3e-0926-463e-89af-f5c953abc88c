<template>
  <DT-View :show="pageShow">
    <!-- 主视图 -->
    <main-view
      v-if="mainView.show"
      :data="mainView.data"
      :themes="themes"
      :search.sync="mainView.search"
      :pagination.sync="mainView.pagination"
      @search="getTableData"
      @handleAdd="handleAdd"
      @handleEdit="handleEdit"
      @handleDelete="handleDelete"
      @paginationChange="getTableData"
    />
    <!-- 表单视图 -->
    <form-view
      v-if="formView.show"
      :title="formView.title"
      :data="formView.data"
      :themes="themes"
      @handleBack="handleBack"
      @handleSuccess="getTableData"
    />
  </DT-View>
</template>

<script>
import Request from "@/service"
import MainView from "./components/mainView"
import FormView from "./components/formView"
export default {
  name: "param",
  components: {
    MainView,
    FormView
  },
  data() {
    return {
      // 页面显示
      pageShow: false,
      // 服务名称
      themes: [],
      // 主视图
      mainView: {
        // 显示
        show: false,
        // 数据
        data: [],
        // 搜索
        search: {
          theme: ""
        },
        // 分页
        pagination: {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      },
      // 表单视图
      formView: {
        // 显示
        show: false,
        // 标题
        title: "",
        // 数据
        data: {}
      }
    }
  },
  mounted() {
    this.getAllThemes()
  },
  methods: {
    // 获取主题
    getAllThemes() {
      this.$dt_loading.show()
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
          console.log(this.themes, "this.themes")
          this.getTableData()
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 获取表格数据
    getTableData() {
      this.$dt_loading.show()
      Request.dashboard
        .getDashboardPagesByThemes({
          theme: this.mainView.search.theme
        })
        .then(res => {
          this.mainView.data = res.data
          this.mainView.show = true
          this.pageShow = true
          this.$dt_loading.hide()
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 新增
    handleAdd() {
      this.mainView.show = false
      this.formView.show = true
      this.formView.data = {}
      this.formView.title = "新增"
    },
    // 编辑
    handleEdit(row) {
      this.mainView.show = false
      this.formView.show = true
      this.formView.data = { ...row }
      this.formView.title = "编辑"
    },
    // 删除
    handleDelete(row) {
      this.$confirm(
        this.$t("common.confirmDeleteData"),
        this.$t("common.attention"),
        {
          confirmButtonText: this.$t("message.confirm.confirmButtonText"),
          cancelButtonText: this.$t("message.confirm.cancelButtonText"),
          type: "warning"
        }
      )
        .then(() => {
          this.$dt_loading.show()
          Request.dashboard
            .deleteDashboardPageByCode({ code: row.code })
            .then(() => {
              this.$message.success(this.$t("message.success.del"))
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 返回
    handleBack() {
      this.formView.data = {}
      this.formView.show = false
      this.mainView.show = true
    }
  }
}
</script>
