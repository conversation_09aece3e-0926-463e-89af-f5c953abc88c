<template>
  <div>
    <!--变量编辑-->
    <div style="width: 96%; margin-left: 2%; margin-top: 2%">
      <el-row>
        <el-col :span="16"> 变量</el-col>
        <el-col :span="8" style="text-align: right; cursor: pointer">
          <i @click="addVariable" class="el-icon-plus"></i>
        </el-col>
      </el-row>
    </div>
    <el-row v-for="(item, index) in variable" :key="index">
      <el-col :offset="1" :span="17">
        <el-button
          size="mini"
          :type="item.type == 'query' ? 'success' : 'warning'"
          plain
          round
          disabled
        >
          {{ item.type }}
        </el-button>
        {{ item.name }}
        <i v-show="item.alias != ''">[{{ item.alias }}]</i>
      </el-col>
      <el-col :span="4" style="text-align: right">
        <i @click="editVariable(item, index)" class="el-icon-edit" />
        <el-popover
          placement="left-start"
          title="确定删除？"
          width="100"
          v-model="popoverVisible[index]"
        >
          <el-button
            size="mini"
            round
            @click="cancelDelVariable(index)"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            size="mini"
            round
            @click="delVariable(index)"
          >
            确认
          </el-button>
          <i
            slot="reference"
            style="margin-left: 10px; margin-right: 5px"
            class="el-icon-delete"
          />
        </el-popover>
      </el-col>
    </el-row>

    <!--编辑变量-->
    <el-dialog
      title="新增变量"
      v-if="dialogTableVisible"
      :show-close="false"
      :close-on-click-modal="false"
      :visible.sync="dialogTableVisible"
      width="360px"
    >
      <el-form
        :model="newVariable"
        :rules="variableRules"
        ref="variableForm"
        abel-position="right"
        label-width="65px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="newVariable.name"></el-input>
        </el-form-item>
        <el-form-item label="别名" prop="alias">
          <el-input v-model="newVariable.alias"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="newVariable.type" style="width: 100%">
            <el-option label="查询变量" value="query" />
            <el-option label="权限变量" value="auth" />
          </el-select>
        </el-form-item>
        <el-form-item label="值类型" prop="valueType">
          <el-select v-model="newVariable.valueType" style="width: 100%">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔" value="boolean" />
            <el-option label="日期" value="date" />
            <el-option label="sql表达式" value="sql" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="默认值"
          prop="defaultValues"
          v-show="newVariable.type == 'query'"
        >
          <span
            v-for="(item, index) in newVariable.defaultValues"
            :key="index"
          >{{ item
          }}<i
            style="border-radius: 6px; border: 1px solid"
            @click="delDefaultValue(index)"
            class="el-icon-close"
          ></i>
          </span>
          <el-input
            v-if="isAddDefaultValue"
            v-model="newDefaultValue"
          ></el-input>
          <el-button
            v-show="!isAddDefaultValue"
            @click="addDefaultValue"
            style="margin-left: 5px"
            size="mini"
            class="el-icon-plus"
          >
            添加
          </el-button>
          <el-button
            v-show="isAddDefaultValue"
            @click="saveDefaultValue"
            size="mini"
          >
            确定
          </el-button>
        </el-form-item>
        <el-row style="text-align: center">
          <el-button @click="dialogTableVisible = false">取消 </el-button>
          <el-button type="primary" @click="saveVariable">保存 </el-button>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "variable-edit",
  props: {
    variable: {
      type: Array,
      default: ()=>[],
    },
  },
  computed: {
    variableName() {
      return this.newVariable.name
    },
  },
  data() {
    return {
      variableIndex: "",
      newVariable: {},
      newDefaultValue: "",
      dialogTableVisible: false,
      isAddDefaultValue: false,
      variableRules: {
        name: [ { required: true, message: "请输入名称", trigger: "blur" } ],
        type: [ { required: true, message: "请选择类型", trigger: "blur" } ],
        valueType: [
          { required: true, message: "请选择值类型", trigger: "blur" },
        ],
      },
      popoverVisible: [],
    }
  },
  watch: {
    variableName: {
      handler(val) {
        if (val) {
          this.newVariable.name = val.toUpperCase()
        }
      },
    },
  },
  methods: {
    // 新增变量
    addVariable() {
      this.dialogTableVisible = true
      this.variableIndex = ""
      this.isAddDefaultValue = false
      this.newVariable = {
        name: "",
        alias: "",
        type: "query",
        value: "",
        valueType: "string",
        defaultValues: [],
      }
    },
    // 编辑变量
    editVariable(item, index) {
      this.dialogTableVisible = true
      this.newVariable = item
      this.variableIndex = index
      this.isAddDefaultValue = false
    },
    // 删除变量
    delVariable(index) {
      this.variable.splice(index, 1)
      this.popoverVisible.splice(index, 1)
    },
    // 取消删除变量
    cancelDelVariable(index) {
      this.$set(this.popoverVisible, index, false)
    },
    // 添加变量默认值
    addDefaultValue() {
      this.newDefaultValue = ""
      this.isAddDefaultValue = true
    },
    // 保存变量默认值
    saveDefaultValue() {
      if (this.newDefaultValue.trim() !== "") {
        this.newVariable.defaultValues.push(this.newDefaultValue)
        this.isAddDefaultValue = false
      }
    },
    // 删除变量默认值
    delDefaultValue(index) {
      this.newVariable.defaultValues.splice(index, 1)
    },
    // 保存变量
    saveVariable() {
      this.$refs["variableForm"].validate((valid) => {
        // 表单验证
        if (valid) {
          if (this.variableIndex !== "") {
            // 更新
            this.variable.splice(this.variableIndex, 1, this.newVariable)
          } else {
            // 新增
            this.variable.push(this.newVariable)
          }
          this.dialogTableVisible = false
          this.$emit("update:variable", this.variable)
        }
      })
    },
  },
}
</script>
