<template>
  <div class="paneBlock">
    <h4>{{ axisType == "xAxis" ? "X轴" : "Y轴" }}</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="12">
          <el-checkbox v-model="AxisForm.showLine" @change="changeAxisStyle">
            显示坐标轴
          </el-checkbox>
        </el-col>
        <el-col span="12">
          <el-checkbox v-model="AxisForm.inverse" @change="changeAxisStyle">
            坐标轴反转
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="AxisForm.lineStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_LINE_STYLES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="AxisForm.lineSize"
            size="mini"
          >
            <el-option
              v-for="item in 10"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="AxisForm.lineColor"
            @change="changeAxisStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">
          <el-checkbox v-model="AxisForm.showLabel" @change="changeAxisStyle">
            显示标签文字
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="AxisForm.labelFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="AxisForm.labelFontSize"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="AxisForm.labelColor"
            @change="changeAxisStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
      <template v-if="axisType == 'xAxis'">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="2" />
          <el-col span="8">旋转角度</el-col>
          <el-col span="10">
            <el-input-number
              controls-position="right"
              v-model="AxisForm.xAxisRotate"
              @change="changeAxisStyle"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="2">
            <el-checkbox
              v-model="AxisForm.showInterval"
              @change="changeAxisStyle"
            />
          </el-col>
          <el-col span="8">刻度间隔</el-col>
          <el-col span="10">
            <el-input-number
              controls-position="right"
              v-model="AxisForm.xAxisInterval"
              @change="changeAxisStyle"
              :disabled="!AxisForm.showInterval"
            ></el-input-number>
          </el-col>
        </el-row>
      </template>
      <template v-else>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="24">
            <el-checkbox
              v-model="AxisForm.showTitleAndUnit"
              @change="changeAxisStyle"
            >
              显示坐标轴名称
            </el-checkbox>
          </el-col>
        </el-row>
        <div v-if="AxisForm.showTitleAndUnit">
          <el-row
            gutter="8"
            type="flex"
            align="middle"
            class="blockRow">
            <el-col span="24">
              <el-input
                v-model="AxisForm.name"
                size="mini"
                placeholder="请输入名称单位"
                clearable
                @blur="changeAxisStyle"
                @input="inputOnInput($event)"
              />
            </el-col>
          </el-row>

          <el-row
            gutter="8"
            type="flex"
            align="middle"
            class="blockRow">
            <el-col span="10">
              <el-select
                placeholder="请选择"
                @change="changeAxisStyle"
                v-model="AxisForm.titleFontFamily"
                size="mini"
              >
                <el-option
                  v-for="item in PIVOT_CHART_FONT_FAMILIES"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col span="10">
              <el-select
                placeholder="请选择"
                @change="changeAxisStyle"
                v-model="AxisForm.titleFontSize"
                size="mini"
              >
                <el-option
                  v-for="item in PIVOT_CHART_FONT_SIZES"
                  :key="item.value"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-col>
            <!-- <el-col span="4">
              <el-color-picker
                v-model="AxisForm.titleColor"
                @change="changeAxisStyle"
              ></el-color-picker>
            </el-col> -->
          </el-row>
          <el-row
            gutter="8"
            type="flex"
            align="middle"
            class="blockRow">
            <el-col span="12">坐标轴名称位置</el-col>
            <el-col span="10">
              <el-select
                placeholder="请选择"
                @change="changeAxisStyle"
                v-model="AxisForm.nameLocation"
                size="mini"
              >
                <el-option label="起始" value="start"> </el-option>
                <el-option label="中间" value="middle"> </el-option>
                <el-option label="结束" value="end"> </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row
            gutter="8"
            type="flex"
            align="middle"
            class="blockRow">
            <el-col span="12">坐标轴名称旋转</el-col>
            <el-col span="10">
              <el-input-number
                controls-position="right"
                v-model="AxisForm.nameRotate"
                @change="changeAxisStyle"
              ></el-input-number>
            </el-col>
          </el-row>
          <el-row
            gutter="8"
            type="flex"
            align="middle"
            class="blockRow">
            <el-col span="12">名称与轴线距离</el-col>
            <el-col span="10">
              <el-input-number
                controls-position="right"
                v-model="AxisForm.nameGap"
                @change="changeAxisStyle"
              ></el-input-number>
            </el-col>
          </el-row>
        </div>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="12">最小值</el-col>
          <el-col span="10">
            <el-input-number
              controls-position="right"
              v-model="AxisForm.min"
              @change="changeAxisStyle"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="12">最大值</el-col>
          <el-col span="10">
            <el-input-number
              controls-position="right"
              v-model="AxisForm.max"
              @change="changeAxisStyle"
            ></el-input-number>
          </el-col>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script>
import {
  CHART_LABEL_POSITIONS,
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_CHART_LINE_STYLES,
} from "@/globalConstants"
export default {
  components: {},
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
    axisType: {
      type: String,
    },
  },
  data() {
    return {
      CHART_LABEL_POSITIONS,
      PIVOT_CHART_FONT_SIZES,
      PIVOT_CHART_FONT_FAMILIES,
      PIVOT_CHART_LINE_STYLES,
      AxisForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.AxisForm = this._.cloneDeep(
        this.chartData.chartStyles[this.axisType]
      )
    },
    changeAxisStyle() {
      this.$emit("changeStyle", this.axisType, this.AxisForm)
    },
    inputOnInput: function() {
      this.$forceUpdate()
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
