<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative'
    }"
  >
    <div class="title">{{ title }}</div>
    <template v-if="currentStep === 0">
      <Step
        :steps="steps"
        :current-step="currentStep"
        style="margin-top: 32px"
      />
      <!-- 算法类型 -->
      <div class="algorithms">
        <div
          class="algorithm-item"
          :class="{
            active: algorithmType === item.id
          }"
          v-for="item in algorithmTypes"
          :key="item.id"
          @click="algorithmType = item.id"
        >
          <img class="algorithm-item-icon" :src="item.icon" />
          <div class="algorithm-item-name">{{ item.name }}</div>
        </div>
      </div>
      <div class="algorithm-item-desc">
        <div class="inner-desc" v-html="desc"></div>
      </div>
      <div class="step-btn">
        <div class="next-step" @click="currentStep++">下一步</div>
      </div>
    </template>

    <template v-if="currentStep === 1">
      <div class="main">
        <GroupTree :is-group-drag-start.sync="isGroupDragStart" />
        <AlgorithmOne
          v-if="algorithmType == 1"
          :is-group-drag-start="isGroupDragStart"
          :is-index-drag-start="isIndexDragStart"
          :steps="steps"
          :current-step.sync="currentStep"
        />
        <AlgorithmTwo
          v-if="algorithmType == 2"
          :is-group-drag-start="isGroupDragStart"
          :is-index-drag-start="isIndexDragStart"
          :steps="steps"
          :current-step.sync="currentStep"
        />
        <AlgorithmThree
          v-if="algorithmType == 3"
          :is-group-drag-start="isGroupDragStart"
          :is-index-drag-start="isIndexDragStart"
          :steps="steps"
          :current-step.sync="currentStep"
        />
        <AlgorithmFour
          v-if="algorithmType == 4"
          :is-group-drag-start="isGroupDragStart"
          :is-index-drag-start="isIndexDragStart"
          :steps="steps"
          :current-step.sync="currentStep"
        />
        <IndexTree :is-index-drag-start.sync="isIndexDragStart" />
      </div>
    </template>
    <template v-if="currentStep === 2">
      <template v-if="algorithmType === 1 || algorithmType === 2">
        <Step
          :steps="steps"
          :current-step="currentStep"
          style="margin-top: 32px"
        />
        <div class="result-title">聚类结果</div>
        <el-table
          :data="tableData"
          style="width: calc(100% - 48px); margin: 0 24px"
          height="calc(100vh - 562px)"
        >
          <el-table-column
            prop="val1"
            label="类簇"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="val2"
            label="样本数量"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="val3"
            label="指标均值"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="val4"
            label="最大值"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="val5"
            label="最小值"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="val6"
            label="指标表现"
            fixed="left"
          ></el-table-column>
          <el-table-column prop="val7" label="最终指标" width="200">
            <template #default="{ row }">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="row.val8"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="val8" label="时间维度" width="120">
            <template #default="{ row }">
              <el-select
                v-model="row.zdlx"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="val9" label="所属指标域" width="120">
            <template #default="{ row }">
              <el-select
                v-model="row.zdlx"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="val10" label="计算周期" width="120">
            <template #default="{ row }">
              <el-select
                v-model="row.zdlx"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="val11" label="单位" width="120">
            <template #default="{ row }">
              <el-select
                v-model="row.zdlx"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column width="200">
            <template slot="header">
              精度
              <el-tooltip
                class="item"
                effect="dark"
                content="精度的数值代表小数点的位数，此处仅支持输入整"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template :slot-scope="{ row }">
              <div style="display: flex; align-items: center">
                <el-input placeholder=""></el-input>
                <el-checkbox
                  true-label="1"
                  false-label="0"
                  style="margin-left: 16px"
                >
                  四舍五入
                </el-checkbox>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="step-btn">
          <el-button size="small" @click="currentStep--">上一步</el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="false"
            @click="currentStep++"
          >
            保存
          </el-button>
        </div>
      </template>
      <template v-if="algorithmType === 3">
        <Step3AlgorithmThree :steps="steps" :current-step.sync="currentStep" />
      </template>
      <Step3AlgorithmFour
        v-if="algorithmType === 4"
        :steps="steps"
        :current-step.sync="currentStep"
      />
    </template>
    <template v-if="currentStep === 3">
      <Step4AlgorithmThree
        :steps="steps"
        :current-step.sync="currentStep"
      ></Step4AlgorithmThree>
    </template>
  </DT-View>
</template>

<script>
import Step from "./components/Step"
import GroupTree from "./components/GroupTree"
import IndexTree from "./components/IndexTree"
import AlgorithmOne from "./components/Step2/AlgorithmOne"
import AlgorithmTwo from "./components/Step2/AlgorithmTwo"
import AlgorithmThree from "./components/Step2/AlgorithmThree"
import AlgorithmFour from "./components/Step2/AlgorithmFour"
import Step3AlgorithmThree from "./components/Step3/Step3AlgorithmThree"
import Step4AlgorithmThree from "./components/Step4/Step4AlgorithmThree"
import Step3AlgorithmFour from "./components/Step3/Step3AlgorithmFour"
export default {
  components: {
    Step,
    GroupTree,
    IndexTree,
    AlgorithmOne,
    AlgorithmTwo,
    AlgorithmThree,
    AlgorithmFour,
    Step3AlgorithmThree,
    Step4AlgorithmThree,
    Step3AlgorithmFour
  },
  props: {},
  data() {
    return {
      currentStep: 0,
      filterText: "",
      // 算法类型
      algorithmType: 1,
      isGroupDragStart: false, // 拖拽群体
      isIndexDragStart: false, // 拖拽指标
      // 算法类型
      algorithmTypes: [
        {
          name: "指定类动态阈值算法",
          icon: require("@/assets/images/algorithm1.png"),
          steps: ["选择算法", "输入", "输出"],
          id: 1,
          desc: `
            <p>算法类型:聚类算法</p>
            <p style=" margin: 28px 0;">算法改进:在聚类分析的基础上根据设定的参照群体（算法目的为计算与该群体相似的样本），根据多次聚类分析中每次聚类每个群体所在类中的参照群体数量，判断其与参照群体的相似度。改进主要有以下两方面</p>
            <p>1.随机加权聚类：一方面，单次聚类由于初始中心点的选取具有随机性，不能保证少量次数得到的结果的稳健和准确性，因此多次实验存在其必要性；另一方面，每个指标在认定中可能存在重要性的差异，而人为很难判断出该差异，通过多次的加权，可以尽可能地取不同的权重</p>
            <p>2.综合得分评定规则：经过多次加权聚类后，还需要综合每次聚类的结果，每次权重不同、初始中心点不同，使得聚类结果存在好坏差异。通过聚类结果中，以参照群体是否存在良好的类别聚集为依据，判断该次聚类效果的好坏。体现在得分中，每个样本的得分=该样本所在类的参照群体占比，参照群体占比越高，得分越高</p>
            `
        },
        {
          name: "智能类动态阈值算法",
          icon: require("@/assets/images/algorithm2.png"),
          steps: ["选择算法", "输入", "输出"],
          id: 2,
          desc: ""
        },
        {
          name: "相似群体鉴别算法",
          icon: require("@/assets/images/algorithm3.png"),

          steps: ["选择算法", "输入", "输出", "保存指标"],
          id: 3,
          desc: ""
        },
        {
          name: "正态算法",
          icon: require("@/assets/images/algorithm4.png"),

          steps: ["选择算法", "输入", "输出"],
          id: 4,
          desc: ""
        }
      ],
      isDraggingOver: false, // 分析群体
      data: [
        {
          label: "学校",
          children: [
            {
              label: "计算机学院",
              children: [
                {
                  label: "软件工程专业",
                  children: [
                    {
                      label: "张三"
                    },
                    {
                      label: "李四"
                    }
                  ]
                },
                {
                  label: "网络工程专业",
                  children: [
                    {
                      label: "王五"
                    },
                    {
                      label: "赵六"
                    }
                  ]
                }
              ]
            },
            {
              label: "文学院",
              children: [
                {
                  label: "汉语言文学专业",
                  children: [
                    {
                      label: "小红"
                    },
                    {
                      label: "小明"
                    }
                  ]
                },
                {
                  label: "英语专业",
                  children: [
                    {
                      label: "小花"
                    },
                    {
                      label: "小杰"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],

      defaultProps: {
        children: "children",
        label: "label"
      },
      groupList: [], // 分析群体,
      indexList: [],
      tableData: [
        {
          val1: "0",
          val2: "70",
          val3: "921",
          val4: "1270",
          val5: "882",
          val6: "超高",
          val7: "0",
          val8: "0",
          val9: "0",
          val10: "0",
          val11: "0",
          val12: "0",
          val13: "0",
          val14: "0",
          val15: "0",
          val16: "0",
          val17: "0",
          val18: "0"
        },
        {
          val1: "0",
          val2: "70",
          val3: "921",
          val4: "1270",
          val5: "882",
          val6: "超高",
          val7: "0",
          val8: "0",
          val9: "0",
          val10: "0",
          val11: "0",
          val12: "0",
          val13: "0",
          val14: "0",
          val15: "0",
          val16: "0",
          val17: "0",
          val18: "0"
        },
        {
          val1: "0",
          val2: "70",
          val3: "921",
          val4: "1270",
          val5: "882",
          val6: "超高",
          val7: "0",
          val8: "0",
          val9: "0",
          val10: "0",
          val11: "0",
          val12: "0",
          val13: "0",
          val14: "0",
          val15: "0",
          val16: "0",
          val17: "0",
          val18: "0"
        },
        {
          val1: "0",
          val2: "70",
          val3: "921",
          val4: "1270",
          val5: "882",
          val6: "超高",
          val7: "0",
          val8: "0",
          val9: "0",
          val10: "0",
          val11: "0",
          val12: "0",
          val13: "0",
          val14: "0",
          val15: "0",
          val16: "0",
          val17: "0",
          val18: "0"
        },
        {
          val1: "0",
          val2: "70",
          val3: "921",
          val4: "1270",
          val5: "882",
          val6: "超高",
          val7: "0",
          val8: "0",
          val9: "0",
          val10: "0",
          val11: "0",
          val12: "0",
          val13: "0",
          val14: "0",
          val15: "0",
          val16: "0",
          val17: "0",
          val18: "0"
        }
      ]
    }
  },
  computed: {
    steps() {
      return this.algorithmTypes.find(item => item.id === this.algorithmType)
        .steps
    },
    // 算法描述
    desc() {
      return this.algorithmTypes.find(item => item.id === this.algorithmType)
        .desc
    },
    title() {
      if (this.currentStep === 0) {
        return "创建算法指标"
      } else {
        return (
          "创建算法指标 — " +
          this.algorithmTypes.find(item => item.id === this.algorithmType).name
        )
      }
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // tree拖拽开始
    handleTreeDragStart(node, event) {
      this.isGroupDragStart = true
      let arr = []
      if (node.isLeaf) {
        arr = [node.data]
      } else {
        arr = this.getAllNodes(node.childNodes)
      }
      console.log(arr, "arr")
      // 在拖拽开始时设置拖拽节点的数据
      event.dataTransfer.setData("groupList", JSON.stringify(arr))
    },

    getAllNodes(treeData) {
      const result = []
      function traverse(node) {
        if (node.childNodes.length) {
          node.childNodes.forEach(child => {
            traverse(child)
          })
        } else {
          result.push(node.data)
        }
      }

      treeData.forEach(node => {
        traverse(node)
      })

      return result
    },
    handleTreeDragEnd() {
      this.isGroupDragStart = false
    },
    dragEnter() {
      this.isDraggingOver = true
    },
    dragLeave() {
      this.isDraggingOver = false
    },
    dropItem(event) {
      const itemType = event.dataTransfer.getData("itemType")
      console.log(itemType)

      if (itemType === "groupType") {
        this.groupList.push(
          ...JSON.parse(event.dataTransfer.getData("groupList"))
        )
      } else {
        if (this.indexList.length > 0) {
          this.$message.warning("指标只能选择一个")
          return
        } else {
          this.indexList.push(
            ...JSON.parse(event.dataTransfer.getData("indexList"))
          )
        }
      }
      this.isDraggingOver = false
    }
  }
}
</script>

<style scoped lang="scss">
.title {
  width: 100%;
  height: 56px;
  box-shadow: inset 0px -1px 0px 0px #ebedf0;
  line-height: 56px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  text-align: left;
  font-style: normal;
  padding-left: 20px;
  box-sizing: border-box;
}
.algorithms {
  width: 1056px;
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  margin: 40px auto 0;
  box-sizing: border-box;
  .algorithm-item {
    position: relative;
    width: 228px;
    height: 144px;
    background: transparent;
    border-radius: 4px;
    border: 1px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &.active {
      background: #f4f7ff;
      border-radius: 4px;
      border: 1px solid #1563ff;
      &::after {
        position: absolute;
        content: "";
        bottom: -35px;
        left: 44%;
        transform: translateX(-50%);
        width: 20px;
        height: 20px;
        border: 1px solid #ebedf0;
        border-bottom: 1px solid transparent;
        border-right: 1px solid transparent;
        transform: rotate(45deg);
        background: #f7f8fa;

        z-index: 9;
      }
      .algorithm-item-name {
        color: #1563ff;
        font-weight: 500;
      }
      .algorithm-item-desc {
        display: block;
      }
    }
    &-icon {
      width: 56px;
      height: 56px;
    }
    &-name {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #303133;
      line-height: 16px;
      text-align: right;
      font-style: normal;
      margin-top: 16px;
    }
  }
}
.algorithm-item-desc {
  margin: 25px auto 32px;
  width: 1056px;
  height: calc(100vh - 582px);
  max-height: 250px;
  background: #f7f8fa;
  border: 1px solid #ebedf0;
  .inner-desc {
    width: calc(100% - 24px);
    height: calc(100% - 43px);
    margin: 20px 0 19px 24px;
    box-sizing: border-box;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #2f3338;
    line-height: 22px;
    overflow: auto;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 6px;
      height: 2px;
      background-color: #cfd6e6;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: transparent;
      border-radius: 6px;
    }
  }
}

.step-btn {
  width: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  bottom: 52px;
  .el-button--primary {
    background: #1563ff;
    border-color: #1563ff;
    &.is-disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
      &:hover {
        color: #fff;
        background-color: #a0cfff;
        border-color: #a0cfff;
      }
    }
    &:hover {
      background-color: rgba(64, 128, 255, 1);
      border-color: rgba(64, 128, 255, 1);
    }
  }
}
.main {
  width: calc(100% - 40px);
  height: 100%;
  height: calc(100vh - 235px);

  margin: 20px;
  display: flex;
}
.result-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #2f3338;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 24px;
}
.next-step {
  width: 76px;

  height: 32px;
  background: #1563ff;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  text-align: right;
  font-style: normal;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  &:hover {
    background-color: rgba(64, 128, 255, 1);
  }
}
.prev-step {
  width: 76px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  text-align: right;
  font-style: normal;
  text-align: center;
  line-height: 32px;
  margin-right: 8px;
  cursor: pointer;
  border: 1px solid #e5e6eb;
  &:hover {
    background-color: #ecf5ff;
  }
}

::v-deep .el-table__fixed::before {
  display: none;
}
::v-deep .el-table__fixed {
  height: 100% !important;
  // z-index: 99;
}
::v-deep .el-table__fixed-body-wrapper {
  height: calc(100% - 56px) !important;
}
</style>
