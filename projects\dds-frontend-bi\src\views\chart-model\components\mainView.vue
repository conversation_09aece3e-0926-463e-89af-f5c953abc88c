<template>
  <div class="rolePage-mainView">
    <!-- 查询 -->
    <DT-Form
      type="search"
      v-model="searchForm"
      :render="searchRender"
      @confirm="handleSearch"
    />
    <!-- 按钮 -->
    <el-button type="primary" icon="el-icon-plus" @click="$emit('handleAdd')">
      新建
    </el-button>
    <!-- 表格 -->
    <DT-Table :data="data" :column="tableColumn" />
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "main-view",
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 搜索（与index同步）
    search: {
      type: Object,
      default: () => {
        return {
          chartName: ""
        }
      }
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    }
  },
  data() {
    return {
      // 搜索表单内容
      searchForm: {},
      // 搜索渲染配置
      searchRender: [
        {
          label: "图表模型名称",
          type: "input",
          key: "chartName",
          props: {
            placeholder: "请输入图表模型名称"
          }
        }
      ],
      // 表格渲染配置
      tableColumn: [
        {
          label: "图表模型名称",
          prop: "chartName"
        },
        {
          label: "图表模型ID",
          prop: "chartCode"
        },
        {
          label: "关联指标数",
          formatter: ({ row }) => row.inds + "个"
        },
        {
          label: "分享次数",
          formatter: ({ row }) => row.shares + "次"
        },
        // 操作
        {
          label: "操作",
          width: 150,
          button: [
            {
              label: "分享",
              onClick: ({ row }) => this.$emit("handleShare", row)
            },
            {
              label: "编辑",
              onClick: ({ row }) => this.$emit("handleEdit", row)
            },
            {
              label: "删除",
              onClick: ({ row }) => this.$emit("handleDelete", row)
            }
          ]
        }
      ]
    }
  },
  mounted() {
    // 页面初始化时配置搜索双向绑定数据，使表单页返回时搜索框数据保持与之前一致
    this.searchForm = { ...this.search }
  },
  methods: {
    // 筛选表单 - 查询
    handleSearch(form) {
      this.pagination.currentPage = 1
      Object.keys(form).forEach(key => (this.search[key] = form[key]))
      this.$emit("search")
    },
    // 分页 - 每页条数改变
    handlePageSizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.$emit("paginationChange")
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.$emit("paginationChange")
    }
  }
}
</script>
