<template>
  <div class="paneBlock">
    <h4>表格</h4>
    <!-- <div class="paneBlock">
      <h4>
        <span>表头样式与分组</span>
        <i class="el-icon-delete"></i>
        <i class="el-icon-edit"></i>
      </h4>
    </div>
    <div class="paneBlock">
      <h4>
        <span>表格数据列</span>
        <i class="el-icon-delete"></i>
        <i class="el-icon-edit"></i>
      </h4>
    </div> -->
    <div class="paneBlock">
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="center"
          class="blockRow">
          <!-- <el-col span="12">
            <el-checkbox
              v-model="tableForm.headerFixed"
              @change="changeTableForm"
              >固定表头</el-checkbox
            >
          </el-col> -->
          <el-col span="12">
            <el-checkbox
              v-model="tableForm.isStripe"
              @change="changeTableForm"
            >
              斑马纹
            </el-checkbox
            >
          </el-col>
          <el-col span="12">
            <el-checkbox
              @change="changeTableForm"
              v-model="tableForm.bordered"
            >
              边框
            </el-checkbox
            >
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>左固定列</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="24">
            <el-select
              v-model="tableForm.leftFixedColumns"
              multiple
              size="mini"
              placeholder="请选择"
              @change="changeTableForm"
            >
              <el-option
                v-for="item in options"
                :key="item"
                :label="item.displayName"
                :value="item.displayName"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>右固定列</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="24">
            <el-select
              v-model="tableForm.rightFixedColumns"
              multiple
              size="mini"
              placeholder="请选择"
              @change="changeTableForm"
            >
              <el-option
                v-for="item in options"
                :key="item"
                :label="item.displayName"
                :value="item.displayName"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>大小</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="24">
            <el-radio-group v-model="tableForm.size" @change="changeTableForm">
              <el-radio-button label="mini">小</el-radio-button>
              <el-radio-button label="small">中</el-radio-button>
              <el-radio-button label="medium">大</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>自动合并相同内容</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="24">
            <el-radio-group
              v-model="tableForm.autoMergeCell"
              @change="changeTableForm"
              size="mini"
            >
              <el-radio-button :label="true">开启</el-radio-button>
              <el-radio-button :label="false">关闭</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>分页</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="14">
            <el-radio-group
              v-model="tableForm.withPaging"
              @change="switchChange('withPaging', tableForm.withPaging)"
              size="mini"
            >
              <el-radio-button :label="true">开启</el-radio-button>
              <el-radio-button :label="false">关闭</el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col span="10" v-if="tableForm.withPaging">
            <el-select
              v-model="tableForm.pageSize"
              size="mini"
              placeholder="请选择"
              @change="switchChange('pageSize', tableForm.pageSize)"
            >
              <el-option
                v-for="item in TABLE_PAGE_SIZES"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="paneBlock">
      <h4>使用原始数据</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="rowBlock">
          <el-col span="24">
            <el-radio-group
              v-model="tableForm.withNoAggregators"
              @change="changeTableForm"
            >
              <el-radio-button :label="true">开启</el-radio-button>
              <el-radio-button :label="false">关闭</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { TABLE_PAGE_SIZES } from "@/globalConstants"
// import widgetlibs from "../../../config/index";
export default {
  components: {},
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableForm: {},
      value1: [],
      TABLE_PAGE_SIZES,
      tabPosition: "left",
    }
  },
  computed: {
    options() {
      return [ ...this.chartData.cols, ...this.chartData.metrics ]
    },
  },
  created() {},
  mounted() {},
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler: function() {
        this.init()
      },
    },
  },
  methods: {
    init() {
      this.tableForm = this._.cloneDeep(this.chartData.chartStyles.table)
    },
    changeTableForm() {
      this.$emit("changeStyle", "table", this.tableForm)
    },
    switchChange(key, value) {
      this.$emit("switchChange", key, value)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
