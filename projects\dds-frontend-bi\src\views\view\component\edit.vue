<template>
  <dt-single-page-view
    class="editPage"
    ref="element"
    :inner-style="{ textAlign: 'left', height: '100vh' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
    :style="{ height: '100vh', padding: 0 }"
  >
    <el-form :model="form" :rules="rules" ref="viewForm">
      <el-container>
        <!--头部-->
        <el-header>
          <header-step :edit-step="editStep"></header-step>
        </el-header>
        <!--第一步-->
        <el-container v-show="editStep == 1">
          <!--左侧-->
          <el-aside
            :style="{
              display: 'flex',
              flexDirection: 'column',
            }"
          >
            <base-edit :form="form"></base-edit>
          </el-aside>
          <el-container>
            <splitpanes horizontal class="default-theme">
              <pane>
                <el-container style="height: 100%">
                  <!--sql编辑区-->
                  <el-main style="background: #ebebeb">
                    <coder-editor
                      :height="threeHeight"
                      :value="form.sql"
                      @onChangeSelect="onChangeSelect"
                      @change="onCmCodeChanges"
                      :grammar-tips="grammarTips"
                    />
                  </el-main>
                  <el-aside>
                    <!--变量-->
                    <variable-edit :variable.sync="variable"></variable-edit>
                  </el-aside>
                </el-container>
              </pane>
              <pane>
                <!--结果展示区-->
                <sql-result
                  :is-running="isRunning"
                  :sql-result="sqlResult"
                  :sql-columns="sqlColumns"
                >
                </sql-result>
              </pane>
            </splitpanes>
            <el-footer>
              <el-row style="margin-top: 15px">
                <el-col
                  :span="12"
                >
                  展示前
                  <el-input-number
                    @change="runSql"
                    @blur="runSql"
                    v-model="total"
                    :step="10"
                    :disabled="!isRunOver"
                    step-strictly
                    :min="1"
                    :max="30000"
                  />
                  条数据 (最大值30000)
                </el-col>
                <el-col :span="4">
                  是否支持预警
                  <el-switch
                    v-model="form.isSimple"
                    :active-value="1"
                    :inactive-value="0"
                  >
                  </el-switch>
                </el-col>

                <el-col :span="8" style="text-align: right">
                  <el-button @click="exportExcel">导出</el-button>
                  <el-button @click="goBack">取消</el-button>
                  <el-button
                    @click="runSql"
                  >
                    {{ selectSql.trim().length == 0 ? "执行" : "执行选中SQL" }}
                  </el-button>
                  <el-button
                    @click="nextStep"
                    :disabled="!isRunOver"
                    :type="isRunOver ? 'success' : 'info'"
                  >
                    下一步
                  </el-button>
                </el-col>
              </el-row>
            </el-footer>
          </el-container>
        </el-container>
        <!--第二步-->
        <el-container v-show="editStep == 2">
          <el-main>
            <el-tabs v-model="activeName">
              <el-tab-pane label="模型" name="model">
                <model-pane :model-list="modelList"></model-pane>
              </el-tab-pane>
              <el-tab-pane label="权限" name="auth">
                <auth-pane
                  v-if="activeName == 'auth'"
                  :all-columns="modelList"
                  :author-variable="authorVariable"
                  :roles.sync="roles"
                ></auth-pane>
              </el-tab-pane>
            </el-tabs>
          </el-main>
          <el-footer>
            <el-row style="text-align: right; margin-top: 15px">
              <el-button @click="lastStep" type="success">上一步</el-button>
              <el-button @click="goBack">取消</el-button>
              <el-button @click="submitFrom">保存</el-button>
            </el-row>
          </el-footer>
        </el-container>
      </el-container>
    </el-form>
  </dt-single-page-view>
</template>

<script>
import Request from "@/service"
import coderEditor from "./coderEditor.vue"
import { Splitpanes, Pane } from "splitpanes"
import "splitpanes/dist/splitpanes.css"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import baseEdit from "./base_edit"
import authPane from "./auth_pane"
import modelPane from "./model_pane"
import variableEdit from "./variable_edit"
import encryptSql from "@/utils/encryptSql.js"
import SqlResult from "./sql_result"
import HeaderStep from "./header_step"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"

export default {
  name: "edit-page",
  components: {
    HeaderStep,
    SqlResult,
    coderEditor,
    Splitpanes,
    Pane,
    baseEdit,
    authPane,
    modelPane,
    variableEdit,
  },
  props: {},
  data() {
    var checkCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("编码不能为空"))
      }
      Request.view
        .checkViewCode({
          id: this.form.id === "" ? "-1" : this.form.id,
          code: value,
        })
        .then((res) => {
          if (res.data) {
            return callback(new Error("编码已存在"))
          } else {
            return callback()
          }
        })
        .catch(() => {})
    }
    return {
      selectSql: "",
      columns: [],
      tables: [],
      id: null,
      show: false,
      loading: true,
      title: "新增",
      editStep: 1,
      oneHeight: 40,
      twoHeight: 10,
      threeHeight: 30,
      form: {
        isVisc: 0,
        id: "",
        name: "",
        description: "",
        sourceId: "",
        code: "",
        sql: "",
        variable: "",
        model: "",
        config: "",
        groupId: 0,
        roles: [],
      },
      rules: {
        name: [ { required: true, message: "请输入名称", trigger: "blur" } ],
        code: [
          { required: true, message: "请输入编码", trigger: "blur" },
          { validator: checkCode, trigger: "blur" },
        ],
      },
      testRes: false,
      variable: [],
      authorVariable: [],
      model: "",
      sqlColumns: [],
      sqlResult: [],
      total: 10,
      isRunOver: false,
      isRunning: false,
      activeName: "model",
      modelList: [],
      roles: [],
    }
  },
  computed: {
    grammarTips() {
      return [ ...this.columns, ...this.tables ]
    },
  },
  mounted: function() {
    this.$nextTick(() => {
      // 监听窗口大小变化
      let context = this
      window.onresize = () => {
        context.oneHeight = window.innerHeight * 0.7
        context.twoHeight = window.innerHeight * 0.3
        context.threeHeight = window.innerHeight * 0.75
      }
      // this.$refs.element.$el.offsetTop：元素距离浏览器顶部的高度
      this.oneHeight = window.innerHeight * 0.7
      this.twoHeight = window.innerHeight * 0.3
      this.threeHeight = window.innerHeight * 0.75
    })
  },
  created() {
    this.id = this.$route.query.id || null
    if (this.id) {
      this.initForm()
    } else {
      this.form.groupId = this.$route.query.group
    }
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
  },
  methods: {
    // 返回列表页
    goBack() {
      this.$router.go(-1)
    },
    // 请求表单数据
    initForm() {
      Request.view
        .getOne({ id: this.id })
        .then((res) => {
          this.form = res.data
          this.model = res.data.model
          this.variable = JSON.parse(res.data.variable)
          this.roles = []
          for (let i = 0; i < res.data.roles.length; i++) {
            this.roles.push({
              roleId: res.data.roles[i].roleId,
              columnAuth: JSON.parse(res.data.roles[i].columnAuth),
              rowAuth: JSON.parse(res.data.roles[i].rowAuth),
            })
          }
          this.isRunOver = true
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // sql变化
    onCmCodeChanges(newsql) {
      this.form.sql = newsql
      this.isRunOver = false
    },
    // 选中sql
    onChangeSelect(sql) {
      this.selectSql = sql
    },
    // 执行sql
    runSql() {
      if (this.form.sql.trim() === "") {
        this.$message.error("请输入sql")
        return
      }
      if (!this.form.sourceId) {
        this.$message.error("请选择数据源")
        return
      }
      this.isRunOver = false
      this.isRunning = true
      const params = {
        sourceId: this.form.sourceId,
        sql: this.form.sql,
        variables: this.variable,
        pageNo: 1,
        pageSize: this.total,
        limit: this.total,
      }

      if (this.selectSql.trim() !== "") {
        params.sql = this.selectSql
      }

      // 加密sql
      params.sql = encryptSql.encrypt(params.sql)

      Request.view
        .runsql(params)
        .then((res) => {
          this.sqlColumns = res.data.columns
          this.sqlResult = res.data.resultList
          this.isRunOver = true
        })
        .catch(() => {})
        .finally(() => {
          this.isRunning = false
        })
    },
    // 下一步
    nextStep() {
      this.$refs["viewForm"].validate((valid) => {
        // 表单验证
        if (valid) {
          this.editStep = 2
          // 模型处理
          if (this.sqlColumns.length > 0) {
            // 新解析的
            let modelKeys = []
            let objModel = []
            if (this.form.id && this.model !== "") {
              objModel = JSON.parse(this.model)
              modelKeys = Object.keys(objModel)
            }
            for (let i = 0; i < this.sqlColumns.length; i++) {
              // 数值类型待补充
              let numberType = [ "INT", "DECIMAL" ]
              let isNumber = numberType.indexOf(this.sqlColumns[i].type) > -1
              let name = this.sqlColumns[i].name
              if (modelKeys.indexOf(name) > -1) {
                this.modelList.push({
                  name: name,
                  alias: objModel[name].alias,
                  sqlType: objModel[name].sqlType,
                  modelType: objModel[name].modelType,
                  visualType: objModel[name].visualType,
                })
              } else {
                this.modelList.push({
                  name: name,
                  sqlType: this.sqlColumns[i].type,
                  modelType: isNumber ? "value" : "category",
                  visualType: isNumber ? "number" : "string",
                })
              }
            }
          } else {
            // 取原有的
            let objModel = JSON.parse(this.model)
            for (const x in objModel) {
              let md = {
                name: x,
                sqlType: objModel[x].sqlType,
                modelType: objModel[x].modelType,
                visualType: objModel[x].visualType,
              }
              this.modelList.push(md)
            }
          }
          this.authorVariable = []
          // 权限变量
          for (const x in this.variable) {
            if (this.variable[x].type === "auth") {
              this.authorVariable.push(this.variable[x])
            }
          }
        }
      })
    },
    // 上一步
    lastStep() {
      this.editStep = 1
      this.modelList = []
    },
    // 表单组确认
    submitFrom() {
      this.form["variable"] = JSON.stringify(this.variable)
      this.form["config"] = ""
      let model = {}
      for (let i = 0; i < this.modelList.length; i++) {
        model[this.modelList[i].name] = {
          alias: this.modelList[i].alias,
          sqlType: this.modelList[i].sqlType,
          visualType: this.modelList[i].visualType,
          modelType: this.modelList[i].modelType,
        }
      }
      this.form["model"] = JSON.stringify(model)
      this.form["source"] = {}
      this.form["roles"] = []

      for (let i = 0; i < this.roles.length; i++) {
        this.form["roles"].push({
          roleId: this.roles[i].roleId,
          columnAuth: JSON.stringify(this.roles[i].columnAuth),
          rowAuth: JSON.stringify(this.roles[i].rowAuth),
        })
      }

      // 加密sql
      this.form.sql = encryptSql.encrypt(this.form.sql)
      if (this.form.id) {
        // 修改
        Request.view
          .update(this.form)
          .then(() => {
            this.$emit("click", { com: "list", opt: "back", data: { id: "" } })
            this.$message.success("操作成功")
            this.$router.go(-1)
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
      } else {
        // 新增
        Request.view
          .create(this.form)
          .then(() => {
            this.$emit("click", { com: "list", opt: "back", data: { id: "" } })
            this.$message.success("操作成功")
            this.$router.go(-1)
          })
          .catch(() => {
            this.$message.error("操作失败")
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    exportExcel() {
      jsonToSheetXlsx({
        data: this.sqlResult,
        filename: "表数据" + new Date().getTime(),
      })
      console.log(this.sqlResult, "sqlResult")
      console.log(this.sqlColumns, "sqlColumns")
    },
    // 表单组重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.el-main {
  border: 0.1px;
  border-color: #d2d2d3;
  border-style: solid;
}

.el-aside {
  border: 0.1px;
  border-color: #d2d2d3;
  border-style: solid;
}

.el-footer {
  border: 0.1px;
  border-color: #d2d2d3;
  border-style: solid;
  border-top: none;
}

::v-deep .ace-sqlserver .ace_print-margin {
  width: 0;
}

.default-theme {
  height: calc(100vh - 168px);
}

.splitpanes__pane {
  overflow: auto;
}

html,
body,
#app {
  margin: 0;
  padding: 0;
  height: 100%;
}
</style>
