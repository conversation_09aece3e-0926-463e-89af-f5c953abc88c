<template>
  <div class="portal-main" :theme="theme">
    <div class="portalBody">
      <div class="gridClass">
        <div class="grid-head">
          <div class="title">{{ currentDashborad.name }}</div>
          <el-row class="row-btn">
            <el-tooltip content="导出" placement="top">
              <span class="el-dropdown-link">
                <svg-icon
                  class="icon"
                  @click="downloadVisible = true"
                  icon-class="download"
                />
              </span>
            </el-tooltip>
          </el-row>
        </div>
        <div class="GolobalPanel" v-if="filters && filters.length">
          <ControlPanelComponent
            ref="GolobalPanel"
            :controls.sync="filters"
            :query-mode="queryMode"
            :select-options="globalSelectOptions"
            @change="getFormValuesRelatedItems"
            @reset="handleResetGlobalFilters"
            type="golobal"
          />
        </div>

        <div class="dashboardList">
          <Dashboard
            :only-view="true"
            ref="Dashboards"
            :global-controls="filters"
            :query-mode="queryMode"
            :widgets="widgets"
            :token="token"
            :form-values-related-items="FormValuesRelatedItems"
            :form-values-related-items-all="FormValuesRelatedItemsAll"
            :current-dashborad="currentDashborad"
            @onReload="getDashboard"
          />
        </div>
      </div>
    </div>
    <DownloadDialog
      :download-visible.sync="downloadVisible"
      :export-title="currentDashborad.name"
      @handleExport="handleExport"
    />
  </div>
</template>

<script>
import ControlPanelComponent from "@/components/Control/Control"
import DownloadDialog from "@/views/portal/component/DownloadDialog"
import Dashboard from "./dashboard/index.vue"
import { mapState } from "vuex"
import axios from "axios"

export default {
  components: {
    ControlPanelComponent,
    Dashboard,
    DownloadDialog,
  },
  props: {
    token: {
      type: String,
    },
  },
  data() {
    return {
      downloadVisible: false,
      currentDashborad: {},
      filters: [],
      widgets: [],
      widgetList: [],
      queryMode: null,
      globalSelectOptions: null,
      originalFilters: null,
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
    }),
  },
  created() {
    this.getDashboard()
  },
  mounted() {},
  watch: {},
  methods: {
    async getDashboard() {
      axios.get("")
      const res = await axios({
        method: "get",
        url: "/dds-server-bi/shareClient/dashboard",
        headers: {
          token: this.token,
        },
      })
      const code = res.data.code
      const data = res.data.data
      console.log(res, "res>>>>>>>>>>>>>>>>>")
      if (code === 200) {
        this.currentDashborad = data
        this.widgets = data.widgets

        this.filters = (data.config && JSON.parse(data.config).filters) || []
        // 控制器赋值
        // 备份原始数据 为了重置
        this.originalFilters = this._.cloneDeep(this.filters)
        this.queryMode =
          (data.config && JSON.parse(data.config).queryMode) || 0
        this.getSelectOptions()
        this.getCurrentDashboradRelations()

        // 是否显示水印
        const isShowWatermark =
          data.config && JSON.parse(data.config).isShowWatermark
        if (isShowWatermark) {
          this.$store.commit("watermark/SET_WATERMARK")
        } else {
          this.$store.commit("watermark/OUT_WATERMARK")
        }
      }
    },
    getSelectOptions() {
      const selectOptions = {}
      this.filters.length &&
        this.filters.forEach((filter) => {
          Object.entries(filter.relatedItems).forEach(async([ , v ]) => {
            if ([ "select", "radio" ].includes(filter.type)) {
              if (filter.optionType === "auto" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: filter.relatedViews[v.viewId].fields,
                  viewId: v.viewId,
                }
                // const { data } = await this.$httpBi.view.getdistinctvalue(param);
                const res = await axios({
                  method: "post",
                  url: "/dds-server-bi/shareClient/getdistinctvalue",
                  headers: {
                    token: this.token,
                  },
                  data: {
                    ...param,
                  },
                })
                this.$set(selectOptions, filter.key, [ ...res.data.data ])
              } else if (filter.optionType === "manual" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: [ filter.valueField, filter.textField ],
                  viewId: filter.valueViewId,
                }
                // 如果加载过一次手动数据  不需要加载第二次
                if (!selectOptions[filter.key]) {
                  this.$set(selectOptions, filter.key, [])
                  // const { data } = await this.$httpBi.view.getdistinctvalue(
                  //   param
                  // );
                  const res = await axios({
                    method: "post",
                    url: "/dds-server-bi/shareClient/getdistinctvalue",
                    headers: {
                      token: this.token,
                    },
                    data: {
                      ...param,
                    },
                  })
                  this.$set(selectOptions, filter.key, [ ...res.data.data ])
                }
              } else if (
                filter.optionType === "custom" &&
                !selectOptions[filter.key]
              ) {
                this.$set(selectOptions, filter.key, [ ...filter.customOptions ])
              }
            }
          })
        })

      this.globalSelectOptions = selectOptions
    },
    // 获取当前页面所有图表关系
    getCurrentDashboradRelations() {
      this.widgetList = []
      let obj = {}
      const deepCloneWidgets = JSON.parse(
        JSON.stringify(this.currentDashborad.widgets)
      )
      deepCloneWidgets.forEach((item) => (obj[item.id] = item))
      this.$nextTick(() => {
        this.currentDashborad.relations.forEach((item) => {
          obj[item.widgetId].widgetId = item.widgetId
          obj[item.widgetId].id = item.id
          this.widgetList.push(obj[item.widgetId])
        })
      })
      setTimeout(() => {
        console.log(this.$refs)
      }, 2000)
      // this.$refs.dashboards.getLayout();
    },
    getFormValuesRelatedItems(formValues) {
      this.FormValuesRelatedItems = Object.keys(formValues).reduce(
        (items, key) => {
          const control = this.filters.find((c) => c.key === key)
          const { relatedItems } = control
          const checkedItems = Object.entries(relatedItems)
            .filter(([ , config ]) => config.checked)
            .map(([ itemId ]) => itemId)
          return Array.from(new Set([ ...items, ...checkedItems ]))
        },
        []
      )
    },
    handleResetGlobalFilters() {
      this.filters = JSON.parse(JSON.stringify(this.originalFilters))
    },
    // 下载
    handleExport({ exportType, title }) {
      if (exportType === "IMG") {
        this.$refs.Dashboards.exportImg(title)
      }
      if (exportType === "PDF") {
        this.$refs.Dashboards.exportPDF(title)
      }
      if (exportType === "EXCEL") {
        this.$refs.Dashboards.exportExcel(title)
      }
      this.downloadVisible = false
    },
  },
}
</script>

<style scoped lang="scss">
@import "../../portal/theme/dark.scss";
@import "../../portal/theme/light.scss";
.portal-main {
  width: 100%;
  height: 100vh;
  .portalBody {
    display: flex;
    height: 100%;
    background-color: var(--theme-color);
    .siderbar {
      width: 224px;
      border-right: 1px solid var(--theme-border-color);
      .portal-btn {
        padding: 16px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        i {
          cursor: pointer;
        }
      }
      .portalTreeNode {
        height: 100%;
        padding: 0 8px;
        overflow: auto;
      }
    }
    .gridClass {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: var(--theme-bg-color);
      .grid-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 18px 16px;
        height: 56px;
        background-color: var(--theme-color);
        color: var(--theme-text-color);
        font-size: 14px;
      }
      .row-btn {
        margin: 0;
        & > * {
          margin-left: 24px;
        }
      }
      .GolobalPanel {
        margin: 16px 16px 0 16px;
        padding: 16px 16px 0;
        background-color: var(--theme-color);
      }
      .dashboardList {
        box-sizing: border-box;
        padding-right: 0px;
        // flex: 1;
        overflow-y: auto;
      }
    }
  }
}

.vue-grid-layout {
  background: #eee;
  touch-action: none;
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .buttonView {
    opacity: 0;
  }
}
.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}
.controlForm {
  height: auto;
  background-color: #fff;
}

i {
  color: var(--theme-icon-color);
}
::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner,
::v-deep .el-range-editor.el-input__inner {
  width: 100%;
}
</style>
