<template>
  <dt-single-page-view>
    <el-page-header @back="goBack"> </el-page-header>
    <vxe-table
      border
      :span-method="mergeRowMethod"
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
    >
      <vxe-column field="sjjnum" title="序号"> </vxe-column>
      <vxe-column field="warn_field" title="监控度量"> </vxe-column>
      <vxe-column field="warn_val" title="数据值"> </vxe-column>
      <vxe-column field="warn_sign" title="预警条件"> </vxe-column>
      <vxe-column field="warn_status" title="阀值"> </vxe-column>
      <vxe-column field="warn_mbz" title="对比值"> </vxe-column>
      <vxe-column field="warn_time" title="触发时间"> </vxe-column>
    </vxe-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="params.currentPage"
      :page-sizes="[10, 20, 40, 80]"
      :page-size="params.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </dt-single-page-view>
</template>

<script>
import Request from '@/service'
export default {
  components: {},
  props: {},
  data() {
    return {
      total: 0,
      loading: true,
      params: {
        currentPage: 1,
        pageSize: 10,
        id: 0
      },
      tableData: []
    }
  },
  computed: {},
  created() {
    this.params.id = this.$route.query.id
    this.getData()
  },
  mounted() {},
  watch: {},
  methods: {
    async getData() {
      this.loading = true
      Request.warning
        .getWarningLogsById(this.params)
        .then(res => {
          if (res.code === 200 && res.data) {
            this.total = res.data.totalCount
            this.tableData = res.data.resultList
          } else if (res.code === 200) {
            this.total = 0
            this.tableData = []
          }
        })
        .catch(() => {
          this.$message.error('获取数据失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    goBack() {
      this.$router.go(-1)
    },
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.params.currentPage = val
      this.getData()
    },

    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['sjjnum']
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="less"></style>
