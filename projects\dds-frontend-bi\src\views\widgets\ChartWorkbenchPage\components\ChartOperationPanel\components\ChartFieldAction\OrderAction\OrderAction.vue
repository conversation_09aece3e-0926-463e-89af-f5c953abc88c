<template>
  <el-dropdown-item>
    <el-dropdown
      placement="right-start"
      style="width: 100%"
      @command="sort"
    >
      <span class="el-dropdown-link inner-dropdown-menu">
        <span>
          <i class="el-icon-sort" />
          <span>排序</span>
        </span>
        <i class="el-icon-arrow-right el-icon--right" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="none">默认</el-dropdown-item>
        <el-dropdown-item command="asc">升序</el-dropdown-item>
        <el-dropdown-item command="desc">降序</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </el-dropdown-item>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
