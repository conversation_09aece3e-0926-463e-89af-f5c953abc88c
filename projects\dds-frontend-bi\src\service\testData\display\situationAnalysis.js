// 引入Mock.js
const Mock = require('mockjs')

export default {
  '/studentEducationalDisplay/topOne': () =>
    Mock.mock({
      code: 200,
      message: 'OK',
      data: {
        'col1|3000-20000': 0,
        'col2|3000-20000': 0,
        'col3|3000-20000': 0,
        'col4|3000-20000': 0,
        'col5': null,
      },
    }),
  '/studentEducationalDisplay/leftOne': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4': [
        {
          'val|10000-100000': 0,
          'name|+1': ['休学', '征兵入伍', '退学', '留级'],
        },
      ],
    }),
  '/studentEducationalDisplay/leftOneOther': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|20': [
        {
          'val1|10000-100000': 0,
          'val2|0-100': 0,

          'name|+1': ['休学', '征兵入伍', '退学', '留级'],
        },
      ],
    }),
  '/studentEducationalDisplay/lefttwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4': [
        {
          'val|100-999': 0,
          'name|+1': [
            '2021-2022-2',
            '2022-2023-1',
            '2022-2023-2',
            '2023-2024-1',
          ],
        },
      ],
    }),
  '/studentEducationalDisplay/leftThree': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|7': [
        {
          'val|10-100': 0.0,
          'name': '@ctitle(4,10)',
        },
      ],
    }),
  '/studentEducationalDisplay/centerOne': () =>
    Mock.mock({
      code: 200,
      message: 'OK',
      data: [
        {
          college: '马克思主义学院',
          deptId: 1,
          val1: 1234,
          val2: '34人 / 10.00%',
          val3: '34人 / 10.00%',
        },
        {
          college: '计算机科学与技术学院',
          val1: 5678,
          val2: '56人 / 20.00%',
          val3: '56人 / 20.00%',
          deptId: 2,
        },
        {
          college: '机械工程学院',
          val1: 91011,
          val2: '78人 / 30.00%',
          val3: '78人 / 30.00%',
          deptId: 3,
        },
        {
          college: '电子信息工程学院',
          val1: 121314,
          val2: '90人 / 40.00%',
          val3: '90人 / 40.00%',
        },
        {
          college: '土木工程学院',
          val1: 151617,
          val2: '12人 / 50.00%',
          val3: '12人 / 50.00%',
        },
        {
          college: '化学与环境工程学院',
          val1: 181920,
          val2: '34人 / 60.00%',
          val3: '34人 / 60.00%',
        },
        {
          college: '外国语学院',
          val1: 212223,
          val2: '56人 / 70.00%',
          val3: '56人 / 70.00%',
        },
        {
          college: '艺术与设计学院',
          val1: 242526,
          val2: '78人 / 80.00%',
          val3: '78人 / 80.00%',
        },
        {
          college: '法学院',
          val1: 272829,
          val2: '90人 / 90.00%',
          val3: '90人 / 90.00%',
        },
        {
          college: '教育科学学院',
          val1: 303132,
          val2: '12人 / 100.00%',
          val3: '12人 / 100.00%',
        },
        {
          college: '体育学院',
          val1: 333435,
          val2: '34人 / 10.00%',
          val3: '34人 / 10.00%',
        },
        {
          college: '化学生物与材料工程学院',
          val1: 363738,
          val2: '56人 / 20.00%',
          val3: '56人 / 20.00%',
        },
        {
          college: '经济与管理学院',
          val1: 394041,
          val2: '78人 / 30.00%',
          val3: '78人 / 30.00%',
        },
        {
          college: '数学与统计学院',
          val1: 424344,
          val2: '90人 / 40.00%',
          val3: '90人 / 40.00%',
        },
        {
          college: '物理与电子科学学院',
          val1: 454647,
          val2: '12人 / 50.00%',
          val3: '12人 / 50.00%',
        },
        {
          college: '生命科学学院',
          val1: 484950,
          val2: '34人 / 60.00%',
          val3: '34人 / 60.00%',
        },
        {
          college: '地球与环境科学学院',
          val1: 515253,
          val2: '56人 / 70.00%',
          val3: '56人 / 70.00%',
        },
        {
          college: '新闻与传播学院',
          val1: 545556,
          val2: '78人 / 80.00%',
          val3: '78人 / 80.00%',
        },
        {
          college: '历史文化学院',
          val1: 575859,
          val2: '90人 / 90.00%',
          val3: '90人 / 90.00%',
        },
        {
          college: '哲学学院',
          val1: 606162,
          val2: '12人 / 100.00%',
          val3: '12人 / 100.00%',
        },
      ],
    }),
  '/studentEducationalDisplay/centerTwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4': [
        {
          'val1|100-999': 0,
          'val2|100-999': 0,
          'name|+1': [
            '2021-2022学年第一学期',
            '2022-2023学年第一学期',
            '2024-2025学年第一学期',
            '2025-2026学年第一学期',
          ],
        },
      ],
    }),

  '/studentEducationalDisplay/rightOne': () =>
    Mock.mock({
      code: 200,
      message: 'OK',
      data: {
        'val1|10-100': 0,
        'val2|10-100': 0,
        'val3|10-100': 0,
      },
    }),
  '/studentEducationalDisplay/rightTwo': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|4': [
        {
          'val|100-999': 0,
          'name|+1': ['公共基础课', '公共选修课', '专业课', '其他'],
        },
      ],
    }),
  '/studentEducationalDisplay/rightTwoOther': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|2': [
        {
          'name': '@ctitle(4,10)',
          'val1|10000-100000': 0,
          'val2|0-100': 0.0,
        },
      ],
    }),
  '/studentEducationalDisplay/rightThree': () =>
    Mock.mock({
      'code': 200,
      'message': 'OK',
      'data|12': [
        {
          'val|10-100': 0.0,
          'name': '@ctitle(4,10)',
        },
      ],
    }),
}
