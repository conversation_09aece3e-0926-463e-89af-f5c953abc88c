<template>
  <el-form label-width="80px" label-position="left">
    <tempalte v-for="(item, index) in options" :key="index">
      <el-collapse v-if="item.children">
        <el-collapse-item :title="item.label">
          <dynamic-form
            :options="item.children"
            :form-data="formData"
            :cur-code="curCode"
            v-on="$listeners"
          ></dynamic-form>
        </el-collapse-item>
      </el-collapse>
      <el-form-item
        v-if="inputShow[item.name] && !item.children"
        :label="item.label"
        :prop="item.name"
        :required="item.required"
      >
        <el-input
          v-if="item.type == 'el-input-text'"
          v-model.trim="form[item.name]"
          type="text"
          size="mini"
          placeholder="请输入内容"
          clearable
          @input="changed($event, item.name)"
        />
        <el-input
          v-if="item.type == 'el-input-textarea'"
          v-model.trim="form[item.name]"
          type="textarea"
          size="mini"
          :rows="3"
          placeholder="请输入内容"
          clearable
          @input="changed($event, item.name)"
        />
        <el-input-number
          v-if="item.type === 'el-input-number'"
          size="mini"
          style="width: 100%"
          v-model.trim="form[item.name]"
          controls-position="right"
          @input="changed($event, item.name)"
        />
        <el-slider
          v-if="item.type == 'el-slider'"
          v-model="form[item.name]"
          @change="changed($event, item.name)"
        />

        <el-switch
          v-if="item.type == 'el-switch'"
          v-model="form[item.name]"
          size="mini"
          @change="changed($event, item.name)"
        />
        <el-select
          v-if="item.type == 'el-select'"
          size="mini"
          v-model="form[item.name]"
          clearable
          placeholder="请选择"
          @change="changed($event, item.name)"
        >
          <el-option
            v-for="itemChild in item.selectOptions"
            :key="itemChild.code"
            :label="itemChild.name"
            :value="itemChild.code"
          />
        </el-select>

        <el-select
          v-if="item.type == 'el-select-chart'"
          size="mini"
          v-model="form[item.name]"
          clearable
          placeholder="请选择"
          filterable
          @change="changed($event, item.name)"
        >
          <el-option
            v-for="itemChild in widgets"
            :key="itemChild.id"
            :label="itemChild.name"
            :value="itemChild.id"
          />
        </el-select>
        <template v-if="item.type === 'controls' && selectedChartId !== 1">
          <el-select
            size="mini"
            v-model="form[item.name]"
            placeholder="请选择"
            clearable
            @change="changed($event, item.name)"
          >
            <el-option
              v-for="item in controls"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
        <template v-if="item.type === 'controls' && selectedChartId == 1">
          <el-form-item label="字段关联控制器" label-width="80">
            <teamplte v-for="(e, index) in form.relation" :key="index">
              <el-select
                size="mini"
                v-model="e.zd"
                placeholder="请选字段"
                clearable
              >
                <el-option
                  v-for="o in fieldOptions"
                  :key="o.displayName"
                  :label="o.displayName"
                  :value="o.displayName"
                  :disabled="form.relation.some(r => r.zd == o.displayName)"
                ></el-option>
              </el-select>
              <el-select
                size="mini"
                v-model="e.kzq"
                placeholder="请选控制器"
                clearable
              >
                <el-option
                  v-for="c in controls"
                  :key="c.key"
                  :label="c.name"
                  :value="c.key"
                  :disabled="form.relation.some(r => r.kzq == c.key)"
                ></el-option>
              </el-select>
              <i class="el-icon-minus" @click="removeDomain(e)"></i>
            </teamplte>
            <i class="el-icon-plus" @click="addDomain"></i>
          </el-form-item>
        </template>

        <ColorPicker
          v-if="item.type === 'el-input-color'"
          placeholder="请选择颜色"
          v-model="form[item.name]"
          @change="val => changed(val, item.name)"
        ></ColorPicker>

        <UploadImage
          v-if="item.type === 'dt-upload'"
          @change="val => changed(val, item.name)"
          v-model="form[item.name]"
        />

        <el-radio-group
          v-if="item.type == 'el-radio-group'"
          v-model="form[item.name]"
          @change="changed($event, item.name)"
        >
          <el-radio
            v-for="itemChild in item.selectOptions"
            :key="itemChild.code"
            :label="itemChild.code"
          >
            {{ itemChild.name }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </tempalte>
  </el-form>
</template>

<script>
import ColorPicker from "./ColorPicker"
import UploadImage from "./UploadImage.vue"
import _ from "lodash"
import { mapGetters } from "vuex"

export default {
  name: "dynamic-form",
  components: { ColorPicker, UploadImage },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    curCode: {
      type: String,
      default: "screen"
    }
  },
  data() {
    return {
      form: {},
      inputShow: {},
      id: 2
    }
  },
  computed: {
    ...mapGetters({
      widgets: "widgets",
      currentLayerWidget: "currentLayerWidget"
    }),
    controls() {
      let widgetConfig = this.widgets.find(item => item.id === this.form.target)
      return widgetConfig?.config?.controls || {}
    },
    selectedChartId() {
      if (this.currentLayerWidget) {
        return this.currentLayerWidget.config.selectedChartId
      }
      return null
    },
    fieldOptions() {
      if (this.currentLayerWidget.config) {
        const config = this.currentLayerWidget.config
        return [...config.cols, ...config.metrics, ...config.color]
      }
      return []
    }
  },
  created() {},
  mounted() {
    console.log(this.$store, "AllProtal")
    this.form = this._.cloneDeep(this.formData)
    this.isShowData()
  },
  watch: {
    options() {
      this.isShowData()
    },
    formData: {
      handler(val) {
        this.form = this._.cloneDeep(val)
        this.isShowData()
      },
      deep: true
    }
  },
  methods: {
    changed: _.debounce(function (val, key) {
      console.log(val, key)
      this.$set(this.form, key, val)
      this.$emit("onChanged", this.form)
      // key为当前用户操作的表单组件
      for (let i = 0; i < this.options.length; i++) {
        let item = this.options[i]
        if (item.relactiveDom === key) {
          this.inputShow[item.name] =
            item.relactiveDomValue && item.relactiveDomValue.includes(val)
          this.inputShow = Object.assign({}, this.inputShow)
        }
      }

      // this.$store.dispatch("display/updateLayer", {
      //   layer: {
      //     ...this.currentLayer,
      //     params: {
      //       ...this.currentLayer.params,
      //       text: value
      //     }
      //   }
      // })
    }, 500),
    // 组件属性 数据是否展示动态还是静态数据
    isShowData() {
      let currentData = {}
      const data = []
      for (let i = 0; i < this.options.length; i++) {
        // 添加默认的inputShow值
        this.$set(this.inputShow, this.options[i].name, true)
        if (this.options[i].selectValue) {
          currentData = this.options[i]
        } else {
          data.push(this.options[i])
        }
      }
      console.log(data, "data")
      console.log(currentData.value, "currentData.value")
      data.forEach(el => {
        if (
          el.relactiveDomValue &&
          !el.relactiveDomValue.includes(this.formData[currentData.name])
        ) {
          console.log(el.name, "el.name")
          this.$set(this.inputShow, el.name, false)
        }
      })
    },
    addDomain() {
      this.form.relation.push({
        zd: "",
        kzq: "",
        key: Date.now()
      })
    },
    removeDomain(item) {
      console.log(item)
      var index = this.form.relation.indexOf(item)
      console.log(index, "index")
      if (index !== -1) {
        this.form.relation.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.layer-setting {
  width: 300px;
  padding: 10px 15px;
  background-color: #fff;
  box-sizing: border-box;
  overflow-y: auto;
}
.el-form-item--small.el-form-item {
  margin-bottom: 5px;
}

::v-deep .el-form-item__label {
  font-size: 12px;
}

::v-deep .el-input-group__append {
  padding: 0;
}
::v-deep .el-collapse {
  border-top: none;
}
</style>
