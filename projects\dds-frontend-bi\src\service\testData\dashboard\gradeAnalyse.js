// 引入Mock.js
const Mock = require("mockjs")

export default {
  "/gradeAnalyse/rowOne": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      data: {
        "val1|0-5.2": 0,
        "val2|0-100": 0,
        "val3|0-100": 0,
        "val4|0-100": 0
      }
    }),
  "/gradeAnalyse/rowTwo": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      data: {
        val1: "@ctitle(4,10)",
        "val2|0-100": 0,
        val3: "@ctitle(4,10)",
        "val4|0-100": 0,
        val5: "@ctitle(4,10)",
        "val6|0-100": 0,
        val7: "@ctitle(4,10)",
        "val8|0-100": 0
      }
    }),
  "/gradeAnalyse/rowThree": () => {
    // 生成空数组
    const chartData = []
    // 定义可选的label和type列表
    const labels = [
      "马克思主义学院",
      "计算机科学与技术学院",
      "机械工程学院",
      "电子信息工程学院",
      "土木工程学院",
      "化学与环境工程学院",
      "外国语学院",
      "艺术与设计学院",
      "法学院",
      "教育科学学院",
      "体育学院",
      "化学生物与材料工程学院",
      "经济与管理学院",
      "数学与统计学院",
      "物理与电子科学学院",
      "生命科学学院",
      "地球与环境科学学院",
      "新闻与传播学院",
      "历史文化学院",
      "哲学学院"
    ]
    const types = [2023, 2022, 2021, 2020]

    // 循环遍历生成数据
    labels.forEach(label => {
      types.forEach(type => {
        // 生成每个组合的数据对象
        const dataItem = {
          val: Mock.mock("@integer(0, 100)"),
          name: label,
          type: type
        }
        // 将数据对象添加到chartData数组中
        chartData.push(dataItem)
      })
    })
    return {
      code: 200,
      message: "OK",
      data: chartData
    }
  },
  "/gradeAnalyse/rowFour": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      data: {
        "val1|0-1000": 0,
        "val2|0-100": 0,
        "val3|0-100": 0,
        "val4|0-100": 0,
        "val5|0-500": 0,
        "val6|0-100": 0
      }
    }),
  "/gradeAnalyse/rowFive": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      "data|4": [
        {
          "val1|100-999": 0,
          "val2|100-999": 0,
          "name|+1": [
            "2021-2022学年第一学期",
            "2022-2023学年第一学期",
            "2024-2025学年第一学期",
            "2025-2026学年第一学期"
          ]
        }
      ]
    })
}
