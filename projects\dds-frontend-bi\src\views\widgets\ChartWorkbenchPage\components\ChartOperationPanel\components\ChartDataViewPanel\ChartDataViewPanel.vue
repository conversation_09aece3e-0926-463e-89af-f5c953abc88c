<template>
  <div class="chart-data-view">
    <!-- 数据指标 -->
    <div class="viewSelectWrapper">
      <h2 class="title">数据指标</h2>
      <div class="viewselect">
        <el-cascader
          :value="selectedViewId"
          :options="views"
          @change="viewSelect"
          :show-all-levels="false"
          filterable
          :props="{
            value: 'id',
            label: 'name',
            emitPath: false,
            expandTrigger: 'hover',
          }"
        ></el-cascader>
        <el-tooltip
          class="item"
          effect="dark"
          content="编辑SQL"
          placement="top"
        >
          <i class="el-icon-edit"></i>
        </el-tooltip>
      </div>
    </div>
    <!-- 分类型 -->
    <div class="columnContainer">
      <h2 class="title">分类型:</h2>
      <ul class="columnList categories">
        <draggable
          v-model="categoryDragItems"
          :group="{ name: 'itxst', pull: 'clone', put: false }"
          @start="onDraggableStart('category')"
        >
          <li class="item" v-for="item in categoryDragItems" :key="item">
            <svg-icon class="icon" :icon-class="item.visualType" />
            {{ item.name }}
          </li>
        </draggable>
      </ul>
    </div>
    <!-- 数值类型 -->
    <div class="columnContainer">
      <h2 class="title">数值型:</h2>
      <ul class="columnList values">
        <draggable
          v-model="valueDragItems"
          :group="{ name: 'itxst', pull: 'clone', put: false }"
          @start="onDraggableStart('value')"
        >
          <transition-group>
            <li class="item" v-for="item in valueDragItems" :key="item">
              <svg-icon class="icon" :icon-class="item.visualType" />
              {{ item.name }}
            </li>
          </transition-group>
        </draggable>
      </ul>
    </div>
  </div>
</template>

<script>
import draggable from "vuedraggable"

import { isEmptyObject ,uuid } from "@/utils/index.js"
export default {
  components: { draggable },
  props: {},
  data() {
    return {
      categoryDragItems: [], // 分类列表
      valueDragItems: [], // 值列表
      views: [], // 数据集列表
      selectedViewId: "", // 选中的数据集id
      selectedView: {}, // 选中的数据集
    }
  },
  computed: {},
  created() {
    this.getViews()
  },
  mounted() {},
  watch: {
    selectedView: {
      handler(val) {
        if (isEmptyObject(val)) return
        this.filterPermissionColumns()
      },
      immediate: true,
    },
  },
  methods: {
    // 获取数据集
    async getViews() {
      const { data } = await this.$httpBi.view.getGroupAndView()
      this.views = data
    },
    // 获取数据集详情
    async onLoadViewDetail() {
      this.categoryDragItems = []
      this.valueDragItems = []
      const { data } = await this.$httpBi.view.getOne({
        id: this.selectedViewId,
      })
      this.selectedView = {
        ...data,
        model: JSON.parse(data.model || "{}"),
        variable: JSON.parse(data.variable || "[]"),
      }
    },
    // 选择数据集
    viewSelect(viewId) {
      this.$confirm("切换 View 会清空所有配置项，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.selectedViewId = viewId
        this.onLoadViewDetail()
      })
    },
    // 过滤权限中的字段
    filterPermissionColumns() {
      // 初始化分类列表和值列表
      this.categoryDragItems = []
      this.valueDragItems = []
      // 将roles数组中roleIdList数组包含roleId的columnAuth数组合并去重
      const { roleIdList } = this.$store.state.user
      const roles = this.selectedView.roles || []
      // 当前角色无权限字段
      let currentNoRolesColumn = []
      if (roles.length && roleIdList) {
        roleIdList.forEach((roleId) => {
          const columnAuth = JSON.parse(
            roles.find((item) => item.roleId === roleId).columnAuth
          )
          currentNoRolesColumn = Array.from(
            new Set(currentNoRolesColumn.concat(columnAuth))
          )
        })
      }
      Object.entries(this.selectedView.model).forEach(([ key, m ]) => {
        // 过滤无权限字段
        if (currentNoRolesColumn.length && currentNoRolesColumn.includes(key))
          return
        // 分类型
        if (m.modelType === "category") {
          this.categoryDragItems.push({
            name: key,
            type: "category",
            visualType: m.visualType,
            checked: false,
            id: uuid(8,16),
            
          })
        } else {
          // 数值类型
          this.valueDragItems.push({
            name: key,
            type: "value",
            visualType: m.visualType,
            checked: false,
            agg: "sum",
            id: uuid(8,16),


          })
        }
      })
    },
    // 拖拽开始
    onDraggableStart(type) {
      this.$emit("onDraggableStart", type)
    },
  },
}
</script>

<style scoped lang="scss">
.chart-data-view {
  width: 250px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  h2 {
    font-size: 12px;
    font-weight: 600;
    height: 28px;
    line-height: 28px;
  }
  .viewSelectWrapper {
    padding: 10px;
    box-sizing: border-box;

    .viewselect {
      display: flex;
      align-items: center;
      justify-content: space-between;
      i {
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
  .columnContainer {
    border-top: 1px solid #d9d9d9;
    flex: 1;
    .title {
      padding: 0 10px;
      box-sizing: border-box;
    }
    .columnList {
      height: calc(100% - 28px);
      overflow-y: auto;

      .item {
        padding-left: 15px;
        font-size: 14px;
        height: 24px;
        line-height: 24px;
        box-sizing: border-box;
      }
      .item:hover {
        background-color: #a0d5f4;
        cursor: grab;
      }
      &.values {
        .item:hover {
          background-color: #d5e9bd;
          cursor: grab;
        }
      }
    }
  }
}
</style>
