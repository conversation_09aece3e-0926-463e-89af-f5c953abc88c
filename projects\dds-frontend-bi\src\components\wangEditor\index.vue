<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editor"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 500px; overflow-y: hidden"
      v-model="html"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
      @onBlur="onBlur"
      @onChange="onChange"
    />
  </div>
</template>

<script>
import Vue from "vue"
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"
export default Vue.extend({
  components: { Editor, Toolbar },
  props: {
    html: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editor: null,
      toolbarConfig: {},
      editorConfig: { placeholder: "请输入内容..." },
      mode: "default", // or 'simple'
    }
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    // onChange: _.debounce(function (value) {
    //   this.$emit("updateHtml", value.getHtml());
    // }, 1000),
    // onChange(value) {
    //   if (this.editor) {
    //     const html = value.getHtml();
    //     this.$emit("updateHtml", html);
    //   }
    // },
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
})
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
