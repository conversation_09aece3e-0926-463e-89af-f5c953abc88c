
export const VideoLayerConfig =  {
  code: 4,
  type: 'video',
  tabName: '文本栏',
  label: '视频',
  icon: 'el-icon-video-play',
  component: "VideoLayer",
  options: {
    // 配置
    style: [
      {
        type: 'el-input-number',
        label: '左边距',
        name: 'left',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '上边距',
        name: 'top',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '宽度',
        name: 'width',
        required: false,
        placeholder: '',
        value: 350,
      },
      {
        type: 'el-input-number',
        label: '高度',
        name: 'height',
        required: false,
        placeholder: '',
        value: 250
      },
      {
        type: 'el-input-textarea',
        label: '地址',
        name: 'videoAdress',
        required: false,
        placeholder: '',
        value: 'https://www.w3school.com.cn//i/movie.ogg',
      },
      {
        type: 'el-switch',
        label: '自动播放',
        name: 'autoplay',
        required: false,
        placeholder: '',
        value: false,
      },
      {
        type: 'el-switch',
        label: '控制台',
        name: 'controls',
        required: false,
        placeholder: '',
        value: true,
      },
      {
        type: 'el-switch',
        label: '循环',
        name: 'loop',
        required: false,
        placeholder: '',
        value: false,
      },
      
    ],
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: '外链'
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
  }
}
