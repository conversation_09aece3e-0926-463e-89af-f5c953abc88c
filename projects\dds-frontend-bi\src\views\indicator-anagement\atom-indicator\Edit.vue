<template>
  <div>
    <el-dialog
      title="编辑"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="700px"
    >
      <el-scrollbar>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="110px"
          class="form"
        >
          <el-form-item label="指标类型" prop="zblx">
            {{ form.zblx }}
          </el-form-item>
          <el-form-item label="指标名称" prop="zbmc">
            <el-input v-model="form.zbmc" clearable></el-input>
          </el-form-item>
          <template v-if="form.zblx == '原子指标'">
            <el-form-item label="度量">
              <el-input v-model="form.zbymc" disabled></el-input>
            </el-form-item>
            <el-form-item label="派生维度" prop="pswd">
              <div class="form-item">
                <span v-if="tempPswd.length">
                  {{ foramtPswd(tempPswd) }}
                </span>
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="openEditDeriveDialog"
                  style="margin-left: 8px"
                >
                  编辑
                </el-button>
              </div>
            </el-form-item>
          </template>
          <el-form-item label="所属指标域">
            <avue-input-tree
              default-expand-all
              style="width: 100%"
              v-model="form.sysjy"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择内容"
              :dic="viewGroup"
            ></avue-input-tree>
          </el-form-item>
          <el-form-item label="归属部门">
            <el-cascader
              v-model="form.deptAllCode"
              style="width: 100%"
              clearable
              placeholder="请选择归属部门"
              :props="cascaderProps"
              @change="handleChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="描述" prop="ms">
            <el-input v-model="form.ms" clearable></el-input>
          </el-form-item>
          <el-form-item label="标签" prop="bq">
            <el-select
              v-model="form.bq"
              filterable
              style="width: 100%"
              multiple
              remote
              allow-create
              default-first-option
              @change="changeTag"
              :multiple-limit="10"
              @remove-tag="removeTag"
              :remote-method="remoteMethod"
              placeholder="请创建或者选择标签"
            >
              <el-option
                v-for="item in formatLabels"
                :key="item.bqmc"
                :label="item.bqmc"
                :value="item.bqmc"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('form')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="editDeriveDialogVisible"
      title="派生维度编辑"
      width="90vw"
      :close-on-click-modal="false"
      top="5vh"
    >
      <el-table
        :data="deriveTableData"
        style="width: 100%"
        border
        class="atomIndicator"
      >
        <el-table-column prop="wdzd" label="字段代码"></el-table-column>
        <el-table-column prop="zdmc" label="标题"></el-table-column>
        <el-table-column prop="zdlx" label="标记类型" width="260">
          <template #default="{ row }">
            <el-select
              v-model="row.zdlx"
              style="width: 220px"
              placeholder="请选择"
              :disabled="row.isDisabled"
              @change="changeDimType($event, row)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="
                  item.value === 'time' &&
                  hasTimeDimension &&
                  row.zdlx !== 'time'
                "
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="wb" label="对应维度" width="300">
          <template #default="{ row }">
            <template
              v-if="
                row.isDisabled && ['ps', 'indOrDim', 'time'].includes(row.zdlx)
              "
            >
              {{ row.dimName }}({{ row.version }})
            </template>
            <template v-else>
              <el-popover
                placement="right"
                width="250"
                trigger="hover"
                @after-leave="resetNoCoverShowCount(row)"
                v-if="
                  row.noCoverValues &&
                  row.noCoverValues.length &&
                  ['ps', 'indOrDim', 'time'].includes(row.zdlx)
                "
              >
                <div>
                  <div class="popover-title">无法覆盖以下字段：</div>
                  <div class="popover-content">
                    <div
                      v-for="field in row.noCoverValues.slice(
                        0,
                        row.noCoverShowCount || 5
                      )"
                      :key="field"
                    >
                      {{ field }}
                    </div>
                    <el-button
                      v-if="
                        row.noCoverValues.length > (row.noCoverShowCount || 5)
                      "
                      type="text"
                      @click.stop="loadMoreNoCover(row)"
                    >
                      继续查看
                    </el-button>
                  </div>
                </div>
                <i
                  class="el-icon-warning"
                  style="
                    margin-right: 5px;
                    font-size: 20px;
                    color: #e6a23c;
                    cursor: pointer;
                  "
                  slot="reference"
                ></i>
              </el-popover>
              <el-select-v2
                v-if="
                  !row.newCreate &&
                  ['ps', 'indOrDim', 'time'].includes(row.zdlx)
                "
                :disabled="row.isDisabled"
                v-model="row.definitionCode"
                :options="allDimTreeList"
                style="width: 220px"
                :props="{
                  label: 'dimName',
                  value: 'definitionCode'
                }"
                filterable
                placeholder="请选择"
                @change="getDimLevels($event, row)"
                clearable
              >
                <template #default="{ item }">
                  <p
                    style="
                      padding: 0 17px;
                      display: flex;
                      justify-content: space-between;
                    "
                  >
                    <span v-tooltip-content="180">{{ item.dimName }}</span>
                    <span>{{ item.version }}</span>
                  </p>
                </template>
              </el-select-v2>
              <span
                v-if="
                  row.newCreate && ['ps', 'indOrDim', 'time'].includes(row.zdlx)
                "
                style="display: flex; align-items: center"
              >
                <span v-tooltip-content="130">
                  {{ row.dimName }}({{ row.version }})
                </span>
                <el-button type="text" @click="removeDim(row)">移除</el-button>
                <el-button type="text" @click="editDim(row)">编辑</el-button>
              </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="wb" label="维度层级" width="260">
          <template #default="{ row }">
            <template v-if="row.isDisabled">
              {{ row.levelName }}
            </template>
            <el-select
              v-if="
                ['ps', 'indOrDim', 'time'].includes(row.zdlx) &&
                row.definitionCode != 'self' &&
                row.definitionCode &&
                !row.isDisabled
              "
              style="width: 220px"
              v-model="row.levelCode"
              placeholder="请选择"
              :disabled="row.isDisabled"
              @change="onLevelCodeChange(row)"
            >
              <el-option
                v-for="item in dimLevelMap[row.definitionCode]"
                :key="item.levelCode"
                :label="item.levelName"
                :value="item.levelCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDeriveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleDeriveSave">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="编辑维度"
      :visible.sync="editDialogVisible"
      width="600px"
      label-width="100px"
      label-position="top"
    >
      <el-form :model="editDimForm" ref="editDimForm">
        <el-form-item label="维度名称" prop="dimName">
          <el-input v-model="editDimForm.dimName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="editDimForm.version"></el-input>
        </el-form-item>
        <el-form-item label="层级名称" prop="levelName">
          <el-input
            v-model="editDimForm.levelName"
            placeholder="请输入层级名称"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editDimForm.tags"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            v-model="editDimForm.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新频率">
          <el-select
            v-model="editDimForm.updateFrequency"
            placeholder="请选择维度类型"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="editDimForm.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEditDim">确 定</el-button>
      </span>
    </el-dialog>
    <AffectScopeDialog ref="AffectScopeDialog" />
  </div>
</template>

<script>
import options from "../mixins/options"
// 变更影响弹窗
import AffectScopeDialog from "../components/AffectScopeDialog.vue"
export default {
  components: {
    AffectScopeDialog
  },
  mixins: [options],
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    yzzbList: {
      type: Array,
      default: () => []
    },
    viewGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var changeZdmc = async (rule, value, callback) => {
      if (this.tempName === value) {
        callback()
      }
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode: this.form.indCode
        })
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === "") {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        callback(new Error("最小值不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === "") {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error("最大值不能小于最小值"))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.form.jldw === "") {
        callback(new Error("请选择单位"))
      } else if (this.form.jldw === "其他" && this.form.diydw === "") {
        callback(new Error("请自定义单位"))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      rules: {
        tempName: "",
        zbmc: [
          {
            required: true,
            message: "请填写指标名称",
            trigger: "blur"
          },
          { validator: changeZdmc, trigger: "blur", required: true }
        ],
        atomid: { required: true, message: "请选择原子指标", trigger: "blur" },
        jsfs: { required: true, message: "请选择计算方式", trigger: "blur" },
        jszq: { required: true, message: "请选择计算周期", trigger: "blur" },
        sjwd: { required: true, message: "请选择时间维度", trigger: "blur" },
        jd: { required: true, message: "请输入精度", trigger: "blur" },
        tvmin: { validator: changeMin, trigger: "blur" },
        tvmax: { validator: changeMax, trigger: "blur" },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: "change"
        }
      },
      pswdOptionsMap: {},
      editDeriveDialogVisible: false,
      deriveTableData: [],
      allDimTreeList: [],
      options: [
        {
          value: "ps",
          label: "维度"
        },
        {
          value: "NULL",
          label: "无"
        },
        {
          value: "time",
          label: "时间维度"
        }
      ],
      dimLevelMap: {},
      editDialogVisible: false,
      editDimForm: {
        definitionCode: "",
        dimName: "",
        version: "",
        categoryCode: "",
        levelName: "",
        tags: "",
        description: "",
        updateFrequency: 1,
        updateType: 0
      },
      editRow: null,
      updateFrequencys: [
        { label: "按日", value: 1 },
        { label: "按周", value: 2 },
        { label: "按月", value: 3 },
        { label: "按学期", value: 4 },
        { label: "按学年", value: 5 },
        { label: "按年", value: 6 }
      ],
      dimensionTypeList: [],
      tempPswd: [],
      addDims: []
    }
  },
  computed: {
    // 当前派生维度
    currentDerive() {
      if (!this.yzzbList.length) return []
      return (
        this.yzzbList.filter(item => item.id === this.form.atomid)[0] || {
          pswd: []
        }
      )
    },
    hasTimeDimension() {
      // 检查所有数据列是否已存在 zdlx === 'time'
      return this.deriveTableData.some(item => item.zdlx === "time")
    }
  },

  created() {
    this.tempName = this.form.zbmc
    if (this.form.xsc) {
      this.form.xsc.forEach(item => {
        this.getPsOption(item.adid)
      })
    }
  },
  mounted() {},
  watch: {},
  methods: {
    open(form) {
      this.visible = true
      this.getLabelSelectList()
      this.getBaseUnit()
      this.getDimTree()
      this.tempPswd = form.pswd || []
    },
    foramtPswd(arr) {
      return arr.map(item => item.zdmc || item.wdzd || item.levelName).join(",")
    },
    // 获取所有维度树
    async getDimTree() {
      const { data } = await this.$httpBi.api.paramPost(
        "/DimManage/getDimList",
        {
          pageSize: -1,
          dimName: "",
          pageNum: 1
        }
      )
      this.allDimTreeList =
        [
          {
            dimName: "自身维度创建",
            definitionCode: "self",
            value: ""
          },
          ...(data.list || [])
        ] || []
    },
    submitForm(formName) {
      let params = this.form
      var Api = null
      if (this.form.lxbm === "yz") {
        Api = this.$httpBi.indicatorAnagement.editYz
      }
      this.$refs[formName].validate(async valid => {
        if (valid) {
          await this.$refs.AffectScopeDialog.isAffectSpace(
            {
              indCode: params.indCode,
              indType: params.lxbm
            },
            async () => {
              const { code } = await Api({
                ind: params,
                optType: "update",
                addDims: this.addDims.map(item => ({
                  ...item,
                  wdlx: item.zdlx
                }))
              })
              if (code === 200) {
                this.$message({
                  message: "修改成功",
                  type: "success"
                })
                this.visible = false
                this.$emit("refresh")
              }
            }
          )
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    handleClose() {
      this.$emit("update:visible", false)
    },
    // 获取层级信息
    async getDimLevels(definitionCode, row) {
      if (!definitionCode) {
        this.$set(row, "definitionCode", "")
        this.$set(row, "noCoverValues", [])
        this.$set(row, "levelCode", "")
        return
      }
      if (definitionCode === "self") {
        await this.createSelfDim(row)
      } else {
        const { data } = await this.$httpBi.api.paramPostQuery(
          "/DimManage/getDimLevelByDefinitionCode",
          { definitionCode }
        )
        if (data.length) {
          if (!row.isDisabled) {
            row.levelCode = data[0].levelCode
            row.levelName = data[0].levelName
          }

          await this.onLevelCodeChange(row)
        } else {
          row.levelCode = ""
          row.levelName = ""
          this.$message.warning("该维度没有层级")
        }
        this.$set(this.dimLevelMap, definitionCode, data)
      }
    },
    async createSelfDim(row) {
      const { data } = await this.$httpBi.api.paramPost("/DimManage/addDim", {
        dimDefinition: {
          categoryCode: "wdlx_jcwd_1930198052817580032",
          dimName: row.wdbm || row.zdmc || row.wdzd,
          description: "",
          version: "v1.0",
          tags: "基于自身创建维度",
          updateFrequency: 1,
          updateType: 0,
          categoryName: "基础维度"
        },
        createType: 0,
        dimLevels: [
          {
            level: 1,
            levelName: row.wdbm || row.zdmc || row.wdzd,
            sourceTable: this.form.tabid || row.id,
            sourceField: row.wdzd,
            fieldValueType: row.sjlx
          }
        ],
        configs: []
      })
      await this.getDimTree()
      this.$set(row, "definitionCode", data.definitionCode)
      this.$set(row, "dimName", data.dimName)
      this.$set(row, "newCreate", true)
      this.$set(row, "version", data.version)
      this.$set(row, "categoryCode", "wdlx_jcwd_1930198052817580032")
      this.$set(row, "tags", "基于自身创建维度")
      this.$set(row, "description", "")
      this.$set(row, "updateFrequency", 1)
      this.$set(row, "updateType", 0)
      await this.getDimLevels(data.definitionCode, row)
      await this.getNoCoverDimValues(row)
    },
    // 选择派生维度
    async change(id, index) {
      this.getPsOption(id)
      const item = {
        ...this.currentDerive.pswd.find(item => item.id === id)
      }
      delete item.id
      this.$set(this.form.xsc, index, {
        ...item,
        adid: id,
        wdzd: this.currentDerive.zddm,
        zdmc: this.currentDerive.zbmc,
        wdzval: [""]
      })
      console.log(this.form.xsc)
    },
    // 获取派生维度
    async getPsOption(id) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionValueById({
          id: id,
          lxbm: "yz"
        })
      // this.pswdOptionsMap[id] = data
      this.$set(this.pswdOptionsMap, id, data)
    },

    // 添加派生维度
    addDerive() {
      if (this.form.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: "暂无派生维度",
          type: "warning"
        })
      }
      this.form.xsc.push({
        adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: "", // getAtomIndicatorList接口返回的zddm字段
        zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
        wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive(item) {
      var index = this.form.xsc.indexOf(item)
      if (index !== -1 && this.form.xsc.length > 1) {
        this.form.xsc.splice(index, 1)
      }
    },
    openEditDeriveDialog: async function () {
      // 1. 先获取已有的 pswd
      const oldPswd = (this.form.pswd || []).map(item => ({
        ...item,
        zdlx: item.wdlx,
        noCoverValues: [],
        newCreate: false,
        isDisabled: true
      }))
      console.log(oldPswd, "oldPswd")
      // 2. 获取新增维度
      let newPswd = []
      const { data: newFields } = await this.$httpBi.api.paramPostQuery(
        "/AtomIndicator/getNewFieldCode",
        { atomIndCode: this.form.indCode }
      )
      newPswd = (newFields || []).map(item => ({
        ...item,
        zdlx: item.wdzd.toUpperCase() === "ID" ? "NULL" : "",
        noCoverValues: [],
        newCreate: false,
        isDisabled: false
      }))
      console.log(newFields, "newFields0")

      console.log(newPswd, "newPswd1")
      // 3. 合并

      // let params = newPswd.map(item => ({
      //   tableSource: item.id || this.form.tabid,
      //   fieldCode: item.zddm || item.wdzd,
      //   fieldName: item.bt || item.zdmc || item.wdzd,
      //   lxbm: "yz"
      // }))
      // // 1. 获取匹配结果
      // const matchResult = await this.matchDim(params)

      // // 2. 合并匹配结果到 newPswd
      // newPswd = newPswd.map((item, idx) => {
      //   const match = matchResult && matchResult[idx]
      //   if (match) {
      //     return {
      //       ...item,
      //       ...match, // 合并匹配到的维度信息
      //       isDisabled: false,
      //       newCreate: false // 标记为新建
      //     }
      //   }
      //   return item
      // })

      // 3. 合并 oldPswd 和 newPswd
      this.deriveTableData = [...oldPswd, ...newPswd]
      console.log(newPswd, "newPswd")
      // // 2. 融合数据
      // this.deriveTableData.forEach(item => {
      //   if (item.definitionCode) {
      //     this.getDimLevels(item.definitionCode, item)
      //   }
      // })
      this.editDeriveDialogVisible = true
    },
    //
    async changeDimType(type, row) {
      if (type === "ps" || type === "time") {
        let params = [
          {
            tableSource: row.id || this.form.tabid,
            fieldCode: row.zddm || row.wdzd,
            fieldName: row.bt || row.zdmc || row.wdzd,
            lxbm: "yz"
          }
        ]
        const [matchResult] = await this.matchDim(params)
        console.log(matchResult, "matchResult")
        this.$set(row, "definitionCode", matchResult.definitionCode)
        this.$set(row, "noCoverValues", matchResult.noCoverValues)
        this.$set(row, "levelCode", matchResult.levelCode)
        this.$set(row, "levelName", matchResult.levelName)
        if (matchResult.definitionCode) {
          this.getDimLevels(matchResult.definitionCode, row)
        }
      }
      console.log(type, row)
    },
    // 匹配相似维度和层级
    async matchDim(params) {
      const { data } = await this.$httpBi.api.paramPost(
        "/AtomIndicator/checkDimMatch",
        params
      )
      return data
    },
    handleDeriveSave() {
      // 校验：如果选择了维度，必须选择对应维度和维度层级
      for (const row of this.deriveTableData) {
        if (!row.zdlx) return this.$message.warning("请选择标记类型")
        if (["ps", "indOrDim", "time"].includes(row.zdlx)) {
          if (!row.definitionCode) {
            this.$message.warning("请选择对应维度")
            return
          }
          if (!row.levelCode) {
            this.$message.warning("请选择维度层级")
            return
          }
        }
      }
      this.tempPswd = this.deriveTableData.filter(item => item.zdlx !== "NULL")
      this.addDims = this.tempPswd.filter(item => !item.isDisabled)
      console.log(this.deriveTableData, "this.deriveTableData")
      console.log(this.addDims)
      this.editDeriveDialogVisible = false
    },
    removeDim(row) {
      this.$confirm("是否删除当前维度树数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$httpBi.api
            .paramDel("/DimManage/deleteDimByCode", {
              code: row.definitionCode
            })
            .then(() => {
              this.$message.success("删除成功")
              this.$set(row, "definitionCode", "")
              this.$set(row, "levelCode", "")
              this.$set(row, "dimName", "")
              this.$set(row, "version", "")
              this.$set(row, "newCreate", false)
              this.$set(row, "levelCode", "")
              this.getDimTree()
            })
        })
        .catch(error => {
          console.error(error)
        })
    },
    editDim(row) {
      this.editRow = row
      this.editDimForm = {
        definitionCode: row.definitionCode || "",
        dimName: row.dimName || "",
        version: row.version || "",
        categoryCode: row.categoryCode || "",
        levelName: row.levelName || "",
        tags: row.tags || "",
        description: row.description || "",
        updateFrequency:
          typeof row.updateFrequency === "undefined" ? 1 : row.updateFrequency,
        updateType: typeof row.updateType === "undefined" ? 0 : row.updateType
      }
      this.editDialogVisible = true
    },
    saveEditDim() {
      this.$refs.editDimForm.validate(async valid => {
        if (valid) {
          await this.$httpBi.api.paramPost("/DimManage/editVersion", {
            ...this.editDimForm
          })
          this.$message.success("编辑成功")
          this.editDialogVisible = false
          if (this.editRow) {
            Object.assign(this.editRow, this.editDimForm)
            this.getDimLevels(this.editRow.definitionCode, this.editRow)
          }
        } else {
          return false
        }
      })
    },
    loadMoreNoCover(row) {
      if (!row.noCoverShowCount) {
        this.$set(row, "noCoverShowCount", 10)
      } else {
        this.$set(row, "noCoverShowCount", row.noCoverShowCount + 5)
      }
    },
    resetNoCoverShowCount(row) {
      this.$set(row, "noCoverShowCount", 5)
    },
    async getNoCoverDimValues(row) {
      console.log(row, "////////")
      // 只有新建维度才请求无覆盖字段
      if (row.isDisabled) return
      const { data } = await this.$httpBi.api.paramPost(
        "/DimManage/getNoCoverDimValues",
        {
          tableSource: row.id || this.form.tabid,
          levelCode: row.levelCode,
          fieldCode: row.wdzd || row.zddm,
          lxbm: "yz"
        }
      )
      this.$set(row, "noCoverValues", data)
    },
    async onLevelCodeChange(row) {
      await this.getNoCoverDimValues(row)
    }
  }
}
</script>

<style scoped lang="scss">
.popover-content {
  max-height: 150px;
  overflow-y: auto;
  // 可选：自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    background: #f5f5f5;
  }
  &::-webkit-scrollbar-thumb {
    background: #cbced1;
    border-radius: 2px;
  }
}
::v-deep .el-scrollbar__wrap {
  max-height: 50vh; // 最大高度
  overflow-x: hidden; // 隐藏横向滚动栏
}
.el-form-item__content {
  display: flex;
}
.form {
  width: 520px;
  margin: 0 auto;
}

// ::v-deep .el-input--small {
//   width: 250px;
// }
::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
