import { prefix } from "../../prefix"
export default [
  // {
  //   name: "themeAnalysisMJJC",
  //   component: () => import("@/views/theme-analysis"),
  //   path: prefix + "/themeAnalysisMJJC"
  // },

  {
    name: "ViewDashboards",
    component: () => import("@/views/theme-analysis/view-dashboards.vue"),
    path: prefix + "/viewDashboards",
    meta: {
          title:"主题场景"
    },
    children: [
      {
        name: "ViewDashboardsView",
        component: () => import("@/views/portal/Portalview"),
        path: prefix + "/viewDashboards/Portalview/:id",
        meta: {
          isEdit: false
        }
      }
    ]
  }
]
