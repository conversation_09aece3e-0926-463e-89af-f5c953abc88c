<template>
  <div :show="pageShow" class="container">
    <header>
      <div class="title">{{ title || '主题' }}</div>
      <div class="btn" v-if="configData.button">
        <el-tooltip
          class="item"
          effect="dark"
          :content="configData.tip"
          placement="top"
        >
          <i class="el-icon-warning-outline icon"></i>
        </el-tooltip>
        <el-button
          type="primary"
          size="mini"
          class="btn"
          plain
          @click="$router.push(configData.path)"
        >
          {{ configData.button }}
        </el-button>
      </div>
    </header>
    <Displays :displays="displays" v-if="displays && displays.length > 0" />
    <Dashboards
      :dashboards="dashboards"
      v-if="dashboards && dashboards.length > 0"
    />
  </div>
</template>

<script>
import Dashboards from "./Dashboards.vue"
import Displays from "./Displays.vue"
import Request from "@/service"

export default {
  components: { Dashboards, Displays },
  props: {},
  data() {
    return {
      pageShow: false,
      title: "",
      dashboards: [],
      displays: [],
      config: null
    }
  },
  computed: {
    configData() {
      if (this.config){
        return JSON.parse(this.config)
      } else {
        return {
          button:null
        }
      }
    }
  },
  created() {
    const theme = this.$route.query.theme
    this.getConfig(theme)
  },
  mounted() {},
  watch: {
    "$route.query.theme"(theme) {
      this.getConfig(theme)
    }
  },
  methods: {
    async getConfig(theme) {
      this.pageShow = false
      this.$dt_loading.show()
      const {
        data: { title, dashboards, displays, config }
      } = await Request.theme.getConfig({
        theme
      })
      this.title = title
      this.dashboards = dashboards
      this.displays = displays
      this.config = config
      // console.log(dashboards, "dashboards")
      const dashboardslist = dashboards.map((item, index) => ({
        ...item,
        icon: "icon_" + ((index + 1) % 20),
        thumbnail: null
      }))
      this.$store.commit("settings/VIEW_DASHBOARDS", dashboardslist)
      this.$dt_loading.hide()
      this.pageShow = true
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  padding: 0 24px 24px;
  background: #fff;
  margin: 15px 16px;
  header {
    width: 100%;
    height: 60px;
    box-shadow: inset 0px -1px 0px 0px #ebedf0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      height: 20px;
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #1d2129;
    }
    .btn {
      .icon {
        margin-right: 16px;
      }
    }
  }
}
</style>
