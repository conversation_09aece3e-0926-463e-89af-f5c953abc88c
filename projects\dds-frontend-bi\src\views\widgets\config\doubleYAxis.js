import ChartTypes from "./ChartTypes"
import {
  PIVOT_DEFAULT_AXIS_LINE_COLOR,
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  CHART_LABEL_POSITIONS,
} from "@/globalConstants"

const doubleYAxis = {
  id: ChartTypes.DoubleYAxis,
  name: "doubleYAxis",
  title: "双Y轴图",
  icon: "doubleYAxis",
  coordinate: "cartesian",
  rules: [ { dimension: 1, metric: [ 2, 9999 ] } ],
  dimetionAxis: "col",
  data: [
    {
      title: "维度",
      type: "category",
      name: "cols",
    },
    {
      title: "左指标",
      type: "value",
      name: "metrics",
    },
    {
      title: "右指标",
      type: "value",
      name: "secondaryMetrics",
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '颜色',
    //   type: 'category',
    //   name: "color"
    // },
    // {
    //   title: '筛选',
    //   type: 'all',
    //   name: "filters"

    // }
  ],
  style: {
    spec: {
      stack: false,
      smooth: false,
      step: false,
      symbol: true,
      showDataZoom: false,
      endValue: null,
    },
    doubleYAxis: {
      yAxisLeft: "line",
      yAxisRight: "bar",
      yAxisSplitNumber: 5,
      dataZoomThreshold: 0,
      showLine: true,
      lineStyle: "solid",
      lineSize: "1",
      lineColor: PIVOT_DEFAULT_AXIS_LINE_COLOR,
      showLabel: true,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: "12",
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
    },
    xAxis: {
      showLine: true,
      lineStyle: "solid",
      lineSize: "1",
      lineColor: PIVOT_DEFAULT_AXIS_LINE_COLOR,
      showLabel: true,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: "12",
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
      xAxisInterval: 0,
      xAxisRotate: 0,
    },
    splitLine: {
      showHorizontalLine: true,
      horizontalLineStyle: "dashed",
      horizontalLineSize: "1",
      horizontalLineColor: PIVOT_DEFAULT_AXIS_LINE_COLOR,
      showVerticalLine: false,
      verticalLineStyle: "dashed",
      verticalLineSize: "1",
      verticalLineColor: PIVOT_DEFAULT_AXIS_LINE_COLOR,
    },
    legend: {
      showLegend: true,
      legendPosition: "right",
      right: 0,
      top: 0,
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR,
      orient: 'horizontal',
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect'
    },
    grid: {
      top: 60,
      bottom: 60,
      left: 20,
      right: 20,
      containLabel: true,
    },
    label: {
      showLabel: false,
      labelPosition: CHART_LABEL_POSITIONS[0].value,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: "12",
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
    },
  },
}

export default doubleYAxis
