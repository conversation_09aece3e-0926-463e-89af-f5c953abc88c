<template>
  <components
    :is="layer.params.component"
    :key="layer.width + layer.height"
    :color="[layer.params.mainColor, layer.params.viceColor]"
    :background-color="layer.params.backgroundColor"
    :dur="layer.params.dur"
  ></components>
</template>

<script>
export default {
  name: "border-image-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
