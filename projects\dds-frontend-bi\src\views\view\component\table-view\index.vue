<template>
  <dt-single-page-view
    :inner-style="{ textAlign: 'left', height: '100vh' }"
    :style="{ height: '100vh', padding: 0 }"
  >
    <el-header>
      <TableViewHead
        first-step-title="表视图"
        :id="form.id"
        :edit-step="editStep"
      />
    </el-header>
    <div style="display: flex" v-show="editStep === 1">
      <LeftSide
        :form="form"
        ref="leftSide"
        :drag-tables="dragTables"
        @changeSourceDb="changeSourceDb"
        @onDragEnd="handleDragEnd"
        @onDragStart="handleDragstart"
      />
      <div class="sql-view" @drop="drop" @dragover="dragover" ref="sqlView">
        <div class="table-links-wrapper" ref="dragContainer">
          <TableWrapper
            @fieldInfo="fieldInfo"
            @handleRelate="handleRelate"
            @drop="drop"
            @dragover="dragover"
            @delDropBox="delDropBox"
            root
            :dargging="dargging"
            :tree="tree"
            v-for="(item, index) in tree"
            :item="item"
            :index="index"
            :key="index"
          />
          <div
            v-if="!dragTables.length"
            class="empty-table-tips-wrapper"
            :class="{ dargging: dargging }"
          >
            <div class="empty-table-tips-wrapper">
              <div class="empty-table-tips">请从左侧拖拽数据表开始创建</div>
            </div>
          </div>
          <div class="table-link-help-wrapper" v-if="dragTables.length == 1">
            <div class="table-link-help-title">
              继续从左侧拖拽数据表进行表关联
            </div>
          </div>
        </div>
        <div style="height: 200px; width: 100%">
          <sql_result
            :is-running="isRunning"
            :sql-result="sqlResult"
            :sql-columns="sqlColumns"
          />
        </div>
        <el-row style="margin-top: 15px" class="footer">
          <el-col :span="12">
            展示前
            <el-input-number
              @change="runSql"
              @blur="runSql"
              v-model="pageSize"
              :step="10"
              :disabled="!isRunOver"
              step-strictly
              :min="1"
              :max="30000"
            />
            条数据 (最大值30000)
          </el-col>
          <el-col :span="4">
            是否支持预警
            <el-switch
              v-model="form.isSimple"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-col>

          <el-col :span="8" style="text-align: right">
            <el-button @click="exportExcel">导出</el-button>
            <el-button @click="goBack">取消</el-button>
            <el-button @click="runSql">执行</el-button>
            <el-button
              @click="nextStep"
              :disabled="!isRunOver"
              :type="isRunOver ? 'success' : 'info'"
            >
              下一步
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-drawer title="我是标题" :visible.sync="drawer" :with-header="false">
        <div class="drawer-content">
          <el-table
            height="500"
            ref="multipleTable"
            :data="fieildData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column
              prop="name"
              label="字段"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="comment"
              label="备注"
              width="120"
            ></el-table-column>
          </el-table>
        </div>
      </el-drawer>

      <el-drawer
        title="我是标题"
        size="40%"
        :visible.sync="relateDrawer"
        :with-header="false"
        :wrapper-closable="false"
      >
        <RelateDrawer
          ref="relateDrawer"
          @handleSelectionChangeRight="handleSelectionChangeRight"
          @handleSelectionChangeLeft="handleSelectionChangeLeft"
          :source-table-info="sourceTableInfo"
          :target-table-info="targetTableInfo"
          @changeRelate="changeRelate"
          @addRelate="addRelate"
          @removeRelate="removeRelate"
          @changeRelateType="changeRelateType"
          @handleSave="handleSave"
          @cancelForm="cancelForm"
        />
      </el-drawer>
    </div>
    <!--第二步-->
    <el-container v-show="editStep == 2">
      <el-main>
        <el-tabs v-model="activeName">
          <el-tab-pane label="模型" name="model">
            <ModelPane :model-list="modelList"></ModelPane>
          </el-tab-pane>
          <el-tab-pane label="权限" name="auth">
            <auth-pane
              v-if="activeName == 'auth'"
              :all-columns="modelList"
              :author-variable="authorVariable"
              :roles.sync="roles"
            ></auth-pane>
          </el-tab-pane>
        </el-tabs>
      </el-main>
      <el-footer>
        <el-row style="text-align: right; margin-top: 15px">
          <el-button @click="lastStep" type="success">上一步</el-button>
          <el-button @click="goBack">取消</el-button>
          <el-button @click="submitFrom">保存</el-button>
        </el-row>
      </el-footer>
    </el-container>
  </dt-single-page-view>
</template>

<script>
import TableViewHead from "../header_step.vue"
import TableWrapper from "./TableWrapper"
import LeftSide from "./LeftSide.vue"
import RelateDrawer from "./RelateDrawer.vue"
import encryptSql from "@/utils/encryptSql.js"
import sql_result from "../sql_result.vue"
import ModelPane from "../model_pane.vue"
import AuthPane from "../auth_pane.vue"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
export default {
  components: {
    TableViewHead,
    TableWrapper,
    LeftSide,
    RelateDrawer,
    sql_result,
    AuthPane,
    ModelPane
  },
  props: {},
  data() {
    return {
      title: "新增",
      form: {
        id: null,
        name: "",
        description: "",
        sourceId: "",
        isVisc: 1,
        code: "",
        sql: "",
        variable: "",
        model: "",
        config: "",
        groupId: 0,
        roles: []
      },
      variable: [],
      model: "",
      activeName: "model",
      authorVariable: [],
      roles: [],
      modelList: [],
      editStep: 1,
      pageSize: 10,
      isSimple: 0,
      isRunOver: false,
      sqlResult: [],
      sqlColumns: [],
      isRunning: false,
      drawer: false,
      relateDrawer: false,
      sourceName: "",
      sourceId: "",
      dragged: null,
      dargging: false,
      tree: [],
      dragTables: [],
      fieildData: [],
      curTableInfo: null,
      sourceTableInfo: null,
      targetTableInfo: null,
      targetTableInfoCopy: null,
      sourceTableInfoCopy: null
    }
  },
  computed: {},
  created() {
    this.id = this.$route.query.id || null
    if (this.id) {
      this.initForm()
      this.title = "编辑"
    } else {
      this.form.groupId = this.$route.query.group
      this.title = "新增"
    }
  },
  mounted() {},
  watch: {
    dragTables: {
      handler(val) {
        this.tree = this.arrayToTree(val)
        console.log(this.tree, "tree")
      },
      deep: true
    },
    "targetTableInfo.fields": {
      handler(val) {
        this.$nextTick(() => {
          this.$refs.relateDrawer.toggleSelection(val, "targetTable")
        })
      },
      deep: true
    },
    "sourceTableInfo.fields": {
      handler(val) {
        this.$nextTick(() => {
          this.$refs.relateDrawer.toggleSelection(val, "sourceTable")
        })
      },
      deep: true
    }
  },
  methods: {
    // 删除
    delDropBox(item) {
      // 从dragTables删除item
      const index = this.dragTables.findIndex(table => table.name === item.name)
      this.dragTables.splice(index, 1)
      if (item.children.length) {
        item.children.forEach(child => {
          this.delDropBox(child)
        })
      }
      this.handleSave()
    },
    // 表单组确认
    async submitFrom() {
      this.form["variable"] = JSON.stringify(this.variable)
      this.form["config"] = ""
      let model = {}
      for (let i = 0; i < this.modelList.length; i++) {
        model[this.modelList[i].name] = {
          alias: this.modelList[i].alias,
          sqlType: this.modelList[i].sqlType,
          visualType: this.modelList[i].visualType,
          modelType: this.modelList[i].modelType
        }
      }
      this.form["model"] = JSON.stringify(model)
      this.form["source"] = {}
      this.form["roles"] = []

      for (let i = 0; i < this.roles.length; i++) {
        this.form["roles"].push({
          roleId: this.roles[i].roleId,
          columnAuth: JSON.stringify(this.roles[i].columnAuth),
          rowAuth: JSON.stringify(this.roles[i].rowAuth)
        })
      }

      // 加密sql
      this.form.sql = encryptSql.encrypt(this.form.sql)
      this.form.config = JSON.stringify(this.tree)
      console.log(this.form, "form")
      if (this.form.id) {
        // 修改
        await this.$httpBi.view.update(this.form)
        this.$message.success("操作成功")
        this.$router.go(-1)
      } else {
        // 新增
        await this.$httpBi.view.create(this.form)
        this.$message.success("操作成功")
        this.$router.go(-1)
      }
    },
    // 上一步
    lastStep() {
      this.editStep = 1
      this.modelList = []
    },
    // 返回列表页
    goBack() {
      this.$router.go(-1)
    },
    // 请求表单数据
    initForm() {
      this.$httpBi.view
        .getOne({ id: this.id })
        .then(res => {
          this.form = { ...res.data, groupId: res.data.groupId.toString() }
          this.dragTables = this.treeToArray(JSON.parse(res.data.config))
          console.log(this.form, "  this.form")
          this.model = res.data.model
          this.variable = JSON.parse(res.data.variable)
          this.roles = []
          for (let i = 0; i < res.data.roles.length; i++) {
            this.roles.push({
              roleId: res.data.roles[i].roleId,
              columnAuth: JSON.parse(res.data.roles[i].columnAuth),
              rowAuth: JSON.parse(res.data.roles[i].rowAuth)
            })
          }
          this.isRunOver = true
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    cancelForm() {
      if (this.targetTableInfoCopy && this.sourceTableInfoCopy) {
        this.relateDrawer = false
        // 取消还原
        this.targetTableInfo = { ...this.targetTableInfoCopy }
        this.sourceTableInfo = { ...this.sourceTableInfoCopy }
        this.dragTables.find(item => {
          if (item.id === this.targetTableInfo.id) {
            item = this.targetTableInfo
          } else if (item.id === this.sourceTableInfo.id) {
            item = this.sourceTableInfo
          }
        })
      } else {
        this.relateDrawer = false
        this.delDropBox(this.targetTableInfo)
        // 删除
      }
    },
    async handleSave() {
      this.$nextTick(async () => {
        const params = {
          model: "visc",
          tables: this.tree[0]
        }
        const { data } = await this.$httpBi.view.getSql(params)
        console.log(data, "data")
        this.form.sql = data
        this.relateDrawer = false
        this.targetTableInfoCopy = null
        this.sourceTableInfoCopy = null
      })
    },
    async runSql() {
      this.isRunning = true
      const params = {
        sourceId: this.sourceId,
        sql: encryptSql.encrypt(this.form.sql),
        variables: [],
        pageNo: 1,
        pageSize: this.pageSize
      }
      const res = await this.$httpBi.view.runsql(params)
      this.sqlColumns = res.data.columns
      this.sqlResult = res.data.resultList
      this.isRunning = false
      this.isRunOver = true
    },
    handleSelectionChangeLeft(val, tableInfo) {
      this.dragTables.find(item => item.name === tableInfo.name).fields = val
      this.sourceTableInfo.fields = val
    },
    handleSelectionChangeRight(val, tableInfo) {
      this.dragTables.find(item => item.name === tableInfo.name).fields = val
      this.targetTableInfo.fields = val
    },
    // 关联字段的更改
    changeRelate(tableInfo, val, key, index) {
      this.dragTables.find(item => item.name === tableInfo.name).ref[index][
        key
      ] = val
      this.targetTableInfo.ref[index][key] = val
    },
    // 改变关联类型
    changeRelateType(val) {
      this.dragTables.find(item => item.name === this.targetTableInfo.name).c =
        val
      this.targetTableInfo.c = val
    },
    // 添加关联字段
    addRelate() {
      this.dragTables
        .find(item => item.name === this.targetTableInfo.name)
        .ref.push({
          fy: null, // --自身字段
          fr: null // --父级字段
        })
    },
    // 删除关联字段
    removeRelate(item) {
      const index = this.targetTableInfo.ref.indexOf(item)
      if (index !== -1 && this.targetTableInfo.ref.length > 1) {
        this.dragTables
          .find(item => item.name === this.targetTableInfo.name)
          .ref.splice(index, 1)
        this.targetTableInfo.ref.splice(index, 1)
      }
    },
    // 点击关系icon
    handleRelate(item) {
      this.targetTableInfoCopy = { ...item }
      console.log(this.targetTableInfoCopy, "   this.targetTableInfoCopy ")
      this.relateDrawer = true
      this.targetTableInfo = item
      const sourceName = item.source
      this.sourceTableInfo = this.dragTables.find(
        item => item.name === sourceName
      )
      this.sourceTableInfoCopy = { ...this.sourceTableInfo }
    },
    //
    handleSelectionChange(val) {
      console.log(val, "val")
      this.dragTables.find(
        item => item.name === this.curTableInfo.name
      ).fields = val
      console.log(this.dragTables, "this.dragTables")
      this.curTableInfo.fields = val
    },
    // 数据源选择
    changeSourceDb(sourceId, sourceName) {
      this.sourceId = sourceId
      this.sourceName = sourceName
    },
    // 点击查看字段详情
    fieldInfo(item) {
      this.curTableInfo = item
      this.fieildData = item.allFields
      this.drawer = true
      this.$nextTick(() => {
        this.toggleSelection(item.fields)
      })
    },
    // 默认选中
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },

    // 获取表字段
    async getTableFields(tableName) {
      const { data } = await this.$httpBi.view.getColumns({
        id: this.sourceId,
        dbName: this.sourceName,
        tableName: tableName
      })
      const allFields = data.columns ?? []
      const tableInfo = this.dragTables.find(item => item.name === tableName)
      tableInfo.allFields = allFields

      // 如果是根节点，直接赋值
      if (this.dragTables.length === 1) {
        console.log(this.dragTables, "dragTables")
        tableInfo.fields = allFields
        this.handleSave()
        return
      } else {
        tableInfo.fields = allFields
        const sourceName = tableInfo.source
        this.sourceTableInfo = this.dragTables.find(
          item => item.name === sourceName
        )
        this.targetTableInfo = tableInfo
        this.relateDrawer = true
      }
    },
    // 拖拽开始
    handleDragstart(data) {
      const index = this.dragTables.findIndex(item => item.name === data.name)
      if (index > -1) {
        this.dargging = false
        return this.$message.warning("当前数据表已经存在，请不要重复关联哦")
      }
      this.dragged = data
      this.dargging = true
      console.log(this.dragged, "拖拽开始")
    },
    // 拖拽结束
    handleDragEnd() {
      console.log("拖拽结束")
      if (this.dragged) {
        this.dragged = null
      }
      this.dargging = false
    },
    // 允许放下拖拽
    dragover(e) {
      // const width = this.$refs.dragContainer.clientWidth;
      // if (e.clientY > width - 20) {
      //   this.$refs.dragContainer.scrollLeft -= 10;
      // }
      e.preventDefault()
    },
    // 放下事件
    async drop(e, source) {
      if (!this.dargging) return
      e.preventDefault()
      // 如果已经拖拽了根元素就不能再拖拽了
      if (this.dragTables.length && !source) {
        return
      }

      this.dragTables.push({
        name: this.dragged.name,
        source,
        target: this.dragged.name,
        allFields: [], // --所有字段
        fields: [], // --已选表字段
        c: "left", // --连接方式
        ref: [
          {
            fy: null, // --自身字段
            fr: null // --父级字段
          }
        ] // --关联字段
      })
      this.getTableFields(this.dragged.name)
    },
    // 数组转树
    arrayToTree(
      items,
      idKey = "target",
      pidKey = "source",
      childKey = "children"
    ) {
      const result = [] // 存放结果集
      const itemMap = {} //
      for (const item of items) {
        itemMap[item[idKey]] = { ...item, [childKey]: [] }
      }
      console.log(itemMap, "itemMap")
      for (const item of items) {
        const id = item[idKey]
        const pid = item[pidKey]
        const treeItem = itemMap[id]

        if (!itemMap[pid]) {
          result.push(treeItem)
        } else {
          itemMap[pid][childKey].push(treeItem)
        }
      }
      return this.renderTree(result, 0)
    },
    // 树转数组
    treeToArray(data) {
      return data.reduce((pre, item) => {
        if (item.children) {
          pre.push(item)
          pre.push(...this.treeToArray(item.children))
        } else {
          pre.push(item)
        }
        return pre
      }, [])
    },
    exportExcel() {
      jsonToSheetXlsx({
        data: this.sqlResult,
        filename: "表数据" + new Date().getTime()
      })
    },
    submitForm() {
      console.log(this.tree, "this.tree")
      const sql = this.renderSql(this.tree)
      console.log(sql, "sql")
    },
    renderTree(data, level) {
      return data.map(item => {
        if (item.children) {
          this.renderTree(item.children, level + 1)
        }
        item.level = level
        return item
      })
    },
    // 下一步
    nextStep() {
      this.$refs.leftSide.validate(valid => {
        if (!valid) return
        this.editStep = 2
        // 模型处理
        if (this.sqlColumns.length > 0) {
          // 新解析的
          let modelKeys = []
          let objModel = []
          if (this.form.id && this.model !== "") {
            objModel = JSON.parse(this.model)
            modelKeys = Object.keys(objModel)
          }
          for (let i = 0; i < this.sqlColumns.length; i++) {
            // 数值类型待补充
            let numberType = ["INT", "DECIMAL"]
            let isNumber = numberType.indexOf(this.sqlColumns[i].type) > -1
            let name = this.sqlColumns[i].name
            if (modelKeys.indexOf(name) > -1) {
              this.modelList.push({
                name: name,
                alias: objModel[name].alias,
                sqlType: objModel[name].sqlType,
                modelType: objModel[name].modelType,
                visualType: objModel[name].visualType
              })
            } else {
              this.modelList.push({
                name: name,
                sqlType: this.sqlColumns[i].type,
                modelType: isNumber ? "value" : "category",
                visualType: isNumber ? "number" : "string"
              })
            }
          }
        } else {
          // 取原有的
          let objModel = JSON.parse(this.model)
          for (const x in objModel) {
            let md = {
              name: x,
              sqlType: objModel[x].sqlType,
              modelType: objModel[x].modelType,
              visualType: objModel[x].visualType
            }
            this.modelList.push(md)
          }
        }
        this.authorVariable = []
        // 权限变量
        for (const x in this.variable) {
          if (this.variable[x].type === "auth") {
            this.authorVariable.push(this.variable[x])
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.list {
  width: 300px;
  padding: 50px;
  border: 1px solid #ccc;

  .list-item-custom {
    width: 200px;
    height: 30px;
    background-color: skyblue;
    margin: 10px 0;
    line-height: 30px;
    cursor: move;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.sql-view {
  flex: 1;
  width: calc(100% - 300px);
  height: calc(100vh - 110px);
  box-sizing: border-box;
  border: 1px solid #d2d2d3;
  border-left: none;
  text-align: center;

  .table-links-wrapper {
    position: relative;
    width: 100%;
    overflow: auto;
    height: calc(100% - 260px);
    padding: 24px;
    box-sizing: border-box;
    background-color: #f2f2f2;

    .table-wrapper {
      .table-link-tip {
        position: absolute;
        align-items: center;
        display: flex;
        justify-content: center;
        cursor: pointer;

        .table-link-line {
          position: absolute;
          z-index: 2;
          background: #c1c1c1;
        }

        .table-link-line.vertical {
          width: 1px;
          left: 18px;
        }

        .table-link-line.horizontal {
          right: 0;
          height: 1px;
        }
      }

      .table-info {
        height: 28px;
        width: 180px;
        background: #fff;
        position: absolute;
        color: #000;
        cursor: pointer;
        padding: 1px 1px 1px 0;
        line-height: 28px;
        border-left: 2px solid #2153d4;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .empty-table-tips-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      border: 1px solid transparent;
      color: rgba(0, 0, 0, 0.65);

      &.dargging {
        border: 1px dashed #c6c6c6;
        background-color: #f8f8f8;
      }
    }

    .table-link-help-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
</style>
