<template>
  <el-date-picker
    v-model="resetValue"
    :type="isMultiple ? 'dates' : 'date'"
    @change="change"
    placeholder="选择日期"
    :value-format="dateFormat"
    size="mini"
    :format="dateFormat"
  >
  </el-date-picker>
</template>

<script>
import dayjs from "dayjs"
export default {
  components: {},
  props: {
    value: {
      type: [ String, Object , Array ],
    },
    dateFormat: {
      type: String,
    },
    isMultiple: {
      type: Boolean,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {
      resetValue:null
    }
  },
  computed: {
  },
  created() {},
  mounted() {},
  watch: {
    value: {
      immediate:true,
      handler(val, ) {
        if (typeof val === "string" || val instanceof Array || !val) {
          return (this.resetValue = val)
        } else {
          const { type, value, valueType } = val
          if (valueType === "prev") {
            return (this.resetValue = dayjs()
              .subtract(value, type)
              .format(this.dateFormat.toUpperCase()))
          } else {
            return (this.resetValue = dayjs()
              .add(value, type)
              .format(this.dateFormat.toUpperCase()))
          }
        }
      },
    },
  },
  methods: {
    change() {
      this.$emit("update:value", this.resetValue)
      this.$emit("change", { [this.item.key]: this.resetValue })
    },
  },
}
</script>

<style scoped lang="less">
</style>
