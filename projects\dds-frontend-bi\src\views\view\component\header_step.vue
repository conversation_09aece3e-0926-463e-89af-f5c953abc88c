<template>
  <el-row>
    <el-col style="width: 150px">
      <el-page-header @back="goBack" :content="id == null ? '新增' : '修改'" />
    </el-col>
    <el-col :offset="4" :span="10">
      <el-steps :active="editStep">
        <el-step :title="firstStepTitle" icon="el-icon-edit"></el-step>
        <el-step title="模型与权限" icon="el-icon-s-custom"></el-step>
      </el-steps>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "header-step",
  props: {
    editStep: {
      type: Number,
      default: 1,
    },
    id: {
      type: String,
      default: null,
    },
    firstStepTitle: {
      type: String,
      default: "SQL",
    },
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>
