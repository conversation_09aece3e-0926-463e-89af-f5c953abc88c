<template>
  <el-dialog
    title="字段设置"
    :visible="fieldModalVisible"
    :show-close="false"
    width="40%"
  >
    <el-form ref="itemForm" :model="itemForm">
      <el-form-item label="别名">
        <el-input v-model="itemForm.alias" size="mini" clearable />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="fieldModalVisible = false">取消</el-button>
      <el-button
        type="primary"
        size="mini"
        @click="handleSumbit"
      >
        提交
      </el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      fieldModalVisible:false,
      itemForm:{}
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleSumbit(){
      this.$emit('onSaveField',this.itemForm)
    }
  },
}
</script>

<style scoped lang="less">
</style>
