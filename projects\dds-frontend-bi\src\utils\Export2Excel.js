import * as xlsx from "xlsx"

const { utils, writeFile } = xlsx

// 自适应宽度，支持 header
function autoWidthFunc(ws, data, header) {
  // 合并 header 和 data 参与宽度计算
  const allRows = header ? [header, ...data] : data
  const colWidth = allRows.map((row) =>
    Object.values(row).map((val) => {
      if (val == null) {
        return { wch: 10 }
      } else if (val.toString().charCodeAt(0) > 255) {
        return { wch: val.toString().length * 2 }
      } else {
        return { wch: val.toString().length }
      }
    })
  )
  const result = colWidth[0]
  for (let i = 1; i < colWidth.length; i++) {
    for (let j = 0; j < colWidth[i].length; j++) {
      if (result[j].wch < colWidth[i][j].wch) {
        result[j].wch = colWidth[i][j].wch
      }
    }
  }
  ws["!cols"] = result
}

// 文件名补全 .xlsx 后缀
function normalizeFileName(filename) {
  return filename.endsWith('.xlsx') ? filename : filename + '.xlsx'
}

// 导出多个sheet的Excel
export function exportSheetExcel({ data, filename, autoWidth = true }) {
  const wb = utils.book_new()
  data.forEach((item) => {
    let ws = utils.json_to_sheet(item.sheetData)
    if (autoWidth) {
      autoWidthFunc(ws, item.sheetData, item.header)
    }
    utils.book_append_sheet(wb, ws, item.sheetName)
  })
  writeFile(wb, normalizeFileName(filename))
}

// 通用导出 JSON/数组 到 Excel
function exportXlsx({
  data,
  filename = "DEF_FILE_NAME",
  header,
  json2sheetOpts = {},
  write2excelOpts = { bookType: "xlsx" },
  type = 'json', // 'json' or 'aoa'
  autoWidth = true
}) {
  let arrData = [ ...data ]
  let worksheet
  if (header) {
    if (type === 'json') {
      arrData.unshift(header)
      json2sheetOpts.skipHeader = true
      worksheet = utils.json_to_sheet(arrData, json2sheetOpts)
    } else {
      arrData.unshift(header)
      worksheet = utils.aoa_to_sheet(arrData)
    }
  } else {
    worksheet = type === 'json' ? utils.json_to_sheet(arrData, json2sheetOpts) : utils.aoa_to_sheet(arrData)
  }
  if (autoWidth) {
    autoWidthFunc(worksheet, data, header)
  }
  const workbook = {
    SheetNames: [ filename ],
    Sheets: {
      [filename]: worksheet,
    },
  }
  writeFile(workbook, normalizeFileName(filename), write2excelOpts)
}

// 导出JSON数据
export function jsonToSheetXlsx(opts) {
  exportXlsx({ ...opts, type: 'json' })
}

// 导出二维数组数据
export function aoaToSheetXlsx(opts) {
  exportXlsx({ ...opts, type: 'aoa' })
}
