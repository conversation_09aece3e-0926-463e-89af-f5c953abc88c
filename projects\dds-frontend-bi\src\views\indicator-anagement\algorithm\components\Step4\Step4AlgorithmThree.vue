<template>
  <div style="padding-bottom: 60px">
    <Step
      :steps="steps"
      :current-step="currentStep"
      style="margin: 32px auto 40px"
    />
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="form"
    >
      <el-form-item label="指标名称" prop="zbmc">
        <el-input v-model="form.zbmc" clearable></el-input>
      </el-form-item>
      <el-form-item label="时间维度" prop="sjwd">
        <el-select v-model="form.sjwd" placeholder="请选择时间维度">
          <el-option
            v-for="(item, index) in sjdwList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计算周期" prop="jszq">
        <el-select v-model="form.jszq" placeholder="请选择计算周期">
          <el-option
            v-for="(item, index) in jszqList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="精度" prop="jd">
        <el-row type="flex" gutter="10">
          <el-col>
            <el-input
              v-model="form.jd"
              placeholder="仅支持输入整数，数值则代表小数点的位数"
            ></el-input>
          </el-col>
          <el-col>
            <el-checkbox v-model="form.sswr" true-label="1" false-label="0">
              四舍五入
            </el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="所属指标域">
        <avue-input-tree
          default-expand-all
          v-model="form.sysjy"
          :props="{
            label: 'name',
            value: 'id'
          }"
          placeholder="请选择所属指标域"
          :dic="viewGroup"
        ></avue-input-tree>
      </el-form-item>
      <el-form-item label="设置阈值">
        <template #label>
          <span
            style="
              display: flex;
              align-items: center;
              justify-content: flex-end;
            "
          >
            设置阈值
            <el-tooltip class="item" effect="dark" content="" placement="top">
              <div slot="content">
                1. 如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                <br />
                2. 如果不填写最大值，仅填写最小值，则表示大于等于最小值。
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </span>
        </template>
        <div style="display: flex">
          <el-form-item prop="tvmin" style="margin-bottom: 0">
            <el-input
              v-model="form.tvmin"
              placeholder="请输入最小值"
              style="width: 122px"
            ></el-input>
          </el-form-item>
          -
          <el-form-item style="margin-bottom: 0">
            <el-input
              v-model="form.tvmax"
              style="width: 122px"
              placeholder="请输入最大值"
            ></el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="单位" prop="jldw">
        <div style="display: flex">
          <el-select
            v-model="form.jldw"
            placeholder="请选择单位"
            :style="{ width: form.jldw === '其他' ? '122px' : '250px' }"
            class="myselect"
          >
            <el-option
              v-for="(item, index) in dwList"
              :key="index"
              :label="item.name"
              :value="item.bm"
            ></el-option>
          </el-select>
          <el-input
            v-if="form.jldw === '其他'"
            v-model="form.diydw"
            style="width: 122px; margin-left: 6px"
            placeholder="请输入单位"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="form.ms" placeholder="请输入描述"></el-input>
      </el-form-item>
      <!-- <el-form-item label="标签">
        <el-input v-model="form.bq" placeholder="请输入标签"></el-input>
      </el-form-item> -->
      <el-form-item label="标签" prop="bq">
        <el-select
          v-model="form.bq"
          filterable
          multiple
          remote
          allow-create
          default-first-option
          @change="changeTag"
          @remove-tag="removeTag"
          :remote-method="remoteMethod"
          placeholder="请创建或者选择标签"
        >
          <el-option
            v-for="item in formatLabels"
            :key="item.bqmc"
            :label="item.bqmc"
            :value="item.bqmc"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          size="small"
          @click="$emit('update:currentStep', currentStep - 1)"
        >
          上一步
        </el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="false"
          @click="$emit('update:currentStep', currentStep + 1)"
        >
          保存指标
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import Step from "../Step"
import options from "../../../mixins/options"

export default {
  components: { Step },
  mixins: [options],
  props: {
    currentStep: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var changeZdmc = async (rule, value, callback) => {
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode:""
        })
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === "") {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        callback(new Error("最小值不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === "") {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error("最大值不能小于最小值"))
      } else {
        callback()
      }
    }
    return {
      viewGroup: [{ id: 0, name: "根目录", children: [] }], // 数据域分组
      yzzbList: [],
      dic: [],
      radio: "1",
      form: {
        atomid: "",
        zbmc: "",
        zblx: "派生指标",
        lxbm: "ps",
        zbymc: "",
        jsfs: "",
        sorttype: "asc",
        sortrange: "all",
        sortlimit: "",
        diydw: "",
        xsc: [
          {
            adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
            atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzd: "", // getAtomIndicatorList接口返回的zddm字段
            zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
            wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
            wdzval: [] // 用户选择的维度值
          }
        ],
        tvmin: "",
        tvmax: "",
        sysjy: 0,
        jszq: "",
        jd: 0,
        sswr: "",
        bq: [],
        ms: "",
        cjr: ""
      },
      rules: {
        zbmc: [
          { required: true, message: "请输入指标名称", trigger: "blur" },
          { max: 20, message: "最大为20个字符", trigger: "blur" },
          { validator: changeZdmc, trigger: "blur" }
        ],
        tvmin: { validator: changeMin, trigger: "blur" },
        tvmax: { validator: changeMax, trigger: "blur" },
        atomid: { required: true, message: "请选择原子指标", trigger: "blur" },
        jsfs: { required: true, message: "请选择计算方式", trigger: "blur" },
        sjwd: { required: true, message: "请选择时间维度", trigger: "blur" },
        jszq: { required: true, message: "请选择计算周期", trigger: "blur" },
        jd: { required: true, message: "请输入精度", trigger: "blur" }
      },
      labels: [],
      newTag: "", // 新建标签
      tempTag: "", // 临时存储标签
      pswdOptionsMap: {}
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="scss">
.el-button--primary {
  background: #1563ff;
  border-color: #1563ff;
  &.is-disabled {
    color: #fff;
    background-color: #a0cfff;
    border-color: #a0cfff;
    &:hover {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
    }
  }
  &:hover {
    background-color: rgba(64, 128, 255, 1);
    border-color: rgba(64, 128, 255, 1);
  }
}
.form {
  width: 500px;
  margin: 0 auto;
}

::v-deep .el-input--small {
  width: 250px;
}

::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
