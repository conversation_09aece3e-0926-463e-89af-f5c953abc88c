<template>
  <div class="portal-main">
    <div class="portalBody" :style="{ backgroundColor: currentTheme.bg }">
      <div class="gridClass">
        <div class="grid-head">
          <div class="title">{{ currentDashborad.name }}</div>
        </div>
        <div
          class="GolobalPanel"
          :style="{ backgroundColor: currentTheme.bg }"
          v-if="filters && filters.length"
        >
          <GolobalPanelComponent
            ref="GolobalPanel"
            :controls.sync="filters"
            :query-mode="queryMode"
            :select-options="globalSelectOptions"
            @change="getFormValuesRelatedItems"
            @reset="handleResetGlobalFilters"
            type="golobal"
          />
        </div>

        <div class="dashboardList">
          <Dashboard
            :only-view="true"
            ref="dashboards"
            :is-authorized="directory.roleIds"
            :global-controls="filters"
            :query-mode="queryMode"
            :widgets="widgets"
            :form-values-related-items="FormValuesRelatedItems"
            :form-values-related-items-all="FormValuesRelatedItemsAll"
            :current-dashborad="currentDashborad"
            @onReload="getWidgetsConfig"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Dashboard from "@/views/dashboard/index1.vue"
import ControlPanelComponent from "@/components/Control/Control"
import { mapGetters,  } from "vuex"
export default {
  components: {
    Dashboard,
    GolobalPanelComponent: ControlPanelComponent,
  },
  props: {
    selectedChartId: {
      type: Number,
      default: 0,
    },
    Drillconfig: {
      type: Object,
      default: () => {},
    },
    chartParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentDashborad: {},
      filters: [],
      queryMode: 0, // 0:立即模式，1：手动模式
      isShowControlPanel: false,
      addWidgetVisible: false, // 新增
      controlConfigVisible: false, // 控制器
      dialogShow: false, // 新增/编辑 portal  dialog
      filterText: "", // 过滤
      isExpandAll: true, // 是否展开全部tree
      refreshTree: true, 
      widgetList: [],
      portalId: "",
      formType: "",
      activeName: "info",
      currentPortal: {
        description: "",
        name: "",
      },
      directory: {
        config: { isShowWatermark: true },
        dashboardPortalId: 0,
        index: 0,
        name: "",
        parentId: 0,
        type: 0,
        roleIds: false,
      },
      rules: {
        name: [ { required: true, message: "请输入名称", trigger: "blur" } ],
        type: [ { required: true, message: "请选择目录", trigger: "change" } ],
      },
      treeList: [],
      globalSelectOptions: {},
      FormValuesRelatedItems: [],
      FormValuesRelatedItemsAll: [],
      originalFilters: [],
    }
  },
  computed: {
    ...mapGetters({
      currentTheme: "currentTheme",
    }),
  },
  created() {
    if (this.$route.query.config) {
      const { selectedChartId, Drillconfig, chartParams } = JSON.parse(
        this.$route.query.config
      )
      this.selectedChartId = selectedChartId
      this.Drillconfig = Drillconfig
      this.dashboardId = Drillconfig.target
      this.chartParams = chartParams
      // this.height = "100vh";
    }
    // this.getAllWidgets();
    // this.currentPortal = this.$route.query;
    // this.portalId = this.$route.query.id ?? null;
    this.getWidgetsConfig()
  },
  mounted() {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },

  methods: {
    handleResetGlobalFilters() {
      this.filters = JSON.parse(JSON.stringify(this.originalFilters))
    },
    // 获取设置控制器关联那些图表
    setControlGetRelatedItems(filters) {
      this.FormValuesRelatedItems = filters.reduce((filter, nextFilter) => {
        const checkedItems = Object.entries(nextFilter.relatedItems)
          .filter(([ , config ]) => config.checked)
          .map(([ itemId ]) => itemId)
        return Array.from(new Set([ ...filter, ...checkedItems ]))
      }, [])
    },
    getFormValuesRelatedItems(formValues) {
      this.FormValuesRelatedItems = Object.keys(formValues).reduce(
        (items, key) => {
          const control = this.filters.find((c) => c.key === key)
          const { relatedItems } = control
          const checkedItems = Object.entries(relatedItems)
            .filter(([ , config ]) => config.checked)
            .map(([ itemId ]) => itemId)
          return Array.from(new Set([ ...items, ...checkedItems ]))
        },
        []
      )
    },
    getSelectOptions() {
      const selectOptions = {}
      this.filters.length &&
        this.filters.forEach((filter) => {
          Object.entries(filter.relatedItems).forEach(async([ , v ]) => {
            if ([ "select", "radio" ].includes(filter.type)) {
              if (filter.optionType === "auto" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: filter.relatedViews[v.viewId].fields,
                  viewId: v.viewId,
                }
                const { data } = await this.$httpBi.view.getdistinctvalue(param)
                this.$set(selectOptions, filter.key, [ ...data ])
              } else if (filter.optionType === "manual" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: [ filter.valueField, filter.textField ],
                  viewId: filter.valueViewId,
                }
                // 如果加载过一次手动数据  不需要加载第二次
                if (!selectOptions[filter.key]) {
                  this.$set(selectOptions, filter.key, [])
                  const { data } = await this.$httpBi.view.getdistinctvalue(
                    param
                  )
                  this.$set(selectOptions, filter.key, [ ...data ])
                }
              } else if (
                filter.optionType === "custom" &&
                !selectOptions[filter.key]
              ) {
                this.$set(selectOptions, filter.key, [ ...filter.customOptions ])
              }
            }
          })
        })

      this.globalSelectOptions = selectOptions
    },
    // 获取当前页面所有图表关系
    getCurrentDashboradRelations() {
      this.widgetList = []
      let obj = {}
      const deepCloneWidgets = JSON.parse(
        JSON.stringify(this.currentDashborad.widgets)
      )
      deepCloneWidgets.forEach((item) => (obj[item.id] = item))
      this.$nextTick(() => {
        this.currentDashborad.relations.forEach((item) => {
          obj[item.widgetId].widgetId = item.widgetId
          obj[item.widgetId].id = item.id
          this.widgetList.push(obj[item.widgetId])
        })
      })
      setTimeout(() => {
        console.log(this.$refs)
      }, 2000)
      // this.$refs.dashboards.getLayout();
    },
    // 获取dashboard
    async getWidgetsConfig() {
      this.filters = []
      const { data, code } = await this.$httpBi.dashboard.widgets({
        dashboardId: this.Drillconfig.target,
      })
      if (code === 200) {
        this.currentDashborad = data
        this.widgets = data.widgets
        const isShowWatermark =
          data.config && JSON.parse(data.config).isShowWatermark
        this.filters = (data.config && JSON.parse(data.config).filters) || []
        // 控制器赋值
        this.filterAssign()
        // 备份原始数据 为了重置
        this.originalFilters = this._.cloneDeep(this.filters)
        this.queryMode =
          (data.config && JSON.parse(data.config).queryMode) || 0
        this.getSelectOptions()
        this.getCurrentDashboradRelations()
        if (isShowWatermark) {
          this.$store.commit("watermark/SET_WATERMARK")
        } else {
          this.$store.commit("watermark/OUT_WATERMARK")
        }
      }
    },
    filterAssign() {
      // 如果是表格下钻
      if (this.Drillconfig.relation && this.selectedChartId === 1) {
        this.Drillconfig.relation.forEach((item, ) => {
          var index = this.filters.findIndex((e) => e.key === item.kzq)
          this.filters[index].defaultValue = this.chartParams[item.zd]
        })
      }
      if (this.Drillconfig.xAxis) {
        const index = this.filters.findIndex(
          (item) => item.key === this.Drillconfig.xAxis
        )
        this.filters[index].defaultValue = this.chartParams.name
      }
      if (this.Drillconfig.series) {
        const index = this.filters.findIndex(
          (item) => item.key === this.Drillconfig.series
        )
        this.filters[index].defaultValue = this.chartParams.seriesName
      }
      if (this.Drillconfig.valueName) {
        const index = this.filters.findIndex(
          (item) => item.key === this.Drillconfig.valueName
        )
        this.filters[index].defaultValue = this.chartParams.seriesName
      }
    },
  },
}
</script>

<style scoped lang="scss">
.portal-main {
  width: 100%;
  height: 100vh;

  background-color: #f0f2f5;
  .portalBody {
    display: flex;
    height: 100%;
    .siderbar {
      width: 250px;
      border-right: 1px solid #ccc;

      .portal-btn {
        padding: 16px;
        display: flex;
        justify-content: flex-end;
        border-bottom: 1px solid #ccc;
        i {
          cursor: pointer;
        }
      }
      .portalTreeNode {
        padding: 0 8px;
        overflow: auto;
      }
    }
    .gridClass {
      flex: 1;
      display: flex;
      flex-direction: column;
      .grid-head {
        padding: 20px 20px 0 20px;
        .title {
          text-align: center;
          font-weight: 700;
        }
      }
      .row-btn {
        margin: 0;
      }
      .GolobalPanel {
        margin: 10px 15px 15px 15px;
        padding: 5px;
        // background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
      }
      .dashboardList {
        box-sizing: border-box;
        padding-right: 0px;
        flex: 1;
        overflow-y: auto;
      }
    }
  }
}
.vue-grid-layout {
  background: #eee;
  touch-action: none;
}
::v-deep .el-tree {
  background-color: #f0f2f5;
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .buttonView {
    opacity: 0;
  }
}
.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}
.controlForm {
  height: auto;
  background-color: #fff;
}

::v-deep .el-row {
  margin-bottom: 5px;
}
::v-deep .el-form-item__label {
  color: rgba(89, 89, 89, 0.65);
  font-size: 12px;
  font-weight: bold;
}
::v-deep .el-date-editor--daterange.el-input__inner {
  width: 100% !important;
}

::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
</style>
