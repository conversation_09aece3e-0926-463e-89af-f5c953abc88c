export default {
  data() {
    return {
      active: "",
      network: true,
      history: [],
      chat: [],
      chatStore: {},
      uuid: ""
    }
  },
  mounted() {
    this.uuid = Date.now()
    this.active = this.uuid
    this.history = [{ uuid: this.uuid, title: "New Chat", isEdit: false }]
    this.chat = [{ uuid: this.uuid, data: [] }]
  },
  methods: {
    getChatByUuidAndIndex(uuid, index) {
      // return this.chatStore.getChatByUuidAndIndex(uuid, index)
      if (!uuid || uuid === 0) {
        if (this.chat.length) return this.chat[0].data[index]
        return null
      }
      const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
      if (chatIndex !== -1) return this.chat[chatIndex].data[index]
      return null
    },
    addChat(uuid, chat) {
      console.log("add", chat)
      // if (!uuid || uuid === 0) {
      if (this.history.length === 0) {
        const uuid = Date.now()
        this.history.push({ uuid, title: chat.text, isEdit: false })
        this.chat.push({ uuid, data: [chat] })
        this.active = uuid
        console.log(this.chat, "this.chat1")
        // this.recordState()
      } else {
        this.chat[0].data.push(chat)
        console.log(this.chat, "this.chat2")

        if (this.history[0].title === "New Chat")
          this.history[0].title = chat.text
        // this.recordState()
      }
      // }

      // const index = this.chat.findIndex(item => item.uuid === uuid)
      // if (index !== -1) {
      //   this.chat[index].data.push(chat)
      //   if (this.history[index].title === "New Chat")
      //     this.history[index].title = chat.text
      // }
    },
    updateChat(uuid, index, chat) {
      console.log("来了更新")
      // if (!uuid || uuid === 0) {
      //   if (this.chat.length) {
      //     this.chat[0].data[index] = chat
      //     // this.recordState()
      //   }
      //   return
      // }
      // const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
      this.$set(this.chat[0].data, index, chat)
      // this.recordState()
    },
    updateChatSome(uuid, index, chat) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data[index] = { ...this.chat[0].data[index], ...chat }
          // this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data[index] = {
          ...this.chat[chatIndex].data[index],
          ...chat
        }
        // this.recordState()
      }
    },
    deleteChatByUuid(uuid, index) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data.splice(index, 1)
        }
        return
      }
      const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data.splice(index, 1)
      }
    }
  }
}
