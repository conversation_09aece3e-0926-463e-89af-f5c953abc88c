<template>
  <div class="portal-main" :theme="theme">
    <VHeader :name="name" v-if="!HighLight" />
    <div class="content" :class="{ highLight: HighLight }">
      <Siderbar
        :style="{width: $store.state.dashboard.isCollapse?'60px':'224px'}"
        v-if="dashboards && !HighLight"
        :dashboards="dashboards"
      />
      <router-view />
    </div>
  </div>
</template>

<script>
import VHeader from "./components/Vheader.vue"
import Siderbar from "./components/Siderbar.vue"
import { mapState } from "vuex"
export default {
  components: { VHeader, Siderbar },
  props: {},
  data() {
    return {
      name: "一卡通",
      HighLight: false,
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      dashboards: (state) => state.settings.dashboards,
    }),
  },
  created() {
    const { HighLight,  } = this.$route.query
    this.HighLight = HighLight
  },
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
@import "../../portal/theme/dark.scss";
@import "../../portal/theme/light.scss";
.content {
  display: flex;
  height: calc(100vh - 64px);
}
.content.highLight {
  height: 100vh;
}
::v-deep .custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .buttonView {
    opacity: 0;
    margin-right: 20px;
  }
}

.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}
.controlForm {
  height: auto;
  background-color: #fff;
}

i {
  color: var(--theme-icon-color);
}
::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner,
::v-deep .el-range-editor.el-input__inner {
  width: 100%;
}
// 菜单图标弹窗样式
.el-select__tags .el-tag .el-select__tags-text {
  max-width: 90px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.menu_icon_popover_list_view {
  display: flex;
  width: 300px;
  flex-wrap: wrap;
  .svg_icon {
    font-size: 20px;
    width: 25%;
    margin: 5px 0;
    cursor: pointer;
  }
  .svg_icon_null {
    width: 25%;
    margin: 5px 0;
    padding-top: 1px;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
  }
}
::-webkit-scrollbar {
  height: 4px;
  width: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
  background-color: var(--theme-color);
}
::-webkit-scrollbar-button {
  display: none;
}
::-webkit-scrollbar-thumb {
  width: 4px;
  min-height: 15px;
  background: var(--scrollbar-color) !important;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5) !important;
}
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  width: 10px;
  padding-left: 14px;
}
</style>
