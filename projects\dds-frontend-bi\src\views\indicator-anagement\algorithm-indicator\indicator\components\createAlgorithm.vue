<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px'
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">创建执行类算法</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          Python
        </div>
        <div class="line" :class="{ active: activeId > 1 }"></div>
        <div class="step-item" :class="{ active: activeId > 1 }">
          <i class="el-icon-data-line"></i>
          指标与维度
        </div>
      </div>
      <el-button
        type="primary"
        style="margin-left: auto"
        @click="$router.push('/ddsBi/algorithmHistory')"
      >
        编辑历史
      </el-button>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="left">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="数据源:">
            <el-select
              @change="sourceDb"
              v-model="form.sourceId"
              placeholder="请选择数据源"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="item in sourcesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库表:">
            <el-tree
              :props="defaultProps"
              class="tree-db"
              :load="loadNode"
              :data="dbs"
              lazy
            >
              <span class="custom-tree-node" slot-scope="{ node }">
                <span>
                  <i v-if="node.data.type == 'DB'" class="el-icon-coin" />
                  <i v-if="node.data.type == 'TABLE'" class="el-icon-s-grid" />
                  {{ node.label }}
                </span>
              </span>
            </el-tree>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="editor-pane">
          <MonacoEditor
            ref="MonacoEditor"
            :init-value.sync="form.sql"
            @mouseup="getSelectCode"
            :hint-data="hintData"
            height="100%"
            language="python"
          />
        </div>
        <div class="config-pane">
          <!-- 算法基本信息 -->
          <h3>
            引用工具函数
            <el-button type="primary" icon="el-icon-plus" circle></el-button>
          </h3>
          <div class="form-section">
            <div class="tool-fun">
              <div class="tool-fun-head">
                test.py
                <i
                  @click="show3 = !show3"
                  class="el-icon-d-arrow-left"
                  style="transform: rotateZ('45deg')"
                ></i>
                <el-button
                  style="margin-left: auto"
                  type="danger"
                  icon="el-icon-minus"
                  circle
                ></el-button>
              </div>

              <el-collapse-transition>
                <div v-show="show3" class="tool-fun-info">
                  <!-- 入参 -->
                  <div class="tool-fun-params">
                    <h4>入参</h4>
                    <div class="param-item">
                      <div class="param-item-name">
                        sql_query

                        <i class="el-icon-document-copy"></i>
                      </div>
                      <div class="param-item-value">
                        从数据库中提取用于单维度聚类分析的数据，为两列数值，第一列为样本id，第二列为用于聚类分析的数据值
                      </div>
                    </div>
                    <!-- <div class="param-item">
                      <div class="param-item-name">
                        index_name
                        <i class="el-icon-document-copy"></i>
                      </div>
                      <div class="param-item-value">
                        指标名称，用于拼接形成指标名
                      </div>
                    </div> -->
                    <div class="param-item">
                      <div class="param-item-name">
                        k
                        <i class="el-icon-document-copy"></i>
                      </div>
                      <div class="param-item-value">聚类的类簇</div>
                    </div>
                  </div>
                  <div class="tool-fun-output">
                    <h4>输出</h4>
                    <div class="output">
                      Dataframe： | 样本id | 数据值 | cluster | |
                      样本id（学号）|用于聚类的原始数据值 |
                      聚类类别（0，1，2...）|
                    </div>
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>
        </div>
        <div class="warm-reminder" v-if="!tableData.length">
          <svg-icon icon-class="warning" style="margin-top: 5px" />
          <div class="warm-reminder-content">
            <p>
              1.为了让算法结果能够录入指标库，最终的输出结果需为一张表格，点击下方的“试计算”且本编译器能渲染后，可以进入下一步实现算法的指标信息配置
            </p>
            <p>2. 为了让生成的指标具有更多的扩展性，建议输出尽量宽的表格</p>
          </div>
        </div>
        <div class="tables" v-else>
          <CommonTable
            :page.sync="page"
            id="xh"
            :table-data="tableData"
            :show-batch-tag="false"
            :table-columns.sync="tableColumns"
          ></CommonTable>
        </div>
        <div class="footer-btn">
          <el-button @click="$emit('backMain')">取消</el-button>
          <el-button type="success" @click="handleRunSql">试计算</el-button>
          <el-button
            type="primary"
            @click="activeId = 2"
            :disabled="!this.tableData.length"
          >
            下一步
          </el-button>
        </div>
      </div>
    </div>
    <div class="form-container" v-if="activeId === 2">
      <!-- 基础信息区域 -->
      <div class="base-info">
        <el-form :model="form" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="算法名称:">
                <el-input
                  v-model="form.algorithmName"
                  placeholder="请输入算法名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运行周期:">
                <el-select v-model="form.runCycle" placeholder="请选择运行周期">
                  <el-option label="每日" value="daily"></el-option>
                  <el-option label="每周" value="weekly"></el-option>
                  <el-option label="每月" value="monthly"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="算法介绍:">
            <el-input
              type="textarea"
              v-model="form.algorithmDescription"
              placeholder="请输入算法介绍"
              rows="3"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 参数配置区域 -->
      <div class="parameter-config">
        <h3>输出结果标注:</h3>
        <el-table :data="parameters" border style="width: 100%">
          <el-table-column prop="fieldCode" label="字段代码"></el-table-column>
          <el-table-column prop="fieldType" label="字段类型">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldType"
                placeholder="请选择字段类型"
              >
                <el-option label="无" value="无"></el-option>
                <el-option label="维度" value="维度"></el-option>
                <el-option
                  label="度量(decimal)"
                  value="度量(decimal)"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
          <el-button @click="$router.go(-1)">取消</el-button>


        <el-button @click="activeId = 1">上一步</el-button>
        <el-button type="primary" @click="activeId = 3">下一步</el-button>
      </div>
    </div>
    <div class="three" v-if="activeId == 3">
      <!--  -->
      <div class="allselect">
        <el-button @click="toggleSelection">全选</el-button>
        <span style="margin-left: 12px">
          已选中{{ multipleSelection.length }}条
        </span>
      </div>
      <el-form
        status-icon
        :rules="rules"
        :model="form"
        ref="ruleForm"
        label-width="0"
        class="ruleForm"
      >
        <el-table
          ref="multipleTable"
          :data="form.tableData"
          tooltip-effect="dark"
          style="width: 100%"
          :max-height="320"
          :row-class-name="tableRowClassName"
          @cell-click="tabClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="zbmc" label="指标名称">
            <template slot-scope="scope">
              <el-form-item
                :ref="'zdmc' + scope.row.index"
                class="is-required"
                :prop="'tableData.' + scope.$index + '.zbmc'"
                :rules="scope.row.openRule ? rules.zbmc : {}"
                style="margin-top: 18px"
              >
                <span
                  v-if="
                    scope.row.index === tabClickIndex &&
                    tabClickLabel === '指标名称'
                  "
                >
                  <el-input
                    v-focus
                    v-model.trim="scope.row.zbmc"
                    maxlength="300"
                    placeholder="请输入标签"
                    size="mini"
                  />
                </span>
                <span v-else>
                  {{ scope.row.zbmc }}
                  <svg-icon style="color: #5b8ff9" icon-class="edit" />
                </span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="zbymc" label="原数据列标题"></el-table-column>
          <el-table-column label="所属数据域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-model="row.sysjy"
                placeholder="请选择数据域"
                :dic="viewGroup"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="描述">
            <template slot-scope="scope">
              <span
                v-if="
                  scope.row.index === tabClickIndex && tabClickLabel === '描述'
                "
              >
                <el-input
                  v-focus
                  v-model="scope.row.ms"
                  maxlength="300"
                  placeholder="请输入描述"
                  size="mini"
                  @blur="inputBlur"
                />
              </span>
              <span v-else>
                {{ scope.row.ms }}
                <el-button type="text">添加</el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="bq" label="标签">
            <template slot-scope="scope">
              <!-- <span
                  v-if="
                    scope.row.index === tabClickIndex && tabClickLabel === '标签'
                  "
                >
                  <el-input
                    v-focus
                    v-model="scope.row.bq"
                    maxlength="300"
                    placeholder="请输入标签"
                    size="mini"
                    @blur="inputBlur"
                  />
                </span>
                <span v-else>
                </span> -->
              {{ scope.row.bq && scope.row.bq.join(",") }}
              <el-button type="text">添加</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="派生维度"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <!-- <span v-for="(item, index) in row.pswd" :key="index">
                {{ item.wdbm?item.wdbm:item.zdmc }}{{ index ? "," : "" }}
              </span> -->
              {{ row.pswd }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="创建人">
            <template>
              {{ $store.state.user.username }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="action-buttons">
          <el-button @click="$router.go(-1)">取消</el-button>


        <el-button @click="activeId = 2">上一步</el-button>
        <el-button type="primary" @click="activeId = 3">保存</el-button>
      </div>
    </div>
  </DT-View>
</template>

<script>
import Request from "@/service"
// import encryptSql from "@/utils/encryptSql.js"
import CommonTable from "@/components/CommonTable.vue"

import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import MonacoEditor from "@/components/MonacoEditor"
import { exportExcel } from "@/utils"

export default {
  components: { MonacoEditor, CommonTable },

  props: {},
  data() {
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = this.form.tableData1.filter(
        item => item.fieldNameModified === value
      )
      if (arr.length > 1) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
      const { data } = await Request.api.paramPost(
        "SqlIndicator/existByNameAndType",
        {
          name: value,
          type: arr[0].tagType,

          dimensions: this.form.tableData1
            .filter(e => e.tagType === "派生维度")
            .map(item => item.fieldNameModified)
        }
      )

      if (data) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
    }
    return {
      multipleSelection: [],
      viewGroup: [{ id: 0, name: "根目录", children: [] }],
      show3: true,
      isSelectSQL: false, // 是否选中sql
      activeId: 1,
      sourcesList: [
        {
          updateTime: null,
          id: 39,
          name: "112.60-dds_platform_dev",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 38,
          name: "ods",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 37,
          name: "演示数据源",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 30,
          name: "演示配置流程库",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 18,
          name: "112.60-dds_platform_3.0.0",
          description: "112.60-dds_platform_3.0.0",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 6,
          name: "113.8库",
          description: "达芬奇",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 5,
          name: "112.60库/dds-platform",
          description: "112.60库dds-platform",
          type: "jdbc"
        },
        {
          updateTime: "2022-02-25 16:32:25",
          id: 2,
          name: "nbda",
          description: "oracleNbda数据库",
          type: "jdbc"
        },
        {
          updateTime: "2022-01-26 10:18:07",
          id: 1,
          name: "dds-platform",
          description: "数字桌面演示数据库",
          type: "jdbc"
        }
      ], // 数据源列表
      form: {
        sourceId: 27, // 数据源id
        sql: `
def main_function():
    sql_query0="""
    SELECT
        user_id,
        sum(txamt) txamt
    FROM
        ws_trade_summary
    WHERE
        txtype_id = '1'
        AND year_id = 2024
    GROUP BY
        user_id
    """
    input_params={
        'sql_query':sql_query0,
        'k':3
    }
    output=calculate(input_params)
    output.columns = ['学号', "消费金额", '聚类类别']
    return output`,
        variables: [],
        tableData1: [],
        tableData: [
    
            {
            zbmc: "聚类类别",
            zbymc: "cluster",
            sysjy: 0,
            ms: "",
            bq: [],
            pswd: "",
            addrss: "admin"
          }
        ]
      },
      rules: {
        fieldNameModified: [
          {
            validator: changeFieldNameModified,
            trigger: "blur",
            required: true
          }
        ]
      },
      dbs: [],
      tableData: [],
      page: {
        total: 1,
        pageSize: 10,
        currentPage: 1
      },
      tableColumns: [
        {
          label: "学号",
          prop: "val",
          visible: true,
          sortable: false
        },
        {
          label: "消费金额",
          prop: "val1",
          visible: true,
          sortable: false
        },
        {
          label: "聚类类别",
          prop: "val2",
          visible: true,
          sortable: false,
          formatter: val2 => {
            return val2
          }
        },
       
      ],
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf"
      },
      tables: [],
      columns: [],
      isFull: false, // 是否全屏
      copySql: "", // 备份sql
      sqlInfo: {}, // 保存的sql信息
      indCode: "",
      numericFields: [], // 数值字段

      code: '# 默认示例代码\nprint("Hello World")',
      cmOptions: {
        tabSize: 4,
        mode: "text/x-python",
        theme: "material-darker",
        lineNumbers: true,
        line: true,
        readOnly: false
      },
      algorithm: {
        name: "",
        description: ""
      },
      parameters: [
        {
          fieldCode: "学号",
          fieldType: "维度"
        },
        {
          fieldCode: "消费金额",
          fieldType: "维度"
        },
        {
          fieldCode: "聚类类别",
          fieldType: "度量(decimal)"
        }
      ]
    }
  },
  computed: {
    hintData() {
      return [
        ...this.tables.map(item => item.name),
        ...this.columns.map(item => item.name)
      ]
    },
    isNewSql() {
      console.log(this.copySql, "this.copySql")
      console.log(this.form.sql, "this.form.sql")

      return this.indCode
    }
  },
  created() {
    this.indCode = this.$route.query.indCode
    if (this.indCode) {
      this.getSqlDetail()
    }
  },
  mounted() {
    this.initSource()
    this.sourceDb()
  },
  watch: {},
  methods: {
    // 获取sql详情
    async getSqlDetail() {
      const { data } = await Request.api.paramPostQuery(
        "/SqlIndicator/getSqlIndicatorByIndCode",
        {
          indCode: this.indCode
        }
      )
      this.form.sql = data.sqlStatement
      this.copySql = data.sqlStatement
      this.sqlInfo = data
      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue()
        this.handleRunSql()
      })
    },

    // 全部导出
    handleAllExport() {
      // 导出
      exportExcel("/api/dds-server-bi/SqlIndicator/exportAllData", {
        sql: this.form.sql
      })
    },
    async handleSave() {
      let params = this.form.tableData1.map(item => {
        return {
          ...item,
          unitName: item.unitName === "其他" ? item.diydw : item.unitName
        }
      })
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/SqlIndicator/save",
            params
          )
          this.$message.success(data)
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {}
          })
        }
      })
    },
    // 初始化数据源
    async initSource() {
      const { data } = await Request.view.getSources()
      this.sourcesList = data
    },
    // 数据源库
    async sourceDb() {
      this.dbs = []
      if (this.form.sourceId) {
        Request.view
          .getDatabases({ id: this.form.sourceId })
          .then(res => {
            for (let i = 0; i < res.data.length; i++) {
              this.dbs.push({ name: res.data[i], type: "DB" })
            }
          })
          .catch(() => {})
      }
      console.log(this.dbs)
    },
    // 树节点加载
    loadNode(node, resolve) {
      // 第一层库
      if (node.level === 0) {
        return resolve(this.dbs)
      }
      // 第二层表
      if (node.level === 1) {
        Request.view
          .getTables({ id: this.form.sourceId, dbName: node.data.name })
          .then(res => {
            this.tables = this.tables.concat(res.data.tables)
            return resolve(res.data.tables)
          })
          .catch(() => {})
      }
      if (node.level === 2) {
        Request.view
          .getColumns({
            id: this.form.sourceId,
            dbName: node.parent.data.name,
            tableName: node.data.name
          })
          .then(res => {
            this.columns = this.columns.concat(res.data.columns)
            return resolve(
              res.data.columns.map(item => ({
                ...item,
                leaf: true
              }))
            )
          })
          .catch(() => {})
      }
      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    getSelectCode() {
      if (this.$refs.MonacoEditor.getSelectionVal()) {
        this.isSelectSQL = true
      } else {
        this.isSelectSQL = false
      }
    },
    // 执行slq
    async handleRunSql() {
      this.tableData = [
            { val: 22126300204, val1: 112.10, val2: 0 },
  { val: 22001000107, val1: 46.42, val2: 1 },
  { val: 22001000110, val1: 118.19, val2: 0 },
  { val: 22126300175, val1: 176.94, val2: 2 },
  { val: 22126300221, val1: 107.00, val2: 0 },
  { val: 22001001123, val1: 115.92, val2: 0 },
  { val: 22126300158, val1: 70.62, val2: 1 },
  { val: 22126300187, val1: 195.68, val2: 2 },
  { val: 21126301531, val1: 5.27, val2: 1 },
  { val: 22001000538, val1: 41.20, val2: 1 }
      ]
    },
    handleExportExcel(selection) {
      console.log(selection, "selection")

      jsonToSheetXlsx({
        data: selection,
        filename: "表数据" + new Date().getTime()
      })
    },
    // 下一步
    nextStep() {
      this.handleRunSql(() => {
        console.log("执行回到")
        this.activeId = 2
        if (this.indCode) {
          this.form.tableData1 = [this.sqlInfo]
        } else {
          this.form.tableData1 = this.tableColumns.map(item => ({
            fieldName: item.prop,
            fieldNameModified: item.prop,
            tagType: "指标",
            period: "1",
            scopeId: 0,
            precision: null,
            rounding: "否",
            thresholdMin: null,
            thresholdMax: null,
            unitName: null,
            description: "",
            tagsName: [],
            createdById: null,
            indCode: null,
            sqlStatementOrigin: this.form.sql
          }))
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }

  .steps {
    width: 450px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;

    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;

      &.active {
        background: #1563ff;
      }
    }

    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;

      &.active {
        color: #1563ff;
      }

      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}

.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100% - 100px);
  padding-top: 20px;

  background-color: #f0f2f5;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;

    ::v-deep .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;

      .el-form-item:nth-child(2) {
        flex: 1 1 0;
        overflow: hidden;

        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px;
          /*高宽分别对应横竖滚动条的尺寸*/
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 6px;
          height: 2px;
          background-color: #cfd6e6;
        }

        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: transparent;
          border-radius: 6px;
        }

        .el-form-item__content {
          height: calc(100% - 45px);
        }
      }
    }

    .custom-tree-node {
      width: 100%;
    }

    .tree-db {
      height: 100%;
      overflow-y: auto;

      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
  }

  .right {
    position: relative;
    width: calc(100% - 340px);
    padding: 20px;
    min-height: calc(100vh - 230px);

    background: #fff;
    padding-bottom: 52px;
    display: flex;
    flex-wrap: wrap;

    .editor-pane {
      width: calc(100% - 340px);
      padding: 20px;
      background: #2d2d2d;
    }

    .config-pane {
      flex: 0 0 320px;
      overflow-y: auto;
      margin-left: 20px;
      border: 1px solid #f1f1f1;

      h3 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        background: #f5f6fa;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 4px 4px 0 0;
      }
    }

    .code-editor {
      height: 100%;
      margin-right: 20px;
    }

    .form-section {
      margin-bottom: 16px;
      background-color: #fff;
      box-sizing: border-box;
      padding: 16px;
      box-sizing: border-box;

      .tool-fun {
        background-color: #fff;
        border: 1px solid #f5f6fa;
        border-radius: 5px;
        padding: 8px 16px;
        .tool-fun-head {
          display: flex;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #222222;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          i {
            color: #1563ff;
            font-size: 20px;
            transform: rotateZ(90deg);
            margin-left: 4px;
            cursor: pointer;
          }
        }
      }
      .tool-fun-info {
        background: #f5f7fa;
        border-radius: 8px;
        .tool-fun-params {
          padding: 6px;
          box-sizing: border-box;
          h4 {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: left;
            font-style: normal;
            margin-bottom: 4px;
          }
          .param-item {
            background-color: #fff;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 16px;
            .param-item-name {
              font-size: 12px;
              color: #108ee9;
              margin-bottom: 8px;
              display: flex;
              justify-content: space-between;
              i {
                font-size: 16px;
                cursor: pointer;
              }
            }
            .param-item-value {
              font-size: 12px;
              line-height: 16px;
              color: #6b6b6b;
            }
          }
        }
        .tool-fun-output {
          padding: 6px;
          box-sizing: border-box;
          h4 {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: left;
            font-style: normal;
            margin-bottom: 4px;
          }
          .output {
            background-color: #fff;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 16px;
            color: #6b6b6b;
          }
        }
      }
    }
    .warm-reminder {
      width: 100%;
      background: #fffbe6;
      border-radius: 2px;
      border: 1px solid #fff1b8;
      margin: 16px auto 24px;
      display: flex;
      padding: 12px 17px 12px;
      box-sizing: border-box;

      .warm-reminder-content {
        margin-left: 8px;
        p {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #323233;
          line-height: 22px;
        }
      }
      .el-icon-close {
        position: absolute;
        right: 12px;
        top: 12px;
        cursor: pointer;
      }
    }
    .tables {
      width: 100%;
      margin: 20px 0;
    }
    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.form-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  ::v-deep .el-row {
    margin-bottom: 0;
  }
  ::v-deep .el-form-item__label {
    font-size: 16px;
    font-weight: 600;
    text-align: left;
  }
  :v-deep .el-select.el-select--small {
    width: 100%;
  }
  .base-info {
    margin-bottom: 20px;
  }

  .parameter-config {
    margin-bottom: 20px;

    h3 {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;

    .el-button {
      margin-left: 10px;
    }
  }
}
.three {
  padding: 24px;
  .allselect {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
  }
  .action-buttons {
    display: flex;
    justify-content: flex-end;

    .el-button {
      margin-left: 10px;
    }
  }
}

.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}

#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}

#project_frame {
  .el-tree-node:not(.is-expanded)
    > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }

  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}

#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
