<template>
  <div
    v-if="searchColumns.length"
    class="table-search"
    :class="{ card: isCard }"
  >
    <el-form ref="formRef" :model="searchParam">
      <Grid
        ref="gridRef"
        :collapsed="collapsed"
        :gap="[20, 0]"
        :cols="searchCol"
        @breakPointUpdate="val => (breakPoint = val)"
      >
        <GridItem
          v-for="(item, index) in searchColumns"
          :key="item.prop"
          :index="index"
          v-bind="getResponsive(item)"
        >
          <el-form-item>
            <template #label>
              {{ item.label }}
              <!-- <el-space :size="4">
                <span>{{ `${item.search?.label ?? item.label}` }}</span>
                <el-tooltip
                  v-if="item.search?.tooltip"
                  effect="dark"
                  :content="item.search?.tooltip"
                  placement="top"
                >
                  <i :class="'iconfont icon-yiwen'"></i>
                </el-tooltip>
              </el-space> -->
              <span>:</span>
            </template>

            <RenderFormItem
              v-if="item.search.render"
              :column="item"
              :search-param="searchParam"
            />
            <template v-if="item.search.slot">
              <slot :name="item.prop + 'Slot'" v-bind="scope"></slot>
            </template>
            <SearchFormItem v-else :column="item" :search-param="searchParam" />
          </el-form-item>
        </GridItem>
        <GridItem suffix>
          <div class="operation">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button
              v-if="showCollapse"
              type="text"
              link
              class="search-isOpen"
              @click="collapsed = !collapsed"
            >
              {{ collapsed ? "展开" : "收起" }}
              <i v-if="collapsed" class="el-icon-arrow-down"></i>
              <i v-else class="el-icon-arrow-up"></i>
            </el-button>
          </div>
        </GridItem>
      </Grid>
    </el-form>
  </div>
</template>

<script>
import cloneDeep from "lodash/cloneDeep"
import Grid from "@/components/Grid/index.vue"
import GridItem from "@/components/Grid/components/GridItem.vue"
import SearchFormItem from "./components/SearchFormItem.vue"
import RenderFormItem from "./components/RenderFormItem.vue"
export default {
  components: { Grid, GridItem, SearchFormItem, RenderFormItem },
  props: {
    columns: {
      type: Array, // 搜索参数
      default: () => [] // 搜索配置列
    },
    searchParam: {
      type: Object,
      default: () => ({}) //  搜索参数
    },
    searchCol: {
      type: [Number, Object],
      default: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }) //  搜索参数
    },
    isCard: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      collapsed: true,
      ArrowDown: "el-icon-arrow-down",
      ArrowUp: "el-icon-arrow-up",
      Delete: "",
      Search: "",
      breakPoint: "xl",
      enumMap: {},
      searchInitParam: {}
    }
  },
  created() {
    this.searchInitParam = cloneDeep(this.searchParam)
  },
  computed: {
    showCollapse() {
      let show = false
      this.searchColumns.reduce((prev, current) => {
        prev +=
          (current.search[this.breakPoint]?.span ?? current.search.span ?? 1) +
          (current.search[this.breakPoint]?.offset ??
            current.search.offset ??
            0)
        if (typeof this.searchCol !== "number") {
          if (prev >= this.searchCol[this.breakPoint]) show = true
        } else {
          if (prev >= this.searchCol) show = true
        }
        return prev
      }, 0)
      return show
    },
    flatColumns() {
      return this.flatColumnsFunc(this.columns)
    },
    searchColumns() {
      return this.flatColumns
        ?.filter(
          item => item.search?.el || item.search?.render || item.search?.slot
        )
        .sort((a, b) => a.search.order - b.search.order)
    }
  },
  watch: {
    columns: {
      handler() {
        this.columns.forEach(column => {
          const defaultValue = column.search?.defaultValue
          if (defaultValue !== undefined && defaultValue !== null) {
            this.searchInitParam[column.prop] = defaultValue
          }
        })
      },
      deep: true
    }
  },

  provide() {
    return {
      enumMapProvide: this
    }
  },

  methods: {
    // 获取响应式设置
    getResponsive(item) {
      return {
        span: item.search ? item.search.span : undefined,
        offset: item.search
          ? item.search.offset !== undefined
            ? item.search.offset
            : 0
          : 0,
        xs: item.search ? item.search.xs : undefined,
        sm: item.search ? item.search.sm : undefined,
        md: item.search ? item.search.md : undefined,
        lg: item.search ? item.search.lg : undefined,
        xl: item.search ? item.search.xl : undefined
      }
    },
    search() {
      this.$emit("search")
    },
    reset() {
      this.$emit("update:searchParam", cloneDeep(this.searchInitParam))
      this.$emit("search")
    },

    flatColumnsFunc(flatArr = []) {
      this.columns.forEach(async col => {
        if (col._children?.length) {
          flatArr.push(...this.flatColumnsFunc(col._children))
          flatArr.push(col)

          // column 添加默认 isShow && isFilterEnum 属性值
          col.isShow = col.isShow ?? true
          col.isFilterEnum = col.isFilterEnum ?? true
        }
        // 设置 enumMap
        await this.setEnumMap(col)
      })
      return flatArr.filter(item => !item._children?.length)
    },
    async setEnumMap({ prop, enum: enumValue }) {
      if (!enumValue) return

      // 如果当前 enumMap 存在相同的值 return
      if (
        this.enumMap[prop] &&
        (typeof enumValue === "function" || this.enumMap[prop] === enumValue)
      ) {
        return
      }

      // 当前 enum 为静态数据，则直接存储到 enumMap
      if (typeof enumValue !== "function") {
        return this.$set(this.enumMap, prop, enumValue)
      }

      // 为了防止接口执行慢，而存储慢，导致重复请求，所以预先存储为[]，接口返回后再二次存储
      this.$set(this.enumMap, prop, [])

      // 当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
      const { data } = await enumValue()
      this.$set(this.enumMap, prop, data)
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  padding: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
  box-shadow: 0 0 12px #0000000d;
  background: #fff;
  padding: 18px 18px 0;
}
// table-search 表格搜索样式
.table-search {
  .el-form {
    .el-form-item {
      display: flex;
      ::v-deep .el-form-item__label {
        display: inline-flex;
        justify-content: flex-end;
        align-items: flex-start;
        flex: 0 0 auto;
        white-space: nowrap;
      }
      ::v-deep .el-form-item__content {
        flex: 1;
      }
      .el-form-item__content > * {
        width: 100%;
      }
    }

    // 去除时间选择器上下 padding
    .el-range-editor.el-input__wrapper {
      padding: 0 10px;
    }
  }
  .operation {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 18px;
  }
}
</style>
