<template>
  <div class="indicator-item">
    <div class="target-item-title">
      {{ item.name }}({{ item.children && item.children.length }})
    </div>

    <template>
      <div class="target-item-content" v-for="(e, i) in item.children" :key="i">
        <IndicatorItem
          v-if="Number(e.type) === 0"
          :item="e"
          :select-indicators="selectIndicators"
          :same-dimension="sameDimension"
          v-on="$listeners"
        />
        <template v-else>
          <div class="target-type">
            <div
              class="target-type-item"
              id="draggable"
              :draggable="!selectIndicators.length || isDrag(e)"
              :style="{
                cursor:
                  !selectIndicators.length || isDrag(e) ? 'move' : 'not-allowed'
              }"
              @dragstart="onDragStart(e, $event)"
              @dragend="onDragEnd"
            >
              <span class="zbmc">{{ e.zbmc }}</span>
              <span class="zblx">{{ e.zblx }}</span>
            </div>
          </div>
          <div class="tags">
            <div
              class="tag-item gray"
              :class="{
                deep: isSameDimensions(wd)
              }"
              v-for="wd in e.xsc.slice(0, 5)"
              :key="wd.id"
            >
              {{ wd.zdmc }}
            </div>
            <span v-if="e.xsc.length > 5">等5项维度</span>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "IndicatorItem",
  components: {},
  props: {
    item: {
      type: Object,
      required: true
    },
    selectIndicators: {
      type: Array,
      required: true
    },
    sameDimension: {
      type: Array,
      required: true
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 是否选中有相同维度
    isSameDimensions(wd) {
      return this.sameDimension.some(e => e.wdmc === wd.zdmc)
    },
    // 是否可以拖拽
    isDrag(item) {
      return item.xsc.some(e => {
        return this.sameDimension.some(ele => ele.wdmc === e.zdmc)
      })
    },
    // 拖拽开始
    onDragStart(data) {
      this.$emit("indicatorsdragStart", data)
    },
    // 拖拽结束
    onDragEnd() {
      this.$emit("indicatorsdragEnd")
    }
  }
}
</script>

<style scoped lang="scss">
.target-item-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #222222;
  line-height: 14px;
  margin-bottom: 20px;
}
.target-item-content {
  display: flex;
  .target-type {
    min-width: 108px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 12px;

    .target-type-item {
      display: flex;
      flex-direction: column;
      .zbmc {
        height: 14px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 14px;
      }
      .zblx {
        margin-top: 4px;
        height: 12px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 12px;
      }
    }
  }
  .tags {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    margin-left: 16px;
    .tag-item {
      padding: 0 8px;
      margin-right: 4px;
      margin-bottom: 4px;
      height: 20px;
      background: rgba(91, 143, 249, 0.08);
      border-radius: 2px;
      border: 1px solid rgba(91, 143, 249, 0.4);
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #5b8ff9;
      line-height: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &.gray {
        background: #f1f1f1;
        color: #666666;
      }
      &.deep {
        background: #1890ff;
        color: #fff;
      }
    }
  }
}
</style>
