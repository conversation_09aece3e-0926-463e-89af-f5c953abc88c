<template>
  <div class="chart-graph">
    <ChartGraphPanelItem
      v-for="chart in widgetlibs"
      :key="chart.id"
      :chart-config="chart"
      :selected-chart="widgetParams.selectedChart"
      v-on="$listeners"
      :dimetions-and-metrics-count="getDimetionsAndMetricsCount"
    />
  </div>
</template>

<script>
import { widgetlibs } from "@/components/ChartGraph/index.js"
import ChartGraphPanelItem from "./ChartGraphPanelItem.vue"
export default {
  components: {
    ChartGraphPanelItem,
  },
  props: {
    widgetParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      widgetlibs,
    }
  },
  computed: {
    // 获取指标和维度的数量
    getDimetionsAndMetricsCount() {
      return [ 2,1 ]
    },
  },
  created() {
   
  },
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.chart-graph {
  width: 100%;
  display: grid;
  justify-items: center;
  grid-template-columns: repeat(auto-fill, minmax(38px, 1fr));
  grid-auto-rows: 1fr;
  grid-gap: 10px;
  height: 150px;
  overflow: auto;
  background-color: #fff;
  padding: 10px;
  box-sizing: border-box;
  span {
    line-height: initial;
    height: 38px;
    font-size: 22px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    width: 38px;
    position: relative;
    .icon {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
    }
  }
  span:hover {
    background-color: #eaedf1;
  }
  span::before {
    content: "";
    width: 100%;
    padding-bottom: 100%;
    display: block;
  }
  .disabledIcon {
    cursor: not-allowed;
    svg{
    cursor: not-allowed;

    }
  }
  .activedIcon {
    background-color: #eaedf1;
  }
}
</style>
