<template>
  <div class="paneBlock">
    <h4>折线图</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="8">
          <el-checkbox v-model="specForm.smooth" @change="changeSpecStyle">
            平滑
          </el-checkbox>
        </el-col>
        <el-col span="8">
          <el-checkbox v-model="specForm.step" @change="changeSpecStyle">
            阶梯
          </el-checkbox>
        </el-col>
        <el-col span="8">
          <el-checkbox v-model="specForm.isAreaStyle" @change="changeSpecStyle">
            区域
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="14">
          <el-checkbox
            v-model="specForm.showDataZoom"
            @change="changeSpecStyle"
          >
            滚动
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
        v-if="specForm.showDataZoom"
      >
        <el-col span="14">默认显示数量</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            placeholder=""
            v-model="specForm.endValue"
            @change="changeSpecStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <!-- <el-row gutter="8" type="flex" align="middle" class="blockRow">
      
        <el-col span="8">
          <el-checkbox v-model="specForm.isSymbol" @change="changeSpecStyle">
            标记
          </el-checkbox>
        </el-col>
        <el-col span="8">
          <el-checkbox v-model="specForm.boundaryGap" @change="changeSpecStyle">
            边界间隙
          </el-checkbox>
        </el-col>
      </el-row> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      specForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.specForm = this._.cloneDeep(this.chartData.chartStyles.spec)
    },
    changeSpecStyle() {
      this.$emit("changeStyle", "spec", this.specForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
