<template>
  <div class="chart-workbench-header">
    <span class="back-button" @click="goBack">
      <i class="el-icon-back" />
      <span>返回</span>
    </span>
    <el-divider direction="vertical"></el-divider>
    <div class="title">
      <div class="name">
        <input
          type="text"
          :placeholder="placeholder.name"
          v-model="name"
          @input="changeName"
        />
        <span>{{ name || placeholder.name }}</span>
      </div>
      <div class="desc">
        <input
          type="text"
          :placeholder="placeholder.description"
          v-model="description"
          @input="changeDesc"
        />
        <span>{{ description || placeholder.description }}</span>
      </div>
    </div>
    <div>
      <el-button
        @click="onSave"
        size="mini"
        type="primary"
        style="float: right; margin: 0 10px 0 0"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    description: {
      type: String,
    },
    name: {
      type: String,
    },
  },
  data() {
    return {
      placeholder: {
        name: "请输入名称",
        description: "请输入描述…",
      },
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    // 保存
    onSave() {
      this.$emit("saveWidget", this.name, this.description)
    },
    // 修改名称
    changeName(e) {
      this.$emit("update:name", e.target.value)
    },
    // 修改描述
    changeDesc(e) {
      this.$emit("update:description", e.target.value)
    },
  },
}
</script>

<style scoped lang="scss">
.chart-workbench-header {
  background-color: #fff;
  padding: 0 20px;
  box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  height: 60px;
  z-index: 9;
  .title {
    flex: 1;
    padding: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: flex-end;

    .name {
      flex-shrink: 0;
      font-size: 1.44em;
      position: relative;
    }

    .desc {
      flex: 1;
      color: "#ccc";
      height: 1.65em;
      line-height: 1.65em;
      position: relative;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      input {
        left: 25px;
      }
    }
    .name,
    .desc {
      span {
        display: block;
        visibility: hidden;
      }
    }
    input {
      width: 100%;
      padding: 0;
      border: 0;
      outline: 0;
      position: absolute;
      bottom: 0;

      cursor: pointer;
    }
  }
}
.back-button {
  cursor: pointer;
}
</style>
