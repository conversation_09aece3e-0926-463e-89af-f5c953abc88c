<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '@/mixins/chartResize'
import { extension, toThousands } from '@/utils'
import ChartEmpty from './ChartEmpty.vue'
export default {
  components: { ChartEmpty },
  mixins: [ resize ],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: '100%',
    },
    // 图表高度
    height: {
      type: String,
      default: '100%',
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => [],
    },
    // X轴字段
    xField: {
      type: String,
      default: 'value',
    },
    // Y轴字段
    yField: {
      type: String,
      default: 'name',
    },
    // 系列名称
    seriesName: {
      type: String,
      default: '',
    },
    // 单位
    unit: {
      type: String,
      default: '',
    },
    sortType: {
      type: String,
      default: 'asc',
    },
  },
  data() {
    return {
      maxWidth: 0,
      chart: null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      },
    },
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.chart.on("click", (params) => {
        this.$emit('chartClick',params)
      })

      this.renderChart()

    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      let dataset = null
      series.push(
        this.createSeriesObject({
          name: this.seriesName ?? this.yField,
        })
      );
      (dataset = [
        { dimensions: [ this.yField, this.xField ], source: this.chartData },
        {
          transform: {
            type: 'sort',
            config: { dimension: this.xField, order: this.sortType },
          },
        },
      ]),
      this.chart.setOption({
        dataset,
        tooltip: {
          // trigger: 'axis',
          trigger: 'axis',

          axisPointer: {
            type: 'shadow',
            label: { show: false, backgroundColor: 'transparent' },
            shadowStyle: {
              color: 'rgba(35,97,219,0.05)',
            },
          },
          formatter: (params) => {
            return `<div>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
  params[0].color
}" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${toThousands(
    params[0].value[
      params[0].dimensionNames[params[0].encode.x[0]]
    ]
  )}${this.unit}
                </span>
              </div>
            </div>`
          },
          className:'echarts-tooltip-diy',
        },

        grid: {
          top: 0,
          bottom: 0,
          left: 0,
          right: '4%',
          containLabel: true,
        },
        legend: {
          show: false,
        },
        xAxis: {
          type: 'value',
          // max: function() {
          //   return max * 1.1
          // },
          boundaryGap: [ '0%', '5%' ],
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },

        yAxis: [
          {
            type: 'category',
            triggerEvent: true,

            axisLabel: {
              fontSize: 12,
              color: '#323233',
              fontWeight: '400',
              formatter: (name) => {
                return name.length > 6 ? name.substr(0, 6) + '...' : name
              },
              // tooltip: {
              //   show: true,
              //   trigger: "item" //鼠标移动上去展示全称
              // }
            },

            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
          },
        ],
        series,
      })

      extension(this.chart, 'yAxis')
    },
    // 创建系列对象
    createSeriesObject({  name,yField = this.yField }) {
      return {
        name,
        encode: {
          x: this.xField,
          y: yField,
        },
        type: "pictorialBar",
        symbolSize: [ "20", "20" ],
        animation: true,
        barMinHeight: 20,
        symbolRepeat: "true",
        symbolPosition: "start",
        symbolOffset: [ 0, 0 ],
        datasetIndex: 1,
        symbol: "image://" + require("@/assets/images/fan.png"),
        label: {
          show: true,
          textStyle: {
            color: "#666666",
            fontSize: 14
          },
          formatter: `{@[1]}${this.unit}`,
          position: "right"
        }
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          (el) => `<div class="content-panel">
        <p >
          <span style="background-color: ${
  el.color
}" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.x[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join('')
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  },
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-drill{
  background:rgba(48,49,51,1) !important;
}
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    min-width: 220px;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 9px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
