<template>
  <el-dialog
    title="新增widget"
    :visible.sync="addWidgetVisible"
    append-to-body
    width="60%"
    @close="closeDialog"
    :before-close="handleClose"
  >
    <el-steps
      :active="active"
      finish-status="success"
      simple
      style="margin-bottom: 20px"
    >
      <el-step title="Widget"></el-step>
      <el-step title="数据更新"></el-step>
    </el-steps>
    <div class="table" v-if="active == 0">
      <el-row gutter="20">
        <el-col span="17">
          <el-checkbox
            v-model="showSelected"
            @change="onShowTypeChange"
          >
            已选
          </el-checkbox
          >
        </el-col>
        <el-col span="7">
          <el-input placeholder="Widget 名称" v-model="name">
            <el-button
              slot="append"
              @click="onSearch(name)"
              icon="el-icon-search"
              style="cursor: pointer"
            ></el-button>
          </el-input>
        </el-col>
      </el-row>
      <el-row gutter="20">
        <el-col
          :lg="8"
          :md="12"
          :sm="24"
          v-for="item in data.slice(
            (pagination.currentPage - 1) * pagination.pageSize,
            Math.min(pagination.currentPage * pagination.pageSize, data.length)
          )"
          :key="item.id"
        >
          <div class="widget selector" @click="handleSelectionChange(item)">
            <h3 class="title">{{ item.name }}</h3>
            <p class="content">{{ item.description }}</p>
            <div
              class="checkmark"
              v-if="selectedWidgets.some(({ id }) => id == item.id)"
            >
              <i class="el-icon-check"></i>
            </div>
          </div>
        </el-col>
      </el-row>
      <dt-pagination
        :hidden="data.total == 0"
        :total="data.length"
        :page-size="pagination.pageSize"
        :current-page="pagination.currentPage"
        :page-sizes="[12, 24, 48, 72]"
        @sizeChange="sizeChange"
        @currentChange="currentChange"
      />
    </div>
    <div class="dataUpdate" v-else>
      <el-form label-width="120px" :model="pollingSetting" :rules="rules">
        <el-form-item label="数据刷新模式">
          <el-select v-model="pollingSetting.polling" placeholder="请选择">
            <el-option label="手动刷新" :value="false"> </el-option>
            <el-option label="定时刷新" :value="true"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="时长"
          v-if="pollingSetting.polling"
          prop="frequency"
        >
          <el-input-number
            v-model="pollingSetting.frequency"
            :min="1"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        :disabled="isDisabledNext"
        v-if="active == 0"
        @click="active = 1"
      >下一步</el-button
      >
      <el-button v-if="active !== 0" @click="active = 0">上一步</el-button>
      <el-button
        v-if="active !== 0"
        type="primary"
        @click="saveDashboardItem"
      >保 存
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {
    addWidgetVisible: {
      type: Boolean,
    },
    currentDashborad: {
      type: Object,
      default: () => {},
    },
    dashboardId: {
      type: Number,
    },
  },
  data() {
    return {
      showSelected: false,
      name: "",
      active: 0,
      pagination: {
        pageSize: 12,
        currentPage: 1,
      },
      data: [],
      widgetList: [],
      selectedWidgets: [],
      pollingSetting: {
        frequency: 60,
        polling: false,
      },
      rules: {
        frequency: [
          { required: true, message: "时长不能为空", trigger: "blur" },
        ],
      },
    }
  },
  computed: {
    isDisabledNext() {
      return this.selectedWidgets.length ? false : true
    },
  },
  created() {
    this.getWidgetList()
  },
  mounted() {},
  watch: {},
  methods: {
    initData() {
      this.active = 0
      this.selectedWidgets = []
    },
    // 保存
    async saveDashboardItem() {
      this.$emit("onSelectWidget", this.selectedWidgets, this.pollingSetting)
    },
    // 选中
    handleSelectionChange(item) {
      if (this.selectedWidgets.some(({ id }) => id === item.id)) {
        this.selectedWidgets = this.selectedWidgets.filter(
          ({ id }) => id !== item.id
        )
        this.onShowTypeChange()
      } else {
        this.selectedWidgets.push({
          ...item,
          config: JSON.parse(item.config),
        })
      }
    },
    onShowTypeChange() {
      if (this.showSelected) {
        this.pagination.currentPage = 1
        this.data = this.getSelectedData()
      } else {
        this.data = this._.cloneDeep(this.widgetList)
      }
    },
    onSearch(name) {
      this.data = this.widgetList.filter((item) => {
        return item.name.indexOf(name) > -1
      })
    },
    // 根据selectedWidgets获取选中的数据
    getSelectedData() {
      return this.data.filter((item) =>
        this.selectedWidgets.some(({ id }) => id === item.id)
      )
    },
    // 获取列表
    async getWidgetList() {
      this.loading = true
      const { data } = await Request.widget.getAll()
      this.widgetList = this._.cloneDeep(data)
      this.data = data
      this.$store.commit("widget/SET_WIDGETS", data)
      this.loading = false
    },

    sizeChange(event) {
      this.pagination.pageSize = event.pageSize
    },
    currentChange(event) {
      this.pagination.currentPage = event.currentPage
    },
    closeDialog() {
      this.$emit("update:addWidgetVisible", false)
    },
  },
}
</script>

<style scoped lang="scss">
.widget {
  height: 120px;
  margin-bottom: 20px;
  border-radius: 2px;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.05);
  transform: translate3d(0, 0, 0);
  transition: all 200ms linear;
  position: relative;
  cursor: pointer;

  &:hover {
    box-shadow: 0 7px 21px 0 rgba(0, 0, 0, 0.15);
    transform: translate3d(0, -5px, 0);

    .pic {
      transform: translate(0, -5px);
    }
  }

  &.selector {
    border: 1px solid;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      transform: none;
    }

    &.selected {
      border-color: #1b98e0;
    }

    .checkmark {
      border-top: 12px solid #1b98e0;
      border-bottom: 12px solid transparent;
      border-right: 20px solid #1b98e0;
      border-left: 20px solid transparent;
      position: absolute;
      top: 0;
      right: -0.5px;

      i {
        color: #ffff;
        position: absolute;
        top: -11px;
        right: -18px;
      }
    }
  }

  .title {
    padding: 12px 40px 0 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: 600;
  }

  .content {
    padding: 8px 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .pic {
    font-size: 36px;
    font-weight: normal;
    line-height: 1;
    color: #1b98e0;
    position: absolute;
    bottom: 8px;
    right: 12px;
    opacity: 0.8;
    transition: transform 300ms linear;
  }

  .delete,
  .copy {
    position: absolute;
    top: 8px;
    font-size: 15px;
    opacity: 0.5;
    transition: transform 200ms ease;

    &:hover {
      opacity: 1;
      transform: scale(1.1, 1.1);
    }
  }

  .delete {
    right: 8px;
  }

  .copy {
    right: 32px;
  }
}
</style>
