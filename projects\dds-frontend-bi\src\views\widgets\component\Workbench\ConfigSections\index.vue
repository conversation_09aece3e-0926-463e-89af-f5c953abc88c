<template>
  <!-- <el-collapse-item title="背景" name="1">
      <background-color-selector v-on="$listeners" />
    </el-collapse-item> -->
  <!-- <el-collapse-item title="标题" name="2" v-if="['xxxx'].includes(name)">
      <title-selector v-on="$listeners" />
    </el-collapse-item> -->
  <div>
    <line-section
      v-if="['line'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <bar-section
      v-if="['bar'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />

    <pie-section
      v-if="['pie'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <FunnelSection
      v-if="['funnel'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <PieTitleSection
      v-if="['pie'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <double-yAxis
      v-if="['doubleYAxis'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <radar-section
      v-if="['radar'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <map-section
      v-if="['map'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />

    <label-section
      v-if="
        ['line', 'bar', 'pie', 'doubleYAxis', 'map', 'radar','funnel'].includes(name)
      "
      v-on="$listeners"
      v-bind="$attrs"
    />
    <GridSection
      v-if="['line', 'bar', 'doubleYAxis',].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <visual-map-section
      v-if="['map'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <DYAxisSection
      v-if="['doubleYAxis'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <legend-selector
      v-if="['line', 'bar', 'pie', 'radar', 'doubleYAxis','funnel'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <axis-section
      v-if="['line', 'bar', 'doubleYAxis'].includes(name)"
      axis-type="xAxis"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <axis-section
      v-if="['line', 'bar'].includes(name)"
      axis-type="yAxis"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <split-line-section
      v-if="['line', 'bar', 'doubleYAxis'].includes(name)"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <scorecard-section
      v-if="name == 'scorecard'"
      v-on="$listeners"
      v-bind="$attrs"
    />
    <table-selector v-if="name == 'table'" v-bind="$attrs" v-on="$listeners" />
  </div>
</template>

<script>
import TableSelector from "./TableSelector.vue"
import LineSection from "./LineSection.vue"
import SplitLineSection from "./SplitLineSection.vue"
import LabelSection from "./LabelSection.vue"
import FunnelSection from "./FunnelSection.vue"
// import BackgroundColorSelector from "./BackgroundColorSelector.vue";
// import TitleSelector from "./TitleSelector.vue";
import LegendSelector from "./LegendSelector.vue"
import AxisSection from "./AxisSection.vue"
import BarSection from "./BarSection.vue"
import PieSection from "./PieSection"
import DoubleYAxis from "./DoubleYAxis.vue"
import DYAxisSection from "./DYAxisSection.vue"
import ScorecardSection from "./ScorecardSection"
import RadarSection from ".//RadarSection"
import MapSection from "./MapSection"
import VisualMapSection from "./VisualMapSection"
import PieTitleSection from "./PieTitleSection.vue"
import GridSection from "./GridSection"
export default {
  components: {
    TableSelector,
    FunnelSection,
    // BackgroundColorSelector,
    // TitleSelector,
    LegendSelector,
    LineSection,
    LabelSection,
    AxisSection,
    SplitLineSection,
    BarSection,
    PieSection,
    PieTitleSection,
    DoubleYAxis,
    DYAxisSection,
    ScorecardSection,
    RadarSection,
    MapSection,
    VisualMapSection,
    GridSection,
  },
  props: {
    name: {
      type: String,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
