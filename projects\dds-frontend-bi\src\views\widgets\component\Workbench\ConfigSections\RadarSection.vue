<template>
  <div class="paneBlock">
    <h4>雷达图</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">形状</el-col>
        <el-col span="12">
          <el-select
            v-model="radarForm.shape"
            placeholder="请选择"
            @change="changeRadarStyle"
          >
            <el-option label="多边形" value="polygon"> </el-option>
            <el-option label="圆形" value="circle"> </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">轴段数</el-col>
        <el-col span="12">
          <el-input-number
            @change="changeRadarStyle"
            v-model="radarForm.splitNumber"
            controls-position="right"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            v-model="radarForm.axisName.show"
            @change="changeRadarStyle"
          >
            显示外标签
          </el-checkbox>
        </el-col>
        <template v-if="radarForm.axisName.show">
          <el-col span="4"> 距离 </el-col>
          <el-col span="10">
            <el-input-number
              v-model="radarForm.axisNameGap"
              controls-position="right"
              @change="changeRadarStyle"
            ></el-input-number>
          </el-col>
        </template>
      </el-row>
      <template v-if="radarForm.axisName.show">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="10">
            <el-select
              placeholder="请选择"
              @change="changeRadarStyle"
              v-model="radarForm.axisName.fontFamily"
              size="mini"
            >
              <el-option
                v-for="item in PIVOT_CHART_FONT_FAMILIES"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col span="10">
            <el-select
              placeholder="请选择"
              @change="changeRadarStyle"
              v-model="radarForm.axisName.fontSize"
              size="mini"
            >
              <el-option
                v-for="item in PIVOT_CHART_FONT_SIZES"
                :key="item.value"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
          <!-- <el-col span="4">
            <el-color-picker
              v-model="radarForm.axisName.color"
              @change="changeRadarStyle"
            ></el-color-picker>
          </el-col> -->
        </el-row>
      </template>
    </div>
  </div>
</template>

<script>
import {
  CHART_LABEL_POSITIONS,
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  CHART_PIE_LABEL_POSITIONS,
} from "@/globalConstants"
export default {
  name: "radar-section",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      radarForm: {},
      CHART_LABEL_POSITIONS,
      PIVOT_CHART_FONT_SIZES,
      PIVOT_CHART_FONT_FAMILIES,
      CHART_PIE_LABEL_POSITIONS,
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.radarForm = this._.cloneDeep(this.chartData.chartStyles.radar)
    },
    changeRadarStyle() {
      this.$emit("changeStyle", "radar", this.radarForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
