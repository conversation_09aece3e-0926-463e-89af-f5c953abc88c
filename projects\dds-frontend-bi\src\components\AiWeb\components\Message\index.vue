<template>
  <div
    class="flex w-full mb-6 overflow-hidden"
    :class="[{ 'flex-row-reverse': inversion }]"
  >
    <div
      v-if="!inversion"
      class="flex items-center justify-center flex-shrink-0 h-10 overflow-hidden rounded-full basis-8"
      :class="[inversion ? 'ml-2' : 'mr-2']"
    >
      <AvatarComponent :image="inversion" />
    </div>
    <div
      class="overflow-hidden text-sm"
      :class="[inversion ? 'items-end' : 'items-start']"
    >
      <!-- <p
        class="text-xs text-[#b4bbc4]"
        :class="[inversion ? 'text-right' : 'text-left']"
      >
        {{ dateTime }}
      </p> -->
      <div
        class="flex items-end gap-1  "
        :class="[inversion ? 'flex-row-reverse' : 'flex-row']"
      >
        <TextComponent
          ref="textRef"
          :inversion="inversion"
          :error="error"
          :text="text"
          :duration="duration"
          :pre-text="preText"
          :loading="loading"
        />
        <div class="flex flex-col">
          <!-- <button
            icon="el-icon-refresh-right"
            v-if="!inversion"
            class="mb-2 transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-300"
            @click="handleRegenerate"
          >
            充值
          </button> -->

          <i
            v-if="!inversion"
            @click="handleRegenerate"
            class="el-icon-refresh-right cursor-pointer mb-2 transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-300"
          ></i>

          <!-- <NDropdown
            :placement="!inversion ? 'right' : 'left'"
            :options="options"
            @select="handleSelect"
          >
            <button
              class="transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-200"
            >
              <SvgIcon icon="ri:more-2-fill" />
            </button>
          </NDropdown> -->
          <!-- <el-dropdown @command="handleSelect">
            <span class="el-dropdown-link">
              <svg-icon
                class="icon"
                @click="downloadVisible = true"
                icon-class="more3"
              />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-document-copy" command="copyText">
                复制
              </el-dropdown-item>
              <el-dropdown-item icon="el-icon-delete-solid" command="delete">
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { copyText } from "@/utils/format"
import AvatarComponent from "./Avatar.vue"
import TextComponent from "./Text.vue"

export default {
  components: { AvatarComponent, TextComponent },
  props: {
    dateTime: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    },
    preText: {
      type: String,
      default: ""
    },
    inversion: {
      type: Boolean,
      default: false
    },
    error: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    duration: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      options: [
        {
          label: "复制",
          key: "copyText",
          icon: "ri:file-copy-2-line"
        },
        {
          label: "删除",
          key: "delete",
          icon: "ri:delete-bin-line"
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleRegenerate() {
      this.$emit("regenerate")
    },
    handleSelect(key) {
      switch (key) {
        case "copyText":
          copyText({ text: this.text ?? "" })
          return
        case "delete":
          this.$emit("delete")
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
