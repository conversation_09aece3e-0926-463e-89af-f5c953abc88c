import ChartTypes from './ChartTypes'
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  CHART_FUNNEL_LABEL_POSITIONS,
  CHART_SORT_MODES,
  CHART_ALIGNMENT_MODES
} from "@/globalConstants"

const funnel= {
  id: ChartTypes.Funnel,
  name: 'funnel',
  title: '漏斗图',
  icon: 'chart_funnel',
  coordinate: 'cartesian',
  rules: [ { dimension: 1, metric: 1 }, { dimension: 0, metric: [ 2, 9999 ] } ],
  dimetionAxis: 'col',
  data: [
    {
      title: "维度",
      type: "category",
      name: "cols",
    },
    {
      title: "指标",
      type: "value",
      name: "metrics",
    },
    {
      title: "颜色",
      type: "category",
      name: "color",
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '筛选',
    //   type: 'all',
    //   name: "filters"

    // }
  ],
  style: {
    label: {
      showLabel: false,
      funnelLabelPosition: CHART_FUNNEL_LABEL_POSITIONS[0].value,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: '12',
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
      labelParts: [ 'dimensionValue', 'indicatorValue' ],
      showLegend: true,
      legendPosition: "right",
      right: 0,
      top: 0,
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR,
      orient: 'horizontal',
      itemWidth: 8,
      itemHeight: 2,
      icon: 'rect'
    },
    legend: {
      showLegend: true,
      legendPosition: 'right',
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR
    },
    spec: {
      sortMode: CHART_SORT_MODES[0].value,
      alignmentMode: CHART_ALIGNMENT_MODES[0].value,
      gapNumber: 1
    },
  }
}

export default funnel
