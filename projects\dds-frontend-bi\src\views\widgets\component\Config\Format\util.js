

export function getFormatConfig(formatType) {
  switch (formatType) {
  case "default":
    return  
  case "numeric":
    return  {
      decimalPlaces: 2,
      unit: "无",
      useThousandSeparator: true,
    }
  case "currency":
    return {
      decimalPlaces: 2,
      unit: "无",
      useThousandSeparator: true,
      prefix: "",
      suffix: "",
    }
  case "percentage":
    return  {
      decimalPlaces: 2,
    }
  case "scientificNotation":
    return {
      decimalPlaces: 2,
     
    }
  default:
    return null
  }
}
export function getFormattedValue(value, format) {
  if (!format || format.formatType === 'default') { return value }
  if (value === null || value === undefined) { return value }

  const { formatType } = format
  if (typeof value === 'string'&& (!value || isNaN(+value))) { return value }

  const config = format[formatType]
  let formattedValue

  switch (formatType) {
  case 'numeric':
  case 'currency':
    var {
      decimalPlaces,
      unit,
      useThousandSeparator } = config
    formattedValue = formatByUnit(value, unit)
    formattedValue = formatByDecimalPlaces(formattedValue, decimalPlaces)
    formattedValue = formatByThousandSeperator(formattedValue, useThousandSeparator)
    if (unit !== '无') {
      formattedValue = `${formattedValue}${unit}`
    }
    if (formatType === 'currency') {
      const { prefix, suffix } = config 
      formattedValue = [ prefix, formattedValue, suffix ].join('')
    }
    break
  case 'percentage':
    formattedValue = (+value) * 100
    formattedValue = isNaN(formattedValue) ? value
      : `${formatByDecimalPlaces(formattedValue,config.decimalPlaces)}%`
    break
  case 'scientificNotation':
    formattedValue = (+value).toExponential(config.decimalPlaces)
    formattedValue = isNaN(formattedValue) ? value : formattedValue
    break
  default:
    formattedValue = value
    break
  }
  return formattedValue
}


/**
 * 格式化小数位数
 * @param {string | number} value   
 * @param {number} decimalPlaces
 * @returns {number}
 */
function formatByDecimalPlaces(value, decimalPlaces) {
  if (isNaN(value)) { return value }
  if (decimalPlaces < 0 || decimalPlaces > 100) { return value }

  return (+value).toFixed(decimalPlaces)
}

/**
 * 千位分隔符
 * @param {string | number} value   
 * @param {boolean} useThousandSeparator
 * @returns {number}
 */
function formatByThousandSeperator(value, useThousandSeparator) {
  if (isNaN(+value) || !useThousandSeparator) { return value }

  const parts = value.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  const formatted = parts.join('.')
  return formatted
}


/**
 * 单位
 * @param {string | number} value   
 * @param {string} unit
 * @returns {number}
 */

function formatByUnit(value, unit) {
  const numericValue = +value
  if (isNaN(numericValue)) { return value }

  let exponent = 0
  switch (unit) {
  case '万':
    exponent = 4
    break
  case '亿':
    exponent = 8
    break
  case 'k':
    exponent = 3
    break
  case 'M':
    exponent = 6
    break
  case 'G':
    exponent = 9
    break
  }
  return numericValue / Math.pow(10, exponent)
}


