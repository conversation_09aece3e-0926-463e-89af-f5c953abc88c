<template>
  <div class="table-wrapper">
    <!-- 如果是根源于不渲染tip -->
    <div
      v-if="!root"
      class="table-link-tip"
      :style="{
        top: getTop(index, 43, parTop) + 'px',
        left: item.level * 180 + (item.level - 1) * 90 + 'px',
      }"
    >
      <!-- 图标 -->
      <div class="table-link-icon-wrapper" @click="handleRelate">
        <i class="el-icon-set-up"></i>
      </div>
      <!-- 横线 -->
      <div
        class="table-link-line horizontal"
        :style="{
          width: index ? '72px' : '90px',
          top: '14px',
        }"
      ></div>
      <!-- 竖线 -->
      <div
        class="table-link-line vertical"
        :style="{
          height: getTop(index, 43, 0) + 'px',
          bottom: '14px',
        }"
      ></div>
    </div>
    <!-- 表信息 -->
    <div
      class="table-info"
      @click="handleFieldInfo"
      :style="{
        top: getTop(index, 42, parTop) + 'px',
        left: item.level * 270 + 'px',
      }"
    >
      <div class="text">
        {{ item.target }}
      </div>
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          <i class="el-icon-more-outline"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            :command="{
              type: 'del',
              item,
            }"
            icon="el-icon-delete"
          >
            删除
          </el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <template v-if="index == tree.length - 1 && !root">
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        show-vertical
        :level="item.level"
        :source="item.source"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop) + 42"
        :tip-top="getTop(index, 43, parTop) + 43"
        :tip-height="getTop(index, 43, 0) + 43"
      />
    </template>
    <!-- 如果有子元素递归渲染 -->
    <template v-if="item.children && item.children.length">
      <table-wrapper
        v-for="(e, i) in item.children"
        :item="e"
        :key="i"
        :tree="item.children"
        :dargging="dargging"
        :index="i"
        v-on="$listeners"
        :par-top="getTop(index, 42, parTop)"
      />
    </template>
    <template v-else>
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        :source="item.target"
        :level="item.level + 1"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop)"
        :tip-top="getTop(index, 43, parTop)"
      />
    </template>
  </div>
</template>

<script>
import DropBox from "./DropBox"
export default {
  name: "table-wrapper",
  components: { DropBox },
  props: {
    item: {
      type: Object,
    },
    index: {
      type: Number,
    },
    tree: {
      type: Array,
    },
    parTop: {
      type: Number,
      default: 0,
    },
    root: {
      type: Boolean,
      default: false,
    },
    dargging: {
      type: Boolean,
      default: false,
    },
    drawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 表字段详情
    handleFieldInfo() {
      this.$emit("fieldInfo", this.item)
    },
    getTop(index, num, top = 0) {
      if (index) {
        return (this.tree[index - 1].children.length + index) * num + top
      } else {
        return index * num + top
      }
    },
    handleRelate() {
      this.$emit("handleRelate", this.item)
    },
    handleCommand(command) {
      if (command.type === "del") {
        this.$emit("delDropBox", command.item)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.table-wrapper {
  position: absolute;

  .table-link-tip {
    width: 90px;
    height: 28px;
    position: absolute;
    align-items: center;
    display: flex;
    justify-content: center;
    cursor: pointer;

    .table-link-line {
      position: absolute;
      z-index: 2;
      background: #c1c1c1;
    }

    .table-link-line.vertical {
      width: 1px;
      left: 18px;
    }

    .table-link-line.horizontal {
      right: 0;
      height: 1px;
    }
  }

  .table-link-tip:hover .table-link-line {
    background: #2153d4;
  }

  .table-info {
    height: 28px;
    width: 180px;
    background: #fff;
    position: absolute;
    color: #000;
    cursor: pointer;
    padding: 1px 1px 1px 0;
    line-height: 28px;
    border-right: 1px solid transparent;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    border-left: 2px solid #2153d4;
    display: flex;
    .text {
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    // .dropdown {
    //   display: none;
    // }
  }

  .table-info:hover {
    border-right: 1px solid #2153d4;
    border-top: 1px solid #2153d4;
    border-bottom: 1px solid #2153d4;
  }
}
</style>
