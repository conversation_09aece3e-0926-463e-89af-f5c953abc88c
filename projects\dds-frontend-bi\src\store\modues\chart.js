let chart = {

  namespaced: true,
  state: {
    dimensions: [],
    metric: [],
    secondaryMetrics:[],
    mergeData:null
  },
  mutations: {
    SET_DIMENSIONS(state, cols) {
      state.dimensions = cols
    },
    SET_METRIC(state, cols) {
      state.metric = cols
    },
    SET_SENCONDARY_METRIC(state,cols){
      state.secondaryMetrics = cols
    },
    SET_MERGEDATA(state,cols){
      state.mergeData = cols
    }
  },

  actions: {

  },
  getters: {

  },
}
export default chart
