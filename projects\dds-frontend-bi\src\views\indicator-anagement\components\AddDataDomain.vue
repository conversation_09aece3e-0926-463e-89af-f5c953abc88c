<template>
  <el-dialog
    :title="title"
    :visible.sync="addDataDomainVisible"
    :close-on-click-modal="false"
    width="40%"
    :before-close="handleClose"
  >
    <avue-form v-model="form" ref="form" :option="option"></avue-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="validate">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "新增数据域"
    },
    addDataDomainVisible: {
      type: Boolean,
      default: false
    },
    viewGroup: {
      type: Array,
      default: () => []
    },
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      option: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            span: 24,
            label: "上级",
            prop: "parentId",
            type: "tree",
            props: {
              label: "name",
              value: "id"
            },
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择上级",
                trigger: "change"
              }
            ]
          },
          {
            span: 24,
            label: "名称",
            prop: "name",
            rules: [
              {
                required: true,
                message: "请输入名称",
                trigger: "change"
              }
            ]
          },
          {
            span: 24,
            label: "描述",
            prop: "description",
            type: "textarea"
          },
        ]
      }
    }
  },
  computed: {},
  created() {
    console.log(this.viewGroup)
    this.option.column[0].dicData = this.viewGroup
    console.log(this.option)
  },
  mounted() {},
  watch: {},
  methods: {
    handleClose() {
      this.$emit("update:addDataDomainVisible", false)
    },
    validate() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (this.form.id) {
            const { code } = await this.$httpBi.indicatorAnagement.editZty(
              this.form
            )
            if (code === 200) {
              this.$message.success("修改成功")
              this.$emit("refresh")
              this.$emit("update:addDataDomainVisible", false)
            }
          } else {
            const { code } = await this.$httpBi.indicatorAnagement.addZty(
              this.form
            )
            if (code === 200) {
              this.$message.success("新增成功")
              this.$emit("refresh")
              this.$emit("update:addDataDomainVisible", false)
            }
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
