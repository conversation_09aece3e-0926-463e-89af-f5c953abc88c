<template>
  <div class="workbench">
    <!-- 图表操作头部 -->
    <ChartHeaderPanel />
    <div class="body">
      <!-- 图表操作面板 -->
      <ChartOperationPanel
        :views="views"
        :selected-view-id="selectedViewId"
        :selected-view="selectedView"
        @onViewSelect="viewSelect"
      />
    </div>
  </div>
</template>

<script>
import ChartHeaderPanel from "./components/ChartHeaderPanel/index.js"
import ChartOperationPanel from "./components/ChartOperationPanel/index.js"
import "splitpanes/dist/splitpanes.css"
export default {
  components: {
    ChartHeaderPanel,
    ChartOperationPanel,
  },
  props: {},
  data() {
    return {
    }
  },
  computed: {},
  created() {
  },
  mounted() {},
  watch: {},
  methods: {
   
  },
}
</script>

<style scoped lang="scss">
.workbench {
  background-color: #eaedf1;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  .body {
    height: calc(100vh - 60px);
    .viewPanel {
      flex: 1;
      min-width: 0;
      padding: 16px;
      display: flex;
      flex-direction: column;
      .widgetBlock {
        flex: 1;
        background-color: #fff;
        padding: 16px;
        box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        min-height: 0;
      }
    }
  }
}

</style>
