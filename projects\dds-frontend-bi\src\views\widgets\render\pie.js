import { decodeMetricName, getTextWidth } from "../component/util.js"
import { getLegendOption, getLabelOption } from "./util"
import pie from '../config/pie'
export default function(chartProps, ) {
  const {  data, cols, metrics, chartStyles, color,  } =
    chartProps
  const { label, legend, spec, title, } = chartStyles
  const { circle, roseType, x, y, outerRadius, innerRadius, radius, borderWidth } = spec
  const labelOption = {
    label: getLabelOption("pie", label, metrics),
  }
  const roseTypeValue = roseType ? "radius" : ""
  const radiusValue =
    (!circle && !roseType) || (!circle && roseType) ? radius + '%' : [ innerRadius + "%", outerRadius + "%" ]

  let seriesObj = {}
  const seriesArr = []
  let legendData = []
  let grouped = {}
  let seriesData = []

  if (metrics.length <= 1) {
    const groupColumns = color
      .map((c) => c.displayName)
      .concat(cols.map((c) => c.displayName))
      .reduce((distinctColumns, col) => {
        if (!distinctColumns.includes(col)) {
          distinctColumns.push(col)
        }
        return distinctColumns
      }, [])
    grouped = data.reduce((obj, val) => {
      const groupingKey = groupColumns
        .reduce((keyArr, col) => keyArr.concat(val[col]), [])
        .join(String.fromCharCode(0))
      if (!obj[groupingKey]) {
        obj[groupingKey] = []
      }
      obj[groupingKey].push(val)
      return obj
    }, {})

    metrics.forEach((metric, ) => {
      const decodedMetricName = decodeMetricName(metric.name)

      Object.entries(grouped).forEach(([ key, value ]) => {
        const legendStr = key.replace(String.fromCharCode(0), " ")
        legendData.push(legendStr)
        value.forEach((v) => {
          const obj = {
            name: legendStr,
            value: v[`${metric.agg}(${decodedMetricName})`],
          }
          seriesData.push(obj)
        })
      })
      seriesObj = {
        name: "",
        type: "pie",
        avoidLabelOverlap: false,
        center: [ x + '%', y + '%' ],


        // center: legend.showLegend
        //   ? [leftValue, topValue]
        //   : [width / 2, height / 2],
        data: seriesData.map((data, ) => {
          return {
            ...data,
            itemStyle: {
              // color:
              //   (color.length && color[0].values[i]?.color) ||
              //   defaultThemeColors[i],
            },
          }
        }),
        itemStyle: {
          borderWidth,
        },
        ...labelOption,
        roseType: roseTypeValue,
        radius: radiusValue,
      }
      seriesArr.push(seriesObj)
    })
  } else {
    console.log(';;;;;')
    legendData = []
    seriesData = metrics.map((m) => {
      legendData.push(m.field.alias ?? m.displayName)
      return {
        name: m.field.alias ?? m.displayName,
        value: data.reduce(
          (sum, record) => sum + record[`${m.agg}(${m.displayName})`],
          0
        ),
        itemStyle: {
          // color: m.color ?? defaultThemeColors[index],
        },
      }
    })
    seriesObj = {
      type: "pie",
      avoidLabelOverlap: false,
      // center: [5width / 2, height / 2],
      center: [ x + '%', y + '%' ],

      itemStyle: {
        borderWidth,
      },
      data: seriesData,
      ...labelOption,
      roseType: roseTypeValue,
      radius: radiusValue,
    }
    seriesArr.push(seriesObj)
  }
  const tooltip = {
    trigger: "item",
    confine: true,

    // backgroundColor: "rgba(50,50,50,0.7)",
    // textStyle: { color: "#fff" },
    // formatter(params) {
    //   const { color, name, value, percent, dataIndex } = params;
    //   const tooltipLabels = [];
    //   if (color) {
    //     tooltipLabels.push(
    //       `<span  style="background:${color};width:10px;height:10px;border-radius:50%;display:inline-block;margin-right: 5px;"></span>`
    //     );
    //   }
    //   tooltipLabels.push(
    //     `${name}<br/>${getFormattedValue(
    //       value,
    //       metrics[metrics.length > 1 ? dataIndex : 0].format
    //     )}（${percent}%）`
    //   );
    //   return tooltipLabels.join("");
    // },
  }
  const legendDataMax = legendData.reduce((a, b) => {
    return getTextWidth(a) > getTextWidth(b) ? a : b
  })
  // 将seriesData中value相加
  const sum = seriesData.reduce((a, b) => {
    return a + b.value
  }, 0)
  return {
    title: {
      ...pie.style.title,
      ...title,
      x: title?.x + "%",
      y: title?.y + "%",
      text: sum
    },
    tooltip,
    legend: {
      ...getLegendOption(legend, legendData, getTextWidth(legendDataMax)),

      formatter: (name) => {
        // 单指标
        // 将seriesData中的数据按照name和name相同过滤
        const value = seriesData.filter(item => item.name === name)[0].value
        return `{b|${name}}{a|${value}} `
      }
    },
    series: seriesArr,
  }
}
