export const BorderImageLayerConfig = {
  code: 6,
  type: "borderImage",
  tabName: "文本栏",
  label: "边框图片",
  icon: "el-icon-picture-outline",
  component: "BorderImageLayer",
  options: {
    // 配置
    style: [
      {
        type: "el-input-number",
        label: "左边距",
        name: "left",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "上边距",
        name: "top",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "宽度",
        name: "width",
        required: false,
        placeholder: "",
        value: 350
      },
      {
        type: "el-input-number",
        label: "高度",
        name: "height",
        required: false,
        placeholder: "",
        value: 250
      },
      {
        type: "el-select",
        label: "装饰类型",
        name: "component",
        required: false,
        placeholder: "",
        selectOptions: [
          {
            name: "边框1",
            url: require("@/assets/images/borderBox_01.gif"),
            code: "dv-border-box-1"
          },
          {
            name: "边框2",
            url: require("@/assets/images/borderBox_02.png"),
            code: "dv-border-box-2"
          },
          {
            name: "边框3",
            url: require("@/assets/images/borderBox_03.png"),
            code: "dv-border-box-3"
          },
          {
            name: "边框4",
            url: require("@/assets/images/borderBox_04.png"),
            code: "dv-border-box-4"
          },
          {
            name: "边框5",
            url: require("@/assets/images/borderBox_05.png"),
            code: "dv-border-box-5"
          },
          {
            name: "边框6",
            url: require("@/assets/images/borderBox_06.png"),
            code: "dv-border-box-6"
          },
          {
            name: "边框7",
            url: require("@/assets/images/borderBox_07.png"),
            code: "dv-border-box-7"
          },
          {
            name: "边框8",
            url: require("@/assets/images/borderBox_08.gif"),
            code: "dv-border-box-8"
          },
          {
            name: "边框9",
            url: require("@/assets/images/borderBox_09.png"),
            code: "dv-border-box-9"
          },
          {
            name: "边框10",
            url: require("@/assets/images/borderBox_10.png"),
            code: "dv-border-box-10"
          },
          {
            name: "边框11",
            url: require("@/assets/images/borderBox_11.png"),
            code: "dv-border-box-11"
          },
          {
            name: "边框12",
            url: require("@/assets/images/borderBox_12.png"),
            code: "dv-border-box-12"
          }
        ],
        value: "dv-border-box-1"
      },
      {
        type: "el-input-color",
        label: "主颜色",
        name: "mainColor",
        required: false,
        placeholder: "",
        value: "rgba(131, 191, 246, 1)"
      },
      {
        type: "el-input-color",
        label: "副颜色",
        name: "viceColor",
        required: false,
        placeholder: "",
        value: "rgba(0, 206, 209, 1)"
      },
      {
        type: "el-input-color",
        label: "背景颜色",
        name: "backgroundColor",
        required: false,
        placeholder: "",
        value: "rgba(28, 27, 27, 0)"
      },
      {
        type: "el-input-number",
        label: "动画时长",
        name: "dur",
        required: false,
        placeholder: "",
        value: 3
      }
    ],
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: "外链"
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
  }
}
