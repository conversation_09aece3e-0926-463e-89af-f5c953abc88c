import mean from "lodash/mean"
import { CHART_LEGEND_POSITIONS, DEFAULT_SPLITER } from "@/globalConstants"
import {
  metricAxisLabelFormatter,
  decodeMetricName,
  getTextWidth,
  getAggregatorLocale
} from "../component/util"
import { getFormattedValue } from "../component/Config/Format/util"
import { getFieldAlias } from "../component/Config/Field/util"

export function getDimetionAxisOption(
  dimetionAxisConfig,
  splitLineConfig,
  data
) {
  const {
    inverse,
    showLine: showLineX,
    lineStyle: lineStyleX,
    lineSize: lineSizeX,
    lineColor: lineColorX,
    showLabel: showLabelX,
    labelFontFamily: labelFontFamilyX,
    labelFontSize: labelFontSizeX,
    nameLocation,
    nameGap,
    nameRotate,
    showInterval,
    xAxisInterval,
    xAxisRotate
  } = dimetionAxisConfig

  const { showLine, lineStyle, lineSize } = splitLineConfig

  const intervalOption = showInterval ? { interval: xAxisInterval } : null

  return {
    data,
    inverse,
    axisLabel: {
      show: showLabelX,
      // color: labelColorX,
      fontFamily: labelFontFamilyX,
      fontSize: Number(labelFontSizeX),
      rotate: xAxisRotate,
      ...intervalOption
    },
    axisLine: {
      show: showLineX,
      lineStyle: {
        color: lineColorX,
        width: Number(lineSizeX),
        type: lineStyleX
      }
    },
    axisTick: {
      show: showLabelX,
      lineStyle: {
        // color: lineColorX,
      }
    },
    splitLine: {
      show: showLine,
      lineStyle: {
        // color: lineColor,
        width: Number(lineSize),
        type: lineStyle
      }
    },
    nameLocation,
    nameRotate,
    nameGap
  }
}

export function getMetricAxisOption(
  metricAxisConfig,
  splitLineConfig,
  title,
  axis,
  percentage
) {
  const {
    inverse,
    showLine: showLineY,
    lineStyle: lineStyleY,
    lineSize: lineSizeY,
    showLabel: showLabelY,
    labelFontFamily: labelFontFamilyY,
    labelFontSize: labelFontSizeY,
    showTitleAndUnit,
    titleFontFamily,
    titleFontSize,
    name,
    nameLocation,
    nameRotate,
    nameGap,
    min,
    max
  } = metricAxisConfig

  const { showLine, lineStyle, lineSize } = splitLineConfig

  return {
    type: "value",
    inverse,
    min: min === 0 ? null : min,
    max: max === 0 ? null : max,
    axisLabel: {
      show: showLabelY,
      // color: labelColorY,
      fontFamily: labelFontFamilyY,
      fontSize: Number(labelFontSizeY),
      formatter: percentage ? "{value}%" : metricAxisLabelFormatter
    },
    axisLine: {
      show: showLineY,
      lineStyle: {
        // color: lineColorY,
        width: Number(lineSizeY),
        type: lineStyleY
      }
    },
    axisTick: {
      show: showLabelY,
      lineStyle: {
        // color: lineColorY,
      }
    },
    name: showTitleAndUnit ? name : "",
    nameLocation,
    nameGap,
    nameRotate,
    nameTextStyle: {
      // color: titleColor,
      fontFamily: titleFontFamily,
      fontSize: Number(titleFontSize)
    },
    splitLine: {
      show: showLine,
      lineStyle: {
        // color: lineColor,
        width: Number(lineSize),
        type: lineStyle
      }
    }
  }
}

export function getLabelOption(type, labelConfig, metrics, emphasis, options) {
  const {
    showLabel,
    labelPosition,
    labelFontFamily,
    labelColor,
    labelFontSize,
    pieLabelPosition,
    funnelLabelPosition
  } = labelConfig

  let position
  switch (type) {
    case "pie":
      position = pieLabelPosition
      break
    case "funnel":
      position = funnelLabelPosition
      break
    default:
      position = labelPosition
      break
  }

  let formatter

  switch (type) {
    case "line":
    case "bar":
      formatter = params => {
        const { value, seriesId } = params
        const m = metrics.find(
          m =>
            m.name === seriesId.split(`${DEFAULT_SPLITER}${DEFAULT_SPLITER}`)[0]
        )
        const formattedValue = getFormattedValue(value, m.format)
        return formattedValue
      }
      break
    case "doubleYAxis":
      formatter = params => {
        const { value, seriesName } = params
        const m = metrics.find(m => m.displayName === seriesName)
        const formattedValue = getFormattedValue(value, m.format)
        return formattedValue
      }
      break

    case "waterfall":
      formatter = params => {
        const { value } = params
        const formattedValue = getFormattedValue(value, metrics[0].format)
        return formattedValue
      }
      break
    case "scatter":
      formatter = params => {
        const { value } = params
        const formattedValue = getFormattedValue(value[0], metrics[0].format)
        return formattedValue
      }
      break
    case "pie":
    case "funnel":
      formatter = params => {
        const { name, value, percent, dataIndex, data } = params
        const formattedValue = getFormattedValue(
          value,
          metrics[metrics.length > 1 ? dataIndex : 0].format
        )
        const { labelParts } = labelConfig
        if (!labelParts) {
          return `${name}\n${formattedValue}（${percent}%）`
        }
        const labels = []
        const multiRate =
          labelParts.filter(label =>
            ["percentage", "conversion", "arrival"].includes(label)
          ).length > 1
        if (labelParts.includes("dimensionValue")) {
          labels.push(name)
        }
        if (labelParts.includes("indicatorValue")) {
          labels.push(formattedValue)
        }
        if (labelParts.includes("conversion") && data.conversion) {
          labels.push(`${multiRate ? "转化率：" : ""}${data.conversion}%`)
        }
        if (labelParts.includes("arrival") && data.arrival) {
          labels.push(`${multiRate ? "到达率：" : ""}${data.arrival}%`)
        }
        if (labelParts.includes("percentage")) {
          labels.push(`${multiRate ? "百分比：" : ""}${percent}%`)
        }
        return labels.join("\n")
      }
      break
    case "radar":
      formatter = params => {
        const { name, value, dataIndex, data } = params
        const metricIdx = data.name ? dataIndex : data.value.indexOf(value)
        const formattedValue = getFormattedValue(
          value,
          metrics[metricIdx].format
        )
        const labelName =
          name ||
          getFieldAlias(metrics[metricIdx].field, {}) ||
          decodeMetricName(metrics[metricIdx].name)
        const { labelParts } = labelConfig
        if (!labelParts) {
          return `${labelName}\n${formattedValue}`
        }
        const labels = []
        if (labelParts.includes("indicatorName")) {
          labels.push(labelName)
        }
        if (labelParts.includes("indicatorValue")) {
          labels.push(formattedValue)
        }
        if (labels.length > 1) {
          labels.splice(1, 0, "\n")
        }
        return labels.join("")
      }
      break
    case "lines":
      formatter = param => {
        const { name } = param
        return name
      }
      break
  }

  const labelOption = {
    show: type === "pie" && pieLabelPosition === "center" ? false : showLabel,
    position,
    distance: 15,
    color: labelColor,
    fontFamily: labelFontFamily,
    fontSize: labelFontSize,
    formatter,
    ...options,
    ...(emphasis && {
      emphasis: {
        show: showLabel,
        position,
        distance: 15,
        color: labelColor,
        fontFamily: labelFontFamily,
        fontSize: labelFontSize,
        formatter,
        ...options
      }
    })
  }

  return labelOption
}

export function getLegendOption(legendConfig, seriesNames, textWidth = 50) {
  const {
    showLegend,
    itemWidth,
    itemHeight,
    orient,
    selectAll,
    fontFamily,
    icon,
    fontSize,
    right,
    top
  } = legendConfig

  let positions = {
    top: top + "%",
    right: right + "%"
  }

  // switch (legendPosition) {
  //   case "top":
  //     orient = { orient: "horizontal" };
  //     positions = { top: 8, left: 8, right: 8, height: 32 };
  //     break;
  //   case "bottom":
  //     orient = { orient: "horizontal" };
  //     positions = { bottom: 8, left: 8, right: 8, height: 32 };
  //     break;
  //   case "left":
  //     orient = { orient: "vertical" };
  //     positions = { left: 8, top: 16, bottom: 24, width: 96 };
  //     break;
  //   default:
  //     orient = { orient: "vertical" };
  //     positions = { right: 8, top: 16, bottom: 24, width: 96 };
  //     break;
  // }

  const selected = {
    selected: seriesNames.reduce(
      (obj, name) => ({
        ...obj,
        [name]: selectAll
      }),
      {}
    )
  }

  return {
    show: showLegend && seriesNames.length > 1,
    data: seriesNames,
    type: "scroll",
    textStyle: {
      fontFamily,
      fontSize,
      // color,
      rich: {
        b: {
          width: Number(textWidth) + 21
        }
      }
    },

    // ...orient,
    orient,
    itemWidth,
    itemHeight,
    ...positions,
    ...selected,
    icon
  }
}

export function getGridPositions(
  legendConfig,
  seriesNames,
  chartName,
  isHorizontalBar,
  yAxisConfig,
  dimetionAxisConfig,
  xAxisData
) {
  const { showLegend, legendPosition, fontSize } = legendConfig
  return CHART_LEGEND_POSITIONS.reduce((grid, pos) => {
    const val = pos.value
    grid[val] = getGridBase(
      val,
      chartName,
      dimetionAxisConfig,
      xAxisData,
      isHorizontalBar,
      yAxisConfig
    )
    if (showLegend && seriesNames.length > 1) {
      grid[val] +=
        legendPosition === val
          ? ["top", "bottom"].includes(val)
            ? 64
            : 64 +
              Math.max(
                ...seriesNames.map(s => getTextWidth(s, "", `${fontSize}px`))
              )
          : 0
    }
    return grid
  }, {})
}

function getGridBase(
  pos,
  chartName,
  dimetionAxisConfig,
  xAxisData,
  isHorizontalBar,
  yAxisConfig
) {
  const labelFontSize = dimetionAxisConfig
    ? dimetionAxisConfig.labelFontSize
    : 12
  const xAxisRotate = dimetionAxisConfig ? dimetionAxisConfig.xAxisRotate : 0
  const maxWidth =
    xAxisData && xAxisData.length
      ? Math.max(
          ...xAxisData.map(s => getTextWidth(s, "", `${labelFontSize}px`))
        )
      : 0

  const bottomDistance =
    dimetionAxisConfig && dimetionAxisConfig.showLabel
      ? isHorizontalBar
        ? 50
        : xAxisRotate
        ? 50 + Math.sin((xAxisRotate * Math.PI) / 180) * maxWidth
        : 50
      : 50

  const yAxisConfigLeft =
    yAxisConfig && !yAxisConfig.showLabel && !yAxisConfig.showTitleAndUnit
      ? 24
      : 64
  const leftDistance =
    dimetionAxisConfig && dimetionAxisConfig.showLabel
      ? isHorizontalBar
        ? xAxisRotate === void 0
          ? 64
          : 24 + Math.cos((xAxisRotate * Math.PI) / 180) * maxWidth
        : yAxisConfigLeft
      : isHorizontalBar
      ? 24
      : yAxisConfigLeft

  switch (pos) {
    case "top":
      return 24
    case "left":
      return leftDistance
    case "right":
      return chartName === "doubleYAxis" ? 64 : 24
    case "bottom":
      return bottomDistance
  }
}

export function makeGrouped(
  data,
  groupColumns,
  xAxisColumn,
  metrics,
  xAxisData
) {
  const grouped = {}
  data.forEach(d => {
    const groupingKey = groupColumns.map(col => d[col]).join(" ")
    const colKey = d[xAxisColumn] || "default"

    if (!grouped[groupingKey]) {
      grouped[groupingKey] = {}
    }
    if (!grouped[groupingKey][colKey]) {
      grouped[groupingKey][colKey] = []
    }
    grouped[groupingKey][colKey].push(d)
  })

  Object.keys(grouped).forEach(groupingKey => {
    const currentGroupValues = grouped[groupingKey]

    grouped[groupingKey] = xAxisData.length
      ? xAxisData.map(xd => {
          if (currentGroupValues[xd]) {
            return currentGroupValues[xd][0]
          } else {
            return metrics.reduce(
              (obj, m) => ({
                ...obj,
                [`${m.agg}(${decodeMetricName(m.name)})`]: 0
              }),
              {
                [xAxisColumn]: xd
                // []: groupingKey
              }
            )
          }
        })
      : [currentGroupValues["default"][0]]
  })

  return grouped
}

// TODO: function explanation
export function getGroupedXaxis(data, xAxisColumn, metrics) {
  if (xAxisColumn) {
    const metricsInSorting = metrics.filter(
      ({ sort }) => sort && sort.sortType !== "default"
    )
    const appliedMetric = metricsInSorting.length ? metricsInSorting[0] : void 0

    const dataGroupByXaxis = data.reduce((grouped, d) => {
      const colKey = d[xAxisColumn]
      if (grouped[colKey] === void 0) {
        grouped[colKey] = 0
      }
      if (appliedMetric) {
        const { agg, name } = appliedMetric
        grouped[colKey] += d[`${agg}(${decodeMetricName(name)})`]
      }
      return grouped
    }, {})

    if (appliedMetric) {
      return Object.entries(dataGroupByXaxis)
        .sort((p1, p2) => {
          return appliedMetric.sort.sortType === "asc"
            ? p1[1] - p2[1]
            : appliedMetric.sort.sortType === "desc"
            ? p2[1] - p1[1]
            : 0
        })
        .map(([key]) => key)
    } else {
      return Object.keys(dataGroupByXaxis)
    }
  }
  return []
}

export function getSymbolSize(sizeRate, size) {
  return sizeRate ? Math.ceil(size / sizeRate) : size
}

export function getCartesianChartMetrics(metrics) {
  return metrics.map(metric => {
    const { name, agg } = metric
    const decodedMetricName = decodeMetricName(name)
    const duplicates = metrics.filter(
      m => decodeMetricName(m.name) === decodedMetricName && m.agg === agg
    )
    const prefix = agg !== "sum" ? `[${getAggregatorLocale(agg)}] ` : ""
    const suffix =
      duplicates.length > 1
        ? duplicates.indexOf(metric)
          ? duplicates.indexOf(metric) + 1
          : ""
        : ""
    return {
      ...metric,
      displayName: `${prefix}${decodedMetricName}${suffix}`
    }
  })
}

export function getCartesianChartReferenceOptions(
  references,
  chartType,
  metrics,
  sourcedata,
  barChart
) {
  if (references) {
    const markLines = []
    const markAreas = []

    references.forEach(ref => {
      const { name, type, data } = ref

      if (type === "line") {
        const { metric, type: valueType, value, label, line } = data

        const axis = getReferenceDataMetricAxis(chartType, {
          barChart,
          metrics,
          metric
        })

        if (axis) {
          const metricData = sourcedata.map(d => {
            const metricObject = metrics.find(m => m.displayName === metric)
            return metricObject && d[`${metricObject.agg}(${metric})`]
          })
          markLines.push({
            ...getReferenceDataOptions(metricData, valueType, value, axis),
            name,
            label: {
              show: label.visible,
              position: label.position,
              color: label.font.color,
              fontSize: label.font.size,
              fontFamily: label.font.family
            },
            lineStyle: {
              color: line.color,
              width: line.width,
              type: line.type
            }
          })
        }
      } else {
        const areaData = data.map((d, index) => {
          const { metric, type: valueType, value, label, band } = d
          const axis = getReferenceDataMetricAxis(chartType, {
            barChart,
            metrics,
            metric
          })
          if (axis) {
            const metricData = sourcedata.map(d => {
              const metricObject = metrics.find(m => m.displayName === metric)
              return metricObject && d[`${metricObject.agg}(${metric})`]
            })
            const dataOptions = getReferenceDataOptions(
              metricData,
              valueType,
              value,
              axis
            )
            return !index
              ? dataOptions
              : {
                  ...dataOptions,
                  name,
                  label: {
                    show: label.visible,
                    position: label.position,
                    color: label.font.color,
                    fontSize: label.font.size,
                    fontFamily: label.font.family
                  },
                  emphasis: {
                    label: {
                      position: label.position
                    }
                  },
                  itemStyle: {
                    color: band.color,
                    borderColor: band.border.color,
                    borderWidth: band.border.width,
                    borderType: band.border.type
                  }
                }
          } else {
            return void 0
          }
        })
        if (areaData.every(d => !!d)) {
          markAreas.push(areaData)
        }
      }
    })

    return {
      ...(markLines.length && { markLine: { data: markLines } }),
      ...(markAreas.length && { markArea: { data: markAreas } })
    }
  }
}

function getReferenceDataOptions(metricData, valueType, value, axis) {
  const option = {}
  if (valueType === "constant") {
    option[axis] = value
  } else {
    option[axis] = calcAggregateReferenceData(valueType, metricData)
  }
  return option
}

function getReferenceDataMetricAxis(chartType, options) {
  switch (chartType) {
    case "bar":
      return options.barChart ? "xAxis" : "yAxis"
    case "scatter": {
      const axisIndexMapping = ["xAxis", "yAxis"]
      const metricIndex = options.metrics.findIndex(
        m => m.name === options.metric
      )
      return axisIndexMapping[metricIndex]
    }

    default:
      return "yAxis"
  }
}

function calcAggregateReferenceData(valueType, metricData) {
  switch (valueType) {
    case "max":
      return Math.max(...metricData)
    case "min":
      return Math.min(...metricData)
    case "average":
      return mean(metricData)
  }
}
