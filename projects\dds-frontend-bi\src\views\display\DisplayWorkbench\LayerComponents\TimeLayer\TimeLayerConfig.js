
export const TimeLayerConfig =  {
  code: 5,
  type: 'time',
  tabName: '文本栏',
  label: '时间器',
  icon: 'el-icon-time',
  component: "TimeLayer",
  options: {
    // 配置
    style: [
      {
        type: 'el-input-number',
        label: '左边距',
        name: 'left',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '上边距',
        name: 'top',
        required: false,
        placeholder: '',
        value: 0,
      },
      {
        type: 'el-input-number',
        label: '宽度',
        name: 'width',
        required: false,
        placeholder: '',
        value: 400,
      },
      {
        type: 'el-input-number',
        label: '高度',
        name: 'height',
        required: false,
        placeholder: '',
        value: 85
      },
      {
        type: 'el-select',
        label: '时间格式',
        name: 'timeFormat',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: "YYYY-MM-DD", name: "日期" },
          { code: "YYYY-MM-DD HH:mm", name: "日期+时分" },
          { code: "YYYY-MM-DD HH:mm:ss", name: "日期+时分秒" },
          { code: "MM-DD", name: "日期无年" },
          { code: "HH:mm", name: "时分" },
          { code: "HH:mm:ss", name: "时分秒" },
          { code: "YYYY-MM-DD-week", name: "日期+星期" },
          { code: "YYYY-MM-DD-HH:mm-week", name: "日期+时分+星期" },
          { code: "YYYY-MM-DD-HH:mm:ss-week", name: "日期+时分秒+星期" },
          { code: "week", name: "星期" },
        ],
        value: 'YYYY-MM-DD HH:mm:ss'
      },
      {
        type: 'el-input-number',
        label: '字体间距',
        name: 'letterSpacing',
        required: false,
        placeholder: '',
        value: '0'
      },
      {
        type: 'el-input-number',
        label: '字体字号',
        name: 'fontSize',
        required: false,
        placeholder: '',
        value: '36'
      },
      {
        type: 'el-input-color',
        label: '字体颜色',
        name: 'color',
        required: false,
        placeholder: '',
        value: '#FAD400'
      },
      {
        type: 'el-input-color',
        label: '字体背景',
        name: 'background',
        required: false,
        placeholder: '',
        value: 'rgba(115,170,229,.5)'
      },
      {
        type: 'el-select',
        label: '文字粗细',
        name: 'fontWeight',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'normal', name: '正常' },
          { code: 'bold', name: '粗体' },
          { code: 'bolder', name: '特粗体' },
          { code: 'lighter', name: '细体' }
        ],
        value: 'normal'
      },
      {
        type: 'el-select',
        label: '对齐方式',
        name: 'textAlign',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'center', name: '居中' },
          { code: 'left', name: '左对齐' },
          { code: 'right', name: '右对齐' },
        ],
        value: 'left'
      },
      
    ],
    // 事件
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: '外链'
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
  }
}
