<template>
  <el-select-v2
    v-model="selectedValue"
    filterable
    multiple
    collapse-tags
    @change="handleChange"
    ref="my-select"
    style="width: 100%"
    v-bind="$attrs"
    :placeholder="placeholder"
    :props="props"
    :options="options"
  >
    <template #header>
      <div class="checkboxWrapper" v-if="isSelectedAll">
        <el-checkbox
          v-model="checked"
          @change="checkChange"
          :true-label="true"
          :false-label="false"
        >
          {{ isSelectedAllName }}
        </el-checkbox>
      </div>
    </template>
  </el-select-v2>
</template>

<script>
export default {
  name: "ClusterMultipleSelect",
  directives: {},
  props: {
    value: {
      type: [String, Number, Object],
      default: null
    },
    size: {
      type: Number,
      default: 20
    },
    placeholder: {
      type: String,
      default: "请选择维度值"
    },
    fetchDataMethod: {
      type: Function,
      required: true
    },
    levelCode: {
      type: String,
      required: true
    },
    dimId: {
      type: String,
      default: ""
    },
    indType: {
      type: String,
      default: ""
    },
    isSelectedAll: {
      type: Boolean,
      default: true
    },
    isSelectedAllName: {
      type: String,
      default: "全部"
    },
    dimValues: {
      type: Array,
      default: () => []
    },
    valueCode: {
      type: String,
      default: "valueCode"
    },
    props: {
      type: Object,
      default() {
        return {
          label: "cluName",
          value: "clusterCode"
        }
      }
    }
  },
  data() {
    return {
      selectedValue: this.value, // 双向绑定的值
      options: [], // 存储下拉框选项
      loading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: "",
      total: 0,
      isDropdownOpen: false, // 下拉框是否打开
      tempValue: [],
      checked: false
    }
  },
  watch: {
    levelCode: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      }
    },
    // 监听父组件传入的value，保持双向绑定
    value: {
      handler(newVal) {
        this.selectedValue = newVal
      },
      immediate: true
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    checkChange(val) {
      if (val) {
        // 全选：只选中“全部”标识码
        this.selectedValue = this.options.map(item => item[this.props.value])
      } else {
        // 取消全选：清空
        this.selectedValue = []
      }
      this.$emit("input", this.selectedValue)
      this.$emit("change", this.selectedValue)
      this.getDimValueByClusterCodes(this.selectedValue)
    },
    async fetchData() {
      const { data } = await this.$httpBi.api.paramPostQuery(
        "/DimManage/getDimClusterByLevelCode",
        {
          levelCode: this.levelCode
        }
      )
      this.options = data
      this.loading = false
    },
    handleChange(val) {
      if (val.length === 0) {
        this.checked = false
      } else {
        this.checked = val.length === this.options.length
      }
      this.$emit("input", val)
      this.$emit("change", val)
      this.getDimValueByClusterCodes(val)
    },
    // 批量通过聚类编码查维度值
    async getDimValueByClusterCodes(val) {
      if (val.length === 0) {
        this.$emit("update:dimValues", [])
        return
      }
      const { data } = await this.$httpBi.api.paramPost(
        "/DimManage/getDimValueByClusterCodes",
        val
      )
      this.$emit(
        "update:dimValues",
        data.map(item => item[this.valueCode])
      )
    }
  }
}
</script>

<style scoped lang="scss">
/* 可根据需求自定义样式 */

::v-deep .el-select__tags {
  display: flex;
  flex-wrap: nowrap;
}
</style>
