<template>
  <el-dialog
    v-if="controlConfigVisible"
    title="全局控制器"
    :visible.sync="controlConfigVisible"
    width="95%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    @close="handleCancel"
  >
    <el-scrollbar style="height: 450px; overflow-y: auto">
      <div class="global-control">
        <div class="control-list">
          <div class="control-list_title global-control_title">
            控制器列表
            <el-input
              style="width: 110px"
              v-model="searchControl"
              placeholder="请搜索"
            ></el-input>
            <el-button
              @click="handleAddControl"
              icon="el-icon-plus"
              circle
            ></el-button>
          </div>
          <div class="control-list_body">
            <div
              v-for="(item, index) in controls"
              class="control-item"
              @click="editControl(index)"
              :class="active === index ? 'control-item_active' : ''"
              :key="index"
            >
              <el-input
                class="control-item_input"
                :disabled="active !== index"
                @blur="okControl(index)"
                v-model="item.name"
              />
              <el-button
                style="margin-left: auto"
                @click.stop="editControl(index)"
                type="text"
                icon="el-icon-edit"
                size="mini"
              />
              <el-button
                @click.stop="delControl(index)"
                type="text"
                icon="el-icon-delete"
                size="mini"
              />
            </div>
          </div>
        </div>
        <div class="control-chart" v-if="!!controls.length">
          <div class="global-control_title">
            关联图表
            <el-checkbox
              v-model="checkAll"
              @change="handleCheckAll"
            >
              全选
            </el-checkbox
            >
          </div>
          <div class="control-chart_body">
            <el-checkbox-group
              @change="handleChecked"
              class="control-chart_top"
              v-model="checked"
            >
              <el-checkbox
                v-for="(item, index) in widgetList"
                :key="index"
                :label="item.id"
              >
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
            <div style="">
              <div class="global-control_title">关联数据视图</div>
              <el-form class="control-chart_bottom">
                <div
                  v-if="!Object.keys(currentControl.relatedViews).length"
                  style="text-align: center; color: #999; margin-top: 30px"
                >
                  暂无数据
                </div>
                <div
                  v-for="(item, index) in Object.keys(
                    currentControl.relatedViews
                  )"
                  :key="index"
                  class="control-chart_item"
                >
                  <div class="control-chart_item_head">
                    <div class="control-chart_item_name">
                      {{ currentControl.relatedViews[item].name }}
                    </div>
                    <el-radio-group
                      style="min-width: 112px"
                      @change="(val) => handleChangeFieldType(val, item)"
                      v-model="currentControl.relatedViews[item].fieldType"
                      size="mini"
                    >
                      <el-radio-button label="column">字段</el-radio-button>
                      <el-radio-button label="variable">变量</el-radio-button>
                    </el-radio-group>
                  </div>
                  <!-- numberRange slider dateRange -->
                  <el-select
                    style="width: 100%"
                    :key="currentControl.type + index"
                    :disabled="!!currentControl.optionWithVariable"
                    v-model="currentControl.relatedViews[item].fields[0]"
                    placeholder="请选择"
                    :multiple="
                      ['numberRange', 'slider', 'dateRange'].includes(
                        currentControl.type
                      ) &&
                        currentControl.relatedViews[item].fieldType === 'variable'
                    "
                  >
                    <template
                      v-if="
                        currentControl.relatedViews[item].fieldType ===
                          'variable'
                      "
                    >
                      <el-option
                        v-for="el in currentControl.relatedViews[item]
                          .variables"
                        :label="el"
                        :value="el"
                        :key="el"
                        :disabled="
                          ['numberRange', 'slider', 'dateRange'].includes(
                            currentControl.type
                          ) &&
                            currentControl.relatedViews[item].fieldType ===
                            'variable' &&
                            currentControl.relatedViews[item].fields[0].length ==
                            2
                        "
                      />
                    </template>
                    <template
                      v-else-if="
                        'slider,numberRange'.includes(currentControl.type)
                      "
                    >
                      <el-option
                        v-for="el in currentControl.relatedViews[item]
                          .valueList"
                        :label="el"
                        :value="el"
                        :key="el"
                      />
                    </template>
                    <template v-else>
                      <el-option
                        v-for="(el, index) in currentControl.relatedViews[item]
                          .fieldList"
                        :key="index"
                        :label="el"
                        :value="el"
                      />
                    </template>
                  </el-select>
                </div>
              </el-form>
            </div>
          </div>
        </div>
        <div class="control-config">
          <el-collapse
            v-if="controls.length > 0"
            style="margin-top: 10px"
            :value="['1', '2', '3']"
          >
            <el-form
              label-position="right"
              label-width="130px"
              :rules="controlItemRules"
              :model="currentControl"
              ref="controlFrom"
            >
              <el-collapse-item title="控制器配置" name="2" disabled>
                <controlConf
                  :controls="controls"
                  @changeType="changeType"
                  :data="currentControl"
                  :operator-type.sync="operatorType"
                  :operators="operatorsEnums[operatorType]"
                  :type="currentControl.type"
                />
              </el-collapse-item>
              <el-collapse-item title="取值配置" name="3" disabled>
                <valueConf
                  :id="this.widgetList[0].id"
                  model="global"
                  @manualVaildateField="manualVaildateField"
                  :variable-list="variableList"
                  :operators="operatorsEnums[operatorType]"
                  :data="currentControl"
                  :type="currentControl.type"
                />
              </el-collapse-item>
            </el-form>
          </el-collapse>
        </div>
      </div>
    </el-scrollbar>
    <div
      slot="footer"
      style="width: 100%; display: flex; justify-content: space-between"
    >
      <el-radio-group v-model="queryMode" size="small">
        <el-radio-button :label="0">立即查询</el-radio-button>
        <el-radio-button :label="1">手动查询</el-radio-button>
      </el-radio-group>
      <div>
        <el-button @click="handleCancel">取 消</el-button>
        <el-button @click="handleSubmit" type="primary">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import controlConf from "../widget/components/controlConfRender"
import valueConf from "../widget/components/valueConfRender"
/**
 * UUID生成器
 * @param len 长度 number
 * @param radix 随机数基数 number
 * @returns {string}
 */
export const uuid = (len, radix) => {
  const chars =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("")
  const uuid = []
  let i
  if (len) {
    for (i = 0; i < len; i++) {
      uuid[i] = chars[Math.floor(Math.random() * radix)]
    }
  } else {
    let r
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-"
    uuid[14] = "4"
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = Math.floor(Math.random() * 16)
        uuid[i] = chars[i === 19 ? ((r % 4) % 8) + 8 : r]
      }
    }
  }
  return uuid.join("")
}
const initControlData = () => {
  return {
    name: "新建控制器",
    type: "inputText",
    width: 0,
    label: "false",
    visibility: "visible",
    operator: "=",
    defaultValue: "",
    cache: false,
    min: 0,
    max: 100,
    optionWithVariable: false,
    step: 1,
    customOptions: [],
    radioType: "normal",
    dateFormat: "yyyy-MM-dd",
    multiple: false,
    expired: 300,
    optionType: "auto",
    defaultValueType: "fixed",
    valueViewId: "",
    relatedViews: {},
    // relatedViews1: {},
    conditions: [
      {
        control: "",
        operator: "=",
        value: "",
      },
    ],
    checked: [],
    valueField: "",
    textField: "",
    parentField: "",
  }
}
export default {
  components: {
    controlConf,
    valueConf,
  },
  props: {
    queryMode: {
      type: Number,
      default: 0,
    },
    globalFiltersConfig: Array,
    widgetList: Array,
    controlConfigVisible: {
      type: Boolean,
    },
    currentDashborad: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchControl: "",
      active: 0,
      variableList: [],
      checkAll: false,
      isEditNames: [],
      allChecked: false,
      checked: [],
      currentControl: {},
      controls: [],
      operatorType: "portion",
      operatorsEnums: {
        all: [
          { label: "等于", value: "=" },
          { label: "不等于", value: "!=" },
          { label: "大于", value: ">" },
          { label: "大于等于", value: ">=" },
          { label: "小于", value: "<" },
          { label: "小于等于", value: "<=" },
        ],
        portion: [
          { label: "等于", value: "=" },
          { label: "不等于", value: "!=" },
        ],
        range: [
          { label: "在...范围内", value: "in" },
          { label: "不在...范围内", value: "not in" },
        ],
      },
      controlItemRules: {
        valueViewId: [
          { required: true, message: "请选择数据视图", trigger: "change" },
        ],
        valueField: [
          { required: true, message: "请选择取值字段", trigger: "change" },
        ],
        operator: [
          { required: true, message: "请选择对应关系", trigger: "change" },
        ],
      },
    }
  },
  watch: {
    controlConfigVisible(val) {
      this.variableList = []
      if (val) {
        let viewIds = {}
        // this.widgetList.forEach(item => {
        //   if(val.includes(item.id)) viewIds[item.viewId] = true
        // })
        this.active = 0
        this.currentDashborad.views.forEach((item) => {
          typeof item.model === "string" &&
            (item.model = JSON.parse(item.model || "[]"))
          typeof item.variable === "string" &&
            (item.variable = JSON.parse(item.variable || "[]"))
          viewIds[item.id] = {
            name: item.name,
            variables: item.variable.map((el) => el.name),
            valueList: [],
            fieldList: [],
          }
          Object.keys(item.model).forEach((el) => {
            if (item.model[el].modelType === "category")
              viewIds[item.id].fieldList.push(el)
            else viewIds[item.id].valueList.push(el)
          })
        })
        // 数据回显处理
        let globalFiltersConfig = JSON.parse(
          JSON.stringify(this.globalFiltersConfig)
        )
        console.log(viewIds, "viewIds")
        console.log(globalFiltersConfig, "globalFiltersConfig")
        this.controls = globalFiltersConfig.map((item) => {
          let checked = []
          Object.keys(item.relatedItems).forEach((el) => {
            item.relatedItems[el].checked && checked.push(+el)
          })
          Object.keys(item.relatedViews).map((el) => {
            if (viewIds[el]) {
              item.relatedViews[el] = {
                // name:
                ...item.relatedViews[el],
                name: viewIds[el].name,
                variables: viewIds[el].variables,
                valueList: viewIds[el].valueList,
                fieldList: viewIds[el].fieldList,
              }
            }
          })
          let res = {
            ...initControlData(),
            ...item,
            checked,
          }
          return res
        })
        this.checked = this.controls[0]?.checked
        this.currentControl = this.controls[0]

        this.$forceUpdate()
      }
    },
  },
  methods: {
    // 点击加载数据时触发验证,只是为了提示效果
    manualVaildateField() {
      this.$refs["controlFrom"].validate()
    },
    handleChangeFieldType(val, item) {
      this.$set(this.currentControl.relatedViews[item].fields, 0, "")
    },
    handleSubmit() {
      if (!this.controls.length) {
        return this.$emit("confirm", [], this.queryMode)
      }
      this.$refs["controlFrom"].validate((valid) => {
        if (!valid) return
        if (!this.verifyDataView()) return
        let res = this.controls.map((item) => this.formatParams(item))
        this.$emit("confirm", res, this.queryMode)
      })
    },
    changeType() {
      Object.keys(this.currentControl.relatedViews).forEach((item) => {
        this.currentControl.relatedViews[item].fields = [ "" ]
      })
      // if(this.currentControl.relatedViews[this.widgetList[0].id]['fieldType'] !== 'variable' && 'numberRange,slider'.includes(type)) {
      //   this.currentControl.relatedViews[this.widgetList[0].id]['fields'][0] = ''
      // }
    },
    verifyDataView() {
      let verify = true
      Object.values(this.currentControl.relatedViews).forEach((item) => {
        if (!item.fields[0]) {
          verify = false
          this.$notify.error({
            title: "错误",
            message: `请选择 ${item.name} 的关联数据视图`,
          })
        }
      })
      return verify
    },
    handleAddControl() {
      if (this.controls.length >= 1) {
        if (!this.verifyDataView()) return
      }
      this.currentControl = initControlData()
      this.currentControl.key = uuid(8, 16)
      this.controls.push(this.currentControl)
      this.checked = []
    },
    // 修改控制器名称
    editControl(index) {
      if (this.active === index) return
      if (!this.verifyDataView()) return
      this.active = index
      this.selControl(this.controls[index])
    },
    // 选中控制器
    selControl(row) {
      this.checked = row.checked
      this.currentControl = row
    },
    // 修改完成控制器名称
    okControl() {},
    // 删除控制器
    delControl(index) {
      // 删除后当前选中状态自动上移
      let currentIndex = index - 1 >= 0 ? index - 1 : 0
      this.currentControl = this.controls[currentIndex]
      this.checked = this.currentControl.checked
      this.active = currentIndex
      this.controls.splice(index, 1)
    },
    handleChecked(val, isCheckAll) {
      let viewIds = {}
      // 统计全部数量 全选判断时使用
      let allLen = {}
      this.widgetList.forEach((item) => {
        allLen[item.id] = true
        if (val.includes(item.id)) viewIds[item.viewId] = true
      })
      let relatedViewsBackUp = JSON.parse(
        JSON.stringify(this.currentControl.relatedViews)
      )
      this.currentControl.relatedViews = {}
      this.currentDashborad.views.forEach((item) => {
        if (viewIds[item.id]) {
          let variables = item.variable.map((el) => el.name)
          let fieldList = []
          let valueList = []
          Object.keys(item.model).forEach((el) => {
            if (item.model[el].modelType === "category") fieldList.push(el)
            else valueList.push(el)
          })
          this.$set(this.currentControl.relatedViews, item.id, {
            name: item.name,
            fieldType: relatedViewsBackUp[item]
              ? relatedViewsBackUp[item].fieldType
              : "column",
            fields: relatedViewsBackUp[item.id]
              ? relatedViewsBackUp[item.id].fields
              : [ "" ],
            variables,
            fieldList,
            valueList,
          })
        }
      })
      // checked去一次重
      this.currentControl.checked = this.checked = [ ...new Set(this.checked) ]
      if (isCheckAll) return // 如果是全选调用此方法则不判断是否选中全选按钮
      if (Object.keys(allLen).length === this.checked.length)
        this.checkAll = true
      else this.checkAll = false
    },
    handleCheckAll(val) {
      if (val) {
        this.checked = this.widgetList.map((item) => item.id)
        this.handleChecked(this.checked, true)
        return
      }
      this.checked = []
      this.currentControl.relatedViews = []
    },
    handleCancel() {
      this.$emit("update:controlConfigVisible", false)
    },
    // 处理控制器提交数据
    formatParams(data) {
      let params = {
        name: data.name,
        key: data.key,
        relatedViews: {},
        type: data.type, // 控制器类型
        width: data.width, // 宽度
        visibility: data.visibility, // 是否可见
        operator: data.operator,
        defaultValue: data.defaultValue,
        defaultValueType: data.defaultValueType,
        relatedItems: {},
      }
      this.widgetList.forEach((item) => {
        params.relatedItems[item.id] = {
          viewId: item.viewId,
        }
        params.relatedItems[item.id].checked = data.checked.includes(item.id)
      })
      Object.keys(data.relatedViews).forEach((item) => {
        params.relatedViews[item] = {
          fieldType: data.relatedViews[item].fieldType,
          fields: data.relatedViews[item].fields,
        }
      })
      // params.relatedViews =
      /*  控制器相关数据处理 */
      if (data.visibility === "conditional")
        params.conditions = data.conditions
      // 过期时间和是否开启缓存
      if ("select,date,radio,treeSelect".includes(params.type)) {
        params.expired = data.expired
        params.cache = data.cache
      }
      // 多选框所需类型
      if ("select,date,treeSelect".includes(params.type))
        params.multiple = data.multiple
      // 单选按钮样式
      if (params.type === "radio")
        params.radioType = this.currentControl.radioType
      // 日期和日期范围设置日期类型
      if ("date,dateRange".includes(params.type))
        params.dateFormat = data.dateFormat
      if (params.type === "slider") {
        params.min = data.min
        params.max = data.max
        params.step = data.step
        params.label = data.label
      }
      /*  取值相关数据处理 */
      if ("select,radio".includes(params.type))
        params.optionType = data.optionType
      if (data.optionType === "manual") {
        params.valueViewId = data.valueViewId
        params.valueField = data.valueField
        params.textField = data.textField
      }
      if (data.optionType === "custom") {
        params.optionWithVariable = data.optionWithVariable
        params.customOptions = data.customOptions
      }
      return params
    },
  },
}
</script>

<style scoped lang="scss">
.global-control {
  /deep/.el-form-item__content {
    margin-bottom: 10px;
  }
  display: flex;
  .control-list {
    width: 25%;
    border-right: 1px solid var(--theme-border-color);
    min-height: 100px;
  }
  .control-chart {
    width: 20%;
    border-left: 1px solid var(--theme-border-color);
    border-right: 1px solid var(--theme-border-color);
  }
  .control-chart_top {
    margin-bottom: 15px;
  }
  .control-chart_bottom {
    padding: 5px 0;
    .control-chart_item {
      padding: 7px 5px;
    }
    .control-chart_item_head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 0;
    }
    .control-chart_item_name {
      width: 130px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .control-config {
    width: 55%;
    padding: 0 20px;
  }
  .control-chart_body {
    margin-top: 5px;
    /deep/.el-checkbox {
      display: block;
      margin: 10px 0 0 5px;
    }
  }
  .global-control_title {
    display: flex;
    padding: 0 5px;
    font-size: 15px;
    color: var(--theme-text-color);
    font-weight: 500;
    justify-content: space-between;
    align-items: center;
  }
  .control-list_title {
    border-bottom: 1px solid var(--theme-border-color);

    padding-bottom: 10px;
  }
  .control-list_item {
    margin: 12px 0;
  }
  .control-item {
    display: flex;
    padding: 5px;
  }
  .control-item_active {
    background: var(--theme-color);
  }
  .control-item:hover {
    cursor: pointer;
    background: var(--theme-color);
  }
  .control-item_input {
    width: 150px;
  }
  .control-item_conditions {
    display: flex;
    flex: 1;
    justify-content: abs($number: 2);
  }
}
</style>
