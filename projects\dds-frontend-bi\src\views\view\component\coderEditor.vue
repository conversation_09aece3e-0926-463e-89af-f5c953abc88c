<template>
  <editor
    v-model="value"
    @init="editorInit"
    lang="sql"
    theme="sqlserver"
    width="100%"
    height="100%"
    ref="refEditor"
    :options="options"
  ></editor>
</template>

<script>
export default {
  components: {
    editor: require("vue2-ace-editor"),
  },
  props: {
    value: {
      type: String,
    },
    grammarTips: {
      type: Array,
    },
  },
  data() {
    return {
      options: {
        enableBasicAutocompletion: true,
        enableSnippets: true,
        enableLiveAutocompletion: true,
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    var elementResizeDetectorMaker = require("element-resize-detector") // 导入
    // 创建实例
    var erd = elementResizeDetectorMaker()
    // 创建实例带参
    // var erdUltraFast = elementResizeDetectorMaker({
    //   strategy: "scroll", //<- For ultra performance.
    //   callOnAdd: true,
    //   debug: true,
    // })
    erd.listenTo(this.$el, () => {
      this.$refs.refEditor.editor.resize()
    })
  },
  watch: {},
  methods: {
    editorInit() {
      require("brace/ext/language_tools")
      require("brace/mode/sql")
      require("brace/mode/sqlserver")
      require("brace/theme/sqlserver")
      require("brace/theme/monokai")
      require("brace/snippets/sqlserver")
      require("brace/snippets/sql")
      require("brace/ext/split")
      const that = this
      this.$refs.refEditor.editor.getSession().on(
        "change",
        this._.debounce(() => {
          this.$emit("change", that.value)
        }, 500)
      )
      this.$refs.refEditor.editor.selection.on(
        "changeCursor",
        this._.debounce(() => {
          const selectText = this.$refs.refEditor.editor.session.getTextRange(
            this.$refs.refEditor.editor.getSelectionRange()
          )
          this.$emit("onChangeSelect", selectText)
        }, 500)
      )

      // this.$refs.refEditor.editor.selection.on("changeCursor", () => {
      //   const selectText = this.$refs.refEditor.editor.session.getTextRange(
      //     this.$refs.refEditor.editor.getSelectionRange()
      //   );
      //   this.$emit("onChangeSelect", selectText);
      // });
    },
    setCompleteData(editor, session, pos, prefix, callback) {
      let data = this.grammarTips.map((item) => ({
        caption: item.name,
        value: item.name,
        meta: item.type,
      }))

      if (prefix.length === 0) {
        return callback(null, [])
      } else {
        return callback(null, data)
      }
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .ace-sqlserver .ace_print-margin {
  width: 0;
}
</style>
