<template>
  <el-dialog
    title="导出"
    :visible.sync="downloadVisible"
    width="560px"
    append-to-body="true"
    :close-on-click-modal="false"
    @close="$emit('update:downloadVisible', false)"
  >
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      label-width="100px">
      <el-form-item label="导出名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入导出名称"></el-input>
      </el-form-item>
      <el-form-item label="导出格式" prop="exportType" style="margin-top: 20px">
        <el-radio-group v-model="form.exportType">
          <el-radio label="EXCEL">Excel</el-radio>
          <el-radio label="IMG">图片</el-radio>
          <el-radio label="PDF">PDF</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="$emit('update:downloadVisible', false)"
      >取 消</el-button
      >
      <el-button type="primary" @click="handleExport">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    downloadVisible: {
      type: Boolean,
      default: false,
    },
    exportTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      form: {
        title: "",
        exportType: "EXCEL",
      },
      rules: {
        title: [ { required: true, message: "请输入导出名称", trigger: "blur" } ],
        exportType: [
          { required: true, message: "请选择导出类型", trigger: "change" },
        ],
      },
    }
  },
  computed: {},
  created() {
    this.form.title = this.exportTitle
  },
  mounted() {},
  watch: {
    exportTitle() {
      this.form.title = this.exportTitle
    },
  },
  methods: {
    handleExport() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("handleExport", this.form)
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="less"></style>
