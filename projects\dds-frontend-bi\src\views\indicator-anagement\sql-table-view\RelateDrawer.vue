<template>
  <div class="main">
    <div class="tables">
      <div class="left-table">
        <div class="table-title">{{ sourceTableInfo.name }}</div>
        <el-table
          height="200"
          ref="sourceTable"
          :data="sourceTableInfo.allFields"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChangeLeft"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="name" label="字段"></el-table-column>
          <el-table-column prop="comment" label="备注"></el-table-column>
        </el-table>
      </div>
      <div class="right-table">
        <div class="table-title">{{ targetTableInfo.name }}</div>
        <el-table
          height="200"
          ref="targetTable"
          :data="targetTableInfo.allFields"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChangeRight"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="name" label="字段"></el-table-column>
          <el-table-column prop="comment" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="relate">
      <!-- <el-select
        :value="targetTableInfo.c"
        @change="changeRelateType"
        placeholder="请选择"
      >
        <el-option label="左关联" value="left"> </el-option>
        <el-option label="右关联" value="right"> </el-option>
      </el-select> -->

      <div class="tab">
        <div
          class="tab-item"
          v-for="(tab, index) in tabs"
          :class="{ active: targetTableInfo.c == tab.value }"
          @click="changeRelateType(tab.value)"
          :key="index"
        >
          {{ tab.label }}
        </div>
      </div>
      <el-form :model="targetTableInfo" ref="form">
        <el-form-item
          label=""
          v-for="(item, index) in targetTableInfo.ref"
          :key="index"
        >
          <div class="relate-content-input">
            <span>{{ index + 1 }}</span>
            <el-form-item
              :prop="'ref.' + index + '.fr'"
              :rules="{
                required: true,
                message: '请选择关联字段',
                trigger: 'blur'
              }"
            >
              <el-select
                :value="item.fr"
                placeholder="请选择"
                @change="val => changeRelate(val, 'fr', index)"
              >
                <el-option
                  v-for="item in sourceTableInfo.fields"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <span class="relation"></span>
            <el-form-item
              :prop="'ref.' + index + '.fy'"
              :rules="{
                required: true,
                message: '请选择关联字段',
                trigger: 'blur'
              }"
            >
              <el-select
                :value="item.fy"
                placeholder="请选择"
                @change="val => changeRelate(val, 'fy', index)"
              >
                <el-option
                  v-for="item in targetTableInfo.fields"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <span
              class="del"
              @click="removeRelate(item)"
              v-if="targetTableInfo.ref.length > 1"
            ></span>
            <span class="add" @click="addRelate"></span>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="demo-drawer__footer"></div>

    <div class="footer-btn">
      <el-button @click="cancelForm">取 消</el-button>

      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    sourceTableInfo: {
      type: Object,
      default: () => {}
    },
    targetTableInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tabIndex: "left",
      tabs: [
        {
          label: "左关联",
          value: "left"
        },
        {
          label: "右关联",
          value: "right"
        }
      ]
    }
  },
  computed: {},
  created() {
    console.log(this.sourceTableInfo, "sourceTableInfo")
  },
  mounted() {},
  methods: {
    handleSelectionChangeLeft(val) {
      this.$emit("handleSelectionChangeLeft", val, this.sourceTableInfo)
    },
    handleSelectionChangeRight(val) {
      this.$emit("handleSelectionChangeRight", val, this.targetTableInfo)
    },
    changeRelate(val, key, index) {
      console.log(val, "s", key)
      this.$emit("changeRelate", this.targetTableInfo, val, key, index)
    },
    // 默认选中
    toggleSelection(rows, tabelRef) {
      if (rows) {
        rows.forEach(row => {
          this.$refs[tabelRef].toggleRowSelection(row, true)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("handleSave")
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    changeRelateType(val) {
      this.$emit("changeRelateType", val)
    },
    addRelate() {
      this.$emit("addRelate")
    },
    removeRelate(item) {
      this.$emit("removeRelate", item)
    },
    cancelForm() {
      this.$emit("cancelForm")
    }
  }
}
</script>

<style scoped lang="scss">
.main {
  padding: 16px;
}
.tables {
  display: flex;
  justify-content: space-around;
  .left-table,
  .right-table {
    width: calc(50% - 16px);
    .table-title {
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      line-height: 24px;
    }
  }
}
.tab {
  display: flex;
  justify-content: flex-end;
  height: 24px;
  margin: 10px 0;

  .tab-item {
    position: relative;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
    padding-bottom: 4px;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #6c8097;
    height: 24px;
    width: 80px;
    background-color: #fff;
    border: 1px solid rgba(30, 123, 214, 0.8);

    cursor: pointer;
    &.active {
      background: rgba(30, 123, 214, 0.8);
      color: #fff;
    }
  }
}
.relate-content-input {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
  span:nth-child(1) {
    width: 23px;
    height: 32px;
    line-height: 32px;
    font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
    font-weight: 500;
    font-size: 14px;
    color: #2f3338;
    text-align: center;
  }
  .relation {
    margin: 0 9px;
    width: 16px;
    height: 8px;
    background: url("~@/assets/images/relation.png") no-repeat center;
    background-size: cover;
  }
  .del {
    margin-left: 14px;
    width: 16px;
    height: 16px;
    background: url("~@/assets/images/del.png") no-repeat center;
    background-size: cover;
    cursor: pointer;
  }
  .add {
    width: 18px;
    height: 18px;
    margin-left: 12px;
    background: url("~@/assets/images/add.png") no-repeat center;
    background-size: cover;
    cursor: pointer;
  }
}
::v-deep .el-form {
  height: calc(100vh - 380px);
}
.footer-btn {
  padding-right: 24px;
  border-top: 1px solid #f0f0f0;
  height: 52px;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
