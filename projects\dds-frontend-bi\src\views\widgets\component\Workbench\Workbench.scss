::v-deep .el-checkbox__label {
  font-size: 12px;
}
::v-deep .el-row {
  margin: 0;
}
::v-deep .el-col {
  display: flex;
  align-items: center;
}
.paneBlock {
  padding: 8px;
  border-bottom: 1px solid #ccc;
  font-size: 12px;

  &:first-child {
    padding-top: 4px;
  }

  h4 {
    margin-top: 4px;
    color: #404040a6;
    line-height: 18px;
    font-weight: bold;
    display: flex;
    flex-direction: row;
    align-items: center;

    span {
      flex: 1;
    }

    i:hover {
      cursor: pointer;
    }

    i + i {
      margin-left: 8px;
    }
  }

  
  .blockRow {
    height: 35px;
  }

  .blockElm {
    width: 100%;
  }

  .addVariable {
    text-align: right;
    cursor: pointer;

    &:hover {
      color: red;
    }
  }
}
