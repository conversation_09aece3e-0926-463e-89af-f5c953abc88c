<template>
  <div class="operating-panel">
    <!-- 图表数据  -->
    <ChartDataViewPanel @onDraggableStart="onDraggableStart" />
    <div class="chart-config-wrapper">
      <!-- 图表图标 -->
      <ChartGraphPanel
        :widget-params="widgetParams"
        @chartSelect="chartSelect"
      />
      <!-- 图表配置-->
      <ChartConfigPanel
        :widget-params="widgetParams"
        :current-draggable-type="currentDraggableType"
        @draggableChange="onDraggableChange"
      />
    </div>
    <!-- 图表视图 -->
    <ChartPresentPanel />
  </div>
</template>

<script>
import ChartDataViewPanel from "./components/ChartDataViewPanel/index.js"
import ChartGraphPanel from "./components/ChartGraphPanel/index.js"
import ChartConfigPanel from "./components/ChartConfigPanel/index.js"
import ChartPresentPanel from "./components/ChartPresentPanel/index.js"
export default {
  components: {
    ChartDataViewPanel,
    ChartGraphPanel,
    ChartConfigPanel,
    ChartPresentPanel,
  },
  props: {},
  data() {
    return {
      widgetParams: {
        selectedChart: 1, // 默认表格
        currentDragType: '', // 当前拖拽字段类型
      },
      currentDraggableType:""
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 选择图表
    chartSelect() {
      // this.widgetParams.selectedChart = chart
    },
    // 拖拽开始
    onDraggableStart(type) {
      this.currentDraggableType = type
      console.log('%cChartOperationPanel.vue line:57 object', 'color: #007acc;',  '拖拽开始',  this.currentDraggableType)
    },
    // 拖拽改变
    onDraggableChange(key, fieldList) {
      this.$set(this.widgetParams, key, fieldList)
    },
  },
}
</script>

<style scoped lang="scss">
.operating-panel {
  flex: 1;
  display: flex;
  min-width: 0;
  height: 100%;
}
.chart-config-wrapper {
  width: 250px;
  height: 100%;
}
</style>
