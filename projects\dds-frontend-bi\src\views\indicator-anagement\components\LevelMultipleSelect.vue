<template>
  <el-select-v2
    v-model="selectedValue"
    filterable
    multiple
    collapse-tags
    @change="handleChange"
    ref="my-select"
    style="width: 100%"
    v-bind="$attrs"
    :placeholder="placeholder"
    :props="props"
    :value-key="valueKey"
    :options="options"
    v-select-tag-tooltip="tooltipWidth"
  >
    <template #header>
      <div class="checkboxWrapper" v-if="isSelectedAll">
        <el-checkbox
          v-model="checked"
          @change="checkChange"
          :true-label="true"
          :false-label="false"
        >
          {{ isSelectedAllName }}
        </el-checkbox>
      </div>
    </template>
  </el-select-v2>
</template>

<script>
export default {
  name: "LevelMultipleSelect",
  directives: {},
  props: {
    value: {
      type: [String, Number, Object],
      default: null,
    },
    size: {
      type: Number,
      default: 20,
    },
    placeholder: {
      type: String,
      default: "请选择维度值",
    },
    fetchDataMethod: {
      type: Function,
      required: true,
    },
    levelCode: {
      type: String,
      required: true,
    },
    dimId: {
      type: String,
      default: "",
    },
    indType: {
      type: String,
      default: "",
    },
    isSelectedAll: {
      type: Boolean,
      default: false,
    },
    isSelectedAllName: {
      type: String,
      default: "全部",
    },
    valueKey: {
      type: String,
      default: "levelCode",
    },
    tooltipWidth: {
      type: Number,
      default: 100,
    },
    props: {
      type: Object,
      default() {
        return {
          label: "value",
          value: "valueCode",
        }
      },
    },
  },
  data() {
    return {
      selectedValue: this.value, // 双向绑定的值
      options: [], // 存储下拉框选项
      loading: false, // 是否正在加载
      current: 1, // 当前分页页码,
      query: "",
      total: 0,
      isDropdownOpen: false, // 下拉框是否打开
      tempValue: [],
      checked: false,
    }
  },
  watch: {
    levelCode: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      },
      immediate: true,
    },
    dimId: {
      handler() {
        this.options = [] // 清空列表
        this.current = 1 // 重置分页页码
        this.loading = true
        this.fetchData() // 调用父组件传入的fetchDataMethod方法
      },
    },
    // 监听父组件传入的value，保持双向绑定
    value: {
      handler(newVal) {
        this.selectedValue = newVal
      },
      immediate: true,
    },
  },
  created() {},
  methods: {
    checkChange(val) {
      if (val) {
        // 全选：只选中“全部”标识码
        this.selectedValue = this.options.map((item) => item[this.props.value])
      } else {
        // 取消全选：清空
        this.selectedValue = []
      }
      this.$emit("input", this.selectedValue)
      this.$emit("change", this.selectedValue)
    },
    // 处理远程搜索的方法
    handleRemote(query) {
      this.options = [] // 清空列表
      this.current = 1 // 重置分页页码
      this.loading = true
      this.query = query
      this.fetchData() // 调用父组件传入的fetchDataMethod方法
    },

    // 加载数据
    async fetchData() {
      const { data } = await this.$httpBi.api.paramPostQuery(
        "/DimManage/getDimValueByLevelCode",
        {
          levelCode: this.levelCode,
          dimId: this.dimId,
          indType: this.indType,
          size: -1,
          page: 1,
        }
      )
      if (this.props.value === "valueItem") {
        this.options = data.list.map((item) => ({
          ...item,
          valueItem: {
            ...item,
          },
        }))
      } else {
        this.options = data.list
      }
      if (this.selectedValue.length === this.options.length) {
        this.checked = true
      } else {
        this.checked = false
      }
      this.loading = false
    },
    handleChange(val, item) {
      console.log(val, item, "///////////////")
      this.$emit("input", val)
      this.$emit("change", val)
    },
  },
}
</script>

<style scoped lang="scss">
/* 可根据需求自定义样式 */

::v-deep .el-select__tags {
  display: flex;
  flex-wrap: nowrap;
}
</style>
