<template>
  <el-tree
    :data="dashboards"
    draggable
    node-key="id"
    highlight-current
    @node-drop="handleDrop"
    @node-click="handleNodeClick"
    :default-expand-all="isExpandAll"
    :filter-node-method="filterNode"
    ref="tree"
    class="tree"
  >
    <div class="custom-tree-node" icon-class slot-scope="{ data }">
      <span class="labelView">
        <i v-show="data.type == 0" class="el-icon-folder-opened" />
        <DT-Icon
          class="svg_icon"
          v-show="data.type !== 0"
          :icon-class="data.icon"
        />
        {{ data.name }}
      </span>
    </div>
  </el-tree>
</template>

<script>
export default {
  components: {},
  props: {
    dashboards: {
      type: Array,
      default: () => [],
    },
  },
  data() {},
  computed: {},
  created() {},
  mounted() {
    this.$refs.tree.setCurrentKey(this.$route.params.id)
  },
  watch: {},
  methods: {
    handleNodeClick(data) {
      if (data.type !== 0) {
        this.dashboardId = data.id
        this.$router.push({
          path: `/ddsBi/viewDashboards/Portalview/${data.id}${window.location.search}`,
        })
        // this.getWidgetsConfig();
      }
    },
  },
}
</script>

<style scoped lang="scss">
.tree {
  height: 100%;
  overflow: auto;
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .buttonView {
    opacity: 0;
  }
}
.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}
</style>
