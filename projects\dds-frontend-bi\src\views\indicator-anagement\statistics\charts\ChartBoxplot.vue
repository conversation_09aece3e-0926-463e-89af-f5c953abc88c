<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
import { toThousands } from "@/utils/index.js"
export default {
  components: {},
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String || Array,
      default: []
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 系列字段
    seriesField: {
      type: String || null,
      default: null
    },
    // 维度名称
    seriesName: {
      type: String || Array || null,
      default: null
    },
    // 是否开启区域颜色
    isAreaStyle: {
      type: Boolean,
      default: true
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#03d083",
        "#333333",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // X轴展示数量  auto会自动折叠隐藏
    interval: {
      type: [String, Number],
      default: "auto"
    },
    // 是否曲线
    smooth: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    dimensions() {
      if (Array.isArray(this.yField)) {
        return [this.xField, ...this.yField]
      } else {
        return [this.xField, this.yField]
      }
    },
    // 判断是否为多系列
    isMultipleSeries() {
      return Array.isArray(this.yField) || this.seriesField !== null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      this.chart.setOption({
        dataset: [
          {
            // prettier-ignore
            source: [
                [850, 740, 900, 1070, 930, 850, 950, 980, 980, 880, 1000, 980, 930, 650, 760, 810, 1000, 1000, 960, 960],
                [960, 940, 960, 940, 880, 800, 850, 880, 900, 840, 830, 790, 810, 880, 880, 830, 800, 790, 760, 800],
                [880, 880, 880, 860, 720, 720, 620, 860, 970, 950, 880, 910, 850, 870, 840, 840, 850, 840, 840, 840],
                [890, 810, 810, 820, 800, 770, 760, 740, 750, 760, 910, 920, 890, 860, 880, 720, 840, 850, 850, 780],
                [890, 840, 780, 810, 760, 810, 790, 810, 820, 850, 870, 870, 810, 740, 810, 940, 950, 800, 810, 870]
            ]
          },
          {
            transform: {
              type: "boxplot",
              config: { itemNameFormatter: "expr {value}" }
            }
          },
          {
            fromDatasetIndex: 1,
            fromTransformResult: 1
          }
        ],
        tooltip: {
          show: false,
          trigger: "item",
          axisPointer: {
            type: "shadow"
          }
        },
        grid: {
          top: 0,
          left: 0,
          right: 10,
          bottom: "0",
          containLabel: true
        },
        xAxis: {
          type: "category",
          splitArea: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#EBEDF0",
              width: 1,
              type: "dashed"
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            formatter: () => ""
          }
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              color: "#EBEDF0",
              width: 1,
              type: "dashed"
            }
          },
          axisLabel: {
            show: false
          }
        },
        series: [
          {
            name: "boxplot",
            type: "boxplot",
            color: ["#0063e3"],
            datasetIndex: 1
          },
          {
            name: "outlier",
            type: "scatter",
            symbol: "circle",
            color: ["#f21a4c00"],
            itemStyle: {
              borderWidth: 1,
              borderColor: "#f21a4c"
            },
            datasetIndex: 2
          }
        ]
      })
      // 设置监听容器大小的改变，改变后重置图表
      // var elementResizeDetectorMaker = require('element-resize-detector');
      // var erd = elementResizeDetectorMaker();
      // var worldMapContainer = this.$el;

      // erd.listenTo(worldMapContainer, () => {
      //   that.$nextTick(() => {
      //     that.chart.resize();
      //   });
      // });
      // this.chart.off().on('click', (params) => {
      //   const p = {
      //     name: params.name,
      //     data: params.data,
      //     dataIndex: params.dataIndex,
      //     seriesIndex: params.seriesIndex,
      //     seriesName: params.seriesName,
      //   };
      //   console.log(p);
      //   that.$emit('barLineCharts', p);
      // });
    },
    // 创建系列对象
    createSeriesObject({
      name,
      color,
      index = 0,
      yField = this.yField,
      isAreaStyle = this.isAreaStyle
    }) {
      console.log(index, "index")
      return {
        type: "line",
        name: name,
        itemStyle: {
          color: "#03d083"
        },
        lineStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },
        symbol: index === 0 ? "circle" : "emptyCircle",
        symbolSize: 8,
        showSymbol: true,
        datasetIndex: 0,
        smooth: this.smooth,
        markLine: {
          symbol: "none",
          lineStyle: {
            color: "#03d083"
            // type: "solid"
          },
          data: [
            {
              name: "",
              xAxis: "a",
              // symbol: "none",
              label: {
                // 文字标签
                show: false
              }
            },
            {
              name: "",
              xAxis: "b",
              // symbol: "none",
              label: {
                // 文字标签
                show: false
              }
            }
          ]
        },

        areaStyle: isAreaStyle
          ? {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: color + "1A" // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: color + "00" // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          : null
      }
    },
    tooltipItemsHtmlString(items) {
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.y[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 12px;
    height: 2px;
  }
}
</style>
