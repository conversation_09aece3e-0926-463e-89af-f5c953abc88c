<template>
  <div class="source-data">
    <!-- 顶部时间筛选区 -->
    <div class="top-filter">
      <div class="select-wrap">
        <div class="select-item">
          <div class="select-label">源数据表:</div>
          <el-select
            v-model="name"
            placeholder="请选择"
            @change="changeDataSoure"
          >
            <el-option
              v-for="item in parent.indicatorData.dataSource"
              :label="item.dataSourceName"
              :value="item.dataSource"
              :key="item.dataSource"
            ></el-option>
          </el-select>
        </div>
        <!-- <div class="select-item">
            <div class="select-label">数据版本</div>

            <el-select v-model="version" placeholder="请选择">
              <el-option label="最新" :value="1"></el-option>
            </el-select>
          </div> -->
      </div>
    </div>
    <CommonTable
      :page.sync="page"
      :table-data="tableData"
      :show-batch-tag="false"
      :loading="loading"
      :table-columns.sync="tableColumn"
      @onload="getTableData"
      @handleExport="handleExport"
      ref="CommonTable"
    ></CommonTable>
  </div>
</template>

<script>
import CommonTable from "@/components/CommonTable.vue"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import { exportExcel } from "@/utils/index"
export default {
  name: "IndicatorSourceData",
  components: { CommonTable },
  props: {},
  data() {
    return {
      name: "",
      comment: "",
      loading: false,
      tableData: [],
      tableColumn: [],
      page: {
        total: 1,
        currentPage: 1,
        pageSize: 10
      }
    }
  },

  created() {
    this.comment = this.parent.indicatorData.dataSource[0].dataSourceName
    this.name = this.parent.indicatorData.dataSource[0].dataSource
    this.getTableData()
  },
  inject: ["parent"],
  methods: {
    async getTableData() {
      this.loading = true

      const { data } = await this.$httpBi.api.paramPost(
        "/indicator/dataSource/getIndicatorTableData",
        {
          name: this.name,
          comment: this.comment,
          currentPage: this.page.currentPage,
          pageSize: this.page.pageSize
        }
      )

      if (data) {
        if (!this.tableColumn.length) {
          this.tableColumn = data.tableColumn.map(item => ({
            label: item.alias,
            prop: item.name,
            visible: true,
            sortable: false
          }))
        }
        this.page.total = data.tableData.totalCount
        this.tableData = data.tableData.list || []
      } else {
        this.page.total = 0
        this.tableData = []
      }

      console.log(this.tableData, "this.tableData")
      this.loading = false
    },
    changeDataSoure() {
      this.comment = this.parent.indicatorData.dataSource.find(
        item => item.dataSource === this.name
      ).dataSourceName
      this.page.currentPage = 1
      this.getTableData()
    },
    handleExport(selection) {
      if (!selection.length) {
        // 导出
        exportExcel(
          "/api/dds-server-bi/indicator/dataSource/exportIndicatorTableData",
          {
            name: this.name,
            comment: this.comment,
          }
        )
        return 
      }
      const header = Object.fromEntries(
        this.tableColumn.map(item => [item.prop, item.label])
      )
      jsonToSheetXlsx({
        data: selection,
        filename: this.comment,
        header,
        autoWidth: false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.top-filter {
  margin-bottom: 20px;
  .total {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .total-info {
      display: flex;
      align-items: center;
      .total-label {
        height: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        margin-right: 12px;
      }
      .total-value {
        height: 24px;
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 24px;
        color: #222222;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.select-wrap {
  display: flex;
  .select-item {
    display: flex;
    align-items: center;
    margin-right: 32px;
    &:last-child {
      margin-right: 0;
    }
    .select-label {
      min-width: max-content;
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 14px;
      text-align: center;
      font-style: normal;
      margin-right: 12px;
    }
  }
}
::v-deep .el-row {
  margin-bottom: 0;
}
</style>
