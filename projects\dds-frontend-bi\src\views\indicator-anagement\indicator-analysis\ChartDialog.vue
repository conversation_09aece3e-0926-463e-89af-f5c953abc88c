<template>
  <el-dialog
    title=""
    :visible.sync="chartDialogVisible"
    v-if="chartDialogVisible"
    width="1000px"
    :before-close="handleCloseChart"
  >
    <div style="display: flex; align-items: center" slot="title">
      <div class="label" style="margin-right: 10px; white-space: nowrap">
        图表类型:
      </div>
      <el-select v-model="chartType" placeholder="请选择">
        <el-option label="折线图" value="ChartLine"></el-option>
        <el-option label="柱状图" value="ChartColumn"></el-option>
        <el-option label="扇形图" value="ChartPie"></el-option>
      </el-select>
      <el-button style="margin-left: 10px" @click="exportImg">
        导出图片
      </el-button>
      <el-button style="margin-left: 10px" @click="exportPdf">
        导出PDF
      </el-button>
      <el-button style="margin-left: 10px" @click="handleExport">
        导出Excel
      </el-button>
    </div>
    <div class="search-item">
      <div class="left" v-if="chartType !== 'ChartPie'">
        <div class="name">显示指标:</div>
        <el-select
          v-model="inds"
          placeholder="请选择"
          @remove-tag="handleRemoveTag"
          @change="handleChange"
          multiple
          collapse-tags
          :popper-append-to-body="false"
          style="width: 350px"
          v-select-tag-tooltip="280"
        >
          <el-option
            v-for="item in rowData"
            :key="item.indicator"
            :label="item.indicator"
            :value="item.indicator"
            :title="item.indicator"
            :disabled="unit !== null && item.indUnit !== unit"
          ></el-option>
        </el-select>
      </div>
      <div class="right" v-if="chartType === 'ChartPie'">
        <div class="name">指标名称:</div>
        <div class="tabs">
          <div
            class="tab"
            :class="chartXField === item.indicator ? 'active' : ''"
            @click="clickTab(item)"
            v-for="(item, i) in rowData"
            :key="i"
          >
            {{ item.indicator }}
          </div>
        </div>
      </div>
      <div class="right" v-else>
        <el-button @click="reverseAxis = !reverseAxis">坐标轴交换</el-button>
      </div>
    </div>
    <div class="chart-box" ref="chartBox">
      <ChartPie
        v-if="chartType === 'ChartPie'"
        :chart-data="chartData"
        color-field="name"
        :angle-field="chartXField"
        :unit="unit"
        subtitle="合计"
        is-formatter-x-axis
        ref="chartAll"
      />
      <components
        v-else
        :is="chartType"
        width="900px"
        height="450px"
        :chart-data="chartData"
        x-field="name"
        :y-field="yField"
        :series-name="yField"
        :y-axis-name="unit ? `单位: (${unit})` : ''"
        :bar-width="16"
        show-data-zoom
        is-formatter-x-axis
        :show-num="8"
        :unit="unit"
        ref="chartAll"
      />
    </div>
  </el-dialog>
</template>

<script>
import html2canvas from "html2canvas"
import ChartLine from "@/components/Charts/ChartLine"
import ChartColumn from "@/components/Charts/ChartColumn"
import ChartPie from "@/components/Charts/ChartPie"
export default {
  components: {
    ChartLine,
    ChartColumn,
    ChartPie
  },
  props: {
    indicators: {
      type: Array,
      default: () => []
    },
    analyzeDims: {
      type: Array,
      default: () => []
    },
    rowData: {
      type: Object,
      default: () => []
    },
    themeName: {
      type: String,
      default: "汇总表"
    },
    headKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartDialogVisible: false,
      // 转换
      reverseAxis: false,
      chartType: "ChartLine",
      chartXField: "",
      inds: [],
      unit: "",
      pieTotal: 0
    }
  },
  computed: {
    chartData() {
      if (this.chartType === "ChartPie") {
        let arr = this.processedHeadKeys.map(item => {
          const obj = {
            name: item.label
          }

          // Add each indicator's value from rowData
          this.rowData.forEach(ele => {
            obj[ele.indicator] = ele[obj.name + "_origin"]
          })

          return obj
        })
        return arr
      }
      if (this.reverseAxis) {
        return this.inds.map(item => {
          const rowItem = this.rowData.find(row => row.indicator === item)
          console.log(rowItem, "")
          return {
            name: item,
            ...rowItem
          }
        })
      } else {
        console.log(this.processedHeadKeys, "headKeys")
        console.log(this.inds, "inds")
        let arr = this.processedHeadKeys.map(item => {
          const obj = {
            name: item.label
          }
          // Add each indicator's value from rowData
          this.inds.forEach(name => {
            console.log(name, "name")
            const rowItem = this.rowData.find(row => row.indicator === name)
            if (rowItem) {
              obj[name] = rowItem[item.prop + "_origin"]
            }
          })
          return obj
        })
        console.log(arr, "arr")
        return arr
      }
    },
    processedHeadKeys() {
      const result = []
      this.headKeys.forEach(item => {
        if (item.children && item.children.length > 0) {
          // 如果有children，将children合并到结果数组中
          result.push(...item.children)
        } else {
          // 如果没有children，将当前项添加到结果数组中
          result.push(item)
        }
      })
      // 过滤掉label为'合计'和'指标'的项
      return result.filter(
        item => item.label !== "合计" && item.label !== "指标"
      )
    }
  },
  created() {},
  mounted() {},
  watch: {
    reverseAxis: {
      handler(val) {
        if (val) {
          this.yField = this.processedHeadKeys.map(item => item.label)
        } else {
          this.yField = [...this.inds]
        }
      }
    },
    chartType(val) {
      if (val === "ChartPie") {
        this.reverseAxis = false
        this.chartXField = this.rowData[0].indicator
        this.unit = this.rowData[0].indUnit
      } else {
        this.inds = [this.rowData[0].indicator]
        this.chartXField = this.rowData[0].indicator
        this.unit = this.rowData[0].indUnit
      }
    },
    inds(val) {
      if (!this.reverseAxis) {
        this.yField = val
      }
    }
  },
  methods: {
    open() {
      this.chartDialogVisible = true
      this.inds = [this.rowData[0].indicator]
      this.chartXField = this.rowData[0].indicator
      this.unit = this.rowData[0].indUnit
      console.log(this.chartXField, "  this.chartXField")
    },
    exportImg() {
      const content = this.$refs.chartBox
      console.log(content, "content")
      html2canvas(content, {
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = `${this.themeName}.jpg` // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    },
    exportPdf() {
      this.ExportSavePdf(this.themeName, this.$refs.chartBox, false)
    },
    handleExport() {
      this.$emit("exportExcel")
    },
    handleCloseChart() {
      this.chartDialogVisible = false
    },
    handleRemoveTag(val) {
      console.log(val, "val")
      console.log(this.inds, "val")

      if (this.inds.length === 0) {
        this.inds = []
        this.unit = ""
      }
    },
    clickTab(item) {
      console.log(item, "item")
      console.log(this.chartData, "this.chartData")
      console.log(this.rowData, "this.rowData")

      this.chartXField = item.indicator
      this.unit = item.indUnit
    },
    handleChange(val) {
      console.log(val, "val")
      if (val.length === 0) {
        this.unit = null
        return
      }
      // Get the last selected indicator
      const lastSelectedIndicator = val[val.length - 1]
      this.unit = this.rowData.find(
        item => item.indicator === lastSelectedIndicator
      ).indUnit
    }
  }
}
</script>

<style scoped lang="scss">
.search-item {
  position: relative;
  display: flex;
  align-items: center;

  z-index: 9999;
  .left {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
  }
  .right {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
  }
  .tabs {
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    overflow: hidden;
    .tab {
      padding: 0 15px;
      border-right: 1px solid #e5e5e5;
      cursor: pointer;
      height: 30px;

      &:last-child {
        border: none;
      }
      &.active {
        background: #2361dbc7;
        color: #fff;
      }
    }
  }
  .name {
    font-size: 16px;
    padding-right: 10px;
  }
}
.chart-box {
  position: relative;
  width: 900px;
  height: 500px;
  padding: 30px;
  margin-top: 16px;
}
.pie-chart {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  .pie-item {
    width: 50%;
    height: 28vh;
  }
}
</style>
