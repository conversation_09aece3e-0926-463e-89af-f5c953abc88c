import service from "../base"
import config from "../config"

/**
 * 看板
 */
export default {
  // 新增
  addPortal(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/addPortal",
      method: "post",
      data
    })
  },
  demo(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/demo",
      method: "get",
      params
    })
  },
  createDashboard(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/createDashboard",
      method: "post",
      data
    })
  },
  createMemDashboardWidget(data, dashboardId) {
    return service({
      url:
        config.VUE_MODULE_DDS_BI +
        `bi/dashboard/createMemDashboardWidget?dashboardId=${dashboardId}`,
      method: "post",
      data
    })
  },
  delMemDashboardWidget(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/delMemDashboardWidget",
      method: "get",
      params
    })
  },
  updMemDashboardWidget(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/updMemDashboardWidget",
      method: "post",
      data
    })
  },
  widgets(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/widgets",
      method: "get",
      params
    })
  },
  dashboards(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/dashboards",
      method: "get",
      params
    })
  },
  delPortal(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/delPortal",
      method: "get",
      params
    })
  },
  deleteDashboard(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/deleteDashboard",
      method: "get",
      params
    })
  },
  getAllPortal(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/getAllPortal",
      method: "post",
      data
    })
  },
  updPortal(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/updPortal",
      method: "post",
      data
    })
  },
  updateDashboards(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/updateDashboards",
      method: "post",
      data
    })
  },
  dashboardRoles(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/dashboard/dashboardRoles",
      method: "get",
      params: data
    })
  },
  getAllThemes() {
    return service({
      url: config.VUE_MODULE_UPMS + "adm/security/menu/getAllThemes",
      method: "get"
    })
  },
  getAllShareLink(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/share/getAll",
      method: "post",
      data
    })
  },
  addShareLink(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/share/add",
      method: "post",
      data
    })
  },
  delShareLink(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/share/del",
      method: "get",
      params
    })
  },
  updShareLink(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/share/upd",
      method: "post",
      data
    })
  },
  getShareAuth(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/share/getShareAuth",
      method: "get",
      params
    })
  },
  // 分享链接登录
  shareLogin(token, params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/shareClient/login",
      method: "post",
      data: params,
      headers: {
        token
      }
    })
  },
  shareDashboard(token) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/shareClient/dashboard",
      method: "get",
      headers: {
        token
      }
    })
  },
  shareData(token, params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/shareClient/data",
      method: "post",
      data: params,
      headers: {
        token
      }
    })
  },
  // 定制看板管理列表
  getDashboardPagesByThemes(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/bi/dashboard/getDashboardPagesByThemes",
      method: "get",
      params
    })
  },
  // 获取编码是否存在
  getDashboardPagesByCode(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/bi/dashboard/getDashboardPagesByCode",
      method: "get",
      params
    })
  },
  addDashboardPage(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/bi/dashboard/addDashboardPage",
      method: "post",
      data
    })
  },
  updateDashboardPage(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/bi/dashboard/updateDashboardPage",
      method: "post",
      data
    })
  },
  deleteDashboardPageByCode(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "/bi/dashboard/deleteDashboardPageByCode",
      method: "get",
      params
    })
  }
}
