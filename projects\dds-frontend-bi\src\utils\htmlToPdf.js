// 导出页面为PDF格式
import html2canvas from "html2canvas"
import JSP<PERSON> from "jspdf"
import dayjs from "dayjs"
export default {
  install(Vue, ) {
    Vue.prototype.ExportSavePdf = function(htmlTitle, element, isWaterMark = true, watermarkText) {
      html2canvas(element, {
        // allowTaint: true,
        useCORS: true,
        // scale: 2, // 提升画面质量，但是会增加文件大小
      }).then((canvas) => {

        // 添加水印
        if (isWaterMark) {
          let tempCanvas = document.createElement('canvas')
          tempCanvas.width = 200
          tempCanvas.height = 200

          let tempCtx = tempCanvas.getContext("2d")
          tempCtx.rotate((-15 * Math.PI) / 150)
          tempCtx.font = "20px Vedana"
          tempCtx.fillStyle = "rgba(200, 200, 200, 0.40)"
          tempCtx.textAlign = "left"
          tempCtx.textBaseline = "Middle"
          tempCtx.fillText(watermarkText, 300 / 8, 180 / 2)

          let ctx1 = canvas.getContext('2d')
          let pattern = ctx1.createPattern(tempCanvas, 'repeat')
          ctx1.fillStyle = pattern
          ctx1.fillRect(0, 0, canvas.width, canvas.height)
        }

        const contentWidth = canvas.width
        const contentHeight = canvas.height

        /* 导出不分页处理 */
        const pageData = canvas.toDataURL("image/jpeg", 1.0)

        const pdfWidth = ((contentWidth + 10) / 2) * 0.75
        const pdfHeight = ((contentHeight + 200) / 2) * 0.75 // 500为底部留白
        const direction = contentWidth > contentHeight ? 'l' : 'p'
        const imgWidth = pdfWidth
        const imgHeight = imgWidth * (contentHeight / contentWidth) // 内容图片这里不需要留白的距离
        const PDF = new JSPDF(direction, "pt", [ pdfWidth, pdfHeight ])
        PDF.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight)
        PDF.save(htmlTitle + dayjs().format("YYYY-MM-DD") + ".pdf")
      })
      // html2canvas(element, {
      //   logging: true,
      //   useCORS: true,
      // }).then(function (canvas) {
      //   var pdf = new JSPDF("p", "mm", "a4"); // A4纸，纵向
      //   var ctx = canvas.getContext("2d");
      //   var a4w = 170;
      //   var a4h = 257; // A4大小，210mm x 297mm，四边各保留20mm的边距，显示区域170x257
      //   var imgHeight = Math.floor((a4h * canvas.width) / a4w); // 按A4显示比例换算一页图像的像素高度
      //   var renderedHeight = 0;

      //   while (renderedHeight < canvas.height) {
      //     var page = document.createElement("canvas");
      //     page.width = canvas.width;
      //     page.height = Math.min(imgHeight, canvas.height - renderedHeight); // 可能内容不足一页

      //     // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
      //     page
      //       .getContext("2d")
      //       .putImageData(
      //         ctx.getImageData(
      //           0,
      //           renderedHeight,
      //           canvas.width,
      //           Math.min(imgHeight, canvas.height - renderedHeight)
      //         ),
      //         0,
      //         0
      //       );
      //     pdf.addImage(
      //       page.toDataURL("image/jpeg", 1.0),
      //       "JPEG",
      //       10,
      //       10,
      //       a4w,
      //       Math.min(a4h, (a4w * page.height) / page.width)
      //     ); // 添加图像到页面，保留10mm边距

      //     renderedHeight += imgHeight;
      //     if (renderedHeight < canvas.height) {
      //       pdf.addPage();
      //     } // 如果后面还有内容，添加一个空页
      //     // delete page;
      //   }
      //   pdf.save(htmlTitle + currentTime);
      // });
    }
  },
}
