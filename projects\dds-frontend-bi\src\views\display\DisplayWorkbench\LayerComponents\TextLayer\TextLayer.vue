<template>
  <!-- <div class="text" :style="styleColor">{{ styleColor.text }}</div> -->
  <div @keydown="handleKeydown" @keyup="handleKeyup" class="text">
    <!-- tabindex >= 0 使得双击时聚焦该元素 -->
    <div
      ref="text"
      :contenteditable="canEdit"
      :class="{ 'can-edit': canEdit }"
      tabindex="0"
      :style="textStyle"
      @dblclick.stop="setEdit"
      @paste="clearStyle"
      @mousedown="handleMousedown"
      @blur="handleBlur"
      @input="handleInput"
      v-html="textStyle.text"
    ></div>
  </div>
</template>

<script>
import { keycodes } from "@/utils/shortcutKey.js"
export default {
  name: "text-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      canEdit: false,
      ctrlKey: 17,
      isCtrlDown: false,
      cancelRequest: null
    }
  },
  computed: {
    textStyle() {
      const {
        color,
        text,
        fontSize,
        fontWeight,
        letterSpacing,
        textAlign,
        verticalAlign,
        background,
        borderRadiusLT,
        borderRadiusRT,
        borderRadiusRB,
        borderRadiusLB,
        fontFamily
      } = this.layer.params
      return {
        color,
        fontWeight,
        text,
        fontSize: fontSize + "px",
        letterSpacing: letterSpacing + "em",
        textAlign,
        fontFamily,
        borderRadius:
          borderRadiusLT +
          "px " +
          borderRadiusRT +
          "px " +
          borderRadiusRB +
          "px " +
          borderRadiusLB +
          "px",
        background,
        verticalAlign
      }
    }
  },
  methods: {
    handleInput(e) {
      this.$emit("input", e.target.innerHTML)
    },

    handleKeydown(e) {
      // 阻止冒泡，防止触发复制、粘贴组件操作
      this.canEdit && e.stopPropagation()
      if (e.keyCode === this.ctrlKey) {
        this.isCtrlDown = true
      } else if (
        this.isCtrlDown &&
        this.canEdit &&
        keycodes.includes(e.keyCode)
      ) {
        e.stopPropagation()
      } else if (e.keyCode === 46) {
        // deleteKey
        e.stopPropagation()
      }
    },

    handleKeyup(e) {
      // 阻止冒泡，防止触发复制、粘贴组件操作
      this.canEdit && e.stopPropagation()
      if (e.keyCode === this.ctrlKey) {
        this.isCtrlDown = false
      }
    },

    handleMousedown(e) {
      if (this.canEdit) {
        e.stopPropagation()
      }
    },

    clearStyle(e) {
      e.preventDefault()
      const clp = e.clipboardData
      const text = clp.getData("text/plain") || ""
      if (text !== "") {
        document.execCommand("insertText", false, text)
      }

      this.$emit("input", e.target.innerHTML)
    },

    handleBlur() {
      this.canEdit = false
    },

    setEdit() {
      console.log("双击触发")
      this.canEdit = true
      // 全选
      this.selectText(this.$refs.text)
    },

    selectText(element) {
      const selection = window.getSelection()
      const range = document.createRange()
      range.selectNodeContents(element)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  }
}
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  display: table;

  div {
    display: table-cell;
    width: 100%;
    height: 100%;
    outline: none;
    word-break: break-all;
    padding: 4px;
  }

  .can-edit {
    cursor: text;
    height: 100%;
  }
}

.preview {
}
</style>
