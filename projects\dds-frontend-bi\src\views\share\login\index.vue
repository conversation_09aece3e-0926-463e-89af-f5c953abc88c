<template>
  <div class="loginPage" :style="{ backgroundImage }">
    <div class="center light">
      <div class="header-logo">
        <img src="@/assets/logo.png" />
      </div>
      <div class="center-header">数字桌面</div>
      <div class="center-form">
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <el-form-item prop="username">
            <span class="svg-container">
              <dt-svg-icon icon-class="user" />
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="请输入登录帐号"
              name="username"
              type="text"
              tabindex="1"
              auto-complete="on"
            />
          </el-form-item>

          <el-form-item prop="password" style="margin-top: 25px">
            <span class="svg-container">
              <dt-svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="请输入密码"
              name="password"
              tabindex="2"
              auto-complete="on"
            />
            <span class="show-pwd" @click="showPwd">
              <dt-svg-icon
                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
              />
            </span>
          </el-form-item>

          <el-button
            :loading="loading"
            type="primary"
            style="width: 100%; margin-bottom: 30px; margin-top: 25px"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      passwordType: "password",
      loginForm: {
        username: "1111",
        password: "1111"
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: "blur",
            validator: "请输入登录帐号"
          }
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            message: "请输入密码"
          }
        ]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = ""
      } else {
        this.passwordType = "password"
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.loginPage {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url("~@/assets/bg.png") no-repeat center;
  background-size: cover;
}
.center {
  // position: relative;
  position: absolute;
  width: 475px;
  left: calc((100% - 475px) / 2);
  top: 25%;
  border-radius: 5px;
  padding: 65px 56px 69px;
  // background: #ffffff;
  box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  .header-logo {
    position: absolute;
    top: -88px;
    left: 50%;
    transform: translate(-50%);
  }
  .center-header {
    font-size: 26px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #222222;
    margin-bottom: 56px;
  }
  .set-language {
    width: 20px;
  }
  .center-form {
    height: 100%;
    width: 100%;
    margin-top: 10px;
    .login-form {
      width: 100%;
      margin: 0 auto;
      .svg-container {
        position: absolute;
        top: 50%;
        left: 12px;
        z-index: 2;
        color: rgba(0, 0, 0, 0.65);
        line-height: 0;
        transform: translateY(-50%);
      }
      .svg-icon {
        color: rgba(0, 0, 0, 0.25);
        font-size: 14px;
      }
      .show-pwd {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        font-size: 20px;
      }
    }
    /deep/.el-input {
      input {
        border: 1px solid #d9d9d9;
        color: rgba(0, 0, 0, 0.65);
        height: 40px;
        padding: 6px 35px;
        font-size: 14px;
        line-height: 40px;
        &:-webkit-autofill {
          box-shadow: 0 0 0px 1000px #e8f0fe inset !important;
          -webkit-text-fill-color: rgba(0, 0, 0, 0.65) important;
        }
      }
    }
    /deep/.el-button {
      height: 40px;

      padding: 0 15px;
      font-size: 16px;
      background-color: #1890ff;
      border-color: #1890ff;
      text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
      margin-bottom: 0px !important;
    }
    /deep/.el-form-item {
      margin: 0 0 0px;
    }
  }
}
</style>
