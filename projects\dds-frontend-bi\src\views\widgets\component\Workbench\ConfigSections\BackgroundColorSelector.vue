<template>
  <div style="width: 100%">
    <el-col>
      <el-form
        ref="colorForm"
        :model="colorForm"
        label-width="70px"
        size="mini"
      >
        <el-form-item label="颜色" class="form-item">
          <el-color-picker
            v-model="colorForm.color"
            class="color-picker-style"
            :predefine="predefineColors"
            @change="changeBackgroundStyle"
          />
        </el-form-item>
        <!-- <el-form-item label="透明度" class="form-item form-item-slider">
          <el-slider
            v-model="value1"
            show-input
            :show-input-controls="false"
          ></el-slider>
        </el-form-item>

        <el-form-item label="边框半径" class="form-item form-item-slider">
          <el-slider
            v-model="colorForm.borderRadius"
            show-input
            :show-input-controls="false"
            input-size="mini"
            @change="changeBackgroundStyle"
          />
        </el-form-item> -->
      </el-form>
    </el-col>
  </div>
</template>

<script>
export default {
  name: "background-color-selector",
  props: {
    param: {
      type: Object,
      required: false
    },
    chart: {
      type: Object,
      required: true
    }
  },
  data() {
    return {}
  },
  watch: {
    chart: {
      handler: function () {}
    }
  },
  mounted() {},
  methods: {
    changeBackgroundStyle() {
      this.$emit("onChangeBackgroundForm", this.colorForm)
    }
  }
}
</script>

<style scoped>
.shape-item {
  padding: 6px;
  border: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-item-slider >>> .el-form-item__label {
  font-size: 12px;
  line-height: 38px;
}
.form-item >>> .el-form-item__label {
  font-size: 12px;
}
.el-select-dropdown__item {
  padding: 0 20px;
}
span {
  font-size: 12px;
}
.el-form-item {
  margin-bottom: 6px;
}
.color-picker-style {
  cursor: pointer;
  z-index: 1003;
}
</style>
