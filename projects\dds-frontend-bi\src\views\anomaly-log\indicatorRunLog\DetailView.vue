<template>
  <div class="detailView">
    <DT-Header :content="title + '异常详情'" @back="$emit('handleBack')" />
    <!-- 查询 -->
    <SearchForm
      @search="handleSearch"
      :columns="columns"
      :is-card="false"
      :search-param.sync="searchForm"
      style="margin-bottom: 24px"
    ></SearchForm>
    <CommonTable
      :page.sync="page"
      :table-data="tableData"
      :show-batch-tag="false"
      :show-selection="false"
      :loading="loading"
      :table-columns.sync="tableColumns"
      @onload="getTableData"
      @handleSortChange="handleSortChange"
      ref="CommonTable"
    >
      <template #btn>
        <div class="btn">批量登记</div>
      </template>
      <template #actionSlot="{ row }">
        <el-button
          type="text"
          v-if="row.isProcessed"
          style="margin-right: 10px"
          @click="handleOpen(row)"
        >
          处理详情
        </el-button>
        <el-button
          v-else
          type="text"
          style="margin-right: 10px"
          @click="handleNotify(row)"
        >
          通知运维
        </el-button>
        <el-button
          type="text"
          style="margin-right: 10px"
          @click="goAppAddress(row)"
        >
          跳转至应用位置
        </el-button>
      </template>
    </CommonTable>
    <DetailDialog ref="DetailDialog" />
  </div>
</template>

<script>
import { exportExcel, isExternal } from '@/utils'
import Request from '@/service'
import SearchForm from '@/components/SearchForm/index.vue'
import CommonTable from '@/components/CommonTable.vue'
import DetailDialog from '../DetailDialog.vue'
export default {
  name: 'main-view',
  components: {
    SearchForm,
    CommonTable,
    DetailDialog
  },
  props: {
    // 数据
    title: {
      type: String,
      default: ''
    },

    indCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      // 搜索表单内容
      searchForm: {
        date: [],
        dataSources: [],
        processStatuses: [],
        exceptionReasons: [],
        order: ''
      },
      // 搜索渲染配置
      columns: [
        {
          label: '应用位置',
          prop: 'appLocation',
          search: {
            el: 'input'
          }
        },
        {
          label: '数据源',
          prop: 'dataSources',
          search: {
            el: 'select',
            props: {
              multiple: true
            }
          }
        },
        {
          prop: 'processStatuses',
          label: '处理状态',
          // fieldNames: {
          //   label: "nr",
          //   value: "id"
          // },
          enum: [
            {
              label: '暂未处理',
              value: '暂未处理'
            },
            {
              label: '已修复',
              value: '已修复'
            },
            {
              label: '无需处理',
              value: '无需处理'
            },
            {
              label: '数据链路正常后自动恢复',
              value: '数据链路正常后自动恢复'
            },
            {
              label: '排查中，问题尚不明确',
              value: '排查中，问题尚不明确'
            }
          ],
          search: {
            el: 'select',
            props: {
              multiple: true
            }
          }
        },
        {
          label: '异常原因',
          prop: 'exceptionReasons',
          enum: [
            {
              label: '计算规则错误',
              value: '计算规则错误'
            },
            {
              label: '数据源数据缺失',
              value: '数据源数据缺失'
            },
            {
              label: '指标正常波动',
              value: '指标正常波动'
            },
            {
              label: '服务器磁盘已满',
              value: '服务器磁盘已满'
            },
            {
              label: '服务器故障',
              value: '服务器故障'
            },
            {
              label: '停电',
              value: '停电'
            },
            {
              label: '服务器重启',
              value: '服务器重启 '
            }
          ],
          search: {
            el: 'select',
            props: {
              multiple: true
            }
          }
        },

        {
          label: '异常状态通知时间',
          prop: 'date',
          search: {
            el: 'date-picker',
            span: 2,
            props: {
              type: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          }
        }
      ],
      tableColumns: [
        {
          label: '应用位置',
          prop: 'appLocation',
          visible: true,
          sortable: false
        },
        {
          label: '异常状况',
          prop: 'alertStatus',
          visible: true,
          sortable: false
        },
        {
          label: '异常状态通知时间',
          prop: 'alertTime',
          visible: true,
          sortable: 'custom'
        },
        {
          label: '数据源',
          prop: 'dataSource',
          visible: true,
          sortable: false
        },
        {
          label: '处理状态',
          prop: 'processStatus',
          visible: true,
          sortable: false
        },
        {
          label: '异常维度',
          prop: 'abnormalDim',
          visible: true,
          sortable: false
        },
        {
          label: '异常原因',
          prop: 'alertReason',
          visible: true,
          sortable: false
        },
        {
          label: '操作',
          prop: 'action',
          width: 150,
          visible: true,
          slot: true,
          sortable: false
        }
      ],
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableData: []
    }
  },
  created () {
    this.getData()
  },
  mounted () {},
  methods: {
    handleSearch () {
      this.$refs.CommonTable.clearSort()
      this.page.currentPage = 1
      this.getData()
    },
    async getData () {
      this.loading = true
      const { data } = await Request.api.paramPost(
        'zeroWarn/getZeroWarnIndicatorDetailsByCode',
        {
          pageNum: this.page.currentPage,
          pageSize: this.page.pageSize,
          indCode: this.indCode,
          ...this.searchForm,
          startTime: this.searchForm.date[0],
          endTime: this.searchForm.date[1]
        }
      )
      this.page.total = data.total
      this.tableData = data.records
      this.loading = false
    },
    handleSortChange ({ order }) {
      this.searchForm.order = order === 'ascending' ? 'asc' : 'desc'
      this.page.currentPage = 1
      this.page.pageSize = 10
      this.getData()
    },
    // 通知运维
    async handleNotify (row) {
      await Request.api.paramPostQuery('/zeroWarn/notifyOperator', {
        code: row.id
      })
      this.$message({
        message: '通知运维成功',
        type: 'success'
      })
    },
    goAppAddress (row) {
      if (isExternal(row.router)) {
        window.open(row.router, '_blank')
        return
      }
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path: row.router
      })
      window.open(routerUrl.href, '_blank')
    },
    handleOpen (row) {
      this.$refs.DetailDialog.open(row)
    },
    // 导出
    handleExportExcel (selection) {
      const ids = selection.map(item => item.xh)
      exportExcel('/api/dds-server-bi/zeroWarn/exportZeroWarnIndicatorList', {
        ...this.form,
        startTime: this.form.time[0],
        endTime: this.form.time[1],
        xhlist: ids
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.detailView {
  background-color: #fff;
  padding: 20px;
}
</style>
