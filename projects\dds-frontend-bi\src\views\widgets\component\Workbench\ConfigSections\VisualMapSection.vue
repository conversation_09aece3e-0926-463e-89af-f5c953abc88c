<template>
  <div class="paneBlock">
    <h4>视觉映射</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="12">
          <el-checkbox
            v-model="visualMapForm.showVisualMap"
            @change="changeVisualMapStyle"
          >
            显示视觉映射
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="2">方向</el-col>
        <el-col span="10">
          <el-select
            v-model="visualMapForm.visualMapDirection"
            size="mini"
            @change="changeVisualMapStyle"
          >
            <el-option
              v-for="item in CHART_VISUALMAP_DIRECYTIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="2">位置</el-col>
        <el-col span="10">
          <el-select
            v-model="visualMapForm.visualMapPosition"
            size="mini"
            @change="changeVisualMapStyle"
          >
            <el-option
              v-for="item in CHART_VISUALMAP_POSITIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="2">宽度</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            size="mini"
            v-model="visualMapForm.visualMapWidth"
            @change="changeVisualMapStyle"
          ></el-input-number>
        </el-col>
        <el-col span="2">高度</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            size="mini"
            v-model="visualMapForm.visualMapHeight"
            @change="changeVisualMapStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeVisualMapStyle"
            v-model="visualMapForm.fontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeVisualMapStyle"
            v-model="visualMapForm.fontSize"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <el-color-picker
            v-model="visualMapForm.fontColor"

            @change="changeVisualMapStyle"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row gutter="8" type="flex" align="middle" class="blockRow">
        <el-col span="6">起始颜色</el-col>
        <el-col span="6">
          <el-color-picker
            v-model="visualMapForm.startColor"
            @change="changeVisualMapStyle"
          ></el-color-picker>
        </el-col>
        <el-col span="6">结束颜色</el-col>
        <el-col span="6">
          <el-color-picker
            v-model="visualMapForm.endColor"
            @change="changeVisualMapStyle"
          ></el-color-picker>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
CHART_VISUALMAP_POSITIONS

import {
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  CHART_VISUALMAP_DIRECYTIONS,
  CHART_VISUALMAP_POSITIONS,
} from "@/globalConstants"
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      CHART_VISUALMAP_POSITIONS,
      CHART_VISUALMAP_DIRECYTIONS,
      PIVOT_CHART_FONT_SIZES,
      PIVOT_CHART_FONT_FAMILIES,
      visualMapForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.visualMapForm = this._.cloneDeep(
        this.chartData.chartStyles.visualMap
      )
    },
    changeVisualMapStyle() {
      this.$emit("changeStyle", "visualMap", this.visualMapForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
