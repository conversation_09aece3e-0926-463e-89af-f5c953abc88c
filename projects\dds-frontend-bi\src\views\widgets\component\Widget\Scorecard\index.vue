<template>
  <div class="scorecard_main" v-if="isRouterAlive">
    <div class="container">
      <p
        class="scorecardTitle headTitle"
        :style="{
          left: 9 + '%',
          top: 9 + '%',
          fontSize: widgetProps.chartStyles.scorecard.titleFontSize + 'px',
          fontFontFamily: widgetProps.chartStyles.scorecard.titleFontFamily
        }"
      >
        {{ widgetProps.chartStyles.scorecard.title }}
      </p>
      <p
        class="scorecardTitle content"
        :style="{
          left: widgetProps.chartStyles.scorecard.moveHeaderLeft + '%',
          top: widgetProps.chartStyles.scorecard.moveHeaderTop + '%',
          fontSize: topFontSize + 'px'
        }"
      >
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.prefixHeaderFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.prefixHeader }}
        </span>
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.headerFontFamily,
            fontWeight: 'bold'
          }"
          v-if="widgetProps.chartStyles.scorecard.headerVisible"
        >
          {{ headerText }}
        </span>
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.suffixHeaderFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.suffixHeader }}
        </span>
      </p>
      <p
        class="scorecardTitle footer"
        :style="{
          left: widgetProps.chartStyles.scorecard.moveContentLeft + '%',
          top: widgetProps.chartStyles.scorecard.moveContentTop + '%',
          fontSize: footerFontSize + 'px',
          transform: `scale(${scale})`,

          transformOrigin: '0% 0%'
        }"
      >
        <span
          :style="{
            fontFamily:
              widgetProps.chartStyles.scorecard.prefixContentFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.prefixContent }}
        </span>
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.contentFontFamily
          }"
          v-if="widgetProps.chartStyles.scorecard.contentVisible"
        >
          {{ contentText }}
        </span>
        <span
          :style="{
            fontFamily:
              widgetProps.chartStyles.scorecard.suffixContentFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.suffixContent }}
        </span>
      </p>
      <p
        class="scorecardTitle footer"
        :style="{
          left: widgetProps.chartStyles.scorecard.moveFooterLeft + '%',
          top: widgetProps.chartStyles.scorecard.moveFooterTop + '%',
          fontSize: footerFontSize + 'px',
          transform: `scale(${scale})`,
          transformOrigin: '0% 0%'
        }"
      >
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.prefixFooterFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.prefixFooter }}
        </span>
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.fontFontFamily
          }"
          v-if="widgetProps.chartStyles.scorecard.footerVisible"
        >
          {{ footerText }}
        </span>
        <span
          :style="{
            fontFamily: widgetProps.chartStyles.scorecard.suffixFooterFontFamily
          }"
        >
          {{ widgetProps.chartStyles.scorecard.suffixFooter }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import { getFormattedValue } from "../../Config/Format/util"
import { debounce } from "@/utils"
import { getTextWidth } from "../../util"

export default {
  props: {
    widgetProps: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      footerFontSize: "",
      topFontSize: "",
      headerText: "",
      contentText: "",
      footerText: "",
      width: "",
      height: "",
      scale: "",
      isRouterAlive: true
    }
  },
  mounted() {
    this.getWidthHeight()
    var elementResizeDetectorMaker = require("element-resize-detector") // 导入
    // 创建实例
    var erd = elementResizeDetectorMaker()
    // 创建实例带参
    elementResizeDetectorMaker({
      strategy: "scroll", // <- For ultra performance.
      callOnAdd: true,
      debug: true
    })
    erd.listenTo(this.$el, () => {
      const { fontSizeFixed } = this.widgetProps.chartStyles.scorecard
      if (!fontSizeFixed) {
        this.getWidthHeight()
      }
    })
  },
  watch: {
    widgetProps: {
      deep: true,
      handler() {
        this.reload()
      }
    }
  },
  methods: {
    getWidthHeight() {
      const {
        prefixHeader,
        suffixHeader,
        prefixContent,
        suffixContent,
        prefixFooter,
        suffixFooter,
        fontSizeFixed
      } = this.widgetProps.chartStyles.scorecard

      const [metricHeader, metricContent, metricFooter] =
        this.widgetProps.metrics // 解构指标对象
      const { headerVisible, contentVisible, footerVisible } =
        this.widgetProps.chartStyles.scorecard // 是否显示指标
      this.headerText = this.getMetricText(metricHeader, headerVisible)
      this.contentText = this.getMetricText(metricContent, contentVisible)
      this.footerText = this.getMetricText(metricFooter, footerVisible)
      this.width = document.querySelector(".scorecard_main").offsetWidth
      this.height = document.querySelector(".scorecard_main").offsetHeight
      // 是否固定字体
      if (!fontSizeFixed) {
        const { titleFontSize, contentFontSize } = this.computeFontSize(
          prefixHeader || "",
          this.headerText,
          suffixHeader || "",
          prefixContent || "",
          this.contentText,
          suffixContent || "",
          prefixFooter || "",
          this.footerText,
          suffixFooter || ""
        )
        this.footerFontSize = titleFontSize
        this.topFontSize = contentFontSize
      } else {
        this.footerFontSize = this.widgetProps.chartStyles.scorecard.fontSizeSub
        this.topFontSize = this.widgetProps.chartStyles.scorecard.fontSizeMain
      }
    },
    // 不固定字体时根据盒子宽高计算字体大小
    computeFontSize(
      prefixHeader,
      headerText,
      suffixHeader,
      prefixContent,
      contentText,
      suffixContent,
      prefixFooter,
      footerText,
      suffixFooter
    ) {
      const hasHeader = prefixHeader || headerText || suffixHeader
      const hasContent = prefixContent || contentText || suffixContent
      const hasFooter = prefixFooter || footerText || suffixFooter

      const { width, height } = this
      const maxPartSize = 16

      const exactWidth =
        width * (width <= 150 ? 1 : width <= 250 ? 0.9 : 0.7) - 16 * 2

      const sumPartsW = Math.max(
        getTextWidth(prefixHeader + headerText + suffixHeader, "", "32px"),
        getTextWidth(prefixContent + contentText + suffixContent, "", "12px"),
        getTextWidth(prefixFooter + footerText + suffixFooter, "", "12px")
      )
      const footerTextWidth =
        getTextWidth(prefixFooter + footerText + suffixFooter, "", "12px") +
        getTextWidth(prefixContent + contentText + suffixContent, "", "12px") +
        width * (this.widgetProps.chartStyles.scorecard.moveFooterLeft / 100)
      this.scale =
        width - footerTextWidth > 0 ? 1 : width / (footerTextWidth + 30)
      const exactHeight =
        height * (height <= 150 ? 1 : height <= 250 ? 0.9 : 0.7) - 40
      const sumPartsH =
        (hasHeader ? 3 : 0) + (hasContent ? 8 : 0) + (hasFooter ? 3 : 0)
      const gapH = 8
      const sumGapH =
        (hasHeader ? gapH : 0) +
        (hasContent ? gapH : 0) +
        (hasFooter ? gapH : 0)

      const exactPartSize = Math.min(
        (exactWidth / sumPartsW) * 3,
        (exactHeight - sumGapH) / sumPartsH,
        maxPartSize
      )
      return {
        titleFontSize: Math.floor(3 * exactPartSize),
        contentFontSize: Math.floor(8 * exactPartSize)
      }
    },
    getMetricText(metric, visible) {
      if (!metric || !visible) {
        return ""
      }
      const { data } = this.widgetProps
      const { displayName, agg, format } = metric
      const metricName = `${agg}(${displayName})`
      const text = data.length
        ? getFormattedValue(data[0][metricName], format)
        : ""
      return text
    },
    // 使表格重新渲染
    reload() {
      this.isRouterAlive = false
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(function () {
        this.isRouterAlive = true
        this.getWidthHeight()
      })
    }
  },
  beforeDestroy() {
    window.removeEventListener("resize", debounce(this.getWidthHeight, 500))
  }
}
</script>

<style scoped lang="scss">
.scorecard_main {
  width: 100%;
  height: 100%;
  .container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .scorecardTitle {
    position: absolute;
    padding: 8px 0 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    span {
      display: inline-block;
      line-height: 1;
      white-space: pre;
    }
  }
  .headTitle {
    color: var(--theme-text-color);
  }
  .content {
    font-family: DINPro-Bold, DINPro;
    color: var(--theme-content-color);
  }
  .footer {
    span:nth-child(1) {
      color: var(--footer1-text-color);
    }
    span:nth-child(2) {
      color: var(--footer2-text-color);
      font-weight: "bold";
    }
    span:nth-child(3) {
      color: var(--footer2-text-color);
      font-weight: "bold";
    }
  }
}
</style>
