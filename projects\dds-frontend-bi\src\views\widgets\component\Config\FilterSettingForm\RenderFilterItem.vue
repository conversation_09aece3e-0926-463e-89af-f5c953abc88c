<template>
  <div class="filterItem noPadding">
    <div class="filterFormItem">
      <p>名称</p>
    </div>
    <div class="filterFormItem">
      <el-select
        v-model="value"
        placeholder="请选择"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <div class="filterFormItem">
      <el-input
        v-model="value"
        placeholder="请输入内容"
      ></el-input>
    </div>
    <el-button
      circle
      icon="el-icon-plus"
      type="primary"
      @click="addParallelNode(filterObj.id)"
    />
    <el-button
      v-if="!filterObj.root"
      circle
      icon="el-icon-s-operation"
      type="primary"
      @click="forkNode(filterObj.id)"
    />

    <el-button
      circle
      icon="el-icon-minus"
      @click="deleteNode"
    />
  </div>
</template>

<script>
import { uuid } from '@/utils/index.js'
export default {
  components: {

  },
  props: {
    filterObj: {
      type: Object,
      default: () => { },
    },
    filters: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      flattenTree: null
    }
  },
  computed: {


  },
  created() {

  },
  mounted() {

  },
  watch: {
    filters: {
      handler() {
        this.flattenTree=this.initFlattenTree(this.filters,{})
      },
      immediate: true,
      deep: true
    }

  },
  methods: {
    addParallelNode(nodeId) {
      const { flattenTree }=this
      console.log(this.flattenTree,nodeId)
      const currentNode=flattenTree[nodeId]
      const newNode={
        id: uuid(8,16),
        type: 'node',
        parent: 0
      }

      if (currentNode.parent) {
        const parent=flattenTree[currentNode.parent]
        newNode.parent=parent.id
        parent.children.push(newNode)
        flattenTree[newNode.id]=newNode
        this.flattenTree={ ...flattenTree }
      } else {
        const parent={
          id: uuid(8,16),
          root: true,
          type: 'link',
          rel: 'and',
          children: []
        }

        newNode.parent=parent.id
        parent.children.push(currentNode)
        parent.children.push(newNode)

        delete currentNode.root
        delete flattenTree[currentNode.id]
        currentNode.id=uuid(8,16)
        currentNode.parent=parent.id

        flattenTree[currentNode.id]=currentNode
        flattenTree[parent.id]=parent
        flattenTree[newNode.id]=newNode

        this.flattenTree={ ...flattenTree }
        this.$emit('onAddTreeNode',parent)
      }
    },
    initFlattenTree(tree,flatten) {
      flatten[tree.id]=tree
      if (tree.children) {
        tree.children.forEach((c) => {
          this.initFlattenTree(c,flatten)
        })
      }
      return flatten
    },
    forkNode(nodeId) {
      const { flattenTree }=this
      const currentNode=flattenTree[nodeId]
      const cloneNode={
        ...currentNode,
        id: uuid(8,16),
        parent: currentNode.id
      }
      const newNode={
        id: uuid(8,16),
        type: 'node',
        parent: currentNode.id
      }

      currentNode.type='link'
      currentNode.rel='and'
      currentNode.children=[ cloneNode,newNode ]

      flattenTree[cloneNode.id]=cloneNode
      flattenTree[newNode.id]=newNode
      this.flattenTree={ ...flattenTree }
      console.log(this.flattenTree,'this.flattenTree')


    },

  },
}
</script>

<style scoped lang="scss">


</style>
