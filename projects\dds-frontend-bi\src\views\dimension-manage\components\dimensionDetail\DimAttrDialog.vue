<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    title="维度属性"
    width="480px"
    custom-class="custom-dialog"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="名称" prop="dimName">
        <el-input v-model="formData.dimName" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="维度类型">
        <el-select v-model="formData.categoryCode" placeholder="请选择类型">
          <el-option
            :label="item.label"
            :value="item.value"
            v-for="item in dimensionTypeList"
            :key="item.categoryCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="默认版本">
        <el-select v-model="formData.defaultVersion" placeholder="请选择类型">
          <el-option
            :label="item.version"
            :value="item.version"
            v-for="item in allVersions"
            :key="item.version"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import Request from "@/service"

export default {
  props: {
    allVersions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: {},
      rules: {
        dimName: [{ required: true, message: "名称不能为空", trigger: "blur" }]
      },
      treeData: [
        {
          label: "一级节点",
          children: [{ label: "二级节点" }, { label: "二级节点" }]
        }
      ],
      treeProps: {
        children: "children",
        label: "label"
      },
      dimensionTypeList:[]
    }
  },
  methods: {
    openDialog(row) {
      this.formData = { ...row }
      this.getDimensionList()
      this.dialogVisible = true
    },
    handleClose() {
      this.$refs.form.resetFields()
    },
      // 获取维度列表
    async getDimensionList() {
      const { data } = await Request.api.paramPost(
        "/DimManage/getDimCategoryList",
        {}
      )
      this.dimensionTypeList = data.map(item => ({
        label: item.categoryName,
        value: item.categoryCode
      }))
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          // 提交逻辑

          this.allVersions.forEach(async item => {
           await Request.api.paramPost(
              "/DimManage/editVersion",
              {
                ...item,
                defaultVersion:this.formData.defaultVersion,
                categoryCode:this.formData.categoryCode,
                dimName:this.formData.dimName,
              }
            )
          })
          this.dialogVisible = false
        }
      })
    },
    handleNodeClick(data) {
      console.log("节点点击:", data)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-row {
  margin-bottom: 0;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0 0 8px 0;
  line-height: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  text-align: left;
  font-style: normal;
}

::v-deep .el-form-item__error {
  font-size: 10px;
}

.custom-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      cursor: pointer;
      color: #999;

      &:hover {
        color: #666;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-select,
  .el-input {
    width: 100%;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 0px 20px 0;

    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
