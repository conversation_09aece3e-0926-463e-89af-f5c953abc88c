<template>
  <div class="content-wrap">
    <div class="content" ref="content">
      <div class="name">{{ detailData.zbmc }}</div>
      <div class="info">
        <div class="table">
          <div class="label">所属表名:</div>
          <div class="text">
            {{ detailData.ttab || "无" }}
          </div>
        </div>
        <div class="indicator-area">
          <div class="label">所属指标域:</div>
          <div class="text">
            {{ detailData.sysjy || "无" }}
          </div>
        </div>
      </div>
      <div class="desc">
        <div class="label">描述:</div>
        <div class="text">
          {{ detailData.ms || "无" }}
        </div>
      </div>
      <div class="tags">
        <div class="label">标签:</div>
        <div class="text">
          <div v-if="detailData.bq">
            <el-tag
              v-for="item in detailData.bq"
              :key="item"
              style="margin-right: 4px"
            >
              {{ item }}
            </el-tag>
          </div>
          <span v-else>无</span>
        </div>
      </div>
      <CommonTable
        :page.sync="page"
        :show-selection="false"
        :table-data="tableData"
        :show-batch-tag="false"
        :table-columns.sync="tablecolumn"
        @onload="getTableData"
        :max-height="480"
        v-loading="loading"
      />

      <!-- <el-table
        :data="tableData"
        style="width: 100%"
        :max-height="480"
        v-loading="loading"
      >
        <el-table-column
          v-for="item in tablecolumn"
          :key="item.name"
          :prop="item.name"
          :label="item.alias"
          :width="
            flexColumnWidth(
              'alias',
              'name',
              tableData,
              tablecolumn,
              item.name,
              $refs.content
            )
          "
        >
          <template #default="scope">
            {{ scope.row[item.name] || "-" }}
          </template>
        </el-table-column>
      </el-table>
      <div class="page">
        <el-pagination
          small
          layout="prev, pager, next"
          :current-page="page.currentPage"
          :total="page.total"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div> -->
    </div>
  </div>
</template>

<script>
import CommonTable from "@/components/CommonTable.vue"
import { flexColumnWidth } from "@/utils"
export default {
  components: { CommonTable },
  props: {},
  data() {
    return {
      indCode: "",
      id: "",
      lxbm: "",
      detailData: {
        id: "",
        lxbm: "",
        ttab: "", // 所属表名
        zbmc: "", // 指标名称
        sysjy: "", // 所属指标域
        ms: "", // 描述
        bq: [] // 标签
      },
      page: {
        total: 1,
        currentPage: 1,
        pageSize: 10
      },
      tableData: [],
      tablecolumn: [],
      loading: false
    }
  },
  created() {
    const { id, indCode,lxbm } = this.$route.query
    this.id = id
    this.lxbm = lxbm
    this.indCode = indCode
    this.getTableColumn()
    this.getAtomIndicatorInfo()
  },
  mounted() {},
  watch: {},
  methods: {
    flexColumnWidth,
    async getAtomIndicatorInfo() {
      const { data } = await this.$httpBi.search.getAtomIndicatorInfo({
        id: this.id,
        lxbm: this.lxbm,
        indCode: this.indCode
      })
      this.detailData = data
    },
    async getTableColumn() {
      const { data: tablecolumn } = await this.$httpBi.search.getTableColumn({
        id: this.id,
        lxbm: this.lxbm,
        indCode: this.indCode
      })
      this.tablecolumn = tablecolumn.map(item => ({
        label: item.alias,
        prop: item.name,
        visible: true,
        sortable: false
      }))
      this.getTableData()
    },
    async getTableData() {
      this.loading = true

      const { data: tableData } = await this.$httpBi.search.getTableData({
        id: this.id,
        lxbm: this.lxbm,
        indCode: this.indCode,
        currentPage: this.page.currentPage,
        pageSize: this.page.pageSize
      })
      this.page.total = tableData.totalCount
      this.tableData = tableData.list
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.content-wrap {
  width: 100%;
  min-width: 1184px;
  background: #f0f2f5;
  padding: 20px;
  box-sizing: border-box;
}
.content {
  background-color: #fff;
  padding: 24px;
  .name {
    height: 18px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    margin-bottom: 24px;
  }
  .info {
    display: flex;
    margin-bottom: 24px;
    .indicator-area {
      display: flex;

      margin-right: 24px;
      .label {
        width: 80px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        margin-right: 10px;

        font-style: normal;
      }
      .text {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .table {
      display: flex;

      margin-right: 24px;
      .label {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        margin-right: 10px;

        font-style: normal;
      }
      .text {
        min-width: 110px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .associated-indicator {
      display: flex;
      width: calc(100% - 250px);
      .label {
        min-width: 65px;
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #060607;
        line-height: 14px;
        text-align: left;
        margin-right: 10px;
        font-style: normal;
      }
      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1463ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1463ff;
        text-align: left;
        font-style: normal;
        span {
          .t {
            text-decoration: underline;
            cursor: pointer;
          }
          .symbol {
            text-decoration: none;
          }
        }
      }
    }
  }
  .desc,
  .tags {
    display: flex;
    margin-bottom: 24px;
    .label {
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #060607;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      margin-right: 10px;
    }
    .text {
      flex: 1;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2f3338;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .date {
    display: flex;
    margin-bottom: 24px;

    .label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #86909c;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      margin-right: 10px;
      width: 90px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #86909c;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    margin-bottom: 24px;
  }

  .page {
    display: flex;
  }
}
</style>
