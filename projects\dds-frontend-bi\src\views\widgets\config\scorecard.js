import ChartTypes from "./ChartTypes"
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
} from "@/globalConstants"

const scorecard = {
  id: ChartTypes.Scorecard,
  name: "scorecard",
  title: "翻牌器",
  icon: "scorecard",
  coordinate: "other",
  rules: [ { dimension: 0, metric: [ 1, 3 ] } ],
  data: [
    // {
    //   title: '维度',
    //   type: 'category',
    //   name: "cols"

    // },
    {
      title: "指标",
      type: "value",
      name: "metrics",
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '颜色',
    //   type: 'category',
    //   name: "color"
    // },
    // {
    //   title: '筛选',
    //   type: 'all',
    //   name: "filters"

    // }
  ],
  style: {
    scorecard: {
      title: "",
      titleFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      titleFontSize: "16",
      titleColor: PIVOT_DEFAULT_FONT_COLOR,

      headerVisible: true,
      headerColor: PIVOT_DEFAULT_FONT_COLOR,
      headerFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      prefixHeader: "",
      suffixHeader: "",
      prefixHeaderColor: PIVOT_DEFAULT_FONT_COLOR,
      prefixHeaderFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      suffixHeaderColor: PIVOT_DEFAULT_FONT_COLOR,
      suffixHeaderFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      moveHeaderLeft: 9,
      moveHeaderTop: 40,

      contentVisible: true,
      contentColor: PIVOT_DEFAULT_FONT_COLOR,
      contentFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      prefixContent: "",
      suffixContent: "",
      prefixContentColor: PIVOT_DEFAULT_FONT_COLOR,
      prefixContentFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      suffixContentColor: PIVOT_DEFAULT_FONT_COLOR,
      suffixContentFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      moveContentLeft: 9,
      moveContentTop: 75,

      footerVisible: true,
      footerColor: PIVOT_DEFAULT_FONT_COLOR,
      fontFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      prefixFooter: "",
      suffixFooter: "",
      prefixFooterColor: PIVOT_DEFAULT_FONT_COLOR,
      prefixFooterFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      suffixFooterColor: PIVOT_DEFAULT_FONT_COLOR,
      suffixFooterFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      moveFooterLeft: 55,
      moveFooterTop: 75,

      fontSizeFixed: true,
      fontSizeMain: "28",
      fontSizeSub: "12",

      gradientFromColor: "#ffffff",
      gradientToColor: "#ffffff",
      gradientDeg: 45,
    },
    spec: {},
  },
}

export default scorecard
