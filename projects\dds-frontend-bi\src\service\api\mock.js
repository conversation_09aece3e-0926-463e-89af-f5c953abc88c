import service from "../base"
import config from "../config"

/**
 *  获取views数据
 */
export default {
  getViews(params) {
    return service({
      url: config.VUE_MODULE_MOCK + "get/views",
      method: "get",
      params
    })
  },
  getData(data) {
    return service({
      url: config.VUE_MODULE_MOCK + "get/data",
      method: "post",
      data
    })
  },
  getdistinctvalue(data) {
    return service({
      url: config.VUE_MODULE_MOCK + "get/getdistinctvalue",
      method: "post",
      data
    })
  }
}
