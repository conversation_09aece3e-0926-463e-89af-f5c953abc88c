<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%',
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px',
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">创建SQL指标</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          SQL
        </div>
        <div class="line" :class="{ active: activeId == 2 }"></div>
        <div class="step-item" :class="{ active: activeId == 2 }">
          <i class="el-icon-data-line"></i>
          指标与维度
        </div>
      </div>
      <!-- <el-button
        icon="el-icon-full-screen"
        style="margin-left: auto"
        circle
        @click="clickFullscreen"
      ></el-button> -->
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="left">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="数据源:">
            <el-select
              @change="sourceDb"
              v-model="form.sourceId"
              placeholder="请选择数据源"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="item in sourcesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库表:">
            <el-tree
              :props="defaultProps"
              class="tree-db"
              :load="loadNode"
              :data="dbs"
              lazy
            >
              <span class="custom-tree-node" slot-scope="{ node }">
                <span>
                  <i v-if="node.data.type == 'DB'" class="el-icon-coin" />
                  <i v-if="node.data.type == 'TABLE'" class="el-icon-s-grid" />
                  {{ node.label }}
                </span>
              </span>
            </el-tree>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <MonacoEditor
          ref="MonacoEditor"
          :init-value.sync="form.sql"
          @mouseup="getSelectCode"
          :hint-data="hintData"
        />
        <div class="tables" v-if="tableData.length || tableColumns.length">
          <CommonTable
            :page.sync="page"
            id="xh"
            :table-data="tableData"
            :show-batch-tag="false"
            :table-columns.sync="tableColumns"
            @onload="handleRunSql"
            @handleSortChange="sortChange"
            @handleExport="handleExportExcel"
            @handleAllExport="handleAllExport"
          ></CommonTable>
        </div>
        <div class="empty" v-else>
          <div class="empty-icon"></div>
          <div class="empty-text">暂无数据</div>
        </div>
        <div class="footer-btn">
          <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
            取消
          </el-button>
          <el-button type="success" @click="handleRunSql">
            <!-- {{ isSelectSQL ? "选中试计算" : "试计算" }} -->
            试计算
          </el-button>
          <el-button
            type="primary"
            :disabled="!tableData.length"
            @click="nextStep"
          >
            下一步
          </el-button>
        </div>
      </div>
    </div>
    <el-form
      status-icon
      :rules="rules"
      :model="form"
      ref="ruleForm"
      label-width="0"
      class="ruleForm"
      v-if="activeId === 2 && !isNewSql"
    >
      <div class="step-two">
        <el-table
          :data="form.tableData1"
          style="
            width: calc(100% - 48px);
            margin: 24px 24px 0;
            overflow-x: auto;
          "
          max-height="calc(100% - 300px)"
          class="sqlIndicator-table"
        >
          <el-table-column
            prop="fieldNameModified"
            label="指标名称"
            width="150"
          >
            <!-- <template #default="{ row }">
            <el-input placeholder="" v-model="row.fieldNameModified"></el-input>
          </template> -->

            <template slot-scope="scope">
              <el-form-item
                :ref="'fieldNameModified' + scope.row.index"
                class="is-required"
                :prop="'tableData1.' + scope.$index + '.fieldNameModified'"
                :rules="rules.fieldNameModified"
                style="margin-top: 15px"
              >
                <el-input
                  placeholder=""
                  v-model="scope.row.fieldNameModified"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="val1" label="类型">
            <template #default="{ row }">
              <el-select v-model="row.tagType" placeholder="请选择">
                <el-option label="派生维度" value="派生维度"></el-option>
                <el-option label="指标" value="指标"></el-option>
                <el-option
                  label="时间维度"
                  value="时间维度"
                  :disabled="
                    form.tableData1.some(item => item.tagType == '时间维度')
                  "
                ></el-option>
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column prop="val1" label="计算方式" width="260">
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.execType"
                  placeholder="请选择"
                  :style="{
                    width: row.execType === 'sort' ? '76px' : '240px',
                  }"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="row.execType === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortType"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="row.sortRange"
                    v-if="row.execType === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="row.sortRange === 'top'"
                    v-model.number="row.sortLimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="计算周期" width="100">
            <template #default="{ row }">
              <el-select
                v-model="row.period"
                placeholder="请选择"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in jszqList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="scopeId" label="所属指标域" width="150">
            <template #default="{ row }">
              <avue-input-tree
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                default-expand-all
                v-model="row.scopeId"
                :key="isFull"
                :popper-append-to-body="!isFull"
                :props="{
                  label: 'name',
                  value: 'id',
                }"
                placeholder="请选择归属域"
                :dic="viewGroup"
              ></avue-input-tree>
            </template>
          </el-table-column>
          <el-table-column label="归属部门" width="150">
            <template #default="{ row }">
              <el-cascader
                clearable
                v-model="row.deptAllCode"
                :props="cascaderProps"
              ></el-cascader>
            </template>
          </el-table-column>
          <el-table-column prop="dataFormat" label="数据类型" width="150">
            <template #default="{ row }">
              <el-select v-model="row.dataFormat" placeholder="请选择数据格式">
                <el-option
                  v-for="item in sjgs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位" width="100">
            <template #default="{ row }">
              <div style="display: flex">
                <el-select
                  v-model="row.unitName"
                  placeholder="请选择单位"
                  :key="isFull"
                  :popper-append-to-body="!isFull"
                  :style="{
                    width: row.unitName === '其他' ? '122px' : '250px',
                  }"
                  class="myselect"
                  v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                </el-select>
                <el-input
                  v-if="row.unitName === '其他'"
                  v-model="row.diydw"
                  style="width: 122px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="150">
            <template #header>
              精度
              <el-tooltip
                class="item"
                effect="dark"
                content="精度的数值代表小数点的位数，此处仅支持输入整数"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; width: 150px"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input
                  style="width: 45px"
                  placeholder=""
                  v-model.number="row.precision"
                ></el-input>
                <el-checkbox
                  true-label="是"
                  false-label="否"
                  style="margin-left: 8px"
                  v-model="row.rounding"
                >
                  四舍五入
                </el-checkbox>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="阈值" width="150">
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center"
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
              >
                <el-input placeholder="" v-model="row.thresholdMin"></el-input>
                -
                <el-input placeholder="" v-model="row.thresholdMax"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" width="150">
            <template #default="{ row }">
              <el-input
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                placeholder="请输入描述"
                v-model="row.description"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="tagsName" label="标签" width="150">
            <template #default="{ row }">
              <el-select
                v-if="row.tagType == '度量' || row.tagType == '度量和维度'"
                v-model="row.tagsName"
                filterable
                multiple
                remote
                allow-create
                default-first-option
                @change="changeTag"
                @visible-change="handleSelectVisibleChange"
                @remove-tag="removeTag"
                :remote-method="remoteMethod"
                placeholder="请创建或者选择标签"
                :key="isFull"
                :popper-append-to-body="!isFull"
              >
                <el-option
                  v-for="item in formatLabels"
                  :key="item.bqmc"
                  :label="item.bqmc"
                  :value="item.bqmc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

        <div class="step-btn">
          <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
            取消
          </el-button>
          <el-button size="small" @click="activeId++">上一步</el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="false"
            :loading="loading"
            @click="handleSave"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-form>
    <el-form
      status-icon
      :model="form"
      ref="middleForm"
      label-width="0"
      class="middleForm"
      v-if="activeId === 3 && !isNewSql"
    >
      <div class="step-two">
        <el-table
          :data="form.tableData2"
          style="width: calc(100% - 48px); margin: 24px 24px 0"
          max-height="calc(100% - 300px)"
          class="sqlIndicator-table"
          border
        >
          <el-table-column prop="zd" label="字段">
            <template #default="{ row }">
              {{ row.fieldName }}
            </template>
          </el-table-column>
          <el-table-column prop="tagType" label="标记类型">
            <template #default="{ row }">
              <el-select v-model="row.tagType" placeholder="请选择">
                <el-option
                  v-for="item in tagOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="
                    item.value === '时间维度' &&
                    hasTimeDimension &&
                    row.tagType !== '时间维度'
                  "
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="wb" label="对应维度" width="260">
            <template #default="{ row }">
              <el-popover
                placement="right"
                width="250"
                trigger="hover"
                @after-leave="resetNoCoverShowCount(row)"
                v-if="
                  row.noCoverValues.length &&
                  ['维度', '度量和维度', '时间维度'].includes(row.tagType)
                "
              >
                <div>
                  <div class="popover-title">无法覆盖以下字段：</div>
                  <div class="popover-content">
                    <div
                      v-for="field in row.noCoverValues.slice(
                        0,
                        row.noCoverShowCount || 5
                      )"
                      :key="field"
                    >
                      {{ field }}
                    </div>
                    <el-button
                      v-if="
                        row.noCoverValues.length > (row.noCoverShowCount || 5)
                      "
                      type="text"
                      @click.stop="loadMoreNoCover(row)"
                    >
                      继续查看
                    </el-button>
                  </div>
                </div>
                <i
                  class="el-icon-warning"
                  style="
                    margin-right: 5px;
                    font-size: 20px;
                    color: #e6a23c;
                    cursor: pointer;
                  "
                  slot="reference"
                ></i>
              </el-popover>
              <el-select-v2
                v-if="
                  ['维度', '度量和维度', '时间维度'].includes(row.tagType) &&
                  !row.newCreate
                "
                v-model="row.definitionCode"
                :options="allDimTreeList"
                :props="{
                  label: 'dimName',
                  value: 'definitionCode',
                }"
                filterable
                placeholder="请选择"
                @change="getDimLevels($event, row)"
                clearable
              >
                <template #default="{ item }">
                  <p
                    style="
                      padding: 0 17px;
                      display: flex;
                      justify-content: space-between;
                    "
                  >
                    <span v-tooltip-content="180">{{ item.dimName }}</span>
                    <span>{{ item.version }}</span>
                  </p>
                </template>
              </el-select-v2>
              <span
                v-if="
                  row.newCreate &&
                  ['维度', '度量和维度', '时间维度'].includes(row.tagType)
                "
                style="display: flex; align-items: center"
              >
                <span v-tooltip-content="130">
                  {{ row.dimName }}({{ row.version }})
                </span>
                <el-button type="text" @click="removeDim(row)">移除</el-button>
                <el-button type="text" @click="editDim(row)">编辑</el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="wb" label="维度层级" width="220">
            <template #default="{ row }">
              <el-select
                v-if="
                  ['维度', '度量和维度', '时间维度'].includes(row.tagType) &&
                  row.definitionCode != 'self'
                "
                v-model="row.levelCode"
                placeholder="请选择"
                @change="onLevelCodeChange(row)"
              >
                <el-option
                  v-for="item in dimLevelMap[row.definitionCode]"
                  :key="item.levelCode"
                  :label="item.levelName"
                  :value="item.levelCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

        <div class="step-btn">
          <el-button @click="$router.push('/ddsBi/indicatorAnagement')">
            取消
          </el-button>
          <el-button size="small" @click="activeId = activeId - 2">
            上一步
          </el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="false"
            @click="handleNextStep"
          >
            下一步
          </el-button>
        </div>
      </div>
    </el-form>
    <EditSql
      v-if="activeId !== 1 && isNewSql"
      @backStop="activeId = 1"
      :is-new-sql="isNewSql"
      :sql="copySql"
      :new-sql="form.sql"
      :is-full="isFull"
      :ind-code="indCode"
      :tag-options="tagOptions"
    />
    <el-dialog
      title="编辑维度"
      :visible.sync="editDialogVisible"
      width="600px"
      label-width="100px"
      label-position="top"
    >
      <el-form :model="editDimForm" :rules="editDimRules" ref="editDimForm">
        <el-form-item label="维度名称" prop="dimName">
          <el-input v-model="editDimForm.dimName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="维度类型">
          <el-select
            v-model="editDimForm.categoryCode"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in dimensionTypeList"
              :key="item.categoryCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="editDimForm.version" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="层级名称" prop="levelName">
          <el-input
            v-model="editDimForm.levelName"
            placeholder="请输入层级名称"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editDimForm.tags" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入描述"
            v-model="editDimForm.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新频率">
          <el-select
            v-model="editDimForm.updateFrequency"
            placeholder="请选择更新频率"
          >
            <el-option
              v-for="item in updateFrequencys"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="editDimForm.updateType">
            <el-radio :label="0">增量同步</el-radio>
            <el-radio :label="1">全量同步</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEditDim">确 定</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from "@/service"
// import encryptSql from "@/utils/encryptSql.js"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import CommonTable from "./CommonTable.vue"
import MonacoEditor from "@/components/MonacoEditor"
import screenfull from "screenfull"
import EditSql from "./EditSql.vue"
import options from "../mixins/options"
import { exportExcel } from "@/utils"

export default {
  components: { MonacoEditor, CommonTable, EditSql },
  mixins: [options],

  props: {},
  data() {
    let messageInstance = null
    const resetMessage = (options) => {
      if (messageInstance) messageInstance.close()
      messageInstance = this.$message(options)
    }
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = this.form.tableData1.filter(
        (item) => item.fieldNameModified === value
      )
      let errorMsg = ""
      if (arr.length > 1) {
        errorMsg = "指标或维度名称已存在,请重新输入"
      }
      const { data } = await Request.api.paramPost(
        "SqlIndicator/existByNameAndType",
        {
          name: value,
          type: arr[0].tagType,

          dimensions: this.form.tableData1
            .filter((e) => e.tagType === "维度" || e.tagType === "度量和维度")
            .map((item) => item.fieldNameModified),
        }
      )
      if (data) errorMsg = "指标或维度名称已存在,请重新输入"
      if (errorMsg) {
        resetMessage({ message: errorMsg, type: "warning" })
        callback(new Error(errorMsg))
      } else {
        callback()
      }
    }
    return {
      isSelectSQL: false, // 是否选中sql
      activeId: 1,
      sourcesList: [
        {
          updateTime: null,
          id: 39,
          name: "112.60-dds_platform_dev",
          description: null,
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 38,
          name: "ods",
          description: null,
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 37,
          name: "演示数据源",
          description: null,
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 30,
          name: "演示配置流程库",
          description: null,
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 18,
          name: "112.60-dds_platform_3.0.0",
          description: "112.60-dds_platform_3.0.0",
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 6,
          name: "113.8库",
          description: "达芬奇",
          type: "jdbc",
        },
        {
          updateTime: null,
          id: 5,
          name: "112.60库/dds-platform",
          description: "112.60库dds-platform",
          type: "jdbc",
        },
        {
          updateTime: "2022-02-25 16:32:25",
          id: 2,
          name: "nbda",
          description: "oracleNbda数据库",
          type: "jdbc",
        },
        {
          updateTime: "2022-01-26 10:18:07",
          id: 1,
          name: "dds-platform",
          description: "数字桌面演示数据库",
          type: "jdbc",
        },
      ], // 数据源列表
      form: {
        sourceId: 27, // 数据源id
        sql: "",
        variables: [],
        tableData1: [],
        tableData2: [],
      },
      rules: {
        fieldNameModified: [
          {
            validator: changeFieldNameModified,
            trigger: "blur",
            required: true,
          },
        ],
      },
      dbs: [],
      tableData: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
      },
      tableColumns: [],
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf",
      },
      tables: [],
      columns: [],
      isFull: false, // 是否全屏
      copySql: "", // 备份sql
      sqlInfo: {}, // 保存的sql信息
      indCode: "",
      numericFields: [], // 数值字段
      tagOptions: [
        {
          value: "维度",
          label: "维度",
        },
        {
          value: "度量和维度",
          label: "度量和维度",
        },
        {
          value: "度量",
          label: "度量",
        },
        {
          value: "时间维度",
          label: "时间维度",
        },
        {
          value: "无",
          label: "无",
        },
      ], // 标记类型
      dimLevelMap: {}, // 维度层级
      allDimTreeList: [], // 所有维度树
      editDialogVisible: false,
      editDimForm: {
        dimName: "",
        version: "",
      },
      editDimRules: {
        dimName: [
          { required: true, message: "请输入维度名称", trigger: "blur" },
        ],
        version: [{ required: true, message: "请输入版本", trigger: "blur" }],
        levelName: [
          { required: true, message: "请输入层级名称", trigger: "blur" },
        ],
      },
      editRow: null,
      updateFrequencys: [
        { label: "按日", value: 1 },
        { label: "按周", value: 2 },
        { label: "按月", value: 3 },
        { label: "按学期", value: 4 },
        { label: "按学年", value: 5 },
        { label: "按年", value: 6 },
      ],
      dimensionTypeList: [],
      loading: false,
    }
  },
  computed: {
    hintData() {
      return [
        ...this.tables.map((item) => item.name),
        ...this.columns.map((item) => item.name),
      ]
    },
    isNewSql() {
      console.log(this.copySql, "this.copySql")
      console.log(this.form.sql, "this.form.sql")

      return this.indCode
    },
    hasTimeDimension() {
      // 检查tableData1和tableData2是否有“时间维度”
      return (
        (this.form.tableData1 &&
          this.form.tableData1.some((item) => item.tagType === "时间维度")) ||
        (this.form.tableData2 &&
          this.form.tableData2.some((item) => item.tagType === "时间维度"))
      )
    },
  },
  created() {
    this.indCode = this.$route.query.indCode
    if (this.indCode) {
      this.getSqlDetail()
    }
    screenfull.on("change", () => {
      this.isFull = screenfull.isFullscreen
    })
  },
  mounted() {
    this.getLabelSelectList()
    this.getBaseUnit()
    this.getAllViewGroup()
    this.initSource()
    this.sourceDb()
    this.getDimTree()
  },
  watch: {},
  methods: {
    // 获取sql详情
    async getSqlDetail() {
      const { data } = await Request.api.paramPostQuery(
        "/SqlIndicator/getSqlIndicatorByIndCode",
        {
          indCode: this.indCode,
        }
      )
      this.form.sql = data.sqlStatement
      this.copySql = data.sqlStatement
      this.sqlInfo = data
      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue()
        this.handleRunSql()
      })
    },
    clickFullscreen() {
      if (!screenfull.enabled) {
        this.$message({
          message: "浏览器不支持全屏功能",
          type: "warning",
        })
        return false
      }
      console.log(screenfull, "screenfull")
      const elment = document.querySelector(".container-wrapper")
      screenfull.toggle(elment)
    },
    // 获取所有维度树
    async getDimTree() {
      const { data } = await Request.api.paramPost("/DimManage/getDimList", {
        pageSize: -1,
        dimName: "",
        pageNum: 1,
      })
      this.allDimTreeList =
        [
          {
            dimName: "自身维度创建",
            definitionCode: "self",
            value: "",
          },
          ...(data.list || []),
        ] || []
    },
    // 全部导出
    handleAllExport() {
      // 导出
      exportExcel("/api/dds-server-bi/SqlIndicator/exportAllData", {
        sql: this.form.sql,
      })
    },
    async handleSave() {
      this.loading = true
      let paramsFilter = this.form.tableData1.map((item) => {
        return {
          ...item,
          tagType: item.tagType === "度量和维度" ? "度量" : item.tagType,
          unitName: item.unitName === "其他" ? item.diydw : item.unitName,
        }
      })
      let isNoDl = this.form.tableData2
        .map((item) => {
          // 将"度量和维度"类型转换为"维度"
          if (item.tagType === "度量和维度") {
            return { ...item, tagType: "维度" }
          }
          return item
        })
        .filter((item) => item.tagType !== "度量")
      let params = paramsFilter.concat(isNoDl)
      console.log(params, "params")
      console.log(isNoDl, "isNoDl")
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/SqlIndicator/save",
            params
          )
          this.$message.success(data)
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {},
          })
          this.loading = false
        } else {
          this.loading = false
        }
      })
    },
    // 初始化数据源
    async initSource() {
      const { data } = await Request.view.getSources()
      this.sourcesList = data
    },
    // 数据源库
    async sourceDb() {
      this.dbs = []
      if (this.form.sourceId) {
        Request.view
          .getDatabases({ id: this.form.sourceId })
          .then((res) => {
            for (let i = 0; i < res.data.length; i++) {
              this.dbs.push({ name: res.data[i], type: "DB" })
            }
          })
          .catch(() => {})
      }
      console.log(this.dbs)
    },
    // 树节点加载
    loadNode(node, resolve) {
      // 第一层库
      if (node.level === 0) {
        return resolve(this.dbs)
      }
      // 第二层表
      if (node.level === 1) {
        Request.view
          .getTables({ id: this.form.sourceId, dbName: node.data.name })
          .then((res) => {
            this.tables = this.tables.concat(res.data.tables)
            return resolve(res.data.tables)
          })
          .catch(() => {})
      }
      if (node.level === 2) {
        Request.view
          .getColumns({
            id: this.form.sourceId,
            dbName: node.parent.data.name,
            tableName: node.data.name,
          })
          .then((res) => {
            this.columns = this.columns.concat(res.data.columns)
            return resolve(
              res.data.columns.map((item) => ({
                ...item,
                leaf: true,
              }))
            )
          })
          .catch(() => {})
      }
      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    getSelectCode() {
      if (this.$refs.MonacoEditor.getSelectionVal()) {
        this.isSelectSQL = true
      } else {
        this.isSelectSQL = false
      }
    },
    // 执行slq
    async handleRunSql(callback) {
      if (this.form.sql.trim() === "") {
        this.$message.warning("请输入sql")
        return
      }
      if (!this.form.sourceId) {
        this.$message.warning("请选择数据源")
        return
      }
      const params = {
        sql: this.form.sql,
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
      }

      // if (this.isSelectSQL) {
      //   params.sql = this.$refs.MonacoEditor.getSelectionVal()
      // }
      // 加密sql
      // params.sql = encryptSql.encrypt(params.sql)
      const { data } = await Request.api.paramPost(
        "/SqlIndicator/tryExecute",
        params
      )
      if (data.error) {
        this.tableColumns = []
        this.tableData = []
        this.page.total = 0
        return this.$message.error(data.error)
      }

      if (data.data.length) {
        // const res = await Request.api.paramPost(
        //   "/SqlIndicator/classifyFields",
        //   data.data
        // )
        // // 获取数值字段
        // this.numericFields = res.data.numericFields || []

        // 设置表头
        this.tableColumns = Object.keys(data.data[0]).map((item) => ({
          label: item,
          prop: item,
          visible: true,
          sortable: false,
        }))
      } else {
        this.tableColumns = []
      }
      console.log(this.tableColumns, "thistableColumns")
      this.tableData = data.data || []
      this.page.total = data.total
      console.log(callback, "callback")
      if (typeof callback === "function") {
        callback()
      }
    },
    handleExportExcel(selection) {
      console.log(selection, "selection")

      jsonToSheetXlsx({
        data: selection,
        filename: "表数据" + new Date().getTime(),
      })
    },
    // 下一步
    async nextStep() {
      this.handleRunSql(async () => {
        this.activeId = 3
        // 组装参数
        const params = this.tableColumns.map((item) => ({
          tableSource: this.form.sql, // 数据源ID
          fieldCode: "",
          fieldName: item.label,
          lxbm: "sq",
        }))
        // 匹配维度
        const matchResult = await this.matchDim(params)
        // 融合数据
        this.form.tableData2 = this.tableColumns.map((item) => {
          const match = matchResult.find((m) => m.fieldName === item.prop)

          if (match.definitionCode) {
            this.$set(this.dimLevelMap, match.definitionCode, match.dimLevels)
          }

          return {
            ...item,
            definitionCode: match?.definitionCode || "",
            levelCode: match?.levelCode || "",
            noCoverValues: match?.noCoverValues || [],
            dimName: match?.dimName || "",
            version: match?.version || "",
            noCoverShowCount: 5,
            newCreate: false,
            fieldName: item.prop,
            fieldNameModified: item.prop,
            // tagType: "指标",
            period: "1",
            scopeId: Number(this.$route.query.sysjy) || 999,
            precision: null,
            rounding: "否",
            thresholdMin: null,
            thresholdMax: null,
            unitName: null,
            description: "",
            tagsName: [],
            createdById: null,
            indCode: null,
            sqlStatementOrigin: this.form.sql,
            tagType: item.prop.toUpperCase() === "ID" ? "无" : "维度",
            deptName: null,
            sortType: null,
            sortRange: null,
            sortLimit: null,
            dataFormat: 0,
            execType: "sum",
          }
        })
        console.log(this.form.tableData2, "this.form.tableData2")
      })
    },
    handleNextStep() {
      let arr = []
      let invalidRow = null
      this.form.tableData2.forEach((item) => {
        if (item.tagType === "度量" || item.tagType === "度量和维度") {
          arr.push(item)
        }
        // 新增校验
        if (["维度", "度量和维度", "时间维度"].includes(item.tagType)) {
          if (!item.definitionCode || !item.levelCode) {
            invalidRow = item
          }
        }
      })
      if (invalidRow) {
        this.$message.warning(
          "维度、度量和维度、时间维度类型必须选择对应维度和维度层级"
        )
        return
      }
      this.form.tableData1 = arr
      if (this.form.tableData1.length === 0) {
        this.$message.warning("请选择度量")
        return
      }
      this.activeId = 2
    },
    // 获取维度版本
    async getDimLevels(definitionCode, row) {
      if (definitionCode === "self") {
        await this.createSelfDim(row)
      } else {
        const { data } = await Request.api.paramPostQuery(
          "/DimManage/getDimLevelByDefinitionCode",
          { definitionCode }
        )
        if (data.length) {
          row.levelCode = data[0].levelCode
          row.levelName = data[0].levelName
          this.onLevelCodeChange(row)
        } else {
          row.levelCode = ""
          row.levelName = ""
          this.$message.warning("该维度没有层级")
        }
        this.$set(this.dimLevelMap, definitionCode, data)
      }
    },

    // 匹配相似维度和层级
    async matchDim(params) {
      const { data } = await Request.api.paramPost(
        "/AtomIndicator/checkDimMatch",
        params
      )
      return data
    },

    // 创建自身维度
    async createSelfDim(row) {
      // 参考原子指标参数风格
      const { data } = await Request.api.paramPost("/DimManage/addDim", {
        dimDefinition: {
          categoryCode: "wdlx_jcwd_1930198052817580032",
          dimName: row.fieldName || row.fieldCode,
          description: "",
          version: "v1.0",
          tags: "基于自身创建维度",
          updateFrequency: 1,
          updateType: 0,
          categoryName: "基础维度",
        },
        createType: 0,
        dimLevels: [
          {
            level: 1,
            levelName: row.fieldName || row.fieldCode,
            sourceTable: this.form.sql,
            sourceField: row.fieldName,
            fieldValueType: row.dataFormat, // 你可以根据实际字段调整
          },
        ],
        configs: [],
      })
      await this.getDimTree()
      // 补充所有维度相关字段，便于后续编辑和回显
      this.$set(row, "definitionCode", data.definitionCode)
      this.$set(row, "dimName", data.dimName)
      this.$set(row, "newCreate", true)
      this.$set(row, "version", data.version)
      this.$set(row, "categoryCode", "wdlx_jcwd_1930198052817580032")
      this.$set(row, "tags", "基于自身创建维度")
      this.$set(row, "description", "")
      this.$set(row, "updateFrequency", 1)
      this.$set(row, "updateType", 0)
      this.getDimLevels(data.definitionCode, row)
    },
    // 移除维度
    removeDim(row) {
      this.$confirm("是否删除当前维度树数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          Request.api
            .paramDel("/DimManage/deleteDimByCode", {
              code: row.definitionCode,
            })
            .then(() => {
              this.$message.success("删除成功")
              this.$set(row, "definitionCode", "")
              this.$set(row, "dimName", "")
              this.$set(row, "version", "")
              this.$set(row, "newCreate", false)
              this.$set(row, "levelCode", "")
              this.getDimTree()
            })
        })
        .catch((error) => {
          console.error(error)
        })
    },
    // 编辑自身维度
    editDim(row) {
      this.editRow = row
      // 针对自身创建维度，回显所有相关字段
      this.editDimForm = {
        definitionCode: row.definitionCode || "",
        dimName: row.dimName || "",
        version: row.version || "",
        categoryCode: row.categoryCode || "",
        levelName: row.levelName || "",
        tags: row.tags || "",
        description: row.description || "",
        updateFrequency:
          typeof row.updateFrequency === "undefined" ? 1 : row.updateFrequency,
        updateType: typeof row.updateType === "undefined" ? 0 : row.updateType,
      }
      this.editDialogVisible = true
    },
    saveEditDim() {
      this.$refs.editDimForm.validate(async (valid) => {
        if (valid) {
          await Request.api.paramPost("/DimManage/editVersion", {
            ...this.editDimForm,
          })
          this.$message.success("编辑成功")
          this.editDialogVisible = false
          // 同步表单数据到row
          if (this.editRow) {
            Object.assign(this.editRow, this.editDimForm)
          }
        } else {
          return false
        }
      })
    },

    // 获取无覆盖维度
    async getNoCoverDimValues(row) {
      console.log(row.fieldName, "row")
      const { data } = await Request.api.paramPost(
        "/DimManage/getNoCoverDimValues",
        {
          tableSource: this.form.sql,
          levelCode: row.levelCode,
          fieldCode: row.fieldCode,
          lxbm: "sq",
          fieldName: row.fieldName,
        }
      )
      row.noCoverValues = data
    },
    loadMoreNoCover(row) {
      if (!row.noCoverShowCount) {
        this.$set(row, "noCoverShowCount", 10)
      } else {
        this.$set(row, "noCoverShowCount", row.noCoverShowCount + 5)
      }
    },
    resetNoCoverShowCount(row) {
      this.$set(row, "noCoverShowCount", 5)
    },
    // 选择层级时
    async onLevelCodeChange(row) {
      await this.getNoCoverDimValues(row)
    },
  },
}
</script>

<style scoped lang="scss">
.popover-content {
  max-height: 150px;
  overflow-y: auto;
  // 可选：自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    background: #f5f5f5;
  }
  &::-webkit-scrollbar-thumb {
    background: #cbced1;
    border-radius: 2px;
  }
}
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }
  .steps {
    width: 406px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;
    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;
      &.active {
        background: #1563ff;
      }
    }
    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;
      &.active {
        color: #1563ff;
      }
      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}
.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100% - 100px);
  padding-top: 20px;

  background-color: #f0f2f5;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;
    ::v-deep .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;
      .el-form-item:nth-child(2) {
        flex: 1 1 0;
        overflow: hidden;
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
          height: 6px;
        }
        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 6px;
          height: 2px;
          background-color: #cfd6e6;
        }
        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: transparent;
          border-radius: 6px;
        }
        .el-form-item__content {
          height: calc(100% - 45px);
        }
      }
    }
    .custom-tree-node {
      width: 100%;
    }
    .tree-db {
      height: 100%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
  }
  .right {
    position: relative;
    width: calc(100% - 340px);
    padding: 20px;
    min-height: calc(100vh - 230px);

    background: #fff;
    padding-bottom: 52px;

    .tables {
      height: calc(100% - 352px);
      width: 100%;
      margin: 20px 0;
    }
    .empty {
      height: calc(100% - 352px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .empty-icon {
        width: 120px;
        height: 120px;
        background: url("~@/assets/images/empty1.png") no-repeat;
      }
      .empty-text {
        height: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #5c646e;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
    }
  }
}
.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
::v-deep .dt-pagination-container {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #b8b8b8;
  }
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}
#project_frame {
  .el-tree-node:not(.is-expanded)
    > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }
  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}
#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
