<!-- eslint-disable vue/no-parsing-error -->
<template>
  <div style="padding: 0 20px 48px; width: 100%">
    <Step :steps="steps" :current-step="currentStep" style="margin-top: 32px" />
    <div class="warm-reminder">
      <svg-icon icon-class="warning" style="margin-top: 5px" />
      <div class="warm-reminder-content">
        <p>
          偏度概念：偏度衡量随机变量概率分布的不对称性，是相对于平均值不对称程度的度量，通过对偏度系数的测量，我们能够判定数据分布的不对称程度以及方向。
        </p>
        <p>
          峰度概念：是研究数据分布陡峭或者平滑的统计量，通过对峰度系数的测量，我们能够判定数据相对于正态分布而言是更陡峭还是更平缓。
        </p>
        <p @click="dialogVisible = true">正态算法模型详情了解>></p>
      </div>
    </div>
    <SectionTitle
      title="#XX学院- XX专业- XX班级#学生 在 #XX指标# 的分布情况"
      style="margin-bottom: 16px"
    />

    <div class="section-content" v-loading="loading">
      <ChartLine
        :chart-data="chartData"
        x-field="name"
        :y-field="['man', 'woman']"
        :series-name="['男', '女']"
        y-axis-name="单位 (人次)"
        unit="人"
      />
    </div>
    <div class="distributes">
      <div class="distribute-item">
        <div class="distribute-item-num">0.5</div>
        <div class="distribute-item-title">相对于正态分布的偏度</div>
      </div>
      <div class="distribute-item">
        <div class="distribute-item-num">1.5</div>
        <div class="distribute-item-title">相对于正态分布的峰度</div>
      </div>
    </div>
    <el-dialog title="正态分布模型详情" :visible.sync="dialogVisible">
      <div class="desc">
        <p>
          1.
          偏度概念：偏度衡量随机变量概率分布的不对称性，是相对于平均值不对称程度的度量，通过对偏度系数的测量，我们能够判定数据分布的不对称程度以及方向。
        </p>
        <p>
          其中x
          ̅为样本均，偏度的衡量是相对于正态分布来说，正态分布的偏度为0，即数据分布是对称的。
        </p>
        <p>
          若偏度大于0，则分布右偏，即分布有一条长尾在右，人数聚集在均值以下区域，整体指标水平偏低；
        </p>
        <p>
          若偏度小于0，则分布为左偏，即分布有一条长尾在左，人数聚集在均值以上区域，整体指标水平偏高；
        </p>
        <p>偏度的绝对值越大，说明分布的偏移程度越严重。</p>

        <p>
          2.
          峰度概念：是研究数据分布陡峭或者平滑的统计量，通过对峰度系数的测量，我们能够判定数据相对于正态分布而言是更陡峭还是更平缓。比如正态分布的峰度为0，均匀分布的峰度为-1.2（平缓），指数分布的峰度6（陡峭）
        </p>

        <p>若峰度≈0 , 分布的峰态服从正态分布；</p>
        <p>
          若峰度>0,分布的峰态陡峭（高尖）；指标表现大量聚集在某一个区域，大量样本的指标水平相似。
        </p>
        <p>
          {{
            `若峰度<0,分布的峰态平缓（矮胖）；指标表现分散，样本的指标较为均匀。`
          }}
        </p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Step from "../Step"
import ChartLine from "@/components/Charts/ChartLine"
import SectionTitle from "@/components/SectionTitle.vue"
export default {
  components: { Step, SectionTitle, ChartLine },
  props: {
    currentStep: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      page: {
        total: 200,
        pageSize: 10,
        currentPage: 1
      },
      tableData: [
        {
          xh: "2019001",
          xm: "张三",
          bq: "标签1",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型1"
        },
        {
          xh: "2019002",
          xm: "李四",
          bq: "标签2",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型2"
        },
        {
          xh: "2019003",
          xm: "王五",
          bq: "标签3",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型3"
        },
        {
          xh: "2019004",
          xm: "赵六",
          bq: "标签4",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型4"
        },
        {
          xh: "2019005",
          xm: "钱七",
          bq: "标签5",
          xb: "男",
          mz: "汉族",
          nj: "2019",
          yx: "计算机学院",
          zy: "计算机科学与技术",
          bj: "1901",
          zq: "2019-2020",
          yjcs: "10",
          yjlx: "预警类型5"
        }
      ],
      tableColumns: [
        {
          label: "学号",
          prop: "xh",
          visible: true,
          sortable: false
        },
        {
          label: "姓名",
          prop: "xm",
          visible: true,
          sortable: false
        },
        {
          label: "标签",
          prop: "bq",
          visible: false,
          sortable: false
        },
        {
          label: "性别",
          prop: "xb",
          visible: true,
          sortable: false
        },
        {
          label: "民族",
          prop: "mz",
          visible: true,
          sortable: false
        },
        {
          label: "年级",
          prop: "nj",
          visible: true,
          sortable: false
        },
        {
          label: "院系",
          prop: "yx",
          visible: true,
          sortable: false
        },
        {
          label: "专业",
          prop: "zy",
          visible: true,
          sortable: false
        },
        {
          label: "班级",
          prop: "bj",
          visible: true,
          sortable: false
        },
        {
          label: "统计周期",
          prop: "zq",
          visible: true,
          sortable: false
        },
        {
          label: "预警次数",
          prop: "yjcs",
          visible: true,
          sortable: false
        },
        {
          label: "预警类型",
          prop: "yjlx",
          visible: true,
          sortable: false
        }
      ],
      chartData: [
        {
          name: "2019-2020",
          man: 100,
          woman: 200
        },
        {
          name: "2018-2019",
          man: 300,
          woman: 100
        },
        {
          name: "2017-2018",
          man: 120,
          woman: 100
        }
      ],
      message: ""
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="scss">
.warm-reminder {
  position: relative;
  width: 100%;
  height: 98px;
  background: #fffbe6;
  border-radius: 2px;
  border: 1px solid #fff1b8;
  margin: 16px auto 24px;
  display: flex;
  padding: 12px 17px 0;
  box-sizing: border-box;
  .warm-reminder-content {
    margin-left: 8px;
    p {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      line-height: 22px;
      height: 22px;
    }
    p:last-child {
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #1563ff;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-decoration-line: underline;
      margin-top: 8px;
      cursor: pointer;
    }
  }
  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
  }
}
.step-btn {
  position: absolute;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  .el-button--primary {
    background: #1563ff;
    border-color: #1563ff;
    &.is-disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
      &:hover {
        color: #fff;
        background-color: #a0cfff;
        border-color: #a0cfff;
      }
    }
    &:hover {
      background-color: rgba(64, 128, 255, 1);
      border-color: rgba(64, 128, 255, 1);
    }
  }
}
.section-content {
  height: 400px;
}
.desc {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #5c646e;
  line-height: 22px;
  font-style: normal;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 30px;
  p:nth-child(5) {
    margin-bottom: 24px;
  }
}

.distributes {
  display: flex;
  margin-top: 40px;
  .distribute-item {
    width: calc(50% - 12px);
    padding: 40px;
    box-sizing: border-box;
    height: 157px;
    border-radius: 8px;
    margin-right: 24px;
    background: #f5f7fa url("~@/assets/images/fd.png") no-repeat top 32px right
      39px;

    .distribute-item-num {
      height: 40px;
      font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
      font-weight: 500;
      font-size: 40px;
      color: #389cff;
      line-height: 40px;
      text-align: left;
      font-style: normal;
    }
    .distribute-item-title {
      height: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #2f3338;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: 14px;
    }
    &:last-child {
      margin-right: 0;
      background: #f5f7fa url("~@/assets/images/pd.png") no-repeat top 32px
        right 39px;
    }
  }
}
</style>
