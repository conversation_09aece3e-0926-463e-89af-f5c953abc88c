<template>
  <div class="filterBlock">
    <div class="filterRel">
      <div class="filterFormItem">
        <el-radio-group v-model="radio">
          <el-radio-button label="and">And</el-radio-button>
          <el-radio-button label="or">Or</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div class="filterList">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    filters: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
