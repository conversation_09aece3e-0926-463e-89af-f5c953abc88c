<template>
  <div>
    <el-row :gutter="8">
      <el-col :span="5">
        <el-form-item label="起始值:">
          <el-select v-model="selected.data[0].type">
            <el-option
              v-for="item in ReferenceValueTypeLabels"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" v-if="selected.data[0].type == 'constant'">
        <el-form-item label="常量值:">
          <el-input-number
            v-model="selected.data[0].value"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="关联指标:"
          prop="[0].metric"
          :rules="[
            { required: true, message: '关联指标不能为空', trigger: 'change' },
          ]"
        >
          <el-select placeholder="请选择指标" v-model="selected.data[0].metric">
            <el-option
              v-for="item in metrics"
              :key="item"
              :label="item.displayName"
              :value="item.displayName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :span="5">
        <el-form-item label="结束值:">
          <el-select v-model="selected.data[1].type">
            <el-option
              v-for="item in ReferenceValueTypeLabels"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5" v-if="selected.data[1].type == 'constant'">
        <el-form-item label="常量值:">
          <el-input-number
            v-model="selected.data[1].value"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="关联指标:"
          prop="[1].metric"
          :rules="[
            { required: true, message: '关联指标不能为空', trigger: 'change' },
          ]"
        >
          <el-select placeholder="请选择指标" v-model="selected.data[1].metric">
            <el-option
              v-for="item in metrics"
              :key="item"
              :label="item.displayName"
              :value="item.displayName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :span="5">
        <el-form-item label="标签:">
          <el-checkbox
            v-model="selected.data[1].label.visible"
          >
            显示标签
          </el-checkbox
          >
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="位置:">
          <el-select v-model="selected.data[1].label.position">
            <el-option
              v-for="item in CHART_LABEL_POSITIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="字体">
          <el-select
            placeholder="请选择"
            v-model="selected.data[1].label.font.family"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label=" ">
          <el-select
            placeholder="请选择"
            v-model="selected.data[1].label.font.size"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label=" ">
          <el-color-picker
            v-model="selected.data[1].label.font.color"
          ></el-color-picker>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :span="5">
        <el-form-item label="区间背景色">
          <el-color-picker
            v-model="selected.data[1].band.color"
          ></el-color-picker>
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="边框样式:">
          <el-select v-model="selected.data[1].band.border.type">
            <el-option
              v-for="item in PIVOT_CHART_LINE_STYLES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label=" ">
          <el-input-number
            v-model="selected.data[1].band.border.width"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label=" ">
          <el-color-picker
            v-model="selected.data[1].band.border.color"
          ></el-color-picker>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  ReferenceValueTypeLabels,
  ReferenceLabelPositionLabels,
} from "./constants"
import {
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_CHART_LINE_STYLES,
  CHART_LABEL_POSITIONS,
} from "@/globalConstants"
export default {
  components: {},
  props: {
    metrics: {
      type: Array,
    },
    selected: {
      type: Array,
    },
  },
  data() {
    return {
      ReferenceValueTypeLabels,
      ReferenceLabelPositionLabels,
      PIVOT_CHART_FONT_FAMILIES,
      PIVOT_CHART_LINE_STYLES,
      PIVOT_CHART_FONT_SIZES,
      CHART_LABEL_POSITIONS,
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
</style>
