import Vue from "vue"
let display = {
  namespaced: true,
  state: {
    widgets: [],
    currentDisplay: {},
    currentSlides: [],
    currentSlide: {
      config: {
        slideParams: {
          width: 1920,
          height: 1080,
          backgroundColor: "#fff",
          backgroundImage: null,
          autoSlideGlobal: true,
          autoPlay: true,
          autoSlide: "10",
          transitionGlobal: true,
          transitionStyleIn: "none",
          transitionStyleOut: "none",
          transitionSpeed: "default",
          scaleMode: "scaleWidth"
        }
      },
      index: 1,
      displayId: 13
    },
    slideLayers: [],
    currentLayer: {}
  },
  mutations: {
    SET_ACTIVE(state, id) {
      const index = state.slideLayers.findIndex(item => item.id === id)
      Vue.set(state.slideLayers, index, {
        ...state.slideLayers[index],
        active: true
      })
    },
    UNSET_ACTIVE(state, id) {
      const index = state.slideLayers.findIndex(item => item.id === id)
      this._vm.$set(state.slideLayers, index, {
        ...state.slideLayers[index],
        active: false
      })
    },
    // 添加silde
    ADD_SLIDE(state, { slideReponse, insertSlideIdx, afterSlides }) {
      state.currentSlide = slideReponse
      state.currentSlides.splice(insertSlideIdx)
      state.currentSlides.push(slideReponse)
      state.currentSlides = state.currentSlides.concat(afterSlides)
    },
    // 删除silde
    DELETE_SLIDE() {
      // state.currentSlides.splice(state.currentSlide.index, 1);
      // if (state.currentSlides.length > 0) {
      //   state.currentSlide = state.currentSlides[0];
      // }
    },
    // 保存slides
    SET_CURRENT_SLIDES(state, slides) {
      state.currentSlides = slides
    },
    // 保存目前大屏
    SET_DISPLAY_INFO(state, display) {
      state.currentDisplay = display
    },
    // 设置当前slide
    SET_CURRENT_SLIDE(state, slide) {
      state.currentSlide = slide
    },
    // 获取layer集合
    SET_SLIDELAYERS(state, slideLayers) {
      state.slideLayers = slideLayers.map(item => ({
        ...item,
        params: JSON.parse(item.params)
      }))
    },
    // 更新layer集合
    UPDATE_SLIDELAYERS(state, slideLayers) {
      const currentLayer = slideLayers.find(item => item.active)
      state.slideLayers = slideLayers
      state.currentLayer = currentLayer
    },
    // 获取所有关联widgets集合
    SET_WIDGETS(state, widgets) {
      state.widgets = widgets
    },
    // 设置当前layer
    SET_CURRENTLAYER(state, currentLayer) {
      state.currentLayer = currentLayer
    },
    UPDATE_LAYERS(state, layer) {
      const index = state.slideLayers.findIndex(item => item.id === layer.id)
      const currentLayer = state.slideLayers.find(item => item.active)
      if (currentLayer) {
        state.currentLayer = currentLayer
      }
      this._vm.$set(state.slideLayers, index, layer)
    }
  },

  actions: {
    // 获取display的slides信息
    async loadDisplaySlides({ commit }, displayId) {
      const { data } = await this._vm.$httpBi.display.getDisplaySlideList({
        displayId
      })
      const { slides, ...rest } = data
      slides.forEach(slide => {
        slide.config = JSON.parse(slide.config || "{}")
      })
      rest.config = JSON.parse(rest.config || "{}")
      commit("SET_CURRENT_SLIDES", slides || [])
      commit("SET_DISPLAY_INFO", rest)
      commit("SET_CURRENT_SLIDE", slides[0])
    },
    // 添加silde
    async addSlide({ state, dispatch }) {
      const slide = {
        id: undefined,
        index: state.currentSlide.index + 1,
        displayId: state.currentDisplay.id,
        config: {
          slideParams: {
            ...state.currentSlide.config.slideParams,
            avatar: undefined
          }
        }
      }
      // delete slide.config.slideParams.backgroundImage;
      const insertSlideIdx =
        state.currentSlides.findIndex(
          ({ id }) => id === state.currentSlide.id
        ) + 1
      state.currentSlides.slice(insertSlideIdx).map(s => ({
        ...s,
        index: s.index + 1
      }))
      await this._vm.$httpBi.display.addDisplaySlide({
        ...slide,
        config: JSON.stringify(slide.config)
      })

      // if (afterSlides.length) {
      //   const data = afterSlides.map((s) => ({
      //     ...s,
      //     displayId: state.currentDisplay.id,
      //     config: JSON.stringify(s.config),
      //   }));
      //   const res = await this._vm.$httpBi.display.addDisplaySlide(data);
      // }
      dispatch("loadDisplaySlides", state.currentDisplay.id)
      // const slideReponse = slide;
      // slideReponse.config = JSON.stringify(slideReponse.config);
      // this.$store.commit("display/ADD_SLIDE", {
      //   slideReponse,
      //   insertSlideIdx,
      //   afterSlides,
      // });
    },
    // 删除slide
    async delete_slide({ dispatch, state }, slideId) {
      await this._vm.$httpBi.display.delDisplaySlide({
        slideId
      })
      dispatch("loadDisplaySlides", state.currentDisplay.id)
    },
    // 更新
    async updateCurrentSlide({ state }, slide) {
      const params = state.currentSlides.map(s => {
        if (s.id === slide.id) {
          s.config = slide.config
        }
        return {
          ...s,
          displayId: state.currentDisplay.id,
          config: JSON.stringify(s.config)
        }
      })
      await this._vm.$httpBi.display.updDisplaySlides(params)
    },
    // 获取关联的slide的配置信息
    async loadSlideDetail({ commit }, { displayId, slideId }) {
      const { data } = await this._vm.$httpBi.display.getDisplaySlideWidgets({
        displayId,
        slideId
      })
      const { items, widgets } = data || {
        items: [],
        views: [],
        widgets: []
      }
      commit("SET_SLIDELAYERS", items)
      commit("SET_WIDGETS", widgets)
    },
    // 新增
    addSlideLayers({ dispatch }, { currentDisplayId, slideId, newLayers }) {
      return new Promise(reslove => {
        this._vm.$httpBi.display
          .addDisplaySlideWidget({
            displayId: currentDisplayId,
            slideId: slideId,
            slideWidgetCreates: newLayers.map(layer => ({
              ...layer,
              params: JSON.stringify(layer.params)
            }))
          })
          .then(() => {
            dispatch("loadSlideDetail", {
              displayId: currentDisplayId,
              slideId
            })
            reslove()
          })
      })
    },
    // 更新updateLayer
    async updateLayer({ commit }, layer) {
      await this._vm.$httpBi.display.updSlidesWidget({
        ...layer,
        params: JSON.stringify(layer.params)
      })
      commit("UPDATE_LAYERS", layer)
    },
    setActive({ commit, state }, id) {
      state.slideLayers.forEach(item => {
        if (item.id === id) {
          commit("SET_ACTIVE", item.id)
        } else {
          commit("UNSET_ACTIVE", item.id)
        }
      })
    },
    unsetActive({ commit }, id) {
      commit("UNSET_ACTIVE", id)
    }
  },
  getters: {}
}
export default display
