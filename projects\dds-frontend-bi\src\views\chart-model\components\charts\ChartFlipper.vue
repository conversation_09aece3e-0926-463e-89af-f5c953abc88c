<template>
  <div class="chart-flipper" :style="{ width, height }">
    <div class="flipper-container">
      <div class="digit-container">
        <div
          v-for="(digit, index) in value"
          :key="index"
          class="digit-box"
          :class="{ 'digit-separator': digit === ',' || digit === '.' }"
        >
          <div class="digit-content">
            <span class="digit-number">{{ digit }}</span>
          </div>
          <div class="digit-border"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChartFlipper",
  props: {
    // 基础属性
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "120px"
    },
    // 数值
    value: {
      type: [String, Number],
      default: "0"
    },
   
  },
  data() {
    return {
      currentValue: "0",
      animationTimer: null
    }
  },
 
}
</script>

<style scoped>
.chart-flipper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.flipper-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.digit-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.digit-box {
  position: relative;
  width: 60px;
  height: 80px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.digit-box.digit-separator {
  width: 20px;
  background: transparent;
}

.digit-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.digit-number {
  font-family: "Courier New", "Monaco", monospace;
  font-size: 48px;
  font-weight: bold;
  color: #00d4ff;
  text-shadow: 0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff;
  line-height: 1;
}

.digit-separator .digit-number {
  font-size: 24px;
  color: #00d4ff;
}

.digit-border {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 2px solid #00d4ff;
  border-radius: 6px;
  box-shadow: inset 0 0 10px rgba(0, 212, 255, 0.3),
    0 0 10px rgba(0, 212, 255, 0.5);
  z-index: 1;
}

.digit-separator .digit-border {
  display: none;
}



@keyframes flip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

/* 发光效果 */
.digit-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: 8px;
  z-index: 3;
  pointer-events: none;
}

/* 悬停效果 */
.chart-flipper:hover .digit-box {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

.chart-flipper:hover .digit-number {
  text-shadow: 0 0 15px #00d4ff, 0 0 25px #00d4ff, 0 0 35px #00d4ff,
    0 0 45px #00d4ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .digit-box {
    width: 45px;
    height: 60px;
  }

  .digit-number {
    font-size: 36px;
  }

  .digit-separator .digit-number {
    font-size: 18px;
  }

  .digit-container {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .digit-box {
    width: 35px;
    height: 50px;
  }

  .digit-number {
    font-size: 28px;
  }

  .digit-separator .digit-number {
    font-size: 14px;
  }

  .digit-container {
    gap: 4px;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  }
}

.digit-border {
  animation: pulse 2s infinite;
}
</style>
