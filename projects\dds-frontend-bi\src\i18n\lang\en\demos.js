export default {
  // demo页面A
  pageA: {
    text: 'dt-single-page-view',
    request: {
      A: 'request A',
      B: 'request B',
      C: 'request C'
    },
    treeDialog: {
      show: 'show treeDialog',
      title: 'treeDialog'
    },
    transfer: {
      show: 'show transfer',
      title: 'transferDialog',
      input: 'transfer + input, input can click'
    },
    svg: 'we provide a lot of public svg icon, you can use it direct， there are all example',
    svgEle: 'Of course, you can also use the icon by element UI, you can check the official website by yourself'
  },
  // demo页面B
  pageB: {
    text: 'dt-double-page-view',
    showUtils: 'public utils',
    checkPermission: 'check Permission',
    userInfo: 'userInfo is saved in vueX',
    userInfoText: 'auto get，you can use is direct',
    showUserInfo: 'console.log userInfo',
    publicLang: 'we provide a lot of public i18n data, you can use it direct， there are some example',
    publicLangAddress: 'data SourceCode at outer layer of project: common/lang'
  }
}