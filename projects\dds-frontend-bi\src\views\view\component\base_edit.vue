<template>
  <div
    style="height: calc(100vh - 110px); display: flex; flex-direction: column"
  >
    <div style="width: 96%; margin-left: 2%; margin-top: 2%">
      <el-form-item prop="groupId">
        <treeselect
          v-model="form.groupId"
          :options="groups"
          style="width: 100%"
          :clearable="false"
          :normalizer="normalizer"
          placeholder="请选择"
        />
      </el-form-item>

      <el-form-item prop="code">
        <el-input v-model="form.code" placeholder="编码"></el-input>
      </el-form-item>
      <el-form-item prop="name">
        <el-input v-model="form.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="form.description"
          placeholder="统计口径说明"
        ></el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-date-picker
          v-model="date"
          type="daterange"
          range-separator=""
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="sourceId">
        <el-select
          @change="sourceDb"
          v-model="form.sourceId"
          placeholder="数据源"
          style="width: 100%"
        >
          <el-option
            v-for="item in sources"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </div>
    <div class="tree" style="flex: 1; overflow-y: auto">
      <el-tree
        :props="treeProps"
        :load="loadNode"
        :data="dbs"
        lazy>
        <span class="custom-tree-node" slot-scope="{ node }">
          <span>
            <i v-if="node.data.type == 'DB'" class="el-icon-coin" />
            <i v-if="node.data.type == 'TABLE'" class="el-icon-s-grid" />{{
              node.label
            }}
          </span></span
        >
      </el-tree>
    </div>
  </div>
</template>

<script>
import Request from "@/service"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  name: "base-edit",
  components: { Treeselect },
  props: {
    form: {
      type: Object,
      default: ()=>{},
    },
  },
  data() {
    return {
      date: [],
      sources: [],
      groups: [],
      dbs: [],
      treeProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf",
      },
      tables: [],
      columns: [],
    }
  },
  created() {
    this.initGroup()
    this.initSource()
  },
  methods: {
    normalizer(node) {
      return {
        id: node.code,
        label: node.name,
        children: node.children,
      }
    },
    // 初始化数据源
    initSource() {
      Request.view
        .getSources()
        .then((res) => {
          this.sources = res.data
          if (this.form.id) {
            this.sourceDb(this.form.sourceId)
          } else {
            this.form.groupId = this.$route.query.group
          }
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 初始化分组
    initGroup() {
      Request.view
        .getAllViewGroup()
        .then((res) => {
          let rt = [ { code: 0, name: "根目录" } ]
          this.groups = rt.concat(res.data)
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 数据源库
    sourceDb(source_id) {
      this.dbs = []
      if (source_id) {
        Request.view
          .getDatabases({ id: source_id })
          .then((res) => {
            for (let i = 0; i < res.data.length; i++) {
              this.dbs.push({ name: res.data[i], type: "DB" })
            }
          })
          .catch(() => {})
      }
    },
    // 树节点加载
    loadNode(node, resolve) {
      // 第一层库
      if (node.level === 0) {
        return resolve(this.dbs)
      }
      // 第二层表
      if (node.level === 1) {
        Request.view
          .getTables({ id: this.form.sourceId, dbName: node.data.name })
          .then((res) => {
            this.tables = this.tables.concat(res.data.tables)
            return resolve(res.data.tables)
          })
          .catch(() => {})
      }
      if (node.level === 2) {
        Request.view
          .getColumns({
            id: this.form.sourceId,
            dbName: node.parent.data.name,
            tableName: node.data.name,
          })
          .then((res) => {
            this.columns = this.columns.concat(res.data.columns)
            return resolve(res.data.columns)
          })
          .catch(() => {})
      }
      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
  },
}
</script>
<style scoped>
::v-deep .el-range-editor.el-input__inner {
  width: 100%;
}
</style>
