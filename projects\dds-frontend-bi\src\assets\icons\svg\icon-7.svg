<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="主题场景优化" transform="translate(-21.000000, -527.000000)">
            <g id="一级导航" transform="translate(0.000000, 112.000000)">
                <g id="数据备份-3" transform="translate(0.000000, 400.000000)">
                    <g id="ic_reshui" transform="translate(21.000000, 15.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path d="M8,0.457392491 L8.43243367,0.825942397 C12.1219538,3.97040679 14,6.9582128 14,9.81816667 C14,12.8790041 11.3002716,15.3333333 8,15.3333333 C4.69970433,15.3333333 2,12.8790493 2,9.81816667 C2,6.9582128 3.87804622,3.97040679 7.56756633,0.825942397 L8,0.457392491 Z M8,2.216 L7.71399036,2.47263552 C4.77587158,5.14331165 3.33333333,7.59951803 3.33333333,9.81816667 C3.33333333,12.1128169 5.40922846,14 8,14 C10.590745,14 12.6666667,12.1127748 12.6666667,9.81816667 C12.6666667,7.53955455 11.1450997,5.01037022 8.04450536,2.25562468 L8,2.216 Z M9.23289316,5.87214802 L10.1957068,6.79451865 L8.99,8.05239249 L10,8.05266667 L10,9.386 L8.666,9.38539249 L8.666,9.84139249 L10,9.84213333 L10,11.1754667 L8.666,11.1753925 L8.66666667,12.0000333 L7.33333333,12.0000333 L7.333,11.1753925 L6,11.1754667 L6,9.84213333 L7.333,9.84139249 L7.333,9.38539249 L6,9.386 L6,8.05266667 L7.009,8.05239249 L5.80432201,6.79451397 L6.76714466,5.87214802 L8,7.15866667 L9.23289316,5.87214802 Z" id="形状结合" fill="currentColor" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
