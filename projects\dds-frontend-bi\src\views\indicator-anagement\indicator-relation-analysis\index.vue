<template>
    <DT-View>
    <IndicatorRelationAnalysis :is-portraits="false"/>
</DT-View>
</template>

<script>
import IndicatorRelationAnalysis from '@/views/search/app-indicators/indicator-detail/IndicatorRelation.vue'
export default {
    components: {
IndicatorRelationAnalysis
    },
    props: {

    },
    data() {
        return {

        }
    },
    computed: {

    },
    created() {

    },
    mounted() {

    },
    watch: {

    },
    methods: {

    },
}
</script>

<style scoped lang="scss">

</style>
