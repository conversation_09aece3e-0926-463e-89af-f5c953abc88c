<template>
  <div class="table-test">
    <h3>表格组件测试</h3>
    
    <!-- 使用新数据格式的表格 -->
    <div class="test-section">
      <h4>使用 headList + tableData 格式</h4>
      <Table
        :headList="headList"
        :tableData="tableData"
        :stripe="true"
        :border="true"
        size="small"
        height="300px"
      />
    </div>
    
    <!-- 使用旧数据格式的表格 -->
    <div class="test-section">
      <h4>使用 tableColumns + tableData 格式</h4>
      <Table
        :tableColumns="tableColumns"
        :tableData="tableData"
        :stripe="true"
        :border="true"
        size="small"
        height="300px"
      />
    </div>
  </div>
</template>

<script>
import Table from './Table.vue'

export default {
  name: 'TableTest',
  components: {
    Table
  },
  data() {
    return {
      // 新格式数据
      headList: [
        {
          prop: "date",
          label: "日期"
        },
        {
          prop: "name",
          label: "姓名"
        },
        {
          prop: "address",
          label: "地址"
        }
      ],
      tableData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄"
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄"
        },
        {
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄"
        },
        {
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄"
        }
      ],
      // 旧格式数据
      tableColumns: [
        { prop: "date", label: "日期", width: 120 },
        { prop: "name", label: "姓名", width: 100 },
        { prop: "address", label: "地址" }
      ]
    }
  }
}
</script>

<style scoped>
.table-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
}

h3 {
  color: #333;
  margin-bottom: 20px;
}

h4 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
}
</style>
