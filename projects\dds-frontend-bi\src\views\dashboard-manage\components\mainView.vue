<template>
  <div>
    <!-- 搜索 -->
    <DT-Form
      type="search"
      v-model="searchForm"
      :render="searchRender"
      @confirm="handleSearch"
    />
    <!-- 新增 -->
    <el-button
      v-if="$checkPermission(['security:config:save'])"
      type="primary"
      @click="$emit('handleAdd')"
    >
      {{ $t("button.add_") }}
    </el-button>
    <!-- 表格 -->
    <DT-Table :data="data" :column="tableColumn" />
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "param-main-view",
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 搜索（与index同步）
    search: {
      type: Object,
      default: () => ({
        theme: ""
      })
    },
    // 服务名称（筛选条件）
    themes: {
      type: Array,
      default: () => []
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    }
  },
  data() {
    return {
      // 搜索表单数据
      searchForm: {},
      // 搜索渲染配置
      searchRender: [
        // 服务名称
        {
          label: "主题",
          type: "select",
          key: "theme",
          option: this.themes
        }
      ],
      // 表格渲染配置
      tableColumn: [
        // 服务名称
        {
          label: "名称",
          prop: "name"
        },
        // 组名称
        {
          label: "路由",
          prop: "router"
        },
        // 键名称
        {
          label: "主题",
          prop: "themename"
        },
        // 操作
        {
          label: this.$t("table.actions"),
          width: 120,
          button: [
            {
              label: this.$t("button.edit"),
              permission: ["security:config:update"],
              onClick: ({ row }) => this.$emit("handleEdit", row)
            },
            {
              label: this.$t("button.delete"),
              permission: ["security:config:delete"],
              onClick: ({ row }) => this.$emit("handleDelete", row)
            }
          ]
        }
      ]
    }
  },
  mounted() {
    // 页面初始化时配置搜索双向绑定数据，使表单页返回时搜索框数据保持与之前一致
    this.searchForm = { ...this.search }
  },
  methods: {
    // 查询
    handleSearch(form) {
      this.pagination.currentPage = 1
      Object.keys(form).forEach(key => (this.search[key] = form[key]))
      this.$emit("search")
    },
    // 分页 - 每页条数改变
    handlePageSizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.$emit("paginationChange")
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.$emit("paginationChange")
    }
  }
}
</script>
