{"name": "rectangle", "title": "矩形", "params": [{"name": "size", "title": "矩形尺寸", "items": [{"name": "width", "title": "宽度（像素）", "component": "inputnumber", "default": 200}, {"name": "height", "title": "高度（像素）", "component": "inputnumber", "default": 120}]}, {"name": "position", "title": "矩形位置", "items": [{"name": "positionX", "title": "x轴位置（像素）", "component": "inputnumber", "labelCol": 15, "wrapperCol": 8}, {"name": "positionY", "title": "y轴位置（像素）", "component": "inputnumber", "labelCol": 15, "wrapperCol": 8}]}, {"name": "background", "title": "背景", "items": [{"name": "backgroundColor", "title": "背景颜色", "component": "colorPicker", "default": "rgba(252,252,252,0)", "labelCol": 10}, {"name": "backgroundImage", "title": "背景图片", "component": "upload", "action": "slide/widget/{layerId}/bgImage", "accept": "image/*", "autoUpdate": true, "labelCol": 12, "wrapperCol": 24}, {"name": "backgroundSize", "title": "图片大小", "component": "select", "values": [{"name": "自动", "value": "auto"}, {"name": "包含", "value": "contain"}, {"name": "铺满", "value": "cover"}], "default": "auto", "labelCol": 10, "wrapperCol": 14}, {"name": "backgroundRepeat", "title": "图片重复", "component": "select", "values": [{"name": "x轴y轴重复", "value": "repeat"}, {"name": "x轴重复", "value": "repeat-x"}, {"name": "y轴重复", "value": "repeat-y"}, {"name": "不重复", "value": "no-repeat"}], "default": "repeat", "labelCol": 10, "wrapperCol": 14}]}, {"name": "border", "title": "边框", "items": [{"name": "borderColor", "title": "边框颜色", "component": "colorPicker", "default": "rgba(252,252,252,0)", "labelCol": 10}, {"name": "borderWidth", "title": "边框粗细", "component": "inputnumber", "default": 0, "labelCol": 10}, {"name": "borderStyle", "title": "边框样式", "component": "select", "values": [{"name": "实线", "value": "solid"}, {"name": "虚线", "value": "dashed"}, {"name": "点线", "value": "dotted"}, {"name": "双框", "value": "double"}], "default": "solid", "labelCol": 10}, {"name": "borderRadius", "tip": "", "title": "圆角半径", "component": "inputnumber", "default": 0, "labelCol": 10}]}]}