<template>
  <el-dialog
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    :close-on-click-modal="false"
    @close="closeDialog"
    :title="formData.levelCode ? '层级编辑' : '层级新增'"
    width="800px"
    custom-class="custom-dialog"
  >
    <!-- 表单内容 -->
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="名称" prop="levelName">
            <el-input v-model="formData.levelName" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签">
            <el-input
              v-model="formData.tags"
              placeholder="请输入标签，多个标签用逗号分隔"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="描述">
        <el-input
          type="textarea"
          :rows="5"
          placeholder="请输入描述"
          v-model="formData.description"
        ></el-input>
      </el-form-item>
      <div style="margin-bottom: 12px">
        <el-checkbox
          label="是否显示"
          :true-label="1"
          :false-label="0"
          v-model="formData.isVisible"
        />
        <el-checkbox
          label="是否唯一"
          :true-label="1"
          :false-label="0"
          v-model="formData.isUnique"
        />
      </div>
      <el-row :gutter="40" v-if="currentVersionItem.createType == 0">
        <el-col :span="12">
          <el-form-item label="来源表" prop="sourceTable">
            <el-select
              v-model="formData.sourceTable"
              placeholder="请选择来源表"
              @change="changeSourceTable"
            >
              <el-option
                :label="item.id"
                :value="item.id"
                v-for="item in sourceTables"
                :key="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源字段" prop="sourceField">
            <el-select
              v-model="formData.sourceField"
              placeholder="请选择来源字段"
            >
              <el-option
                v-for="item in sourceFields"
                :key="item.name"
                :label="item.name"
                :value="item.name"
                :disabled="isFieldUsed(item.name)"
              >
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ item.comment }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="字段值类型">
        <el-select
          v-model="formData.fieldValueType"
          placeholder="请选择字段值类型"
        >
          <el-option label="无" value="" />
          <el-option label="字符串" value="string" />
          <el-option label="整数" value="int" />
          <el-option label="布尔值" value="boolean" />
          <el-option label="日期" value="date" />
          <el-option label="时间" value="time" />
          <el-option label="时间戳" value="timestamp" />
        </el-select>
      </el-form-item>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="层级的时间类型">
            <el-select
              v-model="formData.timeType"
              placeholder="请选择层级的时间类型"
            >
              <el-option label="无" value="0" />
              <el-option label="日" value="day" />
              <el-option label="周" value="week" />
              <el-option label="月" value="month" />
              <el-option label="学期" value="term" />
              <el-option label="年" value="year" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="隐藏维度值">
            <el-select
              v-model="formData.hiddenValues"
              placeholder="请选择隐藏维度值"
            >
              <el-option label="无" value="0" />
              <el-option label="从不" value="1" />
              <el-option label="隐藏空值" value="2" />
              <el-option label="隐藏ParentsNames" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <div style="display: flex; align-items: center; margin-bottom: 12px">
        <el-checkbox
          :true-label="1"
          :false-label="0"
          v-model="formData.isSelectedAll"
          label="支持全选"
          style="margin-right: 20px"
        />

        <span style="margin: 0 12px 0 20px">“全选”名称</span>
        <el-input
          v-model="formData.isSelectedAllName"
          style="width: 120px; margin-left: 12px"
          placeholder="请输入内容"
        ></el-input>
      </div>
      <div v-if="formData.levelCode">
        <el-checkbox
          :true-label="1"
          :false-label="0"
          v-model="formData.enableClustering"
          label="是否对维度值聚类"
          style="margin-bottom: 12px"
        />
      </div>
      <template v-if="formData.enableClustering == 1 && formData.levelCode">
        <div style="display: flex; align-items: center">
          <el-radio-group
            v-model="formData.cluFieldType"
            @change="changeCluFieldType"
          >
            <el-radio label="text">文本型</el-radio>
            <el-radio label="number">数值型</el-radio>
          </el-radio-group>
          <div class="btn" @click="openClusterDialog">添加聚类</div>
        </div>
        <el-tree
          style="margin-top: 10px"
          class="filter-tree"
          ref="tree"
          lazy
          accordion
          :load="loadNode"
          :data="dirAllData"
          node-key="id"
          @node-click="handleNodeClick"
          :render-after-expand="false"
          :props="defaultProps"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :show-expand-icon="false"
        >
          <span class="custom-tree-node node-text" slot-scope="{ node }">
            <div v-show="node.data.id == 'load-more-id'">
              <span class="tree-node-label">
                <el-link>{{ node.data.title }}</el-link>
              </span>
            </div>
            <div v-show="node.data.id != 'load-more-id'" class="tree-node-warp">
              <span class="tree-node-label">
                {{ node.label }}

                <template
                  v-if="formData.cluFieldType === 'number' || !node.data.leaf"
                >
                  : {{ node.data.dimValueCount }}项

                  <el-tooltip
                    v-if="
                      formData.cluFieldType === 'number' &&
                      clusterRepeatWarningList.some(
                        item =>
                          item.clusterCode === node.data.clusterCode &&
                          item.checkForDuplicates
                      )
                    "
                    class="item"
                    effect="dark"
                    content="疑似与其他聚类存在交集"
                    placement="top"
                  >
                    <i class="el-icon-warning" style="color: #e6a23c"></i>
                  </el-tooltip>
                </template>
              </span>
              <span v-if="node.level === 1" class="tree-node-actions">
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="handleEditCluster(node.data)"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  v-if="node.data.cluType != 2"
                  @click.stop="handleDeleteCluster(node.data)"
                >
                  删除
                </el-button>
              </span>
            </div>
          </span>
        </el-tree>
      </template>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>

    <el-dialog
      :visible.sync="dialogVisible1"
      :close-on-click-modal="false"
      title="聚类属性"
      width="400px"
      append-to-body
      custom-class="custom-dialog"
    >
      <el-form
        ref="clusterForm"
        :model="clusterFrom"
        label-width="100px"
        label-position="top"
        :rules="clusterRules"
      >
        <el-form-item label="聚类名称" prop="cluName">
          <el-input
            v-model="clusterFrom.cluName"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <div style="margin-bottom: 12px">
          <el-checkbox
            label="隐藏该聚类"
            :true-label="1"
            :false-label="0"
            v-model="clusterFrom.cluHidden"
          />
        </div>
        <el-form-item
          label="包含维度值:"
          v-if="formData.cluFieldType == 'text' && clusterFrom.cluType != '2'"
          prop="dimValueDimClusterList"
        >
          <div class="dim-value-container" @scroll="handleScroll">
            <template v-if="dimValueList.length > 0">
              <el-checkbox-group v-model="clusterFrom.dimValueDimClusterList">
                <el-checkbox
                  v-for="(item, index) in dimValueList"
                  :key="index"
                  :label="item.value_code"
                  :disabled="isValueUsed(item.value_code)"
                >
                  {{ item.value }}
                  <span
                    v-if="isValueUsed(item.value_code)"
                    style="color: #999; margin-left: 5px"
                  >
                    (已用于聚类: {{ getClusterName(item.value_code) }})
                  </span>
                </el-checkbox>
              </el-checkbox-group>
            </template>
            <div v-else class="empty-state">
              <i class="el-icon-warning-outline"></i>
              <p>暂无维度值数据</p>
            </div>
            <div v-if="isLoading" class="loading-text">加载中...</div>
          </div>
        </el-form-item>
        <el-form-item
          label="聚类条件:"
          v-if="formData.cluFieldType == 'number' && clusterFrom.cluType != '2'"
          prop="cluNumberConditions"
        >
          <div
            v-for="(cond, idx) in clusterFrom.cluNumberConditions"
            :key="idx"
            style="display: flex; align-items: center; margin-bottom: 8px"
          >
            <el-select
              v-model="cond.operator"
              placeholder="请选择"
              style="width: 80px; margin-right: 8px"
            >
              <el-option
                v-for="op in numberConditionOperators"
                :key="op.value"
                :label="op.label"
                :value="op.value"
              />
            </el-select>
            <el-input
              v-model="cond.value"
              placeholder="数值"
              style="width: 100px; margin-right: 8px"
            />
            <el-button
              icon="el-icon-minus"
              @click="removeNumberCondition(idx)"
              type="danger"
              size="mini"
              circle
            />
            <el-select
              v-if="idx < clusterFrom.cluNumberConditions.length - 1"
              v-model="cond.logic"
              placeholder="逻辑"
              style="width: 100px; margin: 0 8px"
            >
              <el-option
                v-for="logic in numberConditionLogics"
                :key="logic.value"
                :label="logic.label"
                :value="logic.value"
              />
            </el-select>
          </div>
          <el-button
            icon="el-icon-plus"
            @click="addNumberCondition"
            type="primary"
            size="mini"
          >
            添加条件
          </el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible1 = false">取消</el-button>
          <el-button type="primary" @click="createCluster">
            {{ clusterFrom.clusterCode ? "确定" : "创建聚类类型" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import Request from "@/service"

export default {
  props: {
    currentVersionItem: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dimensionLevels: {
      type: Array,
      default: () => {
        return []
      }
    },
    sourceTables: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogVisible1: false,
      formData: {
        levelName: "", // 名称
        tags: "",
        tagList: [], // 标签
        description: "", // 描述
        isVisible: 1, // 是否显示 0 否, 1 是
        isUnique: "", // 是否唯一 0 否, 1 是
        sourceTable: "", // 来源表
        sourceField: "", // 来源字段
        fieldValueType: "", // 字段值类型 1 文本, 2 数值
        timeType: "0", // 层级的时间类型: 0 无, day: 日, week: 周, month: 月, term: 学期, year:
        hiddenValues: "0", // 隐藏维度值
        isSelectedAll: "", // 是否全选
        isSelectedAllName: "", // "全选"名称
        enableClustering: "", // 是否对维度值聚类 : 0 否, 1 是
        cluFieldType: "text",
        dimClusterList: [],
        dimValueCount: "",
        levelCode: ""
      },
      rules: {
        levelName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        sourceTable: [
          { required: true, message: "来源表不能为空", trigger: "blur" }
        ],
        sourceField: [
          { required: true, message: "来源字段不能为空", trigger: "blur" }
        ]
      },
      clusterRules: {
        cluName: [
          { required: true, message: "聚类名称不能为空", trigger: "blur" }
        ],
        dimValueDimClusterList: [
          { required: true, message: "包含维度值不能为空", trigger: "blur" }
        ],
        cluNumberConditions: [
          { required: true, message: "聚类条件不能为空", trigger: "blur" }
        ]
      },
      // 聚类
      clusterFrom: {
        dimValueDimClusterList: [], // 初始化选中的值数组
        cluHidden: 0,
        cluName: "",
        cluType: "1",
        cluNumberConditions: []
      },
      // 修改为 Map 来存储值对应的聚类名称
      usedValueCodes: new Map(),
      dimValueList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      // Field
      sourceFields: [],
      treeData: [],
      defaultProps: {
        children: "children",
        label: "cluName",
        isLeaf: "leaf"
      },
      numberConditionOperators: [
        {
          label: "<",
          value: "conditionField <"
        },
        {
          label: ">",
          value: "conditionField >"
        },
        {
          label: "=",
          value: "conditionField ="
        },
        {
          label: "<=",
          value: "conditionField <="
        },
        {
          label: ">=",
          value: "conditionField >="
        },
        {
          label: "≠",
          value: "conditionField !="
        }
      ],
      numberConditionLogics: [
        {
          label: "且",
          value: "and"
        },
        {
          label: "或",
          value: "or"
        }
      ],
      dirAllData: [],
      // 添加当前编辑的聚类代码
      currentEditClusterCode: "",
      clusterRepeatWarningList: [] // 数值型是否重复
    }
  },
  created() {},
  methods: {
    changeSourceTable(val) {
      this.formData.sourceField = ""
      this.getFields(val)
    },
    // 创建聚类类型
    async createCluster() {
      this.$refs.clusterForm.validate(async valid => {
        if (valid) {
          let cluDimConditions = ""
          if (
            this.formData.cluFieldType === "number" &&
            this.clusterFrom.cluNumberConditions.length
          ) {
            cluDimConditions = this.clusterFrom.cluNumberConditions
              .map((item, idx) => {
                let str = ""
                // operator 已经带有字段名（如 conditionField >=），只取操作符部分
                str += item.operator + " " + item.value
                if (
                  item.logic &&
                  idx < this.clusterFrom.cluNumberConditions.length - 1
                ) {
                  str += " " + item.logic
                }
                return str
              })
              .join(" ")
          }

          try {
            await Request.api.paramPost("/dimLevel/saveOrUpdateDimCluster", {
              levelCode: this.formData.levelCode,
              cluName: this.clusterFrom.cluName,
              clusterCode: this.currentEditClusterCode,
              cluHidden: this.clusterFrom.cluHidden,
              cluFieldType: this.formData.cluFieldType,
              cluType: this.clusterFrom.cluType,
              dimValueDimClusterList:
                this.clusterFrom.dimValueDimClusterList.map(item => ({
                  valueCode: item
                })),
              cluDimConditions // 传递拼接后的字符串
            })

            // 关闭弹窗
            this.dialogVisible1 = false
            // 重置表单
            this.$refs.clusterForm.resetFields()
            // 重新请求聚类列表
            this.fetchClusterList()
            // 如果聚类类型为数值类型，则获取聚类重复预警
            if (this.formData.cluFieldType === "number") {
              this.getClusterRepeatWarning()
            }
            this.$message.success("创建成功")
          } catch (error) {
            console.error("创建聚类失败", error)
            this.$message.error("创建失败")
          }
        }
      })
    },
    async getFields(val) {
      const { data } = await Request.api.paramGet(
        "/bi/source/getTableAndColumn",
        {
          dbName: "ods",
          tableName: val
        }
      )
      this.sourceFields = data.columns
    },
    // 切换聚类类型
    changeCluFieldType(val) {
      this.$confirm("切换类型将会清空已创建的聚类，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.formData.cluFieldType = val
          this.clearClusterList()
        })
        .catch(() => {
          if (val === "number") {
            this.formData.cluFieldType = "text"
          }
          if (val === "text") {
            this.formData.cluFieldType = "number"
          }
        })
    },
    // 清空聚类列表
    async clearClusterList() {
      await Request.api.paramGet("/dimLevel/delDimClusterByLevelCode", {
        levelCode: this.formData.levelCode
      })
      this.fetchClusterList()
    },

    async getDimValues() {
      if (this.isLoading) return
      this.isLoading = true
      try {
        const { data } = await Request.api.paramPost(
          "/dimLevel/searchDimValuePageList",
          {
            // 请替换为实际接口
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            definitionCode: this.currentVersionItem.definitionCode,
            levelCode: this.formData.levelCode
          }
        )
        let list = data.list || []
        console.log(data, "/////////////////////////////")
        this.dimValueList =
          this.currentPage === 1 ? list : [...this.dimValueList, ...list]
        this.total = data.totalCount
        console.log(this.dimValueList, "this.dimValueList")
      } catch (error) {
        console.error("获取维度值列表失败", error)
      } finally {
        this.isLoading = false
      }
    },
    // 滚动事件处理
    handleScroll(event) {
      const { target } = event
      if (
        target.scrollTop + target.clientHeight >= target.scrollHeight - 20 &&
        !this.isLoading &&
        this.dimValueList.length < this.total
      ) {
        this.currentPage++
        this.getDimValues()
      }
    },
    openDialog(form) {
      this.formData = {
        ...this.formData,
        ...form,
        cluFieldType: form.cluFieldType || "text"
      }
      this.dialogVisible = true

      if (
        this.currentVersionItem.createType === 0 &&
        this.formData.sourceTable
      ) {
        this.getFields(this.formData.sourceTable)
      }
      // 如果聚类类型为数值类型，则获取聚类重复预警
      if (this.formData.cluFieldType === "number") {
        this.getClusterRepeatWarning()
      }
      // 如果有聚类类型 则获取聚类列表和
      if (this.formData.cluFieldType) {
        // this.fetchClusterList()
        this.getDimValues()
      }
    },
    // 聚类重复预警
    async getClusterRepeatWarning() {
      const { data } = await Request.api.paramGet(
        "/dimLevel/checkForDuplicates",
        {
          levelCode: this.formData.levelCode,
          cluFieldType: this.formData.cluFieldType
        }
      )
      this.clusterRepeatWarningList = data
    },

    // 添加打开聚类弹窗的方法
    openClusterDialog() {
      this.currentEditClusterCode = "" // 重置当前编辑的聚类代码
      // 重置表单
      this.clusterFrom = {
        dimValueDimClusterList: [],
        cluHidden: 0,
        cluName: "",
        cluNumberConditions: [],
        cluType: "1"
      }
      // 获取已使用的值
      this.updateUsedValueCodes()
      this.dialogVisible1 = true
    },
    // 更新已使用的值集合
    updateUsedValueCodes() {
      this.usedValueCodes.clear()
      // 遍历所有聚类（除了未归类）
      this.dirAllData.forEach(cluster => {
        if (Number(cluster.cluType) !== 2 && cluster.dimValueDimClusterList) {
          cluster.dimValueDimClusterList.forEach(item => {
            // 如果不是当前编辑的聚类，才添加到已使用集合中
            if (cluster.clusterCode !== this.currentEditClusterCode) {
              this.usedValueCodes.set(item.valueCode, cluster.cluName)
            }
          })
        }
      })
    },
    // 获取值所属的聚类名称
    getClusterName(valueCode) {
      return this.usedValueCodes.get(valueCode)
    },
    // 判断值是否已被使用
    isValueUsed(valueCode) {
      return this.usedValueCodes.has(valueCode)
    },
    closeDialog() {
      this.formData = this.$options.data().formData
      this.dirAllData = []
      this.dialogVisible = false

    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          // 提交逻辑
          await Request.api.paramPost("/dimLevel/saveOrUpdateDimLevel", {
            ...this.formData,
            level: this.dimensionLevels.length+1,
            tagList: this.formData.tags
              ? this.formData.tags.split(",").filter(tag => tag.trim())
              : [],
            definitionCode: this.currentVersionItem.definitionCode
          })
          this.$emit("refresh")
          this.closeDialog()
        }
      })
    },

    // 处理隐藏维度值输入
    handleHiddenValueInput(inputValue) {
      this.formData.hiddenValueList = inputValue
        .split(",")
        .filter(value => value.trim() !== "")
        .map(value => value.trim())
    },
    // 懒加载方法
    async loadNode(node, resolve) {
      console.log(node, "初始化tree")
      if (node.level === 0) {
        // 只在第一次加载根节点时获取数据
        if (!this.dirAllData.length) {
          const { data } = await Request.api.paramGet(
            "/dimLevel/searchClusterList",
            {
              levelCode: this.formData.levelCode,
              cluFieldType: this.formData.cluFieldType
            }
          )

          // 判断是否有未归类
          const isNotClass = data.findIndex(item => Number(item.cluType) === 2)
          console.log(isNotClass, "isNotClass")
          if (isNotClass === -1) {
            await Request.api.paramPost("/dimLevel/saveOrUpdateDimCluster", {
              levelCode: this.formData.levelCode,
              cluName: "未归类",
              cluType: 2,
              cluFieldType: this.formData.cluFieldType
            })
            // 重新获取更新后的数据
            const { data: updatedData } = await Request.api.paramGet(
              "/dimLevel/searchClusterList",
              {
                levelCode: this.formData.levelCode,
                cluFieldType: this.formData.cluFieldType
              }
            )
            // 如果是数值类型，设置所有节点的 leaf 为 true
            if (this.formData.cluFieldType === "number") {
              updatedData.forEach(item => {
                item.leaf = true
              })
            }
            this.dirAllData = updatedData
            return resolve(updatedData)
          }
          // 如果是数值类型，设置所有节点的 leaf 为 true
          if (this.formData.cluFieldType === "number") {
            data.forEach(item => {
              item.leaf = true
            })
          }
          this.dirAllData = data
        }
        return resolve(this.dirAllData)
      } else if (Number(node.data.cluType) === 2) {
        // 发送请求获取子节点数据，这里用模拟数据代替
        const { currentPage = 1, pageSize = 10 } = node.data.pagination || {}
        await Request.api
          .paramPost("/dimLevel/getUndefineClusterValueByPage", {
            levelCode: this.formData.levelCode,
            parentId: node.data.id,
            currentPage: currentPage,
            pageSize
          })
          .then(res => {
            const list = res.data.list.map(item => {
              return {
                id: item.value_code,
                cluName: item.value,
                leaf: true
              }
            })
            // 假设 fetchChildrenData 返回了分页信息和子节点数据
            // 更新节点的 children 和分页信息

            if (!node.data.children) {
              this.$set(node.data, "children", [])
            }
            if (res.data.currentPage === 1) {
              node.data.children = []
            }

            // 删除"加载更多"节点（如果有的话）
            const children = []
            node.childNodes.forEach(item => {
              children.push(item.data)
            })

            const index = children.findIndex(d => d.id === "load-more-id")
            console.log(index, "index")
            if (index) {
              node.childNodes.splice(index, 1)
              node.data.children.splice(index, 1)
            }

            // 检查是否需要添加"加载更多"节点
            if (
              Math.ceil(res.data.totalCount / res.data.pageSize) >
              res.data.currentPage
            ) {
              list.push({
                id: "load-more-id", // 唯一标识
                title: "加载更多",
                leaf: true,

                isLoadingMoreNode: true
              })
            }
            // 点击"加载更多"合并新返回的数据
            node.data.children.push(...list)

            node.data.pagination = {
              currentPage: res.data.currentPage,
              pageSize: res.data.pageSize,
              total: res.data.totalCount
            }

            node.loaded = true // 标记为已加载，如果需要的话

            // 如果resolve有内容就是懒加载走查询 否则走的是修改
            console.log("否则走的是修改", node.data.children)
            if (resolve) {
              return resolve(node.data.children)
            }
          })
          .catch(error => {
            console.error("加载子节点失败", error)
            // 处理错误情况
          })

        // 标记节点为正在加载
        node.data.loading = true
      } else {
        console.log(node, "///////////////////////")
        let childNodes = node.data.dimValueDimClusterList.map(item => {
          return {
            id: item.valueCode,
            clusterCode: item.clusterCode,
            cluName: item.value,
            leaf: true
          }
        })
        return resolve(childNodes)
      }
    },

    handleNodeClick(data, node) {
      // 根据分页和总条数，判断是否需要点击加载更多
      if (
        node.parent.data.pagination.currentPage <
        node.parent.data.pagination.total / node.parent.data.pagination.pageSize
      ) {
        node.parent.data.pagination.currentPage++
        this.loadNode(node.parent, () => {}) // 触发懒加载以获取更多数据，这里不需要 resolve
      }
    },
    addNumberCondition() {
      this.clusterFrom.cluNumberConditions.push({
        operator: "conditionField <",
        value: "",
        logic: "and"
      })
    },
    removeNumberCondition(idx) {
      this.clusterFrom.cluNumberConditions.splice(idx, 1)
    },
    async fetchClusterList() {
      console.log('打开弹窗')
      const { data } = await Request.api.paramGet(
        "/dimLevel/searchClusterList",
        {
          levelCode: this.formData.levelCode,
          cluFieldType: this.formData.cluFieldType
        }
      )

      // 判断是否有未归类
      const isNotClass = data.findIndex(item => Number(item.cluType) === 2)
      if (isNotClass === -1) {
        // 创建未归类聚类
        await Request.api.paramPost("/dimLevel/saveOrUpdateDimCluster", {
          levelCode: this.formData.levelCode,
          cluName: "未归类",
          cluType: 2,
          cluFieldType: this.formData.cluFieldType
        })
        // 重新获取更新后的数据
        const { data: updatedData } = await Request.api.paramGet(
          "/dimLevel/searchClusterList",
          {
            levelCode: this.formData.levelCode,
            cluFieldType: this.formData.cluFieldType
          }
        )
        this.dirAllData = updatedData
      } else {
        this.dirAllData = data
      }
      if (this.formData.cluFieldType === "number") {
        this.dirAllData.forEach(item => {
          item.leaf = true
        })
      }
    },
    // 编辑聚类
    handleEditCluster(data) {
      this.currentEditClusterCode = data.clusterCode
      this.clusterFrom = {
        cluName: data.cluName,
        cluHidden: data.cluHidden || 0,
        clusterCode: data.clusterCode,

        dimValueDimClusterList:
          data.dimValueDimClusterList?.map(item => item.valueCode) || [],
        cluNumberConditions: data.cluDimConditions
          ? this.parseConditions(data.cluDimConditions)
          : [],
        cluType: data.cluType
      }
      console.log(this.clusterFrom, " this.clusterFrom")
      this.updateUsedValueCodes()
      this.dialogVisible1 = true
    },

    // 删除聚类
    async handleDeleteCluster(data) {
      try {
        await this.$confirm("确认删除该聚类吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })

        await Request.api.paramGet("/dimLevel/delDimCluster", {
          clusterCode: data.clusterCode
        })

        this.$message.success("删除成功")
        this.fetchClusterList()
        if (this.formData.cluFieldType === "number") {
          this.getClusterRepeatWarning()
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除聚类失败", error)
          this.$message.error("删除失败")
        }
      }
    },

    // 解析条件字符串为对象数组
    parseConditions(conditionsStr) {
      if (!conditionsStr || typeof conditionsStr !== "string") return []

      const conditions = []
      const parts = conditionsStr.split(" ")

      for (let i = 0; i < parts.length; i += 4) {
        if (i + 2 < parts.length) {
          const condition = {
            operator: parts[i] + " " + parts[i + 1], // e.g. "conditionField <"
            value: parts[i + 2],
            logic: i + 3 < parts.length ? parts[i + 3] : null // e.g. "and" or "or"
          }
          conditions.push(condition)
        }
      }

      return conditions
    },
    // 检查字段是否已被使用
    isFieldUsed(fieldName) {
      // 如果是编辑模式且当前字段是当前层级的字段，则不禁用
      if (this.formData.levelCode && this.formData.sourceField === fieldName) {
        return false
      }
      // 检查字段是否在其他层级中使用
      return this.dimensionLevels.some(level => level.sourceField === fieldName)
    }
  }
}
</script>

<style lang="scss" scoped>
.btn {
  padding: 0 8px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #1563ff;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #1563ff;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  cursor: pointer;
  &:hover {
    background: #f4f7ff;
  }
  margin-left: 20px;
}
::v-deep .el-dialog__header {
  margin: 0;
  padding: 20px 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdee0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}
::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
}
::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

::v-deep .el-row {
  margin-bottom: 0;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0 0 8px 0;
  line-height: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  text-align: left;
  font-style: normal;
}

::v-deep .el-form-item__error {
  font-size: 10px;
}
.filter-tree {
  background: #f5f7fa;
  border-radius: 8px;
  height: 160px;
  overflow: auto;
}
.custom-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      cursor: pointer;
      color: #999;

      &:hover {
        color: #666;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding: 20px;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-select,
  .el-input {
    width: 100%;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 0px 20px 0;

    .el-button {
      margin-left: 10px;
    }
  }
}
.dim-value-container {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;

  .loading-text {
    text-align: center;
    padding: 10px;
    color: #909399;
  }
}
.tree-node-warp {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.tree-node-actions {
  display: inline-block;

  margin-left: 8px;
}

.tree-node-actions .el-button {
  padding: 0 4px;
}

.tree-node-actions .el-button + .el-button {
  margin-left: 4px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;

  i {
    font-size: 24px;
    margin-bottom: 8px;
  }

  p {
    margin: 0;
  }
}
</style>
