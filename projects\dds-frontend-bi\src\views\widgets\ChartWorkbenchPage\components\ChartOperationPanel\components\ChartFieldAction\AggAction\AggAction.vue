<template>
  <el-dropdown-item>
    <el-dropdown placement="right-start" style="width: 100%" @command="collect">
      <span class="el-dropdown-link inner-dropdown-menu">
        <span>
          <i class="el-icon-c-scale-to-original" />
          <span>汇总方式</span>
        </span>
        <i class="el-icon-arrow-right el-icon--right" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="sum" v-show="visualType == 'number'">
          总计
        </el-dropdown-item>
        <el-dropdown-item command="avg" v-show="visualType == 'number'">
          平均数
        </el-dropdown-item>
        <el-dropdown-item command="max" v-show="visualType == 'number'">
          最大值
        </el-dropdown-item>
        <el-dropdown-item command="min" v-show="visualType == 'number'">
          最小值
        </el-dropdown-item>
        <el-dropdown-item command="count">计数</el-dropdown-item>
        <el-dropdown-item command="COUNTDISTINCT"> 去重计数 </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </el-dropdown-item>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      visualType: 'number',
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
