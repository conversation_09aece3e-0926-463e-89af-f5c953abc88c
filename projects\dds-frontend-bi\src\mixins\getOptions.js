export default {
  // inject: ['form'],
  data() {
    return {
      // 部门选项
      YXoptions: [],
      // 身份选项
      SFoptions: [],
      // 年级选项
      NJoptions: [],
      // 商户选项
      SHoptions: [],
      // 消费类别
      XFLBoptions: [],
      // 区域选项
      QYoptions: [],
      // 时间段选项
      SJDoptions: [],

      // 时间选项
      dateOptions: [
        {
          label: "全部",
          value: null
        },
        {
          label: "按年",
          value: "year"
        },
        {
          label: "按月",
          value: "month"
        },
        {
          label: "按周",
          value: "week"
        },
        {
          label: "按日",
          value: "date"
        },
        {
          label: "按时间跨度",
          value: "datetimerange"
        }
      ],
      // 维度选项
      dimensionOption: [
        {
          label: "部门",
          value: "yx.name"
        },
        {
          label: "身份类型",
          value: "zhsf.name"
        },
        {
          label: "商户",
          value: "sh.shid"
        },
        {
          label: "消费类别",
          value: "xflb.id"
        },
        {
          label: "POS编号",
          value: "pos.id"
        },
        {
          label: "小时段",
          value: "sjd.xsjc"
        },
        {
          label: "区域",
          value: "qy.name"
        },
        {
          label: "时间",
          value: "rq.year"
        },
        {
          label: "学号",
          value: "zhxh.outid"
        },
        {
          label: "姓名",
          value: "zhxm.name"
        },
        {
          label: "年级",
          value: "nj.njid"
        }
      ]
    }
  },
  created() {
    this.getOptions()
  },
  mounted() {
    // const timer = setInterval(() => {
    //   this.getData(this.form)
    // }, 1000 * 60);
    // this.$once("hook:beforeDestroy", () => {
    //   clearInterval(timer);
    // });
  },
  watch: {},
  methods: {
    // 获取选择条件
    async getOptions() {
      const { data } = await this.$httpBi.api.querycondition()
      this.YXoptions = data.yx
      this.QYoptions = data.qy
      this.NJoptions = data.nj
      this.SFoptions = data.sflx
      this.SHoptions = data.sh
      this.XFLBoptions = data.xflb
      this.SJDoptions = data.sjd
    }
  }
}
