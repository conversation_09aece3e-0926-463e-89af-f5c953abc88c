<template>
  <div
    class="table-wrapper"
    :class="{ dargging: dargging, dragover: dragoverIng }"
    @drop.stop="$emit('drop', $event, source)"
    @dragover.stop="dragover"
    @dragleave.stop="dragleave"
  >
    <!-- 如果是根源于不渲染tip -->
    <div
      v-if="!root"
      class="table-link-tip"
      :style="{
        top: tipTop + 'px',
        left: level * 180 + (level - 1) * 90 + 'px',
      }"
    >
      <!-- 横线 -->
      <div
        class="table-link-line horizontal"
        :style="{
          width: showVertical ? '72px' : '90px',
          top: '14px',
        }"
      ></div>
      <!-- 竖线 -->
      <div
        v-if="showVertical"
        class="table-link-line vertical"
        :style="{
          height: tipHeight + 'px',
          bottom: '14px',
        }"
      ></div>
    </div>
    <!-- 表信息 -->
    <div
      class="table-info"
      :style="{
        top: infoTop + 'px',
        left: level * 270 + 'px',
      }"
    >
      拖拽左侧表拖拽至此添加关联表
    </div>
  </div>
</template>

<script>
export default {
  name: "table-wrapper",
  components: {},
  props: {
    source: {
      type: String,
    },
    level: {
      type: Number,
    },
    infoTop: {
      type: Number,
    },
    tipTop: {
      type: Number,
    },
    tipHeight: {
      type: Number,
    },
    showVertical: {
      type: Boolean,
      default: false,
    },
    dargging: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dragoverIng: false,
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    dragover(e) {
      e.preventDefault()
      this.dragoverIng = true
    },
    dragleave() {
      this.dragoverIng = false
    },
  },
}
</script>

<style scoped lang="scss">
.table-wrapper {
  opacity: 0;
  &.dargging {
    opacity: 0.5;
  }
  &.dragover {
    opacity: 1;
  }
  .table-link-tip {
    width: 90px;
    height: 28px;
    position: absolute;
    align-items: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
    .table-link-line {
      position: absolute;
      z-index: 2;
    }
    .table-link-line.vertical {
      width: 1px;
      left: 18px;
      background: linear-gradient(
        transparent 0%,
        transparent 50%,
        #2153d4 50%,
        #2153d4 100%
      );
      background-size: 1px 10px; /* 竖的虚线与横虚线刚好相反，第一个参数控制虚线的粗细，第二个参数控制虚线的间隙 */
      background-repeat: repeat-y;
      background-position: left;
    }
    .table-link-line.horizontal {
      right: 0;
      height: 1px;
      background: linear-gradient(
        to left,
        transparent 0%,
        transparent 50%,
        #2153d4 50%,
        #2153d4 100%
      );
      background-size: 10px 1px;
      background-repeat: repeat-x;
    }
  }
  .table-link-tip:hover .table-link-line {
    background: #2153d4;
  }
  .table-info {
    height: 28px;
    width: 180px;
    position: absolute;
    color: #000;
    cursor: pointer;
    padding: 1px 1px 1px 0;
    line-height: 28px;
    border-left: 2px solid #2153d4;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    border-right: 1px dashed #2153d4;
    border-top: 1px dashed #2153d4;
    border-bottom: 1px dashed #2153d4;
  }
}
</style>
