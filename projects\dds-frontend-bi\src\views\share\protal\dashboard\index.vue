<template>
  <div v-if="layout" ref="Dashboards" id="pdfCentent">
    <grid-layout
      :layout.sync="layout"
      :col-num="12"
      :row-height="30"
      :is-draggable="false"
      :is-resizable="false"
      :is-mirrored="false"
      :vertical-compact="true"
      :margin="[15, 15]"
      :use-css-transforms="true"
      @layout-updated="layoutUpdatedEvent"
    >
      <grid-item
        v-for="(item, index) in layout"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :key="item.id"
        drag-allow-from=".vue-draggable-handle"
        drag-ignore-from=".no-drag"
      >
        <DashboardItem
          :item="item"
          :token="token"
          :index="index"
          :only-view="onlyView"
          :is-authorized="isAuthorized"
          :dashboard-id="currentDashborad.id"
          @handleEditInfo="handleEditInfo"
          @handleEditWarning="handleEditWarning"
          @handleDrillDown="handleDrillDown"
          @setExceldata="setExceldata"
          v-on="$listeners"
          v-bind="$attrs"
        />
      </grid-item>
    </grid-layout>
    <el-dialog
      title="编辑widget"
      :visible.sync="editWidgetVisible"
      width="60%"
      @close="closeDialog"
      :before-close="handleClose"
    >
      <el-form label-width="96px" :model="currentWidget" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item label="图表别名">
              <el-input v-model="currentWidget.alias"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item
              label="时长"
              v-if="currentWidget.polling"
              prop="frequency"
            >
              <el-input-number
                v-model="currentWidget.frequency"
                :min="1"
                controls-position="right"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item label="数据刷新模式">
              <el-select v-model="currentWidget.polling" placeholder="请选择">
                <el-option label="手动刷新" :value="false"> </el-option>
                <el-option label="定时刷新" :value="true"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editWidgetVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSave">保 存 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import html2canvas from "html2canvas"
import { exportSheetExcel } from "@/utils/Export2Excel"
import dayjs from "dayjs"
import { GridLayout, GridItem } from "vue-grid-layout"
import DashboardItem from "./component/DashboardItem.vue"
import { mapState } from "vuex"
export default {
  components: {
    GridLayout,
    GridItem,
    DashboardItem,
  },
  props: {
    token: {
      type: String,
    },
    onlyView: {
      type: Boolean,
      default: false,
    },
    widgets: {
      type: Array,
      default: () => [],
    },
    currentDashborad: {
      type: Object,
      default: () => {},
    },
    allWidgets: Array,
    isAuthorized: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      excelData: [],
      Drillconfig: {},
      currentWidget: {},
      params: {
        alias: "",
        frequency: 60,
        polling: false,
      },
      rules: {
        frequency: [
          { required: true, message: "时长不能为空", trigger: "blur" },
        ],
      },
      editWidgetVisible: false,
      drillDownVisible: false,
      layout: [],
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
    }),
  },
  created() {},
  mounted() {},
  watch: {
    "currentDashborad.relations": {
      deep: true,
      handler() {
        this.getLayout()
      },
    },
  },

  methods: {
    getLayout() {
      if (this.currentDashborad.relations) {
        this.layout = this.currentDashborad.relations.map((item, i) => ({
          ...item,
          w: item.width,
          h: item.height,
          drillConfig: item.config,
          i,
          ...this.currentDashborad.widgets.find((el) => el.id === item.widgetId),
          relationsId: item.id,
        }))
        console.log(this.layout, "layout")
      } else {
        this.layout = null
      }
    },
    handleEditInfo(currentWidget) {
      this.editWidgetVisible = true
      this.currentWidget = { ...currentWidget }
    },

    handleDrillDown(currentWidget) {
      this.drillDownVisible = true
      this.currentWidget = { ...currentWidget }
      const config = this.currentDashborad.relations.find(
        (item) => item.widgetId === currentWidget.widgetId
      ).config
      this.Drillconfig = JSON.parse(config) || {
        target: "",
        xAxis: "",
        series: "",
        valueName: "",
        revealType: 1,
        isOpen: true,
        drillType: 1,
        url: "",
        relation: [ { zd: "", kzq: "" } ],
      }
    },
    handleSaveDrill(config) {
      this.drillDownVisible = false
      this.currentDashborad.relations = this.currentDashborad.relations.map(
        (item) => {
          if (this.currentWidget.relationsId === item.id) {
            return {
              ...item,
              config: JSON.stringify(config),
            }
          } else {
            return item
          }
        }
      )
    },
    handleEditSave() {
      const newLayout = [ ...this.layout ]
      const index = newLayout.findIndex(
        (item) => item.relationsId === this.currentWidget.relationsId
      )
      newLayout.splice(index, 1, this.currentWidget)
      this.layoutUpdatedEvent(newLayout, "edit")
    },
    async layoutUpdatedEvent(newLayout) {
      console.log("更改主题")
      if (this.currentDashborad.relations?.length) {
        const changedItems = this.currentDashborad.relations.map((item) => {
          const { x, y, w, h, alias, frequency, polling } =
            newLayout.find((l) => l.relationsId === item.id)
          return {
            ...item,
            x,
            y,
            drillConfig: item.config,
            width: w,
            height: h,
            alias,
            frequency,
            polling,
          }
        })

        // const { code } = await Request.dashboard.updMemDashboardWidget(
        //   changedItems
        // );
        this.$emit("update:currentDashborad", {
          ...this.currentDashborad,
          relations: changedItems,
        })
        // if (code == 200 && type == "edit") {
        //   this.editWidgetVisible = false;
        //   this.$emit("onReload");
        // }
      }
    },
    setExceldata(data, index, sheetName) {
      this.$set(this.excelData, index, { sheetName, sheetData: data })
    },
    exportExcel(filename) {
      exportSheetExcel({
        data: this.excelData,
        filename,
      })
    },
    exportPDF(filename) {
      this.ExportSavePdf(filename, this.$el)
    },
    exportImg(name) {
      const content = this.$refs.Dashboards
      console.log(content, "content")
      html2canvas(content, {
        useCORS: true, // 开启跨域配置，但和allowTaint不能共存
      }).then((canvas) => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = `${name}${dayjs().format("YYYY-MM-DD")}.jpg` // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    },
  },
}
</script>

<style scoped lang="scss">
.portal-main {
  width: 100%;
  height: calc(100vh - 60px);

  background-color: #f0f2f5;
  .portalBody {
    display: flex;
    height: 100%;
    .siderbar {
      width: 250px;
      border-right: 1px solid #ccc;
      overflow: auto;

      .portal-btn {
        padding: 16px;
        display: flex;
        justify-content: flex-end;
        border-bottom: 1px solid #ccc;
        i {
          cursor: pointer;
        }
      }
      .portalTreeNode {
        padding: 0 8px;
        height: 100%;
        box-sizing: border-box;
      }
    }
    .gridClass {
      flex: 1;
      .grid-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 0 20px;
        box-sizing: border-box;
      }
      .row-btn {
        margin: 0;
      }
    }
  }
}
.vue-grid-layout {
  touch-action: none;
}
::v-deep .el-tree {
  background-color: #f0f2f5;
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .buttonView {
    opacity: 0;
  }
}
.el-tree-node__content .custom-tree-node:hover .buttonView {
  opacity: 1;
}
</style>
