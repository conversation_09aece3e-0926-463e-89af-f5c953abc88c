<template>
  <div>
    <el-radio-group
      v-if="fieldType === 'category'"
      v-model="mode"
      @change="$emit('update:mode', mode)"
    >
      <el-radio-button label="value"> 值筛选 </el-radio-button>
      <el-radio-button label="conditional">条件筛选</el-radio-button>
    </el-radio-group>
    <el-transfer
      v-if="mode === 'value'"
      :titles="['值列表', '所选值']"
      v-model="target"
      :data="list"
      @change="transferChange"
    ></el-transfer>

    <ConditionalFilterForm
      v-if="mode === 'conditional'"
      :filter-tree="filterTree"
      :sql-type="sqlType"
      :filters-name="filtersName"
      @onAddRoot="initFilterTree"
      @onAddTreeNode="addTreeNode"
      @onUpdateTreeNode="updateTreeNode"
      @onDeleteTreeNode="deleteTreeNode"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCancelFilter">取 消</el-button>
      <el-button type="primary" @click="onSaveFilter"> 确 定 </el-button>
    </div>
  </div>
</template>

<script>
import { uuid } from "@/utils/index.js"
import ConditionalFilterForm from "./ConditionalFilterForm.vue"
export default {
  components: {
    ConditionalFilterForm,
  },
  props: {
    mode: {
      type: String,
      default: "value",
    },
    distinctColumnValues: {
      type: Array,
      default: () => [],
    },
    target: {
      type: Array,
      default: () => [],
    },
    fieldType: {
      type: String,
      default: "category",
    },
    sqlType: {
      type: String,
      default: "",
    },
    filtersName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      filterTree: {},
    }
  },
  computed: {
    list() {
      return this.distinctColumnValues.map((item) => ({
        label: item,
        key: item,
      }))
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    transferChange(value) {
      this.$emit("update:target", value)
    },
    onCancelFilter() {
      this.$emit("onCancelFilter")
    },
    getSqlModel(tree) {
      const result = tree.map((t) => {
        let children
        if (t && t.children && t.children.length) {
          children = this.getSqlModel(t.children)
        }
        if (t.type === "link") {
          const filterJson = {
            type: "relation",
            value: t.rel,
            children,
          }
          return filterJson
        } else {
          const filterJson = {
            name: this.filtersName,
            type: "filter",
            value: t.value,
            operator: t.operator,
            sqlType: this.sqlType,
          }
          return filterJson
        }
      })
      return result
    },
    onSaveFilter() {
      if (this.mode === "value") {
        const sql = this.target.map((key) => `'${key}'`).join(",")
        const sqlModel = []
        const filterItem = {
          name: this.filtersName,
          type: "filter",
          value: this.target.map((key) => `'${key}'`),
          operator: "in",
          sqlType: this.sqlType,
        }
        sqlModel.push(filterItem)
        if (sql) {
          this.$emit("onSaveFilter", {
            sqlModel,
            filterSource: this.target.slice(),
          })
        } else {
          this.$emit("onCancelFilter")
        }
      } else if (this.mode === "conditional") {
        if (Object.keys(this.filterTree).length > 0) {
         
          this.$emit("onSaveFilter", {
            filterSource: { ...this.filterTree },
            sqlModel: this.getSqlModel([ { ...this.filterTree } ]),
          })
        } else {
          this.$emit("onCancelFilter")

        }
      } 
    },
    initFilterTree() {
      this.filterTree = {
        id: uuid(8, 16),
        name: this.filtersName,
        operator: "",
        root: true,
        type: "node",
      }
    },
    addTreeNode(tree) {
      this.filterTree = tree
    },
    deleteTreeNode() {
      this.filterTree = {}
    },
    updateTreeNode(flattenTree) {
      // 将flattenTree转为树形结构
      Object.keys(flattenTree).forEach((key) => {
        const node = flattenTree[key]
        if (node.root) {
          this.filterTree = { ...node }
        }
      })
      console.log(this.filterTree, "-------------")
    },
  },
}
</script>

<style scoped lang="scss"></style>
