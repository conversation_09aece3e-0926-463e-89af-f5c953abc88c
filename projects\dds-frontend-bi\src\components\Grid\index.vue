<template>
  <div :style="style">
    <slot></slot>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    cols: {
      type: [Number, Object],
      default: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 })
    },
    collapsed: {
      type: Boolean,
      default: false
    },
    collapsedRows: {
      type: Number,
      default: 1
    },
    gap: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      breakPoint: "xl",
      hiddenIndex: -1
    }
  },
  computed: {
    gridCols() {
      if (typeof this.cols === "object") {
        return this.cols[this.breakPoint] !== undefined
          ? this.cols[this.breakPoint]
          : this.cols
      }

      return this.cols
    },
    gridGap() {
      if (typeof this.gap === "number") return `${this.gap}px`
      if (Array.isArray(this.gap)) return `${this.gap[1]}px ${this.gap[0]}px`
      return "unset"
    },
    style() {
      return {
        display: "grid",
        gridGap: this.gridGap,
        gridTemplateColumns: `repeat(${this.gridCols}, minmax(0, 1fr))`,
        slots: null
      }
    },
    gapValue() {
      return Array.isArray(this.gap) ? this.gap[0] : this.gap
    }
  },
  created() {},
  provide() {
    return {
      parentInstance: this
    }
  },
  beforeMount() {
    this.slots = this.$slots.default
  },
  mounted() {
    this.resize({ target: { innerWidth: window.innerWidth } })
    window.addEventListener("resize", this.resize)
    this.$on("hook:activated", () => {
      this.resize()
      window.addEventListener("resize", this.resize)
    })
    this.$on("hook:unmounted", () => {
      window.removeEventListener("resize", this.resize)
    })

    this.$on("hook:deactivated", () => {
      window.removeEventListener("resize", this.resize)
    })
    this.collapsed && this.findIndex()
  },
  watch: {
    breakPoint: {
      handler() {
        if (this.collapsed) {
          this.findIndex()
        }
      }
    },
    collapsed: {
      handler(value) {
        if (value) {
          return this.findIndex()
        }
        this.hiddenIndex = -1
      }
    }
  },
  methods: {
    findIndex() {
      let fields = []
      let suffix = null
      this.slots.forEach(slot => {
        // suffix
        if (
          typeof slot.componentOptions === "object" &&
          slot.componentOptions.tag === "GridItem" &&
          slot.componentOptions.propsData?.suffix !== undefined
        ) {
          suffix = slot
        } else {
          fields.push(slot)
        }
      })
      // 计算 suffix 所占用的列
      let suffixCols = 0
      if (suffix) {
        suffixCols =
          (suffix.componentOptions.propsData?.[this.breakPoint]?.span ??
            suffix.componentOptions.propsData?.span ??
            1) +
          (suffix.componentOptions.propsData?.[this.breakPoint]?.offset ??
            suffix.componentOptions.propsData?.offset ??
            0)
      }
      try {
        let find = false
        fields.reduce((prev = 0, current, index) => {
          prev +=
            (current.componentOptions.propsData?.[this.breakPoint]?.span ??
              current.componentOptions.propsData?.span ??
              1) +
            (current.componentOptions.propsData?.[this.breakPoint]?.offset ??
              current.componentOptions.propsData?.offset ??
              0)
          if (Number(prev) > this.collapsedRows * this.gridCols - suffixCols) {
            this.hiddenIndex = index
            find = true
            throw "find it"
          }
          return prev
        }, 0)
        if (!find) this.hiddenIndex = -1
      } catch (e) {
        // console.warn(e);
      }
    },
    resize(e) {
      let width = e.target.innerWidth
      switch (!!width) {
        case width < 768:
          this.breakPoint = "xs"
          break
        case width >= 768 && width < 992:
          this.breakPoint = "sm"
          break
        case width >= 992 && width < 1200:
          this.breakPoint = "md"
          break
        case width >= 1200 && width < 1920:
          this.breakPoint = "lg"
          break
        case width >= 1920:
          this.breakPoint = "xl"
          break
      }
      this.$emit("breakPointUpdate", this.breakPoint)
    }
  }
}
</script>

<style scoped lang="scss"></style>
