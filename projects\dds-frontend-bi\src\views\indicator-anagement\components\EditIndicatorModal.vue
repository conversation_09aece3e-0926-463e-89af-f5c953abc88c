<template>
  <div>
    <el-dialog
      title="编辑"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="700px"
      :before-close="handleClose"
    >
      <el-scrollbar>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="110px"
          class="form"
        >
          <el-form-item label="指标类型" prop="zblx">
            {{ form.zblx }}
          </el-form-item>
          <el-form-item label="指标名称" prop="zbmc">
            <el-input v-model="form.zbmc" clearable></el-input>
          </el-form-item>
          <template v-if="form.zblx == '原子指标'">
            <el-form-item label="指标原数据列">
              <el-input v-model="form.zbymc" disabled></el-input>
            </el-form-item>
            <el-form-item label="派生维度" prop="pswd">
              <div class="form-item">
                <span v-for="(item, index) in form.pswd" :key="index">
                  {{
                    index == form.pswd.length - 1 ? item.zdmc : item.zdmc + "；"
                  }}
                </span>
              </div>
            </el-form-item>
          </template>

          <template v-if="form.zblx == '派生指标'">
            <el-form-item label="基础指标" prop="atomid">
              <el-select
                v-model="form.atomid"
                placeholder="基础指标"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in yzzbList"
                  :key="index"
                  :label="item.zbmc"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="计算方式" prop="jsfs">
              <div style="display: flex">
                <el-select
                  v-model="form.jsfs"
                  placeholder="请选择计算方式"
                  :style="{ width: form.jsfs === 'sort' ? '76px' : '250px' }"
                  class="myselect"
                >
                  <el-option
                    v-for="(item, index) in jsfsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <template v-if="form.jsfs === 'sort'">
                  <el-select
                    style="margin-left: 10px"
                    v-model="form.sorttype"
                    placeholder="排序方式"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select
                    style="margin-left: 10px"
                    v-model="form.sortrange"
                    v-if="form.jsfs === 'sort'"
                    :style="{ width: '76px' }"
                    class="myselect"
                  >
                    <el-option
                      v-for="(item, index) in sortDefineList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '76px' }"
                    style="margin-left: 10px"
                    placeholder="数值"
                    v-if="form.sortrange === 'top'"
                    v-model.number="form.sortlimit"
                    clearable
                  ></el-input>
                </template>
              </div>
            </el-form-item>
            <el-form-item
              label="派生维度"
              :style="{ marginBottom: form.xsc.length ? 0 : 15 }"
            >
              <div v-if="!form.xsc.length">无</div>
              <div
                v-else
                class="form-item"
                v-for="(item, index) in form.xsc"
                :key="index"
              >
                <el-row type="flex" gutter="10">
                  <el-col>
                    <!-- :prop="'xsc.' + index + '.adid'"
                :rules="{
                  required: true,
                  message: '请选择派生维度',
                  trigger: 'blur'
                }" -->
                    <el-form-item>
                      <div style="display: flex">
                        <el-select
                          v-model="item.adid"
                          clearable
                          placeholder="请选择派生维度"
                          @change="change($event, index)"
                          style="width: 122px"
                          class="myselect"
                        >
                          <el-option
                            v-for="ele in currentDerive.pswd"
                            :disabled="form.xsc.some(e => e.adid === ele.id)"
                            :key="ele.id"
                            :label="ele.zdmc"
                            :value="ele.id"
                          ></el-option>
                        </el-select>
                        <el-select
                          v-if="
                            pswdOptionsMap[item.adid] &&
                            pswdOptionsMap[item.adid].length
                          "
                          v-model="item.wdzval"
                          multiple
                          collapse-tags
                          style="width: 122px; margin-left: 6px"
                          class="myselect"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="e in pswdOptionsMap[item.adid]"
                            :key="e.bm"
                            :label="e.name"
                            :value="e.bm"
                            :disabled="isDisabled(e.bm, item.wdzval)"
                          ></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-button
                      type="danger"
                      icon="el-icon-minus"
                      circle
                      v-if="form.xsc.length > 1 && index !== 0"
                      @click="removeDerive(item)"
                    ></el-button>
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      @click="addDerive"
                      circle
                    ></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>
            <el-form-item label="时间维度" prop="sjwd">
              <el-select v-model="form.sjwd" placeholder="请选择时间维度">
                <el-option
                  v-for="(item, index) in sjdwList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="计算周期" prop="jszq">
              <el-select v-model="form.jszq" placeholder="请选择计算周期">
                <el-option
                  v-for="(item, index) in jszqList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="精度" prop="jd">
              <el-row type="flex" gutter="10">
                <el-col>
                  <el-input
                    v-model="form.jd"
                    placeholder="仅支持输入整数，数值则代表小数点的位数"
                  ></el-input>
                </el-col>
                <el-col>
                  <el-checkbox
                    v-model="form.sswr"
                    true-label="1"
                    false-label="0"
                  >
                    四舍五入
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="归属域">
              <avue-input-tree
                default-expand-all
                v-model="form.sysjy"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择归属域"
                :dic="viewGroup"
              ></avue-input-tree>
            </el-form-item>
            <el-form-item label="设置阈值">
              <template #label>
                <span
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                  "
                >
                  设置阈值
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content=""
                    placement="top"
                  >
                    <div slot="content">
                      1. 如果不填写最小值，仅填写最大值，则表示小于等于最大值；
                      <br />
                      2. 如果不填写最大值，仅填写最小值，则表示大于等于最小值。
                    </div>
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                </span>
              </template>
              <div style="display: flex">
                <el-form-item prop="tvmin" style="margin-bottom: 0">
                  <el-input
                    v-model="form.tvmin"
                    placeholder="请输入最小值"
                    style="width: 122px"
                  ></el-input>
                </el-form-item>

                -
                <el-form-item style="margin-bottom: 0">
                  <el-input
                    v-model="form.tvmax"
                    style="width: 122px"
                    placeholder="请输入最大值"
                  ></el-input>
                </el-form-item>
              </div>
            </el-form-item>
            <el-form-item label="单位" prop="jldw">
              <div style="display: flex">
                <el-select
                  v-model="form.jldw"
                  placeholder="请选择单位"
                  :style="{ width: form.jldw === '其他' ? '122px' : '250px' }"
                  class="myselect"
                >
                  <el-option
                    v-for="(item, index) in dwList"
                    :key="index"
                    :label="item.name"
                    :value="item.bm"
                  ></el-option>
                </el-select>
                <el-input
                  v-if="form.jldw === '其他'"
                  v-model="form.diydw"
                  style="width: 122px; margin-left: 6px"
                  placeholder="请输入单位"
                ></el-input>
              </div>
            </el-form-item>
          </template>
          <el-form-item label="所属指标域">
            <avue-input-tree
              default-expand-all
              v-model="form.sysjy"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择内容"
              :dic="viewGroup"
            ></avue-input-tree>
          </el-form-item>
          <el-form-item label="描述" prop="ms">
            <el-input v-model="form.ms" clearable></el-input>
          </el-form-item>
          <el-form-item label="标签" prop="bq">
            <el-select
              v-model="form.bq"
              filterable
              multiple
              remote
              allow-create
              default-first-option
              @change="changeTag"
              :multiple-limit="10"
              @remove-tag="removeTag"
              :remote-method="remoteMethod"
              placeholder="请创建或者选择标签"
            >
              <el-option
                v-for="item in formatLabels"
                :key="item.bqmc"
                :label="item.bqmc"
                :value="item.bqmc"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm('form')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import options from "../mixins/options"

export default {
  components: {},
  mixins: [options],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    },
    yzzbList: {
      type: Array,
      default: () => []
    },
    viewGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var changeZdmc = async (rule, value, callback) => {
      if (this.tempName === value) {
        callback()
      }
      const { data } =
        await this.$httpBi.indicatorAnagement.checkIndicatorRepeat({
          zbmc: value,
          indCode:""
        })
      if (data) {
        callback(new Error("字段名称已存在,请重新输入"))
      } else {
        callback()
      }
    }
    const changeMin = (rule, value, callback) => {
      console.log(value > this.form.tvmax)
      console.log(value, this.form.tvmax)
      if (this.form.tvmax === "") {
        callback()
      } else if (Number(value) > Number(this.form.tvmax)) {
        callback(new Error("最小值不能大于最大值"))
      } else {
        callback()
      }
    }
    const changeMax = (rule, value, callback) => {
      if (this.form.tvmin === "") {
        callback()
      } else if (Number(value) < Number(this.form.tvmin)) {
        callback(new Error("最大值不能小于最小值"))
      } else {
        callback()
      }
    }
    const changeDw = (rule, value, callback) => {
      if (this.form.jldw === "") {
        callback(new Error("请选择单位"))
      } else if (this.form.jldw === "其他" && this.form.diydw === "") {
        callback(new Error("请自定义单位"))
      } else {
        callback()
      }
    }
    return {
      rules: {
        tempName: "",
        zbmc: [
          {
            required: true,
            message: "请填写指标名称",
            trigger: "blur"
          },
          { validator: changeZdmc, trigger: "blur", required: true }
        ],
        atomid: { required: true, message: "请选择原子指标", trigger: "blur" },
        jsfs: { required: true, message: "请选择计算方式", trigger: "blur" },
        jszq: { required: true, message: "请选择计算周期", trigger: "blur" },
        sjwd: { required: true, message: "请选择时间维度", trigger: "blur" },
        jd: { required: true, message: "请输入精度", trigger: "blur" },
        tvmin: { validator: changeMin, trigger: "blur" },
        tvmax: { validator: changeMax, trigger: "blur" },
        jldw: {
          required: true,
          validator: changeDw,
          trigger: "change"
        }
      },
      pswdOptionsMap: {}
    }
  },
  computed: {
    // 当前派生维度
    currentDerive() {
      if (!this.yzzbList.length) return []
      return (
        this.yzzbList.filter(item => item.id === this.form.atomid)[0] || {
          pswd: []
        }
      )
    },
    formatLabels() {
      return this.labels.filter(
        item => item.bqmc.includes(this.newTag) || !this.newTag
      )
    }
  },
  created() {
    this.tempName = this.form.zbmc
    if (this.form.xsc) {
      this.form.xsc.forEach(item => {
        this.getPsOption(item.adid)
      })
    }
  },
  mounted() {},
  watch: {},
  methods: {
    submitForm(formName) {
      let params = this.form
      var Api = null
      if (this.form.zblx === "原子指标") {
        Api = this.$httpBi.indicatorAnagement.editYz
      }
      if (this.form.zblx === "派生指标") {
        Api = this.$httpBi.indicatorAnagement.editPs
        params = {
          ...params,
          xsc: this.form.xsc.filter(
            (item, index) => item.adid !== "" || index === 0
          ),
          jldw: this.form.jldw === "其他" ? this.form.diydw : this.form.jldw,
          diydw: this.form.jldw === "其他" ? 1 : 0
        }
      }
      if (this.form.zblx === "复合指标") {
        Api = this.$httpBi.indicatorAnagement.editYz
      }
      if (this.form.zblx === "SQL指标") {
        Api = this.$httpBi.indicatorAnagement.editYz
      }
      this.$refs[formName].validate(async valid => {
        if (valid) {
          const { code } = await Api(params)
          if (code === 200) {
            this.$message({
              message: "修改成功",
              type: "success"
            })
            this.$emit("update:visible", false)
            this.$emit("refresh")
          }
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    handleClose() {
      this.$emit("update:visible", false)
    },

    // 选择派生维度
    async change(id, index) {
      this.getPsOption(id)
      const item = { ...this.currentDerive.pswd.find(item => item.id === id) }
      delete item.id
      this.$set(this.form.xsc, index, {
        ...item,
        adid: id,
        wdzd: this.currentDerive.zddm,
        zdmc: this.currentDerive.zbmc,
        wdzval: [""]
      })
      console.log(this.form.xsc)
    },
    // 获取派生维度
    async getPsOption(id) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionValueById({
          id: id,
          lxbm: "yz"
        })
      // this.pswdOptionsMap[id] = data
      this.$set(this.pswdOptionsMap, id, data)
    },

    // 添加派生维度
    addDerive() {
      if (this.form.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: "暂无派生维度",
          type: "warning"
        })
      }
      this.form.xsc.push({
        adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: "", // getAtomIndicatorList接口返回的zddm字段
        zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
        wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive(item) {
      var index = this.form.xsc.indexOf(item)
      if (index !== -1 && this.form.xsc.length > 1) {
        this.form.xsc.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-scrollbar__wrap {
  max-height: 50vh; // 最大高度
  overflow-x: hidden; // 隐藏横向滚动栏
}
.el-form-item__content {
  display: flex;
}
.form {
  width: 520px;
  margin: 0 auto;
}

::v-deep .el-input--small {
  width: 250px;
}
::v-deep .myselect {
  .el-input--small {
    width: 100%;
  }
}

.el-form-item__content {
  display: flex;
}

::v-deep .el-row {
  margin-bottom: 0px;
}
</style>
