<template>
  <dt-single-page-view
    class="editPage"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <dt-header>
      <el-page-header @back="goBack" :content="查看数据源"> </el-page-header>
    </dt-header>

    <el-container style="height: 800px">
      <el-aside width="150">
        <span v-for="item in tables" :key="item.name" style="margin: 10px">
          <!--          <el-link :underline="false" type="primary"
                             @click="getTableData(item.name)"
                    >{{item.name}}
                    </el-link>-->
          <el-button
            style="border: none; width: 200px; text-align: left"
            :class="{ active: item.name == link }"
            icon="el-icon-s-grid"
            @click="getTableData(item.name, item.comment)"
          >{{ item.name }} </el-button
          ><br />
        </span>
      </el-aside>
      <el-main style="padding-top: 0px">
        {{ currentTb }} {{ currentTbName }}
        <el-row :gutter="20">
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              @click="dialogFormVisible = true"
            >
              新增字段
            </el-button
            >
            <el-button
              type="primary"
              @click="dialogVisible = true"
            >
              修改/删除字段
            </el-button
            >
            <el-button
              type="primary"
              @click="dataItemVisible = true"
            >
              新增数据项
            </el-button
            >
            <el-button
              type="primary"
              @click="delDataItem"
              :disabled="!multipleSelection.length"
            >
              删除数据项
            </el-button
            >
          </el-col>
        </el-row>
        <div>
          <el-table
            v-loading="loading"
            @selection-change="handleSelectionChange"
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              v-for="(item, index) in tbCol"
              :key="index"
              :prop="item.name"
              :label="item.name"
            >
            </el-table-column>

            <el-table-column
              v-for="(item, index) in newTbCol"
              :key="index"
              :prop="item.name"
              :label="item.name"
              #default="{ row }"
            >
              <el-input v-model="row.name"></el-input>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total,sizes,prev, pager, next"
            :total="total"
            style="float: right"
            :page-sizes="[5, 10, 20, 50]"
            :page-size="pageSize"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      :title="form.id ? '修改字段' : '新增字段'"
      :visible.sync="dialogFormVisible"
      width="576px"
    >
      <el-form :model="form" label-width="120px">
        <el-form-item label="字段名">
          <el-input v-model="form.name" placeholder="字段名"></el-input>
        </el-form-item>
        <el-form-item label="中文别名">
          <el-input v-model="form.comment" placeholder="中文别名"></el-input>
        </el-form-item>
        <el-form-item label="数据类型">
          <el-select v-model="form.type" placeholder="请选择数据类型">
            <el-option label="int" value="INT"></el-option>
            <el-option label="char" value="CHAR"></el-option>
            <el-option label="float" value="FLOAT"></el-option>
            <el-option label="varchar" value="VARCHAR"></el-option>
            <el-option label="datetime" value="DATETIME"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务说明">
          <el-input v-model="form.explain" placeholder="业务说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改/删除字段" :visible.sync="dialogVisible">
      <el-table :data="tbCol" style="width: 100%" height="400">
        <el-table-column prop="name" label="字段"> </el-table-column>
        <el-table-column prop="comment" label="中文别名"> </el-table-column>
        <el-table-column prop="type" label="数据类型"> </el-table-column>
        <el-table-column prop="explain" label="业务说明"> </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="editField(row)"
            >
              修改
            </el-button
            >
            <el-button
              type="text"
              size="small"
              @click="delField"
            >
              删除
            </el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="新增数据项" :visible.sync="dataItemVisible" width="576px">
      <el-form :model="form" label-width="120px">
        <div style="display: flex; margin-bottom: 20px">
          <span
            style="width: 120px; text-align: right; padding-right: 12px"
          >字段名</span
          >
          <span style="flex: 1; text-align: center">值</span>
        </div>
        <el-form-item :label="item.name" v-for="item in tbCol" :key="item.name">
          <el-input v-model="item.value" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dataItemVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="dataItemVisible = false"
        >
          保存
        </el-button
        >
      </div>
    </el-dialog>
  </dt-single-page-view>
</template>

<script>
import Request from "@/service"

export default {
  name: "info-page",
  props: {
    id: String,
  },
  data() {
    return {
      dataItemVisible: false,
      dialogVisible: false,
      dialogFormVisible: false,
      form: {
        name: "",
        comment: "",
        type: "",
        explain: "",
        id: null,
      },
      multipleSelection: [],
      show: true,
      loading: true,
      sourceInfo: {},
      currentTb: "",
      currentTbName: "",
      tables: [],
      tbCol: [],
      newTbCol: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      link: "", // 当前选中的分类
    }
  },
  created() {
    this.getSourceInfo()
  },
  methods: {
    goBack() {
      this.$emit("click", { com: "list", opt: "back", data: { id: "" } })
    },
    getSourceInfo() {
      this.loading = true
      Request.source
        .getOne({ id: this.id })
        .then((res) => {
          let config = JSON.parse(res.data.config)
          this.sourceInfo = {
            id: res.data.id,
            name: res.data.name,
            description: res.data.description,
            username: config.username,
            password: config.password,
            url: config.url,
          }
          this.testConnet(this.sourceInfo)
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    testConnet(sourceInfo) {
      let test = {
        username: sourceInfo.username,
        password: sourceInfo.password,
        url: sourceInfo.url,
      }
      Request.source
        .test(test)
        .then((res) => {
          if (res.data) {
            this.getDbTables(sourceInfo.id, sourceInfo.name)
          } else {
            this.$message.error("数据库连接失败")
            this.goBack()
          }
        })
        .catch(() => {})
    },
    getDbTables(id, name) {
      Request.view
        .getTables({ id: id, dbName: name })
        .then((res) => {
          this.tables = res.data.tables
          this.loading = false
        })
        .catch(() => {})
    },
    /*    getTableInfo(name){
          Request.view
              .getColumns({
                id: this.sourceInfo.id,
                dbName: this.sourceInfo.name,
                tableName: name,
              })
              .then((res) => {
                this.tbCol = res.data.columns
                this.getTableData(name);
              })
              .catch((error) => {});
        },*/
    getTableData(name, remark) {
      this.newTbCol = []
      this.currentTb = name
      this.currentTbName = remark
      this.link = name
      this.loading = true
      Request.view
        .getTableData({
          sourceId: this.sourceInfo.id,
          tbName: name,
          pageNo: this.currentPage,
          pageSize: this.pageSize,
        })
        .then((res) => {
          this.tbCol = res.data.columns
          this.tableData = res.data.resultList
          this.total = res.data.totalCount
          this.loading = false
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData(this.currentTb)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableData(this.currentTb)
    },

    handleSave() {
      this.newTbCol.push(this.form)
      this.dialogFormVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    editField(row) {
      this.form = row
      this.form.id = Math.random()
      console.log(this.form)
      this.dialogFormVisible = true
    },
    delField() {
      this.$confirm("确定要删除该字段吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          })
        })
    },
    delDataItem() {
      this.$confirm("此操作将永久删除该数据项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          })
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.my_with {
  width: 500px;
}

.active {
  background: lightblue;
}
</style>
