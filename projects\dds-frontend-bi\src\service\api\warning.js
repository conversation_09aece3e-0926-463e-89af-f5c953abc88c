import service from "../base"
import config from "../config"

/**
 * 预警
 */
export default {
  getWarningByWidget(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/getWarningByWidget",
      method: "get",
      params
    })
  },
  getById(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/getById",
      method: "get",
      params
    })
  },
  addWarning(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/addWarning",
      method: "post",
      data
    })
  },
  delWarning(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/delWarning",
      method: "get",
      params
    })
  },
  getWarningLogsById(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/getWarningLogsById",
      method: "post",
      data
    })
  },
  updateWarning(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/updateWarning",
      method: "post",
      data
    })
  },
  getPage(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/getPage",
      method: "post",
      data
    })
  },
  updStatusWarning(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/warningSet/updStatus",
      method: "post",
      data
    })
  }
}
