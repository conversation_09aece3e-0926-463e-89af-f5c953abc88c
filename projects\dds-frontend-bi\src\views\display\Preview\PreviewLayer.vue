<template>
  <div
    class="preview-layer"
    :style="style"
    @mouseover="hover = true"
    @mouseleave="hover = false"
  >
    <PreviewLayerTool v-if="hover && layer.type === 1" :layer="layer" />
    <!-- @handleClick="handleClick(layer)" -->

    <LayerCore
      :layer="layer"
      v-on="$listeners"
      @handleChartClick="handleClick"
    />
    <el-dialog
      title="下钻"
      v-if="drillVisible"
      append-to-body
      :visible.sync="drillVisible"
      width="60%"
      :before-close="handleClose"
    >
      <components
        :is="drillCon"
        :drillconfig="Drillconfig"
        :chart-params="chartParams"
        :selected-chart-id="chartData.selectedChartId"
      />
    </el-dialog>
  </div>
</template>

<script>
import DrillWidget from "@/views/drill-down/drill-widget.vue"
import DrillPortal from "@/views/drill-down/drill-portal.vue"
import LayerCore from "../DisplayWorkbench/LayerComponents/LayerBox/LayerCore.vue"
import PreviewLayerTool from "./PreviewLayerTool.vue"
export default {
  components: {
    LayerCore,
    PreviewLayerTool,
    DrillWidget,
    DrillPortal
  },
  props: {
    layer: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      hover: false,
      drillCon: null, // 下钻组件名称
      chartParams: "",
      drillVisible: "",
      Drillconfig: {},
      chartData: {}
    }
  },
  computed: {
    style() {
      const { width, height, left, top, index } = this.layer.params
      return {
        position: "absolute",
        width: `${width}px`,
        height: `${height}px`,
        left: `${left}px`,
        top: `${top}px`,
        zIndex: `${index}`
      }
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleClick(params, widgetProps) {
      if (!this.layer.params.isOpen) return
      this.chartData = widgetProps || {
        selectedChartId: null
      }
      console.log(params, "params")
      console.log(widgetProps, "widgetProps")
      console.log(this.layer, "layer")
      // const { drillType,openType } =this.layer.params
      const drillConfig = {
        target: this.layer.params.target,
        xAxis: this.layer.params.xAxis,
        series: "",
        valueName: "",
        revealType: this.layer.params.revealType,
        isOpen: this.layer.params.isOpen,
        drillType: this.layer.params.drillType,
        url: this.layer.params.url,
        relation: this.layer.params.relation
      }
      console.log(drillConfig, "drillConfig")

      // 针对表格单元格点击处理
      // if (this.chartData.selectedChartId === 1) {
      //   const fieldArr = drillConfig.relation.filter(
      //     (item) => item.zd === params.field
      //   )
      //   if (!fieldArr.length) return
      // }
      // 外链打开
      // if (.drillType === 3) {
      //   let url = drillConfig.url + "?"
      //   if (this.chartData.selectedChartId !== 1) {
      //     if (drillConfig.xAxis !== "") {
      //       url += drillConfig.xAxis + "=" + params.name
      //     }
      //     if (drillConfig.series !== "") {
      //       url += drillConfig.series + "=" + params.seriesName
      //     }
      //   } else {
      //     drillConfig.relation.forEach(({ zd, kzq }) => {
      //       url += kzq + "=" + params[zd]
      //     })
      //   }
      //   window.open(url, "_blank")
      //   return
      // }
      // 当前页窗口打开
      if (drillConfig.revealType === 1) {
        if (!drillConfig.target || !drillConfig.isOpen) return

        if (drillConfig.drillType === 1) {
          this.drillCon = "DrillWidget"
        }
        if (drillConfig.drillType === 2) {
          this.drillCon = "DrillPortal"
        }
        this.chartParams = params
        this.drillVisible = true
        this.Drillconfig = drillConfig
      }
      // 新窗口打开
      if (drillConfig.revealType === 2) {
        let obj = {}
        if (widgetProps) {
          if (widgetProps.selectedChartId !== 1) {
            obj = {
              selectedChartId: widgetProps.selectedChartId,
              Drillconfig: drillConfig,
              chartParams: { name: params.name, seriesName: params.seriesName }
            }
          } else if (widgetProps.selectedChartId === 1) {
            obj = {
              selectedChartId: widgetProps.selectedChartId,
              Drillconfig: drillConfig,
              chartParams: params
            }
          }
        } else {
          obj = {
            selectedChartId: 0,
            Drillconfig: drillConfig,
            chartParams: {}
          }
        }
        const strConfig = JSON.stringify(obj)
        let routeUrl = null
        // 图表下钻
        if (drillConfig.drillType === 1) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillWidget?isFullPage=true&config=${strConfig}`
          })
          window.open(routeUrl.href, "_blank")
        }
        // 看板下钻
        if (drillConfig.drillType === 2) {
          routeUrl = this.$router.resolve({
            path: `/ddsBi/drillPortal?isFullPage=true&config=${strConfig}`
          })
          window.open(routeUrl.href, "_blank")
        }
        // 链接
        if (drillConfig.drillType === 3) {
          routeUrl = window.open(drillConfig.url, "_blank")
        }
      }
      // if (!params.onEvent) return
      // if (params.drillType === "url" && params.url) {
      //   window.open(params.url, "_black")
      // }
    }
  }
}
</script>

<style scoped lang="scss"></style>
