<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="dialog-container mx-auto max-w-4xl p-6">
      <!-- 对话内容区域 -->
      <div class="chat-messages space-y-6">
        <!-- 用户问题 -->
        <div class="flex justify-end">
          <div class="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-lg">
            <p>我们院系今年的科研经费总额是多少?</p>
          </div>
        </div>

        <!-- AI回答 -->
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 rounded-full overflow-hidden">
            <img :src="aiAvatar" alt="AI助手" class="w-full h-full object-cover"/>
          </div>
          <div class="bg-white rounded-lg shadow-sm p-4 max-w-3xl">
            <div class="flex items-center text-gray-600 text-sm mb-2">
              <span>智能助手</span>
              <span class="mx-2">·</span>
              <span>用时0.2秒</span>
            </div>
            <div class="mb-4">
              <p class="text-gray-800 mb-2">以下是贵院今年的科研经费总额情况:</p>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-gray-600 text-sm">
                  来源: 科研统计数据, 2023年
                </div>
                <div class="text-4xl font-bold text-blue-500 my-3">
                  281.51 <span class="text-lg">亿元</span>
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between text-sm">
              <div class="text-gray-500">
                相关链接: 
                <a href="#" class="text-blue-500 hover:underline">院系科研经费总览</a>
              </div>
              <div class="flex space-x-3">
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="fas fa-download"></i>
                </button>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="fas fa-copy"></i>
                </button>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="fas fa-share"></i>
                </button>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="far fa-star"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二个用户问题 -->
        <div class="flex justify-end">
          <div class="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-lg">
            <p>其中科研项目类型占比怎么分布?</p>
          </div>
        </div>

        <!-- 第二个AI回答 -->
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 rounded-full overflow-hidden">
            <img :src="aiAvatar" alt="AI助手" class="w-full h-full object-cover"/>
          </div>
          <div class="bg-white rounded-lg shadow-sm p-4 max-w-3xl">
            <div class="flex items-center text-gray-600 text-sm mb-2">
              <span>智能助手</span>
              <span class="mx-2">·</span>
              <span>用时0.3秒</span>
            </div>
            <div class="mb-4">
              <p class="text-gray-800 mb-2">以下是科研经费在不同项目类型中的分布情况:</p>
              <div id="pieChart" class="w-full h-64"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部输入区域 -->
      <div class="mt-6">
        <div class="bg-white rounded-lg shadow-sm p-4">
          <div class="flex items-center">
            <input
              type="text"
              placeholder="向智能问数发起数据问题"
              class="flex-1 outline-none text-gray-700"
              v-model="question"
            />
            <button class="ml-3 text-blue-500">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      question: '',
      aiAvatar: 'https://ai-public.mastergo.com/ai/img_res/fba0135b26317c030f3f6ec2b665a3bc.jpg',
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const chartDom = document.getElementById('pieChart')
      this.chart = echarts.init(chartDom)
      
      const option = {
        animation: false,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}%'
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}\n{c}%'
            },
            labelLine: {
              show: true
            },
            data: [
              { value: 25.1, name: '国家重点项目' },
              { value: 30.4, name: '省部级项目' },
              { value: 24.2, name: '横向合作项目' },
              { value: 20.3, name: '基础研究项目' }
            ]
          }
        ]
      }

      this.chart.setOption(option)
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  }
}
</script>

<style scoped>
.dialog-container {
  width: 100%;
  max-width: 1440px;
  min-height: 1024px;
}

input {
  border: none;
  padding: 0.5rem;
  width: 100%;
}

input:focus {
  outline: none;
}

.chat-messages {
  min-height: 600px;
}
</style>

