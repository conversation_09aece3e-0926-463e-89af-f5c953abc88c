<template>
  <div class="tableContainer" ref="container">
    <!-- :scroll-y="{enabled: false}" -->

    <vxe-table
      v-if="isRouterAlive"
      :data="data"
      class="mytable-style"
      :span-method="rowspanMethod"
      :loading="loading"
      :height="tableHeight"
      @cell-click="handleCell"
      :cell-class-name="cellClassName"
      :border="widgetProps.chartStyles.table.bordered ? 'full' : 'none'"
      :stripe="widgetProps.chartStyles.table.isStripe"
      :size="widgetProps.chartStyles.table.size"
    >
      <vxe-column
        v-for="item in tableColumn"
        :key="item.key"
        :field="item.field"
        :title="item.title"
        :min-width="item.minWidth"
        :fixed="item.fixed"
        align="center"
      />
    </vxe-table>

    <vxe-pager
      v-if="widgetProps.chartStyles.table.withPaging"
      :loading="loading"
      :current-page="widgetProps.pagination.pageNo"
      :page-size="widgetProps.pagination.pageSize"
      :total="widgetProps.pagination.totalCount"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'Total']"
      @page-change="handlePageChange"
    ></vxe-pager>
  </div>
</template>
<script>
import { getFormattedValue } from "../../Config/Format/util"
import { widgetParamsFormat } from "../../util"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"

import Request from "@/service"
export default {
  props: {
    widgetProps: {
      type: Object
    },
    viewId: {
      type: Number
    }
  },
  data() {
    return {
      isRouterAlive: true,
      loading: false,
      tableHeight: 0,
      selectRow: null,
      selectColumn: null
    }
  },
  computed: {
    tableColumn() {
      const { cols, metrics } = this.widgetProps
      const { leftFixedColumns, rightFixedColumns, withNoAggregators } =
        this.widgetProps.chartStyles.table
      return [...cols, ...metrics].map((item, index) => {
        const column = {
          key: index,
          field:
            item.type === "value" && !withNoAggregators
              ? `${item.agg}(${item.displayName})`
              : item.displayName,
          title: item.field.alias ? item.field.alias : item.displayName,
          minWidth: 100,
          fixed: null
        }
        console.log(leftFixedColumns, "leftFixedColumns")
        console.log(rightFixedColumns, "rightFixedColumns")

        if (leftFixedColumns.includes(item.displayName)) {
          column.fixed = "left"
        }
        if (rightFixedColumns.includes(item.displayName)) {
          column.fixed = "right"
        }
        return column
      })
    },
    data() {
      const { metrics, data } = this.widgetProps
      const { withNoAggregators } = this.widgetProps.chartStyles.table
      let resetData = data
      metrics.forEach(item => {
        const foramtname =
          item.type === "value" && !withNoAggregators
            ? `${item.agg}(${item.displayName})`
            : item.displayName
        resetData = resetData.map(e => {
          return {
            ...e,
            [item.displayName]: getFormattedValue(e[foramtname], item.format)
          }
        })
      })
      return resetData
    }
  },
  watch: {
    widgetProps: {
      deep: true,
      handler() {
        this.reload()
      }
    }
  },

  created() {},
  mounted() {
    var elementResizeDetectorMaker = require("element-resize-detector") // 导入
    var erd = elementResizeDetectorMaker()
    erd.listenTo(this.$el, element => {
      this.calHeight(element.offsetHeight)
    })
    // this.calHeight();
  },
  methods: {
    handleCell({ row, column }) {
      console.log(row, column)
      this.selectRow = row
      this.selectColumn = column
      this.$emit(
        "handleChartClick",
        {
          ...row,
          field: column.field,
          seriesType: "table"
        },
        this.widgetProps
      )
    },
    calHeight(height) {
      this.tableHeight = height - 50
    },
    // 使表格重新渲染
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
    handlePageChange(page) {
      this.$emit("onPageChange", page)
    },
    // 自动合并单元格
    rowspanMethod({ row, _rowIndex, column, visibleData }) {
      const { autoMergeCell } = this.widgetProps.chartStyles.table
      const cellValue = row[column.property]
      if (autoMergeCell) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    exportExcel(name, viewId) {
      let params = {
        ...widgetParamsFormat(this.widgetProps, viewId, {
          pageSize: 0,
          pageNo: 0
        }),
        pageSize: 0,
        pageNo: 0
      }
      Request.view.getdata(params).then(res => {
        jsonToSheetXlsx({
          data: res.data.resultList,
          filename: name
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]))
    },
    cellClassName({ row, column }) {
      if ((row === this.selectRow) & (column === this.selectColumn)) {
        return "col-blue"
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.tableContainer {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
::v-deep .mytable-style.vxe-table .vxe-body--column.col-blue {
  background-color: #1890ff;
  color: #fff;
}
::v-deep .vxe-table--render-default .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
  background-color: transparent !important;
}
::v-deep .vxe-table--header-wrapper.body--wrapper,
::v-deep .el-table tr,
::v-deep .el-table__header {
  background-color: transparent !important;
}

::v-deep .vxe-table--body {
  background-color: transparent;
}

::v-deep .vxe-body--column {
  background-image: none !important;
}

::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb {
  background-color: transparent;
}

// 分页器-------------------------------------------------------------------------------------------------------

::v-deep .vxe-pager,
::v-deep .dt-pagination-container {
  background-color: transparent;
}

::v-deep .vxe-pager--prev-btn,
::v-deep .vxe-pager--next-btn,
::v-deep .vxe-pager--num-btn,
::v-deep .vxe-pager--jump-next,
::v-deep .btn-prev,
::v-deep .btn-next,
::v-deep .number,
::v-deep .btn-quicknext {
  background-color: transparent;
}
</style>
