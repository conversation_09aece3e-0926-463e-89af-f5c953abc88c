@import "./variables.scss";
@import "./mixin.scss";
@import "./theme.scss";

/* http://meyerweb.com/eric/tools/css/reset/ 
   v2.0 | 20110126
   License: none (public domain)
*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body .app {
  color: #333333;
  font-family: Arial, Helvetica, "STHeiti STXihei", "Microsoft YaHei", Tohoma,
    sans-serif;
  background-color: $background-color;
}

.app-container {
  padding-bottom: 50px;
}

// /* 定义滚动条高宽及背景
//  高宽分别对应横竖滚动条的尺寸 */
//  ::-webkit-scrollbar {
//     width: 4px;
//     height: 4px;
// }

// /* 定义滚动条轨道
//   内阴影+圆角 */
// ::-webkit-scrollbar-track {
//     background-color: transparent;
// }

// /* 定义滑块
//   内阴影+圆角 */
// ::-webkit-scrollbar-thumb {
//     border-radius: 4px;
//     background-color: rgba(144,147,153,.6);
// }

@keyframes turn {
  0% {
    -webkit-transform: rotate(0deg);
  }
  25% {
    -webkit-transform: rotate(90deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(270deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
.el-pagination {
  text-align: right;
}
