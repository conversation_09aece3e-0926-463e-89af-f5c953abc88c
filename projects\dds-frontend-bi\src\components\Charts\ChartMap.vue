<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
    <div class="top" v-if="isShowTop">
      <div class="title">TOP3省份</div>
      <div
        class="top-item"
        v-for="(item, index) in chartData.slice(0, 3)"
        :key="index"
      >
        <div class="icon"></div>
        <div class="name">{{ item[xField] || "-" }}</div>
        <div class="value">{{ toThousands(item[yField]) }}{{ unit }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"

import "@/assets/js/china.js"
import { toThousands,formatValue } from "@/utils/index.js"
import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String,
      default: "value"
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 维度名称
    seriesName: {
      type: String,
      default: "省份"
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    isShowTop: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      let min = 0
      let max = 100
      this.chartData.forEach(item => {
        min = Math.min(min, item[this.yField])
        max = Math.max(max, item[this.yField])
      })
      min = this.customRound(min, 100, "left")
      max = this.customRound(max, 100, "right")
      console.log(min, max)
      this.chart.setOption({
        title: {},
        dataset: {
          dimensions: [this.xField, this.yField],
          source: this.chartData
        },

        tooltip: {
          trigger: "item",
          showDelay: 0,
          transitionDuration: 0.2,
          formatter: params => {
            console.log(params, "params")
            return `<div>
              <p class="tooltip-title">${params.name}</p>
              <div class="content-panel">
                <p>
                 <span    <span style="background-color: #2361DB" class="tooltip-item-icon"></span>
                 <span>${params.seriesName}</span>
                </p>
                <span class="tooltip-value">
                ${formatValue(params.value)}${this.unit}
                </span>
              </div>
            </div>`
          },
          className: "echarts-tooltip-diy"
        },
        visualMap: {
          top: 0,
          itemWidth: 13,
          show: this.isShowVisualMap,
          orient: "horizontal",
          realtime: false,
          calculable: true,
          min,
          max,
          inRange: {
            color: ["#E7F4FF", "#76A7FA", "#1E41A3"]
          },
          textStyle: {
            color: "#999",
            fontSize: 12
          }
        },

        series: {
          name: this.seriesName,
          type: "map",
          zlevel: 5,
          zoom: 1.2,
          roam: false,
          map: "china",
          nameMap: {
            河南: "河南省",
            山东: "山东省",
            四川: "四川省",
            湖南: "湖南省",
            黑龙江: "黑龙江省",
            内蒙古: "内蒙古自治区",
            吉林: "吉林省",
            北京: "北京市",
            辽宁: "辽宁省",
            河北: "河北省",
            天津: "天津市",
            山西: "山西省",
            陕西: "陕西省",
            甘肃: "甘肃省",
            宁夏: "宁夏回族自治区",
            青海: "青海省",
            新疆: "新疆维吾尔自治区",
            西藏: "西藏自治区",
            重庆: "重庆市",
            江苏: "江苏省",
            安徽: "安徽省",
            湖北: "湖北省",
            浙江: "浙江省",
            福建: "福建省",
            江西: "江西省",
            贵州: "贵州省",
            云南: "云南省",
            广东: "广东省",
            广西: "广西壮族自治区",
            海南: "海南省",
            上海: "上海市",
            台湾: "台湾省",
            澳门: "澳门特别行政区",
            香港: "香港特别行政区"
          },
          // 边框颜色
          itemStyle: {
            areaColor: "#fff",
            borderColor: "#5B8FF9",
            borderWidth: 1
          },
          emphasis: {
            show: false,
            itemStyle: {
              areaColor: this.color // hover时区域颜色
            },
            label: {
              color: "#fff" // hover时字体颜色
            }
          },
          label: {
            show: false, // 是否显示市
            textStyle: {
              color: "black", // 文字颜色
              fontSize: 8 // 文字大小
            }
          }
        }
      })
    },
    customRound(number, precision, direction) {
      console.log(number, direction)
      const factor = number / precision

      if (direction === "left") {
        return Math.floor((precision * factor) / 100) * 100
      } else if (direction === "right") {
        return Math.ceil((precision * (factor + 1)) / 100) * 100
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.x[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    },
    toThousands
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
  .top {
    position: absolute;
    bottom: 0;
    left: 0;
    color: 222;
    .title {
      height: 16px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #222222;
      margin-bottom: 13px;
    }
    .top-item {
      display: flex;
      align-items: center;
      height: 24px;
      margin-bottom: 8px;
      .icon {
        width: 22px;
        height: 24px;
        text-align: center;
      }
      .name {
        height: 16px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        margin: 0 16px;
      }
      .value {
        height: 16px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
      }
    }
    .top-item:nth-child(2) {
      .icon {
        background: url("~@/assets/images/NO1.png") no-repeat center center;
      }
    }
    .top-item:nth-child(3) {
      .icon {
        background: url("~@/assets/images/NO2.png") no-repeat center center;
      }
    }
    .top-item:nth-child(4) {
      .icon {
        background: url("~@/assets/images/NO3.png") no-repeat center center;
      }
    }
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
