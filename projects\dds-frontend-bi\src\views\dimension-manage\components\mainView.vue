<template>
  <div class="rolePage-mainView">
    <!-- 查询 -->
    <!-- <DT-Form
      type="search"
      v-model="searchForm"
      :render="searchRender"
      @confirm="handleSearch"
    /> -->
    <SearchForm
      @search="handleSearch"
      :columns="columns"
      :search-param.sync="searchForm"
      :is-card="false"
    />
    <!-- 按钮 -->
    <div class="btns">
      <el-button type="primary" @click="$emit('handleAdd')">新建维度</el-button>
      <el-button @click="$emit('handleAddType')">新建维度类型</el-button>
      <!-- <el-button
        style="margin-left: auto"
        @click="$emit('lookDimensionRelation')"
      >
        查看维度关系
      </el-button> -->
    </div>

    <!-- 表格 -->
    <DT-Table :data="data" :column="tableColumn" />
    <!-- 分页 -->
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
      @sizeChange="handlePageSizeChange"
      @currentChange="handlePageCurrentChange"
    />
  </div>
</template>

<script>
import SearchForm from "@/components/SearchForm/index.vue"
export default {
  name: "main-view",
  components: {
    SearchForm
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 搜索（与index同步）
    search: {
      type: Object,
      default: () => {
        return {
          chartName: ""
        }
      }
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => {
        return {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      }
    },
    dimensionTypeList: {
      type: Array,
      default: () => []
    },
    versionList: {
      type: Array,
      default: () => []
    }
  },
  inject: ["parent"],

  data() {
    return {
      // 搜索表单内容
      searchForm: {
        dimName: "",
        categoryCode: "",
        version: ""
      },
      // 搜索渲染配置
      columns: [
        {
          label: "维度名称",
          prop: "dimName",
          search: {
            el: "input"
          }
        },
        {
          label: "维度类型",
          prop: "categoryCode",
          search: {
            el: "select"
          },
          enum: [{ label: "任意", value: "" }, ...this.dimensionTypeList]
        },
        {
          label: "维度版本",
          prop: "version",
          search: {
            el: "select"
          },
          enum: [{ label: "任意", value: "" }, ...this.versionList]
        }
      ],
      // 表格渲染配置
      tableColumn: [
        {
          label: "维度类型",
          prop: "categoryName"
        },
        {
          label: "维度名称",
          prop: "dimName"
        },
        {
          label: "维度版本",
          prop: "version"
        },
        {
          label: "描述",
          prop: "description"
        },
        {
          label: "标签",
          prop: "tags"
        },
        {
          label: "层级数",
          prop: "maxDepth"
        },
        {
          label: "已关联数据源表",
          prop: "sourceConfCount"
        },
        {
          label: "应用指标数",
          prop: "appMetricsCount"
        },
        {
          label: "创建人",
          prop: "createdBy"
        },
        // 操作
        {
          label: "操作",
          width: 150,
          button: [
            {
              label: "编辑",
              onClick: ({ row }) => this.$emit("handleEdit", row)
            },
            {
              label: "删除",
              onClick: ({ row }) => this.$emit("handleDelete", row)
            }
          ]
        }
      ]
    }
  },
  mounted() {
    // 页面初始化时配置搜索双向绑定数据，使表单页返回时搜索框数据保持与之前一致
    this.searchForm = { ...this.search }
  },
  methods: {
    // 筛选表单 - 查询
    handleSearch() {
      this.pagination.currentPage = 1
      Object.keys(this.searchForm).forEach(
        key => (this.search[key] = this.searchForm[key])
      )
      this.$emit("search")
    },
    // 分页 - 每页条数改变
    handlePageSizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.pagination.currentPage = 1
      this.$emit("paginationChange")
    },
    // 分页 - 当前页码改变
    handlePageCurrentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.$emit("paginationChange")
    }
  }
}
</script>
<style lang="scss" scoped>
.btns {
  display: flex;
  margin-bottom: 20px;
}
</style>
