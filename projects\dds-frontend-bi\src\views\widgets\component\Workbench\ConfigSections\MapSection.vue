<template>
  <div class="paneBlock">
    <h4>地图</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox v-model="specForm.roam" @change="onSpecChange">
            移动&缩放
          </el-checkbox>
        </el-col>
        <el-col span="4"> 类型 </el-col>
        <el-col span="10">
          <el-select
            v-model="specForm.layerType"
            size="mini"
            @change="onSpecChange"
          >
            <el-option
              v-for="item in CHART_LAYER_TYPES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>

      <el-row
        v-if="specForm.layerType == 'lines'"
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
      >
        <el-col span="3">速度</el-col>
        <el-col span="9">
          <el-input-number
            controls-position="right"
            size="mini"
            v-model="specForm.linesSpeed"
            @change="onSpecChange"
          ></el-input-number>
        </el-col>
        <el-col span="3">标记</el-col>
        <el-col span="9">
          <el-select
            v-model="specForm.symbolType"
            size="mini"
            @change="onSpecChange"
          >
            <el-option
              v-for="item in CHART_LINES_SYMBOL_TYPE"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>

      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox v-model="specForm.isShowBorder" @change="onSpecChange">
            显示边界
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">边界颜色</el-col>

        <el-col span="4">
          <el-color-picker
            v-model="specForm.borderColor"
            @change="onSpecChange"
          ></el-color-picker>
        </el-col>
        <el-col span="6">
          区域颜色
        </el-col>
        <el-col span="4">
          <el-color-picker
            v-model="specForm.areaColor"
            @change="onSpecChange"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox v-model="specForm.isHighlight" @change="onSpecChange">
            启动高亮
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">边界颜色</el-col>

        <el-col span="4">
          <el-color-picker
            v-model="specForm.highlightborderColor"
            @change="onSpecChange"
          ></el-color-picker>
        </el-col>
        <el-col span="6">
          区域颜色
        </el-col>
        <el-col span="4">
          <el-color-picker
            v-model="specForm.highlightareaColor"
            @change="onSpecChange"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">标签颜色</el-col>

        <el-col span="4">
          <el-color-picker
            v-model="specForm.highlightLabelColor"
            @change="onSpecChange"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="6">地图范围</el-col>
        <el-col span="18">
          <Treeselect
            v-model="specForm.areaCode"
            size="mini"
            :normalizer="normalizer"
            @input="onSpecChange"
            :options="places" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { CHART_LAYER_TYPES, CHART_LINES_SYMBOL_TYPE } from "@/globalConstants"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import places from '@/assets/json/places.json'
export default {
  name: "legend-selector",
  components: {
    Treeselect,
  },
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      places,
      CHART_LAYER_TYPES,
      CHART_LINES_SYMBOL_TYPE,
      specForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  created() {
   
  },
  mounted() {},
  methods: {
    init() {
      this.specForm = this._.cloneDeep(this.chartData.chartStyles.spec)
    },
    onSpecChange() {
      
      this.$emit("changeStyle", "spec", this.specForm)
    },
    normalizer(node) {
      return {
        id: node.code,
        label: node.name,
        children: node.children,
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
