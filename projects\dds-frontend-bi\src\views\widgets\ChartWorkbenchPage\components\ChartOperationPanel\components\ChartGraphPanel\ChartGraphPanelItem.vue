<script>
export default {
  functional: true,
  props: {
    chartConfig: {
      type: Object,
      defau1t: () => {},
    },
    dimetionsAndMetricsCount: {
      type: Array,
      defau1t: () => [ 0, 0 ],
    },
    selectedChart: {
      type: Number,
      defau1t: 1,
    },
  },
  render(h, context) {
    const { chartConfig, dimetionsAndMetricsCount, selectedChart } =
      context.props
    const { title, icon, rules, id } = chartConfig
    const [ dcount, count ] = dimetionsAndMetricsCount
    // 验证图表是否可用
    const checkChartEnable = (dimensionCount, metricCount, chart) => {
      const chartArr = Array.isArray(chart) ? chart : [ chart ]
      const enabled = chartArr.every(({ rules }) => {
        const currentRulesChecked = rules.some(({ dimension, metrics }) => {
          if (Array.isArray(dimension)) {
            if (
              dimensionCount < dimension[0] ||
              dimensionCount > dimension[1]
            ) {
              return false
            }
          } else if (dimensionCount !== dimension) {
            return false
          }

          if (Array.isArray(metrics)) {
            if (metricCount < metrics[0] || metricCount > metrics[1]) {
              return false
            }
          } else if (metricCount !== metrics) {
            return false
          }
          return true
        })
        return currentRulesChecked
      })

      return enabled
    }
    const contents = rules.map(({ dimension, metrics }, ruleIdx) => {
      const subContents = []
      if (Array.isArray(dimension)) {
        subContents.push(
          `${dimension[0]}个 到 ${
            dimension[1] === 9999 ? "多" : dimension[1]
          }个 维度`
        )
      } else {
        subContents.push(`${dimension}个 维度`)
      }
      if (Array.isArray(metrics)) {
        subContents.push(
          `${metrics[0]}个 到 ${metrics[1] === 9999 ? "多" : metrics[1]}个 指标`
        )
      } else {
        subContents.push(`${metrics}个 指标`)
      }
      if (rules.length > 1) {
        return <p key={ruleIdx}>{subContents.join("，")}</p>
      }
      return subContents.map((item, idx) => (
        <p key={`${ruleIdx}_${idx}`}>{item}</p>
      ))
    })
    const enable = checkChartEnable(dcount, count, chartConfig)
    const active = selectedChart === id
    const overlay = (
      <div>
        <p>{title}</p>
        {contents}
      </div>
    )

    return (
      <el-tooltip placement="bottom">
        <div slot="content" style="lineHeight: 20px">
          {overlay}
        </div>
        <span
          class={[ enable ? "" : "disabledIcon", active ? "activedIcon" : "" ]}
          onClick={() => context.listeners.chartSelect(enable, chartConfig)}
        >
          <svg-icon
            class="icon"
            icon-class={enable ? icon : icon + "_disabled"}
          />
        </span>
      </el-tooltip>
    )
  },
}
</script>
