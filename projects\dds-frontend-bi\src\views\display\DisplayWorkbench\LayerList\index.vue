<template>
  <div class="layer-list">
    <div class="radio-group">
      <el-radio-group :value="currentLayerId" @input="radioChange">
        <el-radio
          :label="item.id"
          v-for="(item, index) in layersIndexRankList"
          :key="index"
        >
          {{ item.name }}
        </el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
export default {
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      layersIndexRankList: "layersIndexRankList",
      currentLayerId: "currentLayerId",
    }),
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    radioChange(id) {
      const currentLayer = this.layersIndexRankList.find((item) => item.id === id)
      this.$store.commit("display/SET_CURRENT_LAYER", currentLayer)
    },
  },
}
</script>

<style scoped lang="scss">
.layer-list {
  background-color: #fff;
  .radio-group {
    padding: 10px;
    box-sizing: border-box;
    width: 150px;
  }
}
::v-deep .el-radio-group{
  display: flex;
  flex-direction: column;
}
</style>
