import {
  decodeMetricName,
  metricAxisLabelFormatter,
} from "../component/util"

import {
  getLegendOption,
  getLabelOption,
  getDimetionAxisOption,
  getCartesianChartReferenceOptions,
} from "./util"
export default function(chartProps) {
  const {
    data,
    cols,
    metrics,
    chartStyles,
    // color,
    // tip,
    references,
  } = chartProps

  const { grid, legend, spec, doubleYAxis, xAxis, splitLine, label, } = chartStyles


  const { stack, smooth, step, symbol } = spec

  const { yAxisLeft, yAxisRight, yAxisSplitNumber, showLabel } = doubleYAxis

  const {
    showVerticalLine,
    verticalLineSize,
    verticalLineStyle,
    showHorizontalLine,
  } = splitLine

  const selectedItems = []
  const { secondaryMetrics } = chartProps

  const metricsLabelOption = getLabelOption("doubleYAxis", label, metrics)
  const secondaryMetricsLabelOption = getLabelOption(
    "doubleYAxis",
    label,
    secondaryMetrics
  )
  // const secondaryMetricsLabelOption = {
  //   show: label.showLabel,
  //   color: label.labelColor,
  //   fontFamily: label.labelFontFamily,
  //   fontSize: label.labelFontSize,
  //   position: label.labelPosition,
  //   formatter(params){
  //       const { value, seriesName } = params
  //       const m = secondaryMetrics.find(
  //         (m) =>
  //         m.displayName === seriesName
  //         )
  //       const formattedValue = getFormattedValue(value, m.format)
  //       return formattedValue
  //   }
  // }

  const xAxisData = showLabel ? data.map((d) => d[cols[0].displayName]) : []
  const seriesData = secondaryMetrics
    ? getAixsMetrics(
      "metrics",
      metrics,
      data,
      stack,
      metricsLabelOption,
      references,
      selectedItems,
      { key: "yAxisLeft", type: yAxisLeft }
    ).concat(
      getAixsMetrics(
        "secondaryMetrics",
        secondaryMetrics,
        data,
        stack,
        secondaryMetricsLabelOption,
        references,
        selectedItems,
        { key: "yAxisRight", type: yAxisRight }
      )
    )
    : getAixsMetrics(
      "metrics",
      metrics,
      data,
      stack,
      metricsLabelOption,
      references,
      selectedItems,
      { key: "yAxisLeft", type: yAxisLeft }
    )
  const seriesObj = {
    series: seriesData.map((series) => {
      if (series.type === "line") {
        return {
          ...series,
          symbol: symbol ? "emptyCircle" : "circle",
          smooth,
          step,
        }
      } else {
        return {
          ...series,
          stack: stack ? "total" : "",
        }
      }
    }),
  }

  let legendOption
  let gridOptions
  if (seriesData.length > 1) {
    const seriesNames = seriesData.map((s) => s.name)
    legendOption = {
      legend: getLegendOption(legend, seriesNames),
    }
    gridOptions = {
      grid,
      // grid: spec.showDataZoom
      //   ? {
      //     ...getGridPositions(
      //       legend,
      //       seriesNames,
      //       "doubleYAxis",
      //       false,
      //       null,
      //       xAxis,
      //       xAxisData
      //     ),
      //     bottom: 90,
      //   }
      //   : getGridPositions(
      //     legend,
      //     seriesNames,
      //     "doubleYAxis",
      //     false,
      //     null,
      //     xAxis,
      //     xAxisData
      //   ),
    }
  }

  let leftMax
  let rightMax

  if (stack) {
    leftMax = metrics.reduce(
      (num, m) =>
        num +
        Math.max(
          ...data.map((d) => d[`${m.agg}(${decodeMetricName(m.name)})`])
        ),
      0
    )
    rightMax = secondaryMetrics.reduce(
      (num, m) =>
        num +
        Math.max(
          ...data.map((d) => d[`${m.agg}(${decodeMetricName(m.name)})`])
        ),
      0
    )
  } else {
    leftMax = Math.max(
      ...metrics.map((m) =>
        Math.max(...data.map((d) => d[`${m.agg}(${decodeMetricName(m.name)})`]))
      )
    )
    rightMax = Math.max(
      ...secondaryMetrics.map((m) =>
        Math.max(...data.map((d) => d[`${m.agg}(${decodeMetricName(m.name)})`]))
      )
    )
  }
  const leftInterval = getYaxisInterval(leftMax, yAxisSplitNumber - 1)
  const rightInterval =
    rightMax > 0
      ? getYaxisInterval(rightMax, yAxisSplitNumber - 1)
      : leftInterval
  const xAxisSplitLineConfig = {
    showLine: showVerticalLine,
    // lineColor: verticalLineColor,
    lineSize: verticalLineSize,
    lineStyle: verticalLineStyle,
  }

 
  const option = {
    tooltip: {
      confine: true,

      // ...TOOLTIP_STYLE,
      trigger: "axis",
      // axisPointer: { type: "cross" },
      // formatter(params) {
      //   const tooltipLabels = [
      //     getFormattedValue(params[0].name, cols[0].format),
      //     "<br/>",
      //   ];
      //   params.reduce((acc, param) => {
      //     const { color, value, seriesIndex } = param;
      //     if (color) {
      //       acc.push(
      //         `<span class="widget-tooltip-circle" style="background: ${color}"></span>`
      //       );
      //     }
      //     acc.push(
      //       getFieldAlias(allMetrics[seriesIndex].field, {}) ||
      //         decodeMetricName(allMetrics[seriesIndex].name)
      //     );
      //     acc.push(
      //       ": ",
      //       getFormattedValue(value, allMetrics[seriesIndex].format),
      //       "<br/>"
      //     );
      //     return acc;
      //   }, tooltipLabels);
      //   return tooltipLabels.join("");
      // },
    },
    xAxis: getDimetionAxisOption(xAxis, xAxisSplitLineConfig, xAxisData),
    yAxis: [
      {
        type: "value",
        key: "yAxisIndex0",
        min: 0,
        max:
          rightMax > 0
            ? rightInterval * (yAxisSplitNumber - 1)
            : leftInterval * (yAxisSplitNumber - 1),
        interval: rightInterval,
        position: "right",
        splitLine: {
          show: showHorizontalLine,
        },
        ...getDoubleYAxis(doubleYAxis),
      },
      {
        type: "value",
        key: "yAxisIndex1",
        min: 0,
        max: leftInterval * (yAxisSplitNumber - 1),
        interval: leftInterval,
        position: "left",
        splitLine: {
          show: showHorizontalLine,
        },
        ...getDoubleYAxis(doubleYAxis),
      },
    ],
    ...seriesObj,
    ...gridOptions,
    ...legendOption,
    dataZoom: [
      {
        show: spec.showDataZoom ? true : false,
        endValue: spec.endValue && spec.showDataZoom ? spec.endValue : null, // 初始化滚动条
        // backgroundColor: "#F0F2F5",
        // type: "slider",
        // handleIcon:
        //   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAxFJREFUSEu1lV9IU1Ecx3/n7o9ru4TzBjLxz2Z3GivHWGLsQdIehIQZQkNkBfXay4ReLLEIs/kSbC+91kNDZAONgYFQLnwYyRhT15R5cxNlQ9hlEnftj7obx1yozWi13cdzf+d8fn/O+X4RnP2hxcVFlUwmGyAIohshpEEIXcDhPM8neJ4P5fP5+VQqNdXR0RHBy8WOQkUW0cLCgoqiqBdisfg2SZICmUwGVVVVIBQKD8P39/chm81CKpUCjuMOcrmci2XZx52dnb+BTgOIQCBglkqlr2pqaki5XA4EQfyhSIB8Pg/JZBJYluXS6fQDnU7nAIB8YdNxgGB5efkhSZITdXV1CGdcyocrisViPMdxw1qt9iUAHOD9BQDh9/vvVFdXv2loaECFVpQCKLRua2uL393dvafX69/iSjAAud1uurW11d/Y2EiWmvnpJHAlm5ubXDgc1huNRgYDRCsrKw6FQmGiKKrUpIvGsywL8Xjc2dbWZkYul+uSTqcLqlQqwVkD9Xq9YDAYThxWbK0QgAcfiUQOAoHAFeTz+Z7U19c/q62tPTN7j8cDXV1dJ/4XWzsesLOzA9vb209RMBj80NTUdIMkybICOI7Ds/iIQqFQjKZphUgkKitgb28PGIaJo7W1tWxLS4sYoWKP+ifzX1rE8zyEw+Fc5QEVb9HS0pKnubn5eiWGvLGx8Qkr53O1Wj1SiWu6vr4+jqxW61WTyfS5Eg/N6XRew1dH7vP5XiuVylvllIpoNPquvb39PgZIR0dHL5vN5nmlUikrh9hFo9GUw+HoHhsb+3IodgBAzszM3NVqtbZyyHUwGLT09fVhueYO5RoAsLucn5ubs9A0/eh/DIdhGGtPT48dAL4BQPaX4QCABEOmp6cHNBrNOEVRslIsM5FIpFZXV0f6+/unjg7PFAynoEGCo0rIoaEh9eDg4DBFUTf/xvRZln0/OTk5YbPZ1nFbcOanLbMAwQ4vBoBzePgWi+Vib2+vkaIog0QioYVCofzIGpOZTIZhWdY7OzvrttvtXwHgOwCkASB33PR/AK8MqDBvjq1bAAAAAElFTkSuQmCC",
        // moveHandleSize: 0,
        // showDataShadow: false,
        // fillerColor: "#CED4D9",
        // handleStyle: {
        //   color: "#63659F",
        // },
        // height: 18,
        // xAxisIndex: [0],
        // startValue: 0,
        // bottom: 0,
        // borderColor: "transparent",
        // brushSelect: false,
        // textStyle: {
        //   color: "rgba(0, 0, 0, 0.65)",
        //   fontWeight: "400",
        //   fontSize: 12,
        //   fontFamily: "PingFangSC-Regular, PingFang SC",
        //   lineHeight: 18,
        // },
      },
    ],
  }

  return option
}

export function getAixsMetrics(
  type,
  axisMetrics,
  data,
  stack,
  labelOption,
  references,
  selectedItems,
  axisPosition
) {
  const seriesNames = []
  const seriesAxis = []
  const referenceOptions = getCartesianChartReferenceOptions(
    references,
    "doubleYAxis",
    axisMetrics,
    data
  )
  axisMetrics.forEach((m, amIndex) => {
    console.log(m, "getAixsMetrics")
    const decodedMetricName = m.field.alias ?? decodeMetricName(m.name)
    seriesNames.push(decodedMetricName)
    const stackOption =
      stack && axisPosition.type === "bar" && axisMetrics.length > 1
        ? { stack: axisPosition.key }
        : null
    const itemData = data.map((g, index) => {
      const itemStyle =
        selectedItems &&
          selectedItems.length &&
          selectedItems.some((item) => item === index)
          ? { itemStyle: { opacity: 1, borderWidth: 6 } }
          : null
      return {
        value: g[`${m.agg}(${m.displayName})`],
        ...itemStyle,
      }
    })

    seriesAxis.push({
      name: decodedMetricName,
      type:
        axisPosition && axisPosition.type
          ? axisPosition.type
          : type === "metrics"
            ? "line"
            : "bar",
      ...stackOption,
      yAxisIndex: type === "metrics" ? 1 : 0,
      data: itemData,
      label: labelOption,
      ...(amIndex === axisMetrics.length - 1 && referenceOptions),
      itemStyle: {
        opacity: selectedItems && selectedItems.length > 0 ? 0.25 : 1,
      },
    })
  })
  return seriesAxis
}

export function getYaxisInterval(max, splitNumber) {
  const roughInterval = parseInt(`${max / splitNumber}`, 10)
  const divisor = Math.pow(10, `${roughInterval}`.length - 1)
  return (parseInt(`${roughInterval / divisor}`, 10) + 1) * divisor
}

export function getDoubleYAxis(doubleYAxis) {
  const {
    inverse,
    showLine,
    lineStyle,
    lineSize,
    showLabel,
    labelFontFamily,
    labelFontSize,
  } = doubleYAxis

  return {
    inverse,
    axisLine: {
      show: showLine,
      lineStyle: {
        // color: lineColor,
        width: Number(lineSize),
        type: lineStyle,
      },
    },
    axisLabel: {
      show: showLabel,
      // color: labelColor,
      fontFamily: labelFontFamily,
      fontSize: Number(labelFontSize),
      formatter: metricAxisLabelFormatter,
    },
  }
}
