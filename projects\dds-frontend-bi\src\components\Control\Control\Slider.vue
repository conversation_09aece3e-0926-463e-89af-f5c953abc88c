<template>
  <el-slider
    style="margin-left: 5%; width: 90%"
    v-model="value"
    range
    show-stops
    :max="max"
    :min="min"
    :step="step"
    @change="change"
    size="mini"
  ></el-slider>
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: Array,
      default: () => [ 60, 99 ],
    },
    max: {
      type: Number,
    },
    min: {
      type: Number,
    },
    step: {
      type: Number,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    change() {
      this.$emit("update:value", this.value)
      this.$emit("change", { [this.item.key]: this.value })
    },
  },
}
</script>

<style scoped lang="less">
</style>
