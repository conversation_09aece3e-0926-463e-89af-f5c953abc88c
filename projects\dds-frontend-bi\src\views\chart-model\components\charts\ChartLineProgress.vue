<template>
  <div class="progress-bar-container">
    <div class="progress-label">{{ label }}{{ data }}%</div>
    <div class="progress-bg">
      <div class="progress-fg" :style="{ width: data + '%' }"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressBar',
  props: {
    // 进度占比，0 - 100
    data: {
      type: Number,
      required: true,
      validator: value => {
        return value >= 0 && value <= 100
      }
    },
    // 描述信息
    label: {
      type: String,
      required: true
    }
  }
}
</script>

<style scoped>
.progress-bar-container {
  width: 300px; /* 可根据需求调整宽度 */
}
.progress-label {
  color: #000;
  margin-bottom: 4px;
}
.progress-bg {
  background-color: #1f2b42; /* 背景条颜色，可自定义 */
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
}
.progress-fg {
  background-color: #409eff; /* 进度条颜色，可自定义 */
  height: 100%;
  transition: width 0.3s ease;
}
</style>
