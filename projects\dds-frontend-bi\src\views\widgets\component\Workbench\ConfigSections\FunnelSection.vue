<template>
  <div class="paneBlock">
    <h4>漏斗图</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <!-- <el-col span="4">
          排序
        </el-col>
        <el-col span="8">
          <el-select
            placeholder="请选择"
            @change="changeSpecStyle"
            v-model="specForm.sortMode"
            size="mini"
          >
            <el-option
              v-for="item in CHART_SORT_MODES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col> -->
        <el-col span="4">
          间距
        </el-col>
        <el-col span="12">
          <el-input-number
            min="0"
            controls-position="right"
            placeholder=""
            v-model="specForm.gapNumber"
            @input="changeSpecStyle"
          ></el-input-number>
        </el-col>
        <el-col span="4">对齐</el-col>
        <el-col span="6">
          <el-select
            placeholder="请选择"
            @change="changeSpecStyle"
            v-model="specForm.alignmentMode"
            size="mini"
          >
            <el-option
              v-for="item in CHART_ALIGNMENT_MODES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  PIVOT_CHART_LINE_STYLES,
  CHART_ALIGNMENT_MODES,
  CHART_SORT_MODES,
} from "@/globalConstants"

export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      CHART_SORT_MODES,
      PIVOT_CHART_LINE_STYLES,

      CHART_ALIGNMENT_MODES,
      specForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.specForm = this._.cloneDeep(this.chartData.chartStyles.spec)
      console.log(this.specForm, "this.specForm")
    },
    changeSpecStyle() {
      console.log(this.specForm, "this.specForm")
      this.$emit("changeStyle", "spec", this.specForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
