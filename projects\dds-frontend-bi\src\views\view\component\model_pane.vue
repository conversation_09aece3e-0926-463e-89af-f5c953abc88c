<template>
  <el-table
    :data="modelList"
    border
    stripe
    width="100%">
    <el-table-column prop="name" label="字段名称" />
    <el-table-column prop="alias" label="别名">
      <template slot-scope="scope">
        <el-input v-model="scope.row.alias" placeholder="请输入别名" />
      </template>
    </el-table-column>
    <el-table-column prop="modelType" label="数据类型">
      <template slot-scope="scope">
        <el-radio-group v-model="scope.row.modelType">
          <el-radio label="category">维度</el-radio>
          <el-radio label="value">指标</el-radio>
        </el-radio-group>
      </template>
    </el-table-column>
    >
    <el-table-column prop="visualType" label="可视化类型">
      <template slot-scope="scope">
        <el-select v-model="scope.row.visualType">
          <el-option value="number" label="数字" />
          <el-option value="string" label="字符" />
          <el-option value="date" label="日期" />
          <el-option value="geoCountry" label="地理国家" />
          <el-option value="geoProvince" label="地理省份" />
          <el-option value="geoCity" label="地理城市" />
        </el-select>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: "model-pane",
  props: {
    modelList: {
      type: Array,
      default:()=> [],
    },
  },
}
</script>
