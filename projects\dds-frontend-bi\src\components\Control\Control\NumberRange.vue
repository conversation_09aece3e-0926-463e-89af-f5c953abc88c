<template>
  <div class="numberRange">
    <el-input
      v-model="value[0]"
      @input="changeInput"
    />
    -<el-input
      v-model="value[1]"
      @input="changeInput"
    />
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: [ String , Object , Array ],
    },
    item:{
      type:Object
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    changeInput() {
      this.$emit("update:value", this.value)
      this.$emit("change", { [this.item.key]: this.value })

    },
  },
}
</script>

<style scoped lang="scss">
.numberRange {
  display: flex;
}
</style>
