<template>
  <DT-View :show="pageShow">
    <!-- 主视图 -->
    <main-view
      v-if="mainView.show"
      :data="mainView.data"
      :search.sync="mainView.search"
      :pagination.sync="mainView.pagination"
      :dimension-type-list="dimensionTypeList"
      :version-list="versionList"
      @search="getTableData"
      @handleAdd="handleAdd"
      @handleEdit="handleEdit"
      @handleDelete="handleDelete"
      @lookDimensionRelation="lookDimensionRelation"
      @handleClose="handleClose"
      @handleAddType="handleAddType"
      @paginationChange="getTableData"
    />
    <!-- 图表视图 -->
    <chart-view v-if="chartView.show" @goBack="goBack" />

    <!-- 新增维度类型 -->
    <dimension-type ref="dimensionType" @refresh="afreshRender" />

    <!-- 表单视图 -->
    <form-view
      ref="formView"
      @refresh="afreshRender(2)"
      @createDimension="createDimension"
    />
  </DT-View>
</template>

<script>
import Request from "@/service"
import MainView from "./components/mainView"
import ChartView from "./components/chartView"
import FormView from "./components/formView"
import DimensionType from "./components/dimensionType"
export default {
  name: "role",
  components: {
    MainView,
    FormView,
    ChartView,
    DimensionType
  },
  data() {
    return {
      pageShow: true,
      // 用户id
      roleId: null,
      // 主视图
      mainView: {
        show: false,
        // 数据
        data: [],
        // 搜索
        search: {
          dimName: "",
          categoryCode: "",
          version: ""
        },
        // 分页配置
        pagination: {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      },
      // 图表视图
      chartView: {
        show: false,
        data: {},
        // 分页配置
        pagination: {
          total: 0,
          pageSize: 10,
          currentPage: 1
        }
      },

      // 维度类型列表
      dimensionTypeList: [],
      // 维度版本列表
      versionList: [],
      updateFrequencys: [
        {
          label: "按日",
          value: 1
        },
        {
          label: "按周",
          value: 2
        },
        {
          label: "按月",
          value: 3
        },
        {
          label: "按学期",
          value: 4
        },
        {
          label: "按学年",
          value: 5
        },
        {
          label: "按年",
          value: 6
        }
      ]
    }
  },
  created() {
    Promise.all([this.getVersionList(), this.getDimensionList()])
      .then(() => {
        this.getTableData()
      })
      .catch(error => {
        console.error("初始化请求失败:", error)
      })
  },
  mounted() {},
  provide() {
    return {
      parent: this
    }
  },
  methods: {
    // ------------------------ 主视图 ------------------------
    // 拉取表格数据
    getTableData() {
      console.log("我快")
      let param = {
        pageNum: this.mainView.pagination.currentPage,
        pageSize: this.mainView.pagination.pageSize,
        ...this.mainView.search
      }
      this.$dt_loading.show()
      Request.api
        .paramPost("/DimManage/getDimList", param)
        .then(res => {
          this.mainView.pagination.total = res.data.totalCount
          this.mainView.data = res.data.list
          this.mainView.show = true
          this.pageShow = true
          this.$dt_loading.hide()
        })
        .catch(() => this.$dt_loading.hide())
    },
    // 添加维度类型
    handleAddType() {
      this.$refs.dimensionType.openDialog()
    },
    // 重新渲染下拉
    async afreshRender(type = 1) {
      this.mainView.show = false
      if (type === 1) {
        await this.getDimensionList()
        this.$nextTick(() => {
          this.mainView.show = true
        })
      } else {
        await this.getVersionList()
        // this.getTableData()
      }
    },

    // 获取维度列表
    async getDimensionList() {
      const { data } = await Request.api.paramPost(
        "/DimManage/getDimCategoryList",
        {}
      )
      this.dimensionTypeList = data.map(item => ({
        label: item.categoryName,
        value: item.categoryCode
      }))
    },
    // 获取维度版本
    async getVersionList() {
      const { data } = await Request.api.paramGet(
        "/DimManage/getAllDimVersion",
        {}
      )
      console.log("完成")
      this.versionList = data.map(item => ({
        label: item,
        value: item
      }))
    },
    // 查看维度关系
    lookDimensionRelation() {
      this.chartView.show = true
      this.mainView.show = false
    },
    goBack() {
      this.chartView.show = false
      this.mainView.show = true
      this.getTableData()
    },
    // 新增
    handleAdd() {
      this.$refs.formView.openDialog()
    },
    // 创建维度
    async createDimension(row) {
      // 跳转到维度详情页面
      this.$router.push({
        path: "/ddsBi/dimensionDetail",
        query: { dimName: row.dimName, definitionCode: row.definitionCode }
      })
    },
    // 编辑
    async handleEdit(row) {
      // 跳转到维度详情页面
      this.$router.push({
        path: "/ddsBi/dimensionDetail",
        query: { dimName: row.dimName,
          definitionCode: row.definitionCode
        }
      })
    },
    // 复制
    async handleCopy(row) {
      const { data } = await Request.api.paramGet("/indicator/chart/getOne", {
        chartCode: row.chartCode
      })
      this.$refs.formView.openDialog({
        ...data,
        chartCode: "",
        chartName: data.chartName + "(副本)",
        description: data.description + "(副本)"
      })
    },
    // 删除
    handleDelete(row) {
      this.$confirm("是否删除选中数据", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$dt_loading.show()
          Request.api
            .paramDel("/DimManage/deleteDimByCode", {
              code: row.definitionCode
            })
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 开启
    handleOpen(row) {
      this.$confirm(
        "该名单一旦开启后，关联的预警指标或学生预警应用在后续的计算时自动去除白名单里的人群",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$dt_loading.show()
          Request.permission.role
            .delRole([row.roleId])
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 关闭
    handleClose(row) {
      this.$confirm(
        "该名单一旦禁用后，关联的预警指标或学生预警应用在后续的计算时不再去除白名单里的人群，直到再次开启",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$dt_loading.show()
          Request.permission.role
            .delRole([row.roleId])
            .then(() => {
              // 若当前并非首页且本页仅有一条数据，则页码减一后拉取数据
              if (
                this.mainView.pagination.currentPage > 1 &&
                this.mainView.data.length === 1
              ) {
                this.mainView.pagination.currentPage -= 1
              }
              this.$message.success("删除成功")
              this.getTableData()
            })
            .catch(() => this.$dt_loading.hide())
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 返回
    handleBack() {
      this.mainView.show = true
      this.formView.show = false
      this.grantRole.show = false
      this.setUsers.show = false
    }
  }
}
</script>
