<template>
  <dt-single-page-view>
    <el-row :gutter="15">
      <el-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4">
        <div class="unit" @click="showPortalForm('add')">
          <div class="central">
            <div><i class="el-icon-plus" /></div>
            <div>创建新 大屏</div>
          </div>
        </div>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
        v-for="displayItem in displayList"
        :key="displayItem.id"
      >
        <div
          class="unit"
          :style="{
            backgroundImage: displayItem.avatar
              ? env === 'development'
                ? `url('http://***************${displayItem.avatar}')`
                : `url('${origin}${displayItem.avatar}')`
              : 'url()',
          }"
          @click="goDashboard(displayItem)"
        >
          <header>
            <h3 class="title">{{ displayItem.name }}</h3>
            <p class="content">{{ displayItem.description }}</p>
          </header>
          <div class="portalActions">
            <el-tooltip
              class="edit"
              effect="dark"
              content="设置"
              placement="top-start"
            >
              <i
                class="el-icon-setting"
                @click.stop="showPortalForm('set', displayItem)"
              />
            </el-tooltip>
            <el-tooltip
              class="copy"
              effect="dark"
              content="复制"
              placement="top-start"
            >
              <i
                class="el-icon-copy-document"
                @click.stop="showPortalForm('copy', displayItem)"
              />
            </el-tooltip>

            <el-popconfirm
              title="确定删除大屏吗？"
              @onConfirm="handleDel(displayItem.id)"
            >
              <el-tooltip
                slot="reference"
                class="del"
                effect="dark"
                content="删除"
                placement="top-start"
              >
                <i class="el-icon-delete" @click.stop />
              </el-tooltip>
            </el-popconfirm>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-dialog
      :title="
        formType === 'add'
          ? '新增大屏'
          : formType == 'set'
            ? '设置大屏'
            : '复制大屏'
      "
      :border="true"
      :visible.sync="dialogShow"
      width="35%"
      @close="handleCancel"
    >
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="info">
            <el-form
              :model="displayInfo"
              :rules="rules"
              ref="ruleForm"
              label-width="100px"
              class="demo-ruleForm"
            >
              <el-form-item label="名称" prop="name">
                <el-input
                  v-model="displayInfo.name"
                  placeholder="请输入名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="描述">
                <el-input
                  v-model="displayInfo.description"
                  placeholder="请输入描述"
                ></el-input>
              </el-form-item>
              <!-- <el-form-item label="是否发布">
                <el-radio-group v-model="displayInfo.publish">
                  <el-radio :label="true">发布</el-radio>
                  <el-radio :label="false">编辑</el-radio>
                </el-radio-group>
              </el-form-item> -->
            </el-form>
          </el-tab-pane>
          <!-- <el-tab-pane label="权限管理" name="permission">
            <el-checkbox v-model="displayInfo.avatar" label="1">
              演示角色
            </el-checkbox>
          </el-tab-pane> -->
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </dt-single-page-view>
</template>

<script>
import { getDefaultSlideParams } from "./utils"
export default {
  components: {},
  props: {},
  data() {
    return {
      dialogShow: false,
      formType: "",
      displayList: [],
      activeName: "info",
      displayInfo: {
        config: {
          displayParams: {
            autoPlay: true,
            autoSlide: 10,
            transitionStyle: "none",
            transitionSpeed: "default",
          },
        },
        avatar: "",
        description: "",
        id: 0,
        name: "",
        publish: true,
      },
      rules: {
        name: [ { required: true, message: "名称不能为空", trigger: "blur" } ],
      },
    }
  },
  computed: {
    env(){
      return process.env.NODE_ENV
    },
    origin(){
      return window.location.origin
    }
  },
  created() {
    this.getdisplayList()
  },
  mounted() {},
  watch: {},
  methods: {
    // 显示dialog
    showPortalForm(formType, displayItem) {
      this.dialogShow = true
      this.formType = formType
      if (formType === "set") {
        this.displayInfo = { ...displayItem }
      } else if (formType === "copy") {
        this.displayInfo = { ...displayItem, name: displayItem.name + "_copy" }
      }
    },
    // 提交保存
    async handleSave() {
      // 新增&复制
      if (this.formType === "add" || this.formType === "copy") {
        const {
          data: { id: displayId },
        } = await this.$httpBi.display.addDisplay({
          ...this.displayInfo,
          config: JSON.stringify(this.displayInfo.config),
        })
        this.handleCancel()
        this.$message.success("新增成功")
        this.getdisplayList()
        // 添加第一个默认slide
        this.addSlide(displayId)
      } else {
        // 设置
        const { code } = await this.$httpBi.display.updDisplay(this.displayInfo)
        if (code === 200) {
          this.handleCancel()
          this.$message.success("修改成功")
          this.getdisplayList()
        }
      }
    },
    // 删除
    async handleDel(id) {
      const { code } = await this.$httpBi.display.delDisplay({ id })
      if (code === 200) {
        this.$message.success("删除成功")
        this.getdisplayList()
      }
    },
    // 取消
    handleCancel() {
      Object.assign(this.$data.displayInfo, this.$options.data().displayInfo)
      this.dialogShow = false
    },
    // 获取大屏列表
    async getdisplayList() {
      const { data } = await this.$httpBi.display.getDisplayList()
      this.displayList = data
    },
    // 添加slide
    async addSlide(displayId) {
      const slide = {
        displayId,
        index: 0,
        config: JSON.stringify({ slideParams: getDefaultSlideParams() }),
      }
      await this.$httpBi.display.addDisplaySlide(slide)
    },
    // 跳转
    goDashboard(displayItem) {
      // this.$router.push(`PortalDetail?isFullPage=true&id=${displayItemid}`);
      this.$router.push({
        name: "displayEditor",
        query: {
          isFullPage: true,
          displayId: displayItem.id,
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.unit {
  height: 120px;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center center;
  margin-bottom: 20px;
  border-radius: 2px;
  transform: translate3d(0, 0, 0);
  transition: transform 200ms linear, box-shadow 200ms linear,
    background-size 500ms linear;
  position: relative;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  cursor: pointer;

  &:hover {
    background-size: 110%;
    transform: translate3d(0, -5px, 0);
    box-shadow: 0 7px 21px 0 rgba(0, 0, 0, 0.15);
  }

  &.editing {
    opacity: 0.3;
  }

  header {
    background-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.35) 0%,
      rgba(0, 0, 0, 0) 100%
    );
    border-radius: 10px 10px 0 0;
  }

  .title {
    padding: 12px 80px 0 12px;
    color: #fff;
    font-size: 1em;
    font-weight: normal;
  }

  .content {
    padding: 8px 12px 24px;
    color: #fff;
    font-size: 0.65em;
  }

  .portalActions {
    position: absolute;
    right: 8px;
    top: 8px;
    color: #fff;
    .edit {
      margin-right: 7px;
    }
    .copy {
      margin-right: 7px;
    }
  }

  .central {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &:hover {
      color: skyblue;
      cursor: pointer;
    }
  }
}
</style>
