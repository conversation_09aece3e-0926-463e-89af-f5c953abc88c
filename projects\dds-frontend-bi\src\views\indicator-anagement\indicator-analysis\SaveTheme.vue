<template>
  <el-dialog
    title="保存为分析主题"
    :border="true"
    :visible.sync="dialogShow"
    @close="handleCancel"
    width="550px"
  >
    <el-form
      :model="themeInfo"
      ref="ruleForm"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="主题名称" prop="themeName">
        <el-input
          v-model="themeInfo.themeName"
          placeholder="请输入主题名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="仅我可见">
        <el-radio-group v-model="themeInfo.onlyCreater">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogShow = false">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    filterDims: {
      type: Array,
      default: () => [],
    },
    indicators: {
      type: Array,
      default: () => [],
    },
    analyzeDims: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogShow: false,
      themeInfo: {
        themeName: "",
        onlyCreater: 0,
      },
      rules: {
        themeName: [
          { required: true, message: "请输入主题名称", trigger: "change" },
        ],
      },
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    open() {
      this.dialogShow = true
    },
    async handleSave() {
      const params = {
        ...this.themeInfo,
        id: null,
        indicators: this.indicators.map((item) => ({
          ...item,
          id: null,
          themeCode: null,
        })),
        analyzeDims: this.analyzeDims.map((item) => {
          return {
            ...item,
            id: null,
            themeCode: null,

            dimList: item.dimList.map((e) => {
              return {
                ...e,
                id: null,
                themeCode: null,
                clusterCodes:
                  (e.clusterCodes && e.clusterCodes.join(",")) || "",
                dimType: 1,
                dimValList:
                  e.enableClustering === 1
                    ? Object.entries(e.clusterNameMapValue).map(
                        ([key]) => ({
                          dimVal: key,
                          dimCol: e.dimCol,
                        })
                      )
                    : e.dimValList.map((el) => ({
                        dimVal: el.valueCode,
                        dimCol: e.dimCol,
                      })),
              }
            }),
          }
        }),
        filterDims: this.filterDims.map((item) => ({
          ...item,
          id: null,
          themeCode: null,
          dimType: 2,
          clusterCodes:
            (item.clusterCodes && item.clusterCodes.join(",")) || "",

          dimValList:
            item.enableClustering === 1
              ? Object.entries(item.clusterNameMapValue).map(
                  ([key]) => ({
                    dimVal: key,
                    dimCol: item.dimCol,
                  })
                )
              : item.dimValList.map((el) => ({
                  dimVal: el.valueCode,
                  dimCol: item.dimCol,
                })),
        })),
      }

      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            // 先调用检查接口
            const checkResult =
              await this.$httpBi.analyzeTheme.checkAnalyzeTheme(params)
            const { code, message } = checkResult.data

            if (code === 1) {
              // 直接保存
              await this.$httpBi.analyzeTheme.saveAnalyzeTheme(params)
              this.dialogShow = false
              this.$message.success("保存成功")
            } else if (code === 2) {
              // 弹出确认框
              await this.$confirm("已存在重名主题,是否覆盖？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
              await this.$httpBi.analyzeTheme.saveAnalyzeTheme(params)
              this.dialogShow = false
              this.$message.success("保存成功")
            } else if (code === 3) {
              this.$message.error(message)
            }
          } catch (error) {
            console.error("保存主题失败:", error)
          }
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="scss"></style>
