<template>
  <div>
    <el-select
      popper-class="dark"
      v-model="value"
      placeholder="请选择"
      :popper-append-to-body="false"
      @change="change"
      :multiple="isMultiple"
      size="mini"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.text"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [ String , Array ],
    },
    isMultiple: {
      type: Boolean,
    },
    item: {
      type: Object,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    change() {
      this.$emit("update:value", this.value)
      this.$emit("change", { [this.item.key]: this.value })
    },
  },
}
</script>

<style scoped lang="scss">

</style>
