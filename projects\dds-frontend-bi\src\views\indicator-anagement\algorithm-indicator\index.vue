<template>
  <div>
    <div class="btns" v-if="!showDetail">
      <div class="left">
        <el-radio-group v-model="type">
          <el-radio-button label="tools">工具算法</el-radio-button>
          <el-radio-button label="indicator">指标算法</el-radio-button>
        </el-radio-group>
      </div>
      <div class="right">
        <!-- <el-button
          type="primary"
          @click="$router.push('/ddsMain/system/pushPath')"
        >
          环境配置
        </el-button> -->
        <el-button
          type="primary"
        >
          环境配置
        </el-button>
      </div>
    </div>
    <!-- <IndicatorWarning v-if="type === 'indicator'" />
    <ThemeWarning v-else /> -->
    <components :is="type" :show-detail.sync="showDetail"></components>
  </div>
</template>

<script>
// 主题预警
import Tools from "./tools/index.vue"
import Indicator from "./indicator/index.vue"
export default {
  components: {
    Tools,
    Indicator
    // ThemeWarning,
    // IndicatorWarning
  },
  props: {},
  data() {
    return {
      type: "tools",
      showDetail:false
    }
  },
  computed: {},
  created() {
    const { type } = this.$route.query
    if (type) {
      this.type = type
    }
  },
  mounted() {},
  watch: {},
  methods: {}
}
</script>

<style scoped lang="scss">
.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  margin-top: 20px;
}
</style>
