<template>
  <BaseDataConfigSection v-bind="$attrs" v-on="$listeners" :action="['filters']"> </BaseDataConfigSection>
</template>

<script>
import BaseDataConfigSection from "./BaseDataConfigSection.vue"
export default {
  components: {
    BaseDataConfigSection,
  },
  props: {},
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss"></style>
