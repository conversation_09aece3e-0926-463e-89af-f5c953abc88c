import ChartTypes from './ChartTypes'
import {
  PIVOT_DEFAULT_AXIS_LINE_COLOR,
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  PIVOT_DEFAULT_HEADER_BACKGROUND_COLOR
} from '@/globalConstants'


const table = {
  id: ChartTypes.Table,
  name: 'table',
  title: '表格',
  icon: 'chart_table',

  coordinate: 'other',
  rules: [ { dimension: [ 0, 9999 ], metric: [ 0, 9999 ] } ],
  data: [
    {
      title: '维度',
      type: 'category',
      name: "cols"

    },
    {
      title: '指标',
      type: 'value',
      name: "metrics"
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
   

  ],
  style: {
    table: {
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR,
      lineStyle: 'solid',
      lineColor: PIVOT_DEFAULT_AXIS_LINE_COLOR,
      headerBackgroundColor: PIVOT_DEFAULT_HEADER_BACKGROUND_COLOR,

      headerConfig: [],
      columnsConfig: [],
      leftFixedColumns: [],
      rightFixedColumns: [],
      headerFixed: true,
      autoMergeCell: false,
      bordered: true,
      size: 'small',
      withPaging: true,
      pageSize: '20',
      withNoAggregators: true
    },
    spec: {

    }
  }
}

export default table
