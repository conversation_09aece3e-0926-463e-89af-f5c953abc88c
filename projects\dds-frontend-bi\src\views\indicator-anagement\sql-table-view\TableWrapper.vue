<template>
  <div class="table-wrapper">
    <!-- 如果是根源于不渲染tip -->
    <div
      v-if="!root"
      class="table-link-tip"
      @click="handleRelate"
      :style="{
        top: getTop(index, 43, parTop) + 'px',
        left: item.level * 180 + (item.level - 1) * 90 + 'px'
      }"
    >
      <!-- 图标 -->
      <div class="table-link-icon-wrapper">
        <div class="relation"></div>
      </div>
      <!-- 横线 -->
      <div
        class="table-link-line horizontal"
        :style="{
          width: index ? '72px' : '90px',
          top: '14px'
        }"
      ></div>
      <!-- 竖线 -->
      <div
        class="table-link-line vertical"
        :style="{
          height: getTop(index, 43, 0) + 'px',
          bottom: '14px'
        }"
      ></div>
    </div>
    <!-- 表信息 -->
    <div
      class="table-info"
      @click="handleFieldInfo"
      :style="{
        top: getTop(index, 42, parTop) + 'px',
        left: item.level * 270 + 'px'
      }"
    >
      <div class="text">
        {{ item.target }}
      </div>
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          <i class="el-icon-more-outline"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            :command="{
              type: 'del',
              item
            }"
            icon="el-icon-delete"
          >
            删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <template v-if="index == tree.length - 1 && !root">
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        show-vertical
        :level="item.level"
        :source="item.source"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop) + 42"
        :tip-top="getTop(index, 43, parTop) + 43"
        :tip-height="getTop(index, 43, 0) + 43"
      />
    </template>
    <!-- 如果有子元素递归渲染 -->
    <template v-if="item.children && item.children.length">
      <table-wrapper
        v-for="(e, i) in item.children"
        :item="e"
        :key="i"
        :tree="item.children"
        :dargging="dargging"
        :index="i"
        v-on="$listeners"
        :par-top="getTop(index, 42, parTop)"
      />
    </template>
    <template v-else>
      <!-- :style="{
          position: 'absolute',
          top: getTop(index, 42, parTop) + 'px',
          left: (item.level + 1) * 270 + 'px',
        }" -->
      <DropBox
        :source="item.target"
        :level="item.level + 1"
        :dargging="dargging"
        v-on="$listeners"
        :info-top="getTop(index, 42, parTop)"
        :tip-top="getTop(index, 43, parTop)"
      />
    </template>
  </div>
</template>

<script>
import DropBox from "./DropBox"
export default {
  name: "table-wrapper",
  components: { DropBox },
  props: {
    item: {
      type: Object
    },
    index: {
      type: Number
    },
    tree: {
      type: Array
    },
    parTop: {
      type: Number,
      default: 0
    },
    root: {
      type: Boolean,
      default: false
    },
    dargging: {
      type: Boolean,
      default: false
    },
    drawer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      arr: []
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 表字段详情
    handleFieldInfo() {
      this.$emit("fieldInfo", this.item)
    },

    getTop(index, num, top = 0) {
      if (index) {
        let height = 0

        for (let i = 0; i < index; i++) {
          height += this.findMaxLength(this.tree[i], this.tree) * num
        }

        return height + top
      } else {
        console.log(index * num + top)

        return index * num + top
      }
    },

    findMaxLength(node) {
      if (!node.children || node.children.length === 0) {
        return 1 // 如果没有子节点或者子节点数组为空，则返回 0
      }

      let total = 0 // 当前节点的 children 数组长度

      // 遍历子节点，找到最长的 children 数组长度
      // if (node.children.length > 1) {
      //   total += 1
      // }
      node.children.forEach((child, index) => {
        if (node.children.length > 1) {
          total += this.findMaxLength(child, node)
        } else {
          total += 1
        }
        if (index === node.children.length - 1) {
          total += 1
        }
      })
      console.log(this.arr, "this.arr")
      return total
    },

    handleRelate() {
      this.$emit("handleRelate", this.item)
    },
    handleCommand(command) {
      if (command.type === "del") {
        this.$emit("delDropBox", command.item)
      }
    },
    // 查找树形结构最后面的节点的长度
    findLastLength(node, parent) {
      if (!node.children || node.children.length === 0) {
        this.arr.push(parent)
        return 1
      }
      let total = 0
      node.children.forEach(child => {
        total += this.findLastLength(child, node.children)
      })
      console.log(this.arr, "this.arr")
      return total
    }
  }
}
</script>

<style scoped lang="scss">
.table-wrapper {
  position: absolute;

  .table-link-tip {
    width: 90px;
    height: 28px;
    position: absolute;
    align-items: center;
    display: flex;
    justify-content: center;
    cursor: pointer;

    .table-link-line {
      position: absolute;
      z-index: 2;
      background: #c1c1c1;
    }

    .table-link-line.vertical {
      width: 1px;
      left: 18px;
    }

    .table-link-line.horizontal {
      right: 0;
      height: 1px;
    }
  }

  .table-link-tip:hover .table-link-line {
    background: #2153d4;
  }

  .table-info {
    height: 28px;
    width: 180px;
    background: #fff;
    position: absolute;
    color: #000;
    cursor: pointer;
    padding: 1px 1px 1px 0;
    line-height: 28px;
    border-right: 1px solid transparent;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    border-left: 2px solid #2153d4;
    display: flex;
    .text {
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    // .dropdown {
    //   display: none;
    // }
  }

  .table-info:hover {
    border-right: 1px solid #2153d4;
    border-top: 1px solid #2153d4;
    border-bottom: 1px solid #2153d4;
  }
}
.relation {
  margin: 0 9px;
  width: 16px;
  height: 8px;
  background: url("~@/assets/images/relation.png") no-repeat center;
  background-size: cover;
}
</style>
