<template>
  <div>
    <div class="paneBlock">
      <h4>饼图</h4>
      <div class="blockBody">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="10">
            <el-checkbox v-model="specForm.circle" @change="changeSpecStyle">
              圆环图
            </el-checkbox>
          </el-col>
          <el-col span="12">
            <el-checkbox v-model="specForm.roseType" @change="changeSpecStyle">
              玫瑰图
            </el-checkbox>
          </el-col>
        </el-row>
      </div>
      <div class="blockBody" style="margin-bottom: 20px">
        <el-row class="blockRow">
          <el-col span="24">左右边距</el-col>
          <el-col span="14">
            <el-slider
              style="width: 100%"
              v-model="specForm.x"
              :format-tooltip="(val) => val + '%'"
              @change="changeSpecStyle"
            ></el-slider>
          </el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              max="100"
              controls-position="right"
              placeholder=""
              v-model="specForm.x"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row class="blockRow">
          <el-col span="24">上下边距</el-col>
          <el-col span="14">
            <el-slider
              style="width: 100%"
              v-model="specForm.y"
              @change="changeSpecStyle"
              :format-tooltip="(val) => val + '%'"
            ></el-slider>
          </el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              max="100"
              controls-position="right"
              placeholder=""
              v-model="specForm.y"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
      </div>
      <div class="blockBody" style="margin-bottom: 20px" v-if="specForm.circle">
        <el-row class="blockRow">
          <el-col span="24">外圆环大小</el-col>
          <el-col span="14">
            <el-slider
              style="width: 100%"
              v-model="specForm.outerRadius"
              :format-tooltip="(val) => val + '%'"
              @change="changeSpecStyle"
            ></el-slider>
          </el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              max="100"
              controls-position="right"
              placeholder=""
              v-model="specForm.outerRadius"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row class="blockRow">
          <el-col span="24">内圆环大小</el-col>
          <el-col span="14">
            <el-slider
              style="width: 100%"
              v-model="specForm.innerRadius"
              @change="changeSpecStyle"
              :format-tooltip="(val) => val + '%'"
            ></el-slider>
          </el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              max="100"
              controls-position="right"
              placeholder=""
              v-model="specForm.innerRadius"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
      </div>
      <div class="blockBody" style="margin-bottom: 20px" v-else>
        <el-row class="blockRow">
          <el-col span="24">饼图大小</el-col>
          <el-col span="14">
            <el-slider
              style="width: 100%"
              v-model="specForm.radius"
              :format-tooltip="(val) => val + '%'"
              @change="changeSpecStyle"
            ></el-slider>
          </el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              max="100"
              controls-position="right"
              placeholder=""
              v-model="specForm.radius"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
      </div>
      <div class="blockBody" style="margin-bottom: 20px">
        <el-row
          align="middle"
          class="blockRow">
          <el-col span="14">边框间距</el-col>
          <el-col span="10">
            <el-input-number
              min="0"
              controls-position="right"
              placeholder=""
              v-model="specForm.borderWidth"
              @change="changeSpecStyle"
            ></el-input-number>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      specForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.specForm = this._.cloneDeep(this.chartData.chartStyles.spec)
      console.log(this.specForm, "this.specForm")
    },
    changeSpecStyle() {
      this.$emit("changeStyle", "spec", this.specForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
