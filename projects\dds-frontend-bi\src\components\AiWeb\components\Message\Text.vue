<template>
  <div class="text-black overflow-auto w-100" :class="wrapClass">
    <Loding v-if="loading" />
    <template v-else>
      <div ref="textRef" class="leading-relaxed break-words px-3 py-2">
        <!-- <div
          v-if="!inversion && preText == '我们院系今年的科研经费总额是多少'"
          class="bg-white rounded-lg"
        >
          <textDemo />
        </div>
        <div
          v-if="!inversion && preText.includes('生师比')"
          class="bg-white rounded-lg"
        >
          <textDemo1 />
        </div>
        <div
          v-if="!inversion && preText.includes('职称分布')"
          class="bg-white rounded-lg"
        >
          <pieDemo />
        </div> -->
        <div
          v-if="!inversion"
          class="bg-white rounded-lg"
        >
          <chartDemo :chartData="text" :duration="duration" />
        </div>
         <!-- <div v-else-if="!inversion" class="markdown-body" v-html="formatText" /> -->
        <div
          v-else
          class="whitespace-pre-wrap text-white"
          v-text="text"
        /> 
      </div>
    </template>
  </div>
</template>

<script>
// import MarkdownIt from 'markdown-it'
// import hljs from 'highlight.js'
// import mdKatex from '@traptitech/markdown-it-katex'

// import linkAttributes from 'markdown-it-link-attributes'
import Loding from './Loding.vue'
// import textDemo from './textDemo.vue'
// import textDemo1 from './textDemo1.vue'

// import pieDemo from './pieDemo.vue'
import chartDemo from './chartDemo.vue'
export default {
  components: { Loding, chartDemo,  },
  props: {
    inversion: {
      type: Boolean,
      default: false
    },
    error: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    preText: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    duration: {
      type: Number,
      default: 0
    },
  },
  data () {
    return {
      mdi: null
    }
  },
  computed: {
    wrapClass () {
      return [
        'text-wrap',
        'min-w-[20px]',
        'rounded-md',
        // this.inversion ? "bg-red-600" : "bg-yellow-400",
        this.inversion ? 'bg-blue-500' : 'bg-gray-100',
        this.inversion ? 'user' : 'ai',

        { 'text-red-500': this.error }
      ]
    },
    // formatText () {
    //   const value = this.text ?? ''
    //   if (!this.inversion) return this.mdi.render(value)
    //   return value
    // }
  },
  created () {
    // this.mdi = new MarkdownIt({
    //   linkify: true,
    //   highlight: (code, language) => {
    //     const validLang = !!(language && hljs.getLanguage(language))
    //     if (validLang) {
    //       const lang = language ?? ''
    //       return this.highlightBlock(
    //         hljs.highlight(lang, code, true).value,
    //         lang
    //       )
    //     }
    //     return this.highlightBlock(hljs.highlightAuto(code).value, '')
    //   }
    // })

    // // 使用 linkAttributes 插件，并配置目标属性和 rel 属性
    // this.mdi.use(linkAttributes, {
    //   pattern: /https?:\/\/(www\.)?/i, // 匹配所有 HTTP/HTTPS 链接
    //   attrs: {
    //     target: '_blank', // 设置所有链接在新标签页打开
    //     rel: 'noopener' // 增加这个属性可以提高安全性
    //   }
    // })

    // this.mdi.use(mdKatex, {
    //   blockClass: 'katexmath-block rounded-md p-[10px]',
    //   errorColor: ' #cc0000'
    // })
  },
  mounted () {},
  watch: {},
  methods: {
    highlightBlock (str, lang) {
      return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
    }
  }
}
</script>

<style lang="scss">
.text-black.ai {
  border-radius: 6px;
  background-image: linear-gradient(#fff, #fff),
    // 跟背景色保持一致，根据实际情况修改
    linear-gradient(
        180deg,
        rgba(67, 192, 255, 1),
        rgba(120, 140, 255, 1)
      ); // 取border-image的渐变色，按实际来修改
  background-origin: border-box;
  background-clip: content-box, border-box;
  padding: 2px;
}
.markdown-body {
  background-color: transparent;
  font-size: 14px;

  p {
    white-space: pre-wrap;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }

  pre code,
  pre tt {
    line-height: 1.65;
  }

  .highlight pre,
  pre {
    background-color: #fff;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;
        &:hover {
          color: #65a665;
        }
      }
    }
  }
}

html.dark {
  .highlight pre,
  pre {
    background-color: #282c34;
  }
}
</style>
