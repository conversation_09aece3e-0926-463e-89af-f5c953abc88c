<template>
  <div class="chart-radar" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartRadar',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    indicator: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    angleField: {
      type: String,
      default: 'name'
    },
    radiusField: {
      type: String,
      default: 'value'
    },
    maxField: {
      type: String,
      default: 'max'
    },
    seriesName: {
      type: String,
      default: '数据'
    },
    // 样式配置
    color: {
      type: String,
      default: '#13c2c2'
    },
    colors: {
      type: Array,
      default: () => ['#13c2c2', '#1890ff', '#52c41a', '#faad14', '#f5222d']
    },
    // 雷达图配置
    radarShape: {
      type: String,
      default: 'polygon' // 'polygon', 'circle'
    },
    splitNumber: {
      type: Number,
      default: 5
    },
    radius: {
      type: [String, Number],
      default: '75%'
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    showSplitLine: {
      type: Boolean,
      default: true
    },
    showSplitArea: {
      type: Boolean,
      default: false
    },
    // 区域填充
    showArea: {
      type: Boolean,
      default: true
    },
    areaOpacity: {
      type: Number,
      default: 0.3
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      // 判断使用哪种数据格式
      const useNewFormat = this.indicator.length > 0 && this.series.length > 0

      let indicatorConfig, radarData, legendData

      if (useNewFormat) {
        // 新格式：indicator + series
        indicatorConfig = this.indicator.map(item => ({
          name: item.name,
          max: item.max || 1000, // 默认最大值
          axisLabel: item.axisLabel || { show: true }
        }))

        // 从 series 中提取数据
        radarData = this.series[0].data || []
        legendData = radarData.map(item => item.name)
      } else {
        // 旧格式：chartData + angleField + radiusField
        indicatorConfig = this.chartData.map(item => ({
          name: item[this.angleField],
          max: item[this.maxField] || 100
        }))

        radarData = [{
          value: this.chartData.map(item => item[this.radiusField]),
          name: this.seriesName
        }]
        legendData = [this.seriesName]
      }
      
      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: this.showTooltip,
          trigger: 'item',
          formatter: (params) => {
            let result = `${params.name}<br/>`
            params.value.forEach((value, index) => {
              result += `${indicatorConfig[index].name}: ${value}<br/>`
            })
            return result
          }
        },
        legend: {
          show: this.showLegend,
          data: legendData,
          top: 10,
          right: 20,
          textStyle: {
            color: '#666666',
            fontSize: 12
          }
        },
        radar: {
          indicator: indicatorConfig,
          shape: this.radarShape,
          radius: this.radius,
          splitNumber: this.splitNumber,
          center: ['50%', '50%'],
          name: {
            textStyle: {
              color: '#666666',
              fontSize: 12
            }
          },
          splitLine: {
            show: this.showSplitLine,
            lineStyle: {
              color: '#d9d9d9',
              width: 1,
              type: 'solid'
            }
          },
          splitArea: {
            show: this.showSplitArea,
            areaStyle: {
              color: ['rgba(250, 250, 250, 0.3)', 'rgba(200, 200, 200, 0.3)']
            }
          },
          axisLine: {
            lineStyle: {
              color: '#d9d9d9'
            }
          }
        },
        series: [
          {
            name: useNewFormat ? '' : this.seriesName,
            type: 'radar',
            data: radarData,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              color: this.color,
              width: 2
            },
            itemStyle: {
              color: this.color
            },
            areaStyle: this.showArea ? {
              color: this.hexToRgba(this.color, this.areaOpacity)
            } : null,
            label: {
              show: this.showLabel,
              color: '#666666',
              fontSize: 12,
              formatter: (params) => {
                return params.value
              }
            },
            emphasis: {
              lineStyle: {
                width: 4
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ],
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    hexToRgba(hex, alpha) {
      const r = parseInt(hex.slice(1, 3), 16)
      const g = parseInt(hex.slice(3, 5), 16)
      const b = parseInt(hex.slice(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-radar {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
