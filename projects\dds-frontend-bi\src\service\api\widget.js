import service from '../base'
import config from '../config'

/**
 * 图表
 */
export default {
 
  getPage(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/getPage',
      method: 'post',
      data
    })
  },
  getAll(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/getAll',
      method: 'get',
      data
    })
  },
 
  getOne(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/getOne',
      method: 'get',
      params
    })
  },
  create(data){
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/create',
      method: 'post',
      data
    })
  },
  update(data){
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/update',
      method: 'post',
      data
    })
  },
  delete(params){
    return service({
      url: config.VUE_MODULE_DDS_BI + 'bi/widget/delete',
      method: 'delete',
      params
    })
  },
    
}

