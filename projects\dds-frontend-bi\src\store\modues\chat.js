let chat = {
  namespaced: true,
  state: {
    ...getLocalState()
  },
  mutations: {
    toggleNetwork(state) {
      state.network = !state.network
      // this.reloadRoute()
      // if (this.getChatHistoryByCurrentActive) {
      // this.getChatHistoryByCurrentActive.network = !this.getChatHistoryByCurrentActive.network;
      // }
    },
    addHistory(state, { history, chatData }) {
      state.history.unshift(history)
      state.chat.unshift({ uuid: history.uuid, data: chatData })
      state.active = history.uuid
      state.reloadRoute(history.uuid)
    },
    updateHistory(state, { uuid, edit }) {
      const index = state.history.findIndex(item => item.uuid === uuid)
      if (index !== -1) {
        state.history[index] = { ...state.history[index], ...edit }
        state.recordState()
      }
    }
  },
  actions: {
    async deleteHistory(index) {
      this.history.splice(index, 1)
      this.chat.splice(index, 1)

      if (this.history.length === 0) {
        this.active = null
        this.reloadRoute()
        return
      }

      if (index > 0 && index <= this.history.length) {
        const uuid = this.history[index - 1].uuid
        this.active = uuid
        this.reloadRoute(uuid)
        return
      }

      if (index === 0) {
        if (this.history.length > 0) {
          const uuid = this.history[0].uuid
          this.active = uuid
          this.reloadRoute(uuid)
        }
      }

      if (index > this.history.length) {
        const uuid = this.history[this.history.length - 1].uuid
        this.active = uuid
        this.reloadRoute(uuid)
      }
    }
  },
  getters: {
    getEnabledNetwork(state) {
      return state.network === true
    },
    getChatHistoryByCurrentActive(state) {
      const index = state.history.findIndex(item => item.uuid === state.active)
      if (index !== -1) return state.history[index]
      return null
    },

    getChatByUuid(state) {
      return uuid => {
        if (uuid) return state.chat.find(item => item.uuid === uuid)?.data ?? []
        return state.chat.find(item => item.uuid === state.active)?.data ?? []
      }
    }
  }
}
export default chat
