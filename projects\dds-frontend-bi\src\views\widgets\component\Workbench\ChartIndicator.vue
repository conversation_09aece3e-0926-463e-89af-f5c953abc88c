
<script>
import { checkChartEnable } from "../util"
export default {
  functional: true,
  props: {
    chartInfo: {
      type: Object,
    },
    DimetionsAndMetricsCount: {
      type: Array,
    },
    selectedChart: {
      type: Object,
    },
  },
  render(h, context) {
    const { chartInfo, DimetionsAndMetricsCount, selectedChart } =
      context.props
    const { title, icon, rules, id } = chartInfo
    const [ dcount, count ] = DimetionsAndMetricsCount

    const contents = rules.map(({ dimension, metric }, ruleIdx) => {
      const subContents = []
      if (Array.isArray(dimension)) {
        subContents.push(
          `${dimension[0]}个 到 ${
            dimension[1] === 9999 ? "多" : dimension[1]
          }个 维度`
        )
      } else {
        subContents.push(`${dimension}个 维度`)
      }
      if (Array.isArray(metric)) {
        subContents.push(
          `${metric[0]}个 到 ${metric[1] === 9999 ? "多" : metric[1]}个 指标`
        )
      } else {
        subContents.push(`${metric}个 指标`)
      }
      if (rules.length > 1) {
        return <p key={ruleIdx}>{subContents.join("，")}</p>
      }
      return subContents.map((item, idx) => (
        <p key={`${ruleIdx}_${idx}`}>{item}</p>
      ))
    })
    const enable = checkChartEnable(dcount, count, chartInfo)
    const active = selectedChart.id === id
    const overlay = (
      <div>
        <p>{title}</p>
        {contents}
      </div>
    )
    return (
      <el-tooltip placement="bottom">
        <div slot="content" style="lineHeight: 20px">
          {overlay}
        </div>
        <span
          class={[ enable ? "" : "disabledIcon", active ? "activedIcon" : "" ]}
          onClick={() => context.listeners.chartSelect(enable, chartInfo)}
        >
          <svg-icon
            class="icon"
            icon-class={enable ? icon : icon + "_disabled"}
          />
        </span>
      </el-tooltip>
    )
  },
}
</script>

