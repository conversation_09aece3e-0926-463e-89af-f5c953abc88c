<template>
  <div class="source-data">
    <CommonTable
      :page.sync="page"
      :table-data="tableData"
      :show-batch-tag="false"
      :loading="loading"
      :table-columns.sync="tableColumn"
      @onload="getTableData"
      @handleExport="handleExport"
      ref="CommonTable"
    ></CommonTable>
  </div>
</template>

<script>
import CommonTable from "@/components/CommonTable.vue"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import { exportExcel } from "@/utils/index"
export default {
  name: "IndicatorSourceData",
  components: { CommonTable },
  props: {},
  data() {
    return {
      name: "",
      comment: "",
      loading: false,
      tableData: [],
      tableColumn: [],
      page: {
        total: 1,
        currentPage: 1,
        pageSize: 10
      }
    }
  },

 
  mounted() {
    this.comment = this.parent.indicatorData.dataSource[0].dataSourceName
    this.name = this.parent.indicatorData.dataSource[0].dataSource
    this.getTableData()
  },
  inject: ["parent"],
  methods: {
    async getTableData() {
      this.loading = true

      const { data } = await this.$httpBi.api.paramPost(
        "/indicator/dataSource/getIndicatorTableData",
        {
          name: this.name,
          comment: this.comment,
          currentPage: this.page.currentPage,
          pageSize: this.page.pageSize
        }
      )

      if (data) {
        if (!this.tableColumn.length) {
          this.tableColumn = data.tableColumn.map(item => ({
            label: item.alias,
            prop: item.name,
            visible: true,
            sortable: false
          }))
        }
        this.page.total = data.tableData.totalCount
        this.tableData = data.tableData.list || []
      } else {
        this.page.total = 0
        this.tableData = []
      }

      console.log(this.tableData, "this.tableData")
      this.loading = false
    },
    changeDataSoure() {
      this.comment = this.parent.indicatorData.dataSource.find(
        item => item.dataSource === this.name
      ).dataSourceName
      this.page.currentPage = 1
      this.getTableData()
    },
    handleExport(selection) {
      if (!selection.length) {
        // 导出
        exportExcel(
          "/api/dds-server-bi/indicator/dataSource/exportIndicatorTableData",
          {
            name: this.name,
            comment: this.comment,
          }
        )
        return 
      }
      const header = Object.fromEntries(
        this.tableColumn.map(item => [item.prop, item.label])
      )
      jsonToSheetXlsx({
        data: selection,
        filename: this.comment,
        header,
        autoWidth: false
      })
    }
  }
}
</script>

<style scoped lang="scss">

.info-item{
    display: flex;
    margin-bottom: 10px;
    span{
        color: #222;
    }   
    
}
</style>
