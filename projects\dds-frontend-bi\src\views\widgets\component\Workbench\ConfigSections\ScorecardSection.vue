<template>
  <div class="paneBlock">
    <h4>翻牌器</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">标题配置</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">
          <el-input
            v-model="scorecardForm.title"
            size="mini"
            placeholder="请输入名称单位"
            clearable
            @blur="changeScorecardStyle"
            @input="$forceUpdate()"
          />
        </el-col>
      </el-row>

      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeScorecardStyle"
            v-model="scorecardForm.titleFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeScorecardStyle"
            v-model="scorecardForm.titleFontSize"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            v-model="scorecardForm.titleColor"
            @change="changeScorecardStyle"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">头部配置</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            @change="changeScorecardStyle"
            v-model="scorecardForm.headerVisible"
          >
            指标可见
          </el-checkbox>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="字体"
            v-model="scorecardForm.headerFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            @change="changeScorecardStyle"
            v-model="scorecardForm.headerColor"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="前缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.prefixHeader"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            v-model="scorecardForm.prefixHeaderFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            @change="changeScorecardStyle"
            v-model="scorecardForm.prefixHeaderColor"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="后缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.suffixHeader"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            v-model="scorecardForm.suffixHeaderFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            @change="changeScorecardStyle"
            v-model="scorecardForm.suffixHeaderColor"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            @change="changeScorecardStyle"
            v-model="scorecardForm.moveHeaderLeft"
          ></el-input-number>
        </el-col>
        <el-col span="14"> 左偏移百分比% </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            @change="changeScorecardStyle"
            v-model="scorecardForm.moveHeaderTop"
          ></el-input-number>
        </el-col>
        <el-col span="14">上偏移百分比%</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">尾部配置1</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            @change="changeScorecardStyle"
            v-model="scorecardForm.contentVisible"
          >
            指标可见
          </el-checkbox>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="字体"
            v-model="scorecardForm.contentFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            v-model="scorecardForm.contentColor"
            @change="changeScorecardStyle"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="前缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.prefixContent"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            v-model="scorecardForm.prefixContentFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            v-model="scorecardForm.prefixContentColor"
            @change="changeScorecardStyle"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="后缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.suffixContent"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeScorecardStyle"
            v-model="scorecardForm.suffixContentFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            @change="changeScorecardStyle"
            v-model="scorecardForm.suffixContentColor"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            @change="changeScorecardStyle"
            v-model="scorecardForm.moveContentLeft"
          ></el-input-number>
        </el-col>
        <el-col span="14"> 左偏移百分比% </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            @change="changeScorecardStyle"
            v-model="scorecardForm.moveContentTop"
          ></el-input-number>
        </el-col>
        <el-col span="14">上偏移百分比%</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">尾部配置2</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            @change="changeScorecardStyle"
            v-model="scorecardForm.footerVisible"
          >
            指标可见
          </el-checkbox>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="字体"
            v-model="scorecardForm.fontFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            @change="changeScorecardStyle"
            v-model="scorecardForm.footerColor"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="前缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.prefixFooter"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            v-model="scorecardForm.prefixFooterFontFamily"
            size="mini"
            @change="changeScorecardStyle"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            v-model="scorecardForm.prefixFooterColor"
            @change="changeScorecardStyle"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input
            placeholder="后缀"
            @input="changeScorecardStyle"
            v-model="scorecardForm.suffixFooter"
          />
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeScorecardStyle"
            v-model="scorecardForm.suffixFooterFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="4">
          <!-- <el-color-picker
            v-model="scorecardForm.suffixFooterColor"
            @change="changeScorecardStyle"
          ></el-color-picker> -->
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            v-model="scorecardForm.moveFooterLeft"
            @change="changeScorecardStyle"
          ></el-input-number>
        </el-col>
        <el-col span="14"> 左偏移百分比% </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-input-number
            controls-position="right"
            @change="changeScorecardStyle"
            v-model="scorecardForm.moveFooterTop"
          ></el-input-number>
        </el-col>
        <el-col span="14">上偏移百分比%</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">字体大小配置</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            @change="changeScorecardStyle"
            v-model="scorecardForm.fontSizeFixed"
          >
            固定字体大小
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        v-if="scorecardForm.fontSizeFixed"
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
      >
        <el-col span="12">头部字体大小</el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeScorecardStyle"
            v-model="scorecardForm.fontSizeMain"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        v-if="scorecardForm.fontSizeFixed"
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow"
      >
        <el-col span="12">底部字体大小</el-col>
        <el-col span="10">
          <el-select
            placeholder="样式"
            @change="changeScorecardStyle"
            v-model="scorecardForm.fontSizeSub"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <!-- <el-row gutter="8" type="flex" align="middle" class="blockRow">
        <el-col span="24">背景渐变色配置</el-col>
      </el-row>
      <el-row gutter="8" type="flex" align="middle" class="blockRow">
        <el-col span="2" />
        <el-col span="8">from</el-col>
        <el-col span="10">
          <el-color-picker
            v-model="scorecardForm.gradientFromColor"
            @change="changeScorecardStyle"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row gutter="8" type="flex" align="middle" class="blockRow">
        <el-col span="2" />
        <el-col span="8">to</el-col>
        <el-col span="10">
          <el-color-picker
            v-model="scorecardForm.gradientToColor"
            @change="changeScorecardStyle"
          ></el-color-picker>
        </el-col>
      </el-row>
      <el-row gutter="8" type="flex" align="middle" class="blockRow">
        <el-col span="2" />
        <el-col span="8">渐变角度</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            v-model="scorecardForm.gradientDeg"
            @change="changeScorecardStyle"
          ></el-input-number>
        </el-col>
      </el-row> -->
    </div>
  </div>
</template>

<script>
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_CHART_FONT_SIZES,
} from "@/globalConstants"
export default {
  name: "scorecard-section",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      PIVOT_CHART_FONT_FAMILIES,
      PIVOT_CHART_FONT_SIZES,
      scorecardForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.scorecardForm = this._.cloneDeep(
        this.chartData.chartStyles.scorecard
      )
    },
    changeScorecardStyle() {
      this.$emit("changeStyle", "scorecard", this.scorecardForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
