<template>
  <video
    :autoplay="layer.params.autoplay"
    :controls="layer.params.controls"
    :loop="layer.params.loop"
    :src="layer.params.videoAdress"
  >
    您的浏览器不支持 video 标签。
  </video>
</template>

<script>
export default {
  name: "video-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
    }
  },
  computed: {},
}
</script>

<style scoped lang="scss">
video {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
