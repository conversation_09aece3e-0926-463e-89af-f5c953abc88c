<template>
  <el-dialog
    title="颜色配置"
    :visible="colorModalVisible"
    show-close
    width="400px"
    @close="colorModalVisible = false"
  >
    <div class="sketchColor">
      <ul>
        <li
          v-for="(item, index) in distinctvalue"
          :key="index"
          @click="tempIndex = index"
          :class="[tempIndex == index ? 'active' : '']"
        >
          <span :style="{ background: item.color }"></span>
          <span>{{ item.displayName || item.name }}</span>
        </li>
      </ul>
      <sketch-picker
        v-model="colors"
        @input="updateColor"
        :preset-colors="defaultThemeColors"
      ></sketch-picker>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="onCancelColor">取消</el-button>
      <el-button
        type="primary"
        @click="onSaveColor"
        size="mini"
      >
        确定
      </el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { Sketch } from "vue-color"
const defaultTheme = require("@/assets/json/echartsThemes/default.project.json")
const defaultThemeColors = defaultTheme.theme.color
export default {
  components: {
    "sketch-picker": Sketch,
  },
  props: {},
  data() {
    return {
      colorModalVisible: false,
      tempIndex: 0, // 默认当前
      distinctvalue: [],
      colors: "#fff",
      defaultThemeColors,
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 更新颜色
    updateColor(val) {
      this.distinctvalue[this.tempIndex].color = val.hex8
    },
    onSaveColor() {
      console.log(this.distinctvalue, "this.distinctvalue")

      this.$emit("onSaveColor", this.distinctvalue)
    },
    onCancelColor() {
      this.$emit("onCancelColor", this.distinctvalue)
    },
  },
}
</script>

<style scoped lang="scss">
.sketchColor {
  display: flex;
  justify-content: space-between;
  ul {
    width: 150px;
    height: 300px;
    overflow-y: auto;
    li {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 10px;
      font-weight: bold;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      padding: 5px 0 5px 5px;
      cursor: pointer;
      span:nth-child(1) {
        width: 20px;
        height: 20px;
        background-color: skyblue;
        border-radius: 50%;
        margin-right: 5px;
      }
      span:nth-child(2) {
        flex: 1;
      }
      &.active {
        background-color: #f7f7f7;
        color: #1b98e0;
      }
    }
  }
}
</style>
