<template>
  <div class="content-box">
    <Panels
      @onCallback="(params, index) => $emit('onCallback', params, index)"
      :panels="panels"
      style="margin-bottom: 24px"
    />
  </div>
</template>

<script>
import Panels from "@/components/Panels/index.vue"
export default {
  components: {
    Panels
  },
  data() {
    return {
      panels: [
        {
          title: "当前未解决的异常指标数",
          content: 0,
          unit: "",
          id: "",
          subList: [
            {
              subTitle: "当前未解决的异常次数",
              subValue: 0,
              subUnit: ""
            },
          ]
        },
        {
          title: "近30日异常指标数",
          content: 0,
          unit: "",
          subList: [
            {
              subTitle: "近30日异常次数",
              subValue: 0,
              subUnit: ""
            },
          ]
        },
        {
          title: "近30日已解决的异常次数",
          content: 0,

          subList: [
          ]
        },
       
      ]
    }
  },
  inject: ["parent"],
  watch: {
    "parent.data": {
      handler(data) {
        this.panels[0].content = data.unresolvedIndicatorNum
        this.panels[0].subList[0].subValue =data.unresolvedExceptionNum
        this.panels[1].content =data.recentOneMonthIndicatorNum
        this.panels[1].subList[0].subValue = data.recentOneMonthExceptionNum
        this.panels[2].content =data.recentOneMonthResolvedNum
     
      },
      deep: true,
      immediate: true
    }
  },
}
</script>

<style scoped lang="scss">
.page-box {
  width: 100%;
}
</style>
