<template>
  <div class="controls">
    <el-form ref="ruleForm" class="controlForm" v-if="isRouterAlive">
      <el-row :gutter="10">
        <layoutCol
          v-for="(item, index) in controls"
          :width="item.width"
          :key="item.key"
          :type="type"
        >
          <el-form-item
            :label="item.name"
            v-if="
              item.visibility == 'conditional'
                ? isMeet(item.conditions[0], item, index)
                : item.visibility == 'hidden'
                  ? false
                  : true
            "
          >
            <component
              :is="item.type.replace(item.type[0], item.type[0].toUpperCase())"
              :item="item"
              :value.sync="item.defaultValue"
              v-on="$listeners"
              :options="
                ['select', 'radio'].includes(item.type)
                  ? transformOptions(item, selectOptions[item.key])
                  : ''
              "
              :default-value-type="item.defaultValueType"
              :is-multiple="item.multiple"
              :option-type="item.optionType"
              :radio-type="item.radioType"
              :date-format="item.dateFormat"
              :max="item.max"
              :min="item.min"
              :step="item.step"
            />
          </el-form-item>
        </layoutCol>
      </el-row>
    </el-form>
    <div class="actions" v-if="queryMode">
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="manualSearch"
      >
        查询
      </el-button
      >
      <el-button
        type="primary"
        icon="el-icon-refresh"
        @click="reset"
      >
        重置
      </el-button
      >
    </div>
  </div>
</template>

<script>
import InputText from "./InputText"
import Select from "./Select"
import Radio from "./Radio.vue"
import Date from "./Date.vue"
import DateRange from "./DateRange.vue"
import NumberRange from "./NumberRange.vue"
import Slider from "./Slider.vue"
import layoutCol from "./layoutCol.vue"
import { mapGetters } from "vuex"
export default {
  components: {
    InputText,
    Select,
    Radio,
    Date,
    DateRange,
    NumberRange,
    Slider,
    layoutCol,
  },
  props: {
    queryMode: {
      type: Number,
      default: 0,
    },
    controls: {
      type: Array,
      default: () => [],
    },
    selectOptions: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
  },
  
  computed: {
    ...mapGetters({
      currentTheme: "currentTheme",
    }),
  },
  data() {
    return {
      isRouterAlive: true,
    }
  },
  watch: {
    controls: {
      deep: true,
      handler(val) {
        console.log('%cindex.vue line:118 val', 'color: #007acc;', val)
        this.$emit("update:controls", val)
        this.$emit("search")
        // if (!this.queryMode) {
        //   this.$emit("search");
        // }
      },
    },
    selectOptions: {
      deep: true,
      handler() {
        this.reload()
      },
    },
  },
  methods: {
    // 查询
    manualSearch() {
      this.$emit("search")
    },
    // 重置
    reset() {
      this.$emit("reset")
    },
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function() {
        this.isRouterAlive = true
      })
    },
    isMeet({ control, operator, value }) {
      const itemControl = this.controls.find((item) => item.key === control)
      if (operator === "=") {
        return itemControl.defaultValue === value
      } else if (operator === "!=") {
        return itemControl.defaultValue !== value
      }
    },
    transformOptions(control, options) {
      if (!options || !control) return []
      switch (control.type) {
      case "select":
      case "radio":
        switch (control.optionType) {
        case "auto":
          return Object.values(
            options.reduce((obj, o) => {
              Object.values(control.relatedViews).forEach(({ fields }) => {
                const value = o[fields[0]]
                if (value !== void 0 && !obj[value]) {
                  obj[value] = { value, text: value }
                }
              })
              return obj
            }, {})
          )
        case "manual":
          var { valueField, textField } = control
          return Object.values(
            options.reduce((obj, o) => {
              const value = o[valueField]
              if (!obj[value]) {
                obj[value] = {
                  value,
                  text: textField ? o[textField] : o[valueField],
                }
              }
              return obj
            }, {})
          )
        default:
          return options
        }
        // case ControlTypes.TreeSelect:
        //   const { valueField, textField, parentField } = control;
        //   return options.map((o) => ({
        //     id: o[valueField],
        //     pId: o[parentField],
        //     value: o[valueField],
        //     title: textField ? o[textField] : o[valueField],
        //   }));
      default:
        return []
      }
    },
  },
}
</script>

<style scoped lang="scss">
.el-form-item {
  display: flex;
  // margin-right: 0;
  margin-bottom: 24px;
}
::v-deep .el-form-item__label {
  display: flex;
  color: var(--theme-text-color);
}
::v-deep .el-form-item__content {
  margin-left: 0;
  flex: 1 1;
}
.controls {
  padding: 4px 8px 8px;
  box-sizing: border-box;
}
.actions {
  display: flex;
  justify-content: flex-end;
}
</style>
