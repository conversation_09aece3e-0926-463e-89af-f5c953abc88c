<template>
  <div class="indicator-trend">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-row>
        <el-form-item label="时间：">
          <el-date-picker
            v-model="date"
            type="daterange"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            @change="handleDateChange"
          ></el-date-picker>
        </el-form-item>
      </el-row>
      <!-- <el-row type="flex">
        <el-form-item label="过滤维度：">
          <el-button type="primary" icon="el-icon-plus" @click="resize">
            添加
          </el-button>
        </el-form-item>
        <el-form-item label="图表类型：" style="margin-left: auto">
          <el-select v-model="formInline.chartType" placeholder="请选择">
            <el-option
              v-for="item in chartType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row> -->
    </el-form>
    <div class="chart-box">
      <ChartLine
        :chart-data="chartData"
        x-field="label"
        y-field="value"
        :series-name="parent.indicatorData.zbmc"
        y-axis-name=""
        :bar-width="16"
        show-data-zoom
        :show-num="8"
        :unit="unit"
        ref="ChartLine"
      />
    </div>
  </div>
</template>

<script>
import ChartLine from "@/components/Charts/ChartLine"

export default {
  name:"IndicatorTrend",
  components: { ChartLine },
  props: {},
  data() {
    return {
      date: [],
      unit: "",
      formInline: {},
      chartData: [],
      availableDates: [], // 存储可选择的日期数组
      pickerOptions: {
        disabledDate: time => {
          // 将时间戳转换为日期字符串
          const dateStr = this.formatDate(time)
          // 如果日期不在可用日期数组中，则禁用
          return !this.availableDates.includes(dateStr)
        }
      }
    }
  },
  computed: {},
  created() {
    this.initData()
  },
  mounted() {},
  inject: ["parent"],
  methods: {
    // 格式化日期为 yyyy-MM-dd 格式
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, "0")
      const day = String(date.getDate()).padStart(2, "0")
      return `${year}-${month}-${day}`
    },
    async initData() {
      // 先获取可用日期
      this.fetchAvailableDates()
       this.fetchChartData()
    },
    async fetchAvailableDates() {
      try {
        const { code, data } = await this.$httpBi.api.paramGet(
          "/indicator/trends/indicatorCornData",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm
          }
        )
        if (code === 200) {
          this.availableDates = data.map(item => item.rq)
        }
      } catch (error) {
        console.error("获取可用日期失败:", error)
      }
    },
    async handleDateChange(val) {
      if (val) {
        await this.fetchChartData()
      }
    },
    async fetchChartData() {
      try {
        const { code, data } = await this.$httpBi.api.paramPost(
          "/indicator/trends/indicatorTrends",
          {
            indCode: this.parent.indCode,
            indType: this.parent.lxbm,
            startTime: this.date[0],
            endTime: this.date[1]
          }
        )
        if (code === 200) {
          this.chartData = data||[]
          if (this.chartData.length > 0) {
            this.unit = this.chartData[0].dw
          }
        }
      } catch (error) {
        console.error("获取趋势数据失败:", error)
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-row {
  margin-bottom: 0;
}
.indicator-trend {
  width: 100%;
}
.chart-box {
  width: 100%;
  height: 344px;
}
</style>
