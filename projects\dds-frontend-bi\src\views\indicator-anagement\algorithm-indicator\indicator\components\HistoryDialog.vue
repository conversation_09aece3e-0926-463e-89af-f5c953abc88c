<template>
  <el-dialog
    title="运行历史"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    width="850px"
    :close-on-click-modal="false"
  >
    <el-table :data="tableData" style="width: 100%">
      <el-table-column
        prop="col1"
        label="运行日期"
        width="170"
        column-key="col1"
        :filters="getFilters('col1')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col2"
        label="算法版本"
        column-key="col2"
        :filters="getFilters('col2')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col3"
        label="运行类型"
        column-key="col3"
        :filters="getFilters('col3')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col4"
        label="入参"
        column-key="col4"
        :filters="getFilters('col4')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col5"
        label="输出"
        column-key="col5"
        :filters="getFilters('col5')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col6"
        label="运行时长"
        column-key="col6"
        :filters="getFilters('col6')"
        :filter-method="filterHandler"
      ></el-table-column>
      <el-table-column
        prop="col7"
        label="调用算法"
        column-key="col7"
        :filters="getFilters('col7')"
        :filter-method="filterHandler"
      ></el-table-column>
    </el-table>
    <DT-Pagination
      :hidden="pagination.total == 0"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      :current-page="pagination.currentPage"
    />
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      tableData: [
        {
          col1: "2025-05-11 21:23:30",
          col2: "250101-1",
          col3: "试运行",
          col4: "{xxxxx}",
          col5: "{xxxxx}",
          col6: "25s",
          col7: "算法名;算法名"
        },
        {
          col1: "2025-05-06 21:23:30",
          col2: "250101-1",
          col3: "指标计算",
          col4: "{xxxxx}",
          col5: "{xxxxx}",
          col6: "25s",
          col7: "算法名;算法名"
        },
        {
          col1: "2025-05-05 21:23:30",
          col2: "250101-1",
          col3: "指标计算",
          col4: "{xxxxx}",
          col5: "{xxxxx}",
          col6: "25s",
          col7: "算法名;算法名"
        },
        {
          col1: "2025-05-03 21:23:30",
          col2: "250101-1",
          col3: "指标计算",
          col4: "{xxxxx}",
          col5: "{xxxxx}",
          col6: "25s",
          col7: "算法名;算法名"
        },
        {
          col1: "2025-05-01 21:23:30",
          col2: "250101-1",
          col3: "指标计算",
          col4: "{xxxxx}",
          col5: "{xxxxx}",
          col6: "25s",
          col7: "算法名;算法名"
        }
      ],
      pagination: {
        total: 5,
        pageSize: 10,
        currentPage: 1
      }
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    open() {
      this.dialogVisible = true
    },
    filterHandler(value, row, column) {
      const property = column["property"]
      return row[property] === value
    },
    getFilters(property) {
      const data = this.tableData.filter(item => {
        return item[property]
      })
      // 去重
      const result = [...new Set(data.map(item => item[property]))]
      return result.map(item => {
        return {
          text: item,
          value: item
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
}

::v-deep .el-dialog__body {
  padding: 0 24px 20px;
}
</style>
