export default {
  // demo页面A
  pageA: {
    text: '单视图页面',
    request: {
      A: '接口请求A',
      B: '接口请求B',
      C: '接口请求C'
    },
    treeDialog: {
      show: '展示树状选择弹窗',
      title: '树状选择弹窗'
    },
    transfer: {
      show: '展示穿梭框弹窗',
      title: '穿梭框弹窗',
      input: '穿梭框 + 输入框, 输入框可点击'
    },
    svg: '我们提供了一部分svg图标，可直接使用，以下是全部举例',
    svgEle: '当然，你也可以使用element UI提供的icon图标，可自行查阅官网'
  },
  // demo页面B
  pageB: {
    text: '双视图页面',
    showUtils: '通用工具类',
    checkPermission: '判断按钮权限',
    userInfo: '用户信息存放在VueX中 ',
    userInfoText: '已自动获取，可直接使用',
    showUserInfo: '在控制台输出',
    publicLang: '我们提供了大量的公共国际化参数，可直接使用，以下是部分举例',
    publicLangAddress: '数据配置源码在项目最外层common/lang文件夹中'
  }
}