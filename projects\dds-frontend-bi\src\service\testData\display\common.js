// 引入Mock.js
const Mock = require("mockjs")

export default {
  "/common/getTermCode": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      data: [
        { name: "2023-2024学年第一学期", val: "202320241" },
        { name: "2023-2024学年第二学期", val: "202420251" },
        { name: "2024-2025学年第一学期", val: "202520261" },
        { name: "2024-2025学年第二学期", val: "202620271" },
        { name: "2025-2026学年第一学期", val: "202720281" },
        { name: "2025-2026学年第二学期", val: "202820291" },
        { name: "2026-2027学年第一学期", val: "202920301" },
        { name: "2026-2027学年第二学期", val: "203020311" },
        { name: "2027-2028学年第一学期", val: "203120321" },
        { name: "2027-2028学年第二学期", val: "203220331" }
      ]
    }),
  "/common/getWeekList": () =>
    Mock.mock({
      code: 200,
      message: "OK",
      data: [
        {
          name: "第三周",
          val: "3",
          monday: "03-19",
          tuesday: "03-20",
          wednesday: "03-21",
          thursday: "03-22",
          friday: "03-23",
          saturday: "03-24",
          sunday: "03-25"
        },
        {
          name: "第二周",
          val: "2",
          monday: "02-19",
          tuesday: "02-20",
          wednesday: "02-21",
          thursday: "02-22",
          friday: "02-23",
          saturday: "02-24",
          sunday: "02-25"
        },

        {
          name: "第一周",
          val: "1",
          monday: "01-19",
          tuesday: "01-20",
          wednesday: "01-21",
          thursday: "01-22",
          friday: "01-23",
          saturday: "01-24",
          sunday: "01-25"
        }
      ]
    })
}
