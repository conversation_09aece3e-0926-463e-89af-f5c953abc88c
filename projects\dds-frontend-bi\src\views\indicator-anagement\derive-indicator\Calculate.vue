<template>
  <el-dialog
    title="试计算"
    :visible.sync="dialogVisible"
    width="950px"
    top="10vh"
    :before-close="handleClose"
  >
    <div class="sub-title">
      <div class="sub-text">维度</div>
      <div class="sub-line"></div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 8px">
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">
        选择维度
      </div>
      <div class="right" style="flex: 1">
        <el-row :gutter="15">
          <el-col
            :span="10"
            v-for="(item, index) in this.filterDims"
            :key="index"
          >
            <div style="display: flex; align-items: center; margin: 8px 0">
              <span class="dimName" :title="item.dimAndLevelName">
                {{ item.dimAndLevelName }}
              </span>
              <LevelMultipleSelect
                v-model="item.wdzval"
                v-if="item.levelCode && !item.enableClustering"
                :dim-id="item.dimId"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                :props="{
                  label: 'value',
                  value: 'value'
                }"
                ind-type="ps"
                style="width: 205px; margin-left: 6px"
                :level-code="item.levelCode"
              />
              <!-- 使用聚类 -->
              <ClusterMultipleSelect
                v-if="item.levelCode && item.enableClustering"
                :dim-values.sync="item.wdzval"
                v-model="item.clusterCodes"
                value-code="value"
                :is-selected-all="item.isSelectedAll"
                :is-selected-all-name="item.isSelectedAllName"
                :disabled="item.isDisabled"
                :level-code="item.levelCode"
                style="width: 205px; margin-left: 6px"
              />
            </div>
          </el-col>
          <el-col :span="2" style="margin: 8px 0">
            <el-button @click="openDimensionDialog">选择过滤维度</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <div
      style="display: flex; align-items: center; margin-top: 0"
      v-if="sjsForm.isTime"
    >
      <div class="label" style="margin-right: 10px; flex: 0 0 60px">
        时间范围
      </div>
      <el-date-picker
        v-model="sjsForm.timeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
      ></el-date-picker>
      <el-button @click="getCalculationResult" style="margin-left: 70px">
        计算
      </el-button>
    </div>
    <el-button
      v-else
      @click="getCalculationResult"
      style="margin-left: 70px; margin-top: 0"
    >
      计算
    </el-button>

    <div class="sub-title" style="margin-top: 24px">
      <div class="sub-text">试计算结果</div>
      <div class="sub-line"></div>
    </div>
    <el-table
      :data="sjstableData"
      v-loading="loading"
      style="width: 100%"
      :max-height="300"
    >
      <el-table-column
        prop="indName"
        label="指标"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="valShow"
        label="值"
        width="220"
      >
        <template #default="{ row }">
          {{ row.valShow | formatValue }}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="dw" label="单位" width="100">
        <template #default="{ row }">
          {{ row.dw || "-" }}
        </template>
      </el-table-column>
    </el-table>
    <FilterDimDialog
      :filter-dims.sync="filterDims"
      :props="{
        label: 'dimAndLevelName',
        value: 'dimCol',
        valueList: 'wdzval',
      }"
      ref="FilterDimDialog"
      @getDimsValue="getDimsValue"
    />
  </el-dialog>
</template>

<script>
import options from "../mixins/options"
import FilterDimDialog from "../components/FilterDimDialog.vue"
import LevelMultipleSelect from "../components/LevelMultipleSelect.vue"
import ClusterMultipleSelect from "../components/ClusterMultipleSelect.vue"

export default {
  components: { FilterDimDialog, LevelMultipleSelect, ClusterMultipleSelect },
  mixins: [options],
  props: {},
  data() {
    return {
      dialogVisible: false,
      pswdOptionsMap: {},
      xsc: [],
      sjstableData: [],
      sjsForm: {
        id: "",
        lxbm: "",
        xsc: [],
        timeRange: [],
        TimeWdzd: null,
        startTime: null,
        endTime: null
      },
      loading: false,
      calculationRow: {},
      filterDims: [],
      allFilterDims: [],
      isCollapsed: true // 折叠状态
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    open() {
      this.filterDims = []
      this.dialogVisible = true
    },
    openDimensionDialog() {
      this.$refs.FilterDimDialog.open(this.allFilterDims)
    },
    async getCalculationResult() {
      this.loading = true
      const { data } = await this.$httpBi.indicatorAnagement.tryToCaluIndicator(
        {
          ...this.sjsForm,
          startTime: this.sjsForm.timeRange?.[0] || null,
          endTime: this.sjsForm.timeRange?.[1] || null,
          indCode: this.calculationRow.indCode,
          xsc: this.filterDims
            .filter(item => item.zdmc !== "无")
            .map(item => ({
              ...item,
              wdzd: item.dimCol
            }))
        }
      )
      this.sjstableData = data
      this.loading = false
    },
    async calculation(row) {
      this.calculationRow = row

      this.sjsForm = {
        id: "",
        lxbm: "",
        xsc: [
          {
            id: "",
            lxbm: "",
            wdzval: [] // 用户选择的维度值
          }
        ]
      }
      this.sjstableData = []
      this.sjsForm.id = row.id
      this.sjsForm.lxbm = row.lxbm

      const { data } = await this.$httpBi.api.paramGet(
        "/DimManage/getDimLevelByIndCode",
        {
          indCode: row.indCode
        }
      )
      this.open()

      // 处理时间维度
      const timeDim = data.find(item => item.fieldType === "time")
      this.sjsForm.isTime = !!timeDim
      this.sjsForm.TimeWdzd = timeDim ? timeDim.dimCol : null
      this.allFilterDims = data.filter(item => item.fieldType !== "time")

      this.getCalculationResult()
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    }
  }
}
</script>

<style scoped lang="scss">
.sub-title {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .sub-text {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-right: 5px;
  }

  .sub-line {
    height: 1px;
    flex: 1;
    background: #e5e6eb;
  }
}
.dimName {
  flex: 0 0 50%;
  border: 1px solid #dcdfe6;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-row {
  margin-bottom: 0;
}
</style>
