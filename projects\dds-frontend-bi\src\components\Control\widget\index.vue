<template>
  <el-dialog
    title="图表控制器配置"
    :visible.sync="controlConfigVisible"
    width="80%"
    @close="handleCancel"
    top="10vh"
    :close-on-click-modal="false"
  >
    <el-container style="height: 500px">
      <el-container>
        <el-aside width="250px">
          <div class="control-title">
            控制器列表
            <el-input
              style="width: 110px"
              v-model="searchControl"
              placeholder="请搜索"
            ></el-input>
            <el-button @click="addControl" type="text">
              <i
                style="font-size: 23px"
                class="el-icon-circle-plus-outline"
              ></i>
            </el-button>
          </div>
          <div>
            <div
              class="control-item"
              @click.stop="editControl(index)"
              :class="active === index ? 'control-item_active' : ''"
              v-for="(item, index) in controls"
              :key="index"
            >
              <el-input
                class="control-item_input"
                :disabled="active !== index"
                v-model="item.name"
              />
              <el-button
                style="margin-left: auto"
                @click.stop="editControl(index)"
                type="text"
                icon="el-icon-edit"
                size="mini"
                circle
              />
              <el-button
                @click.stop="delControl(index)"
                type="text"
                icon="el-icon-delete"
                size="mini"
                circle
              />
            </div>
          </div>
        </el-aside>
        <el-main ref="main">
          <el-form
            label-position="right"
            label-width="130px"
            :model="currentControl"
            ref="controlFrom"
            :rules="controlItemRules"
          >
            <h2 class="control-container-title">
              <!-- <span></span> -->
              {{ currentControl.name }}
              <!-- <span></span> -->
            </h2>
            <i v-show="controls.length <= 0">暂无控制器</i>
            <el-collapse
              v-if="controls.length > 0"
              style="margin-top: 10px"
              :value="['1', '2', '3']"
            >
              <el-collapse-item title="关联设置" name="1" disabled>
                <el-form-item style="margin-top: 5px" label="关联类型：">
                  <el-radio-group
                    @change="changeRelated"
                    v-model="currentControl.relatedViews[view.id]['fieldType']"
                  >
                    <el-radio-button label="column">字段</el-radio-button>
                    <el-radio-button label="variable">变量</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  prop="relatedViews"
                  :label="
                    currentControl.relatedViews[view.id]['fieldType'] ===
                      'column'
                      ? '关联字段：'
                      : '关联变量：'
                  "
                >
                  <el-select
                    v-model="currentControl.relatedViews[view.id]['fields'][0]"
                  >
                    <template
                      v-if="
                        currentControl.relatedViews[view.id]['fieldType'] ===
                          'variable'
                      "
                    >
                      <el-option
                        v-for="item in variables"
                        :label="item"
                        :value="item"
                        :key="item"
                      />
                    </template>
                    <template
                      v-else-if="
                        'slider,numberRange'.includes(currentControl.type)
                      "
                    >
                      <el-option
                        v-for="item in valueDragItems"
                        :label="item.displayName"
                        :value="item.displayName"
                        :key="item.displayName"
                      />
                    </template>
                    <template v-else>
                      <el-option
                        v-for="(item, index) in fields"
                        :key="index"
                        :label="fields[index]"
                        :value="fields[index]"
                      />
                    </template>
                  </el-select>
                </el-form-item>
              </el-collapse-item>
              <!-- 控制器相关配置 -->
              <el-collapse-item title="控制器配置" name="2" disabled>
                <!-- 控制器配置 -->
                <render-form-item
                  :controls="controls"
                  @changeType="changeType"
                  :data="currentControl"
                  :operator-type.sync="operatorType"
                  :operators="operatorsEnums[operatorType]"
                  :type="currentControl.type"
                />
              </el-collapse-item>
              <!-- 取值相关配置 -->
              <el-collapse-item title="取值配置" name="3" disabled>
                <render-val-item
                  :view="view"
                  :id="view.id"
                  :variable-list="view.variable"
                  :operators="operatorsEnums[operatorType]"
                  :data="currentControl"
                  :type="currentControl.type"
                />
              </el-collapse-item>
            </el-collapse>
          </el-form>
        </el-main>
      </el-container>
    </el-container>
    <span slot="footer">
      <el-row>
        <el-col :span="12">
          <el-radio-group v-model="queryMode">
            <el-radio-button :label="0">立即查询</el-radio-button>
            <el-radio-button :label="1">手动查询</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="saveControls">确 定</el-button>
      </el-row>
    </span>
  </el-dialog>
</template>

<script>
import elEnums from "./components/controlConfRender"
import valueConfEnums from "./components/valueConfRender"
export default {
  components: {
    renderFormItem: elEnums,
    renderValItem: valueConfEnums,
  },
  props: {
    id: [ Number , String ],
    valueDragItems: Array,
    // 组件显示、隐藏
    controlConfigVisible: {
      type: Boolean,
    },
    // 控制器配置
    controls: {
      type: Array,
      default: ()=>[],
    },
    // 当前图表选择的数据视图
    view: {
      type: Object,
    },
    widgetId: {
      type: Number,
    },
    // 查询模式
    queryMode: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      // columns: [],
      searchControl: "",
      variables: [],
      fields: [],
      active: 0,
      isEditNames: [],
      currentControl: {},
      columnsValues: [],
      operatorsEnums: {
        all: [
          { label: "等于", value: "=" },
          { label: "大于", value: ">" },
          { label: "大于等于", value: ">=" },
          { label: "小于", value: "<" },
          { label: "小于等于", value: "<=" },
        ],
        portion: [
          { label: "等于", value: "=" },
          { label: "不等于", value: "!=" },
        ],
        range: [
          { label: "在...范围内", value: "in" },
          { label: "不在...范围内", value: "not in" },
        ],
      },
      operatorType: "",
      popoverVisible: [],
      controlItemRules: {
        valueViewId: [
          { required: true, message: "请选择数据视图", trigger: "change" },
        ],
        valueField: [
          { required: true, message: "请选择取值字段", trigger: "change" },
        ],
        relatedViews: [
          { required: true, message: "请选择关联字段", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              // 判断数据模型变化情况
              if (
                this.currentControl.relatedViews[this.view.id]["fieldType"] ===
                "column"
              ) {
                if (
                  ("number,slider".includes(this.currentControl.type) &&
                    !this.valueDragItems.filter(
                      (item) =>
                        item.displayName === value[this.view.id].fields[0]
                    ).length) ||
                  ("select,inputText,radio,date,dateRange,treeSelect".includes(
                    this.currentControl.type
                  ) &&
                    !this.fields.filter(
                      (item) => item === value[this.view.id].fields[0]
                    ).length)
                ) {
                  this.$refs.main.$el.scrollTop = 0
                  return callback(new Error("数据模型已发生变化，请重新选择"))
                } else return callback()
              }

              if (this.currentControl.relatedViews[this.view.id]["fields"][0])
                callback()
              else callback(new Error("请选择关联字段"))
            },
          },
        ],
        max: [ { required: true, message: "请输入最大值", trigger: "change" } ],
        min: [ { required: true, message: "请输入最小值", trigger: "change" } ],
        operator: [
          { required: true, message: "请选择对应关系", trigger: "change" },
        ],
      },
      initControlData: {
        name: "新建控制器",
        type: "inputText",
        width: 0,
        label: "false",
        visibility: "visible",
        operator: "=",
        defaultValue: "",
        cache: false,
        min: 0,
        max: 100,
        optionWithVariable: false,
        step: 1,
        radioType: "normal",
        dateFormat: "yyyy-MM-dd",
        multiple: false,
        expired: 300,
        optionType: "auto",
        defaultValueType: "fixed",
        valueViewId: "",
        relatedViews: {},
        conditions: [
          {
            control: "",
            operator: "=",
            value: "",
          },
        ],
        valueField: "",
        textField: "",
        parentField: "",
      },
    }
  },
  created() {
    this.initView()
    // 初始化控制器列表
    if (this.controls.length > 0) {
      this.controls = this.controls.map((item) => {
        this.isEditNames.push(false)
        return { ...this.initControlData, ...item }
      })
      this.currentControl = this.controls[0]
      // 设置对应关系列表项
      if (this.currentControl.multiple) this.operatorType = "range"
      else this.operatorType = "portion"
    }
  },
  methods: {
    changeType() {
      // if(this.currentControl.relatedViews[this.view.id]['fieldType'] !== 'variable' && 'numberRange,slider'.includes(type)) {
      //   this.currentControl.relatedViews[this.view.id]['fields'][0] = ''
      // }
    },
    initView() {
      let model =
        typeof this.view.model === "string" ? JSON.parse(this.view.model) : {}
      // this.columns = Object.keys(model).filter(item => model[item].modelType === 'category')
      this.fields = Object.keys(model).filter(
        (item) => model[item].modelType === "category"
      )
      typeof this.view.variable === "string" &&
        (this.view.variable = JSON.parse(this.view.variable || "[]"))
      this.variables = this.view.variable.map((item) => item.name)
    },
    // 新增控制器
    addControl() {
      this.currentControl = JSON.parse(JSON.stringify(this.initControlData))
      this.currentControl.key = this.view.id + "-" + this.controls.length
      this.active = this.controls.length
      this.currentControl.relatedViews = {
        [this.view.id]: {
          fieldType: "column",
          fields: [ this.fields[0] ],
        },
      }
      this.operatorType = "portion"
      this.controls.push(this.currentControl)

      this.isEditNames.push(false)

      // this.fields = this.columns
    },
    // 修改控制器名称
    editControl(index) {
      if (this.active === index) return
      this.active = index
      this.selControl(this.controls[index])
    },
    // 选中控制器
    selControl(row) {
      this.currentControl = row
    },
    // 修改完成控制器名称
    // okControl(index) {
    //   this.$set(this.isEditNames, index, false)
    // },
    // 删除控制器
    delControl(index) {
      // 删除后当前选中状态自动上移
      let currentIndex = index - 1 >= 0 ? index - 1 : 0
      this.currentControl = this.controls[currentIndex]
      this.checked = this.currentControl.checked
      this.active = currentIndex
      this.controls.splice(index, 1)
      // this.isEditNames.splice(index, 1)
    },
    // 修改关联类型
    changeRelated() {
      this.$set(this.currentControl.relatedViews[this.view.id].fields, 0, "")
    },
    // 取消
    handleCancel() {
      this.controls = []
      this.$emit("update:controlConfigVisible", false)
    },
    // 关闭
    handleClose() {
      this.controls = []
    },
    // 处理控制器提交数据
    formatParams(data) {
      let params = {
        name: data.name,
        key: data.key,
        relatedViews: data.relatedViews, // 关联字段和关联类型
        type: data.type, // 控制器类型
        width: data.width, // 宽度
        visibility: data.visibility, // 是否可见
        // 显示条件 如果visibility为条件则传，否则为undefined(undefined浏览器自动过滤)
        conditions:
          data.visibility === "conditional" ? data.conditions : undefined,
        operator: data.operator,
        defaultValue: data.defaultValue,
        defaultValueType: data.defaultValueType,
      }
      /*  控制器相关数据处理 */
      // 过期时间和是否开启缓存
      if ("select,date,radio,treeSelect".includes(params.type)) {
        params.expired = data.expired
        params.cache = data.cache
      }
      // 多选框所需类型
      if ("select,date,treeSelect".includes(params.type))
        params.multiple = data.multiple
      // 单选按钮样式
      if (params.type === "radio")
        params.radioType = this.currentControl.radioType
      // 日期和日期范围设置日期类型
      if ("date,dateRange".includes(params.type))
        params.dateFormat = data.dateFormat
      if (params.type === "slider") {
        params.min = data.min
        params.max = data.max
        params.step = data.step
        params.label = data.label
      }
      /*  取值相关数据处理 */
      if ("select,radio".includes(params.type))
        params.optionType = data.optionType
      if (data.optionType === "manual") {
        params.valueViewId = data.valueViewId
        params.valueField = data.valueField
        params.textField = data.textField
      }
      if (data.optionType === "custom") {
        params.optionWithVariable = data.optionWithVariable
        params.customOptions = data.customOptions
      }
      return params
    },
    // 保存
    saveControls() {
      let emptyNames = this.controls.filter((item) => !item.name)
      if (emptyNames.length) {
        return this.$notify.error({
          title: "错误",
          message: `请检查控制器列表的控制器名称是否填写完整`,
        })
      }
      this.$refs["controlFrom"].validate((valid) => {
        if (!valid) return
        // 通用
        this.$emit(
          "saveControls",
          this.controls.map((item) => this.formatParams(item))
        )
        this.$emit("update:controlConfigVisible", false)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-main {
  border: 1px;
  border-color: #d2d2d3;
  border-style: solid;
}
.control-container-title {
  padding: 10px 0 15px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    width: 50px;
    height: 2px;
    background: #1890ff;
    margin: 0 10px;
  }
}

.el-aside {
  color: #333;
  text-align: center;
}
.control-title {
  border-bottom: 1px solid #f0f0f0;
  line-height: 40px;
  text-align: left;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.control-item {
  display: flex;
  padding: 5px;
}
.control-item_active {
  background: #f9f9f9;
}
.control-item:hover {
  cursor: pointer;
  background: #f9f9f9;
}
.control-item_input {
  width: 150px;
}
.control-item_conditions {
  display: flex;
  flex: 1;
  justify-content: abs($number: 2);
}
</style>
