<template>
  <div class="paneBlock">
    <h4>Y轴</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">左Y轴</el-col>
        <el-col span="10">右Y轴</el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="doubleYAxisForm.yAxisLeft"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_YAXIS_OPTIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="doubleYAxisForm.yAxisRight"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_YAXIS_OPTIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="14">y轴刻度条数</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            placeholder=""
            v-model="doubleYAxisForm.yAxisSplitNumber"
            @change="changeAxisStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="14">超出后缩放</el-col>
        <el-col span="10">
          <el-input-number
            controls-position="right"
            placeholder=""
            v-model="doubleYAxisForm.dataZoomThreshold"
            @change="changeAxisStyle"
          ></el-input-number>
        </el-col>
      </el-row>

      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="12">
          <el-checkbox
            v-model="doubleYAxisForm.showLine"
            @change="changeAxisStyle"
          >
            显示坐标轴
          </el-checkbox>
        </el-col>
        <el-col span="12">
          <el-checkbox
            v-model="doubleYAxisForm.inverse"
            @change="changeAxisStyle"
          >
            坐标轴反转
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            @change="changeAxisStyle"
            placeholder="请选择"
            v-model="doubleYAxisForm.lineStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_LINE_STYLES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="doubleYAxisForm.lineSize"
            size="mini"
          >
            <el-option
              v-for="item in 10"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="doubleYAxisForm.lineColor"
            @change="changeAxisStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="24">
          <el-checkbox
            v-model="doubleYAxisForm.showLabel"
            @change="changeAxisStyle"
          >
            显示标签文字
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="doubleYAxisForm.labelFontFamily"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            placeholder="请选择"
            @change="changeAxisStyle"
            v-model="doubleYAxisForm.labelFontSize"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            v-model="doubleYAxisForm.labelColor"
            @change="changeAxisStyle"
          ></el-color-picker>
        </el-col> -->
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  CHART_LABEL_POSITIONS,
  PIVOT_CHART_FONT_SIZES,
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_CHART_LINE_STYLES,
  PIVOT_CHART_YAXIS_OPTIONS,
} from "@/globalConstants"
export default {
  components: {},
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
    axisType: {
      type: String,
    },
  },
  data() {
    return {
      CHART_LABEL_POSITIONS,
      PIVOT_CHART_FONT_SIZES,
      PIVOT_CHART_FONT_FAMILIES,
      PIVOT_CHART_LINE_STYLES,
      PIVOT_CHART_YAXIS_OPTIONS,
      doubleYAxisForm: {},
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.doubleYAxisForm = this._.cloneDeep(
        this.chartData.chartStyles.doubleYAxis
      )
    },
    changeAxisStyle() {
      this.$emit("changeStyle", "doubleYAxis", this.doubleYAxisForm)
    },
    inputOnInput: function() {
      this.$forceUpdate()
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
