<template>
  <div class="left-group">
    <div class="title">群体</div>
    <div class="tabs">
      <div class="tab-item active">按组织机构</div>
      <div class="tab-item">按重点人群</div>
    </div>
    <el-tree
      class="group-tree"
      draggable
      :data="data"
      :allow-drop="() => false"
      :allow-drag="() => true"
      :props="defaultProps"
      @node-drag-start="handleGroupTreeDragStart"
      @node-drag-end="handleGroupTreeDragEnd"
    ></el-tree>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    isGroupDragStart: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: [
        {
          label: "学校",
          children: [
            {
              label: "计算机学院",
              children: [
                {
                  label: "软件工程专业",
                  children: [
                    {
                      label: "张三"
                    },
                    {
                      label: "李四"
                    }
                  ]
                },
                {
                  label: "网络工程专业",
                  children: [
                    {
                      label: "王五"
                    },
                    {
                      label: "赵六"
                    }
                  ]
                }
              ]
            },
            {
              label: "文学院",
              children: [
                {
                  label: "汉语言文学专业",
                  children: [
                    {
                      label: "小红"
                    },
                    {
                      label: "小明"
                    }
                  ]
                },
                {
                  label: "英语专业",
                  children: [
                    {
                      label: "小花"
                    },
                    {
                      label: "小杰"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 群体tree拖拽开始
    handleGroupTreeDragStart(node) {
      this.$emit("update:isGroupDragStart", true)
      let arr = []
      if (node.isLeaf) {
        arr = [node.data]
      } else {
        arr = this.getAllNodes(node.childNodes)
      }
      console.log(arr, "arr")
      // 在拖拽开始时设置拖拽节点的数据
      event.dataTransfer.setData("itemType", "groupType")
      event.dataTransfer.setData("groupList", JSON.stringify(arr))
    },
    getAllNodes(treeData) {
      const result = []
      function traverse(node) {
        if (node.childNodes.length) {
          node.childNodes.forEach(child => {
            traverse(child)
          })
        } else {
          result.push(node.data)
        }
      }

      treeData.forEach(node => {
        traverse(node)
      })

      return result
    },
    handleGroupTreeDragEnd() {
      this.$emit("update:isGroupDragStart", false)
    }
  }
}
</script>

<style scoped lang="scss">
.left-group {
  width: 224px;
  height: 100%;
  background: #ffffff;
  border-radius: 5px 0px 0px 5px;
  border: 1px solid #e4e7ed;
  .title {
    width: 224px;
    height: 40px;
    border-radius: 5px 0px 0px 0px;
    box-shadow: inset 0px -1px 0px 0px #ebedf0;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2f3338;
    line-height: 40px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding-left: 12px;
    box-sizing: border-box;
  }
  .group-tree {
    padding: 0 12px;
    box-sizing: border-box;
    height: calc(100% - 120px);
    overflow: auto;
    ::v-deep .el-tree-node__label {
      width: 100%;
    }
    // ::v-deep .el-tree-node.is-current.is-focusable {
    //
    //   .el-tree-node__label {
    //     background: #f4f7ff;
    //     box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    //       0px 6px 6px -4px rgba(0, 42, 128, 0.12);
    //     border-radius: 4px;
    //     border: 1px solid #1563ff;
    //     color: #1563ff;
    //   } }

    //滚动条样式
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 6px;
      height: 2px;
      background-color: #cfd6e6;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: transparent;
      border-radius: 6px;
    }
  }
  .tabs {
    display: flex;
    padding: 0 12px;
    box-sizing: border-box;
    margin: 16px 0;

    .tab-item {
      width: 96px;
      height: 30px;
      border-radius: 15px;
      border: 1px solid #dcdfe6;
      cursor: pointer;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #606266;
      line-height: 30px;
      text-align: center;
      &:first-child {
        margin-right: 8px;
      }
      &.active {
        background: rgba(21, 99, 255, 0.1);
        color: #1563ff;
        font-weight: 500;
      }
    }
  }
}
</style>
<style lang="scss">
#project_frame
  .group-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .group-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .custom-tree-node,
#project_frame .group-tree .el-tree-node.is-current > .el-tree-node__content {
  position: relative;
  background: #f4f7ff;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;

  &::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 9px;
    width: 12px;
    height: 10px;
    background: url("~@/assets/images/tree-icon.png") no-repeat center;
  }
  .el-tree-node__label {
    background: transparent;
  }
}

#project_frame .group-tree .el-tree-node {
  border: 1px solid transparent !important;
}
</style>
