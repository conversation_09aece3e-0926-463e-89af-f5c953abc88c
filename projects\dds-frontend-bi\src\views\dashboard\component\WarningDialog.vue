<template>
  <div>
    <el-dialog
      title="数据预警"
      :visible.sync="warningVisible"
      append-to-body
      width="50%"
      :close-on-click-modal="false"
      @closed="$emit('update:warningVisible', false)"
    >
      <el-button type="primary" @click="addWarning">新增预警</el-button>
      <el-table :data="warningList" v-loading="loading">
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column property="warnName" label="预警名称"></el-table-column>
        <el-table-column label="预警状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.triggerStatus ? 'success' : 'danger'"
              disable-transitions
            >
              {{ scope.row.triggerStatus ? "开启" : "禁用" }}
            </el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="editWarning(scope.row)"
            >
              编辑
            </el-button
            >
            <el-divider direction="vertical"></el-divider>

            <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定删除这个预警吗？"
              @onConfirm="handleDel(scope.row.id)"
            >
              <el-button slot="reference" type="text">删除</el-button>
            </el-popconfirm>
            <el-divider direction="vertical"></el-divider>
            <el-button
              type="text"
              @click="goRecord(scope.row.id)"
            >
              预警记录
            </el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        title="设置预警规则"
        :visible.sync="dialogVisible"
        :before-close="handleClose"
        append-to-body
        width="70%"
        :close-on-click-modal="false"
      >
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="ruleForm"
        >
          <el-form-item label="预警名称" prop="warnName">
            <el-input
              type="text"
              v-model="ruleForm.warnName"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="开启预警">
            <el-switch
              v-model="ruleForm.triggerStatus"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
          <el-form-item label="数据权限">
            <el-radio
              disabled
              v-model="ruleForm.isAuth"
              :label="0"
            >
              不区分权限
            </el-radio
            >
          </el-form-item>
          <el-form-item label="预警规则" prop="checkPass">
            <div class="ruleList">
              <el-radio
                v-model="ruleForm.isTrigger"
                :label="1"
              >
                满足<i style="color: #409eff">全部</i>条件
              </el-radio
              >
              <el-radio
                v-model="ruleForm.isTrigger"
                :label="0"
              >
                满足<i style="color: #409eff">任一</i>条件
              </el-radio
              >

              <div
                class="formItem"
                v-for="(item, index) in ruleForm.triggerRules"
                :key="index"
              >
                <el-row type="flex" align="middle">
                  <el-col :span="24">
                    <el-switch
                      v-model="item.calu"
                      :active-value="1"
                      :inactive-value="0"
                    >
                    </el-switch>
                    动态阀值
                  </el-col>
                </el-row>
                <el-row type="flex">
                  <el-col :span="6">
                    <el-form-item
                      :prop="'triggerRules.' + index + '.indexField'"
                      :rules="{
                        required: true,
                        message: '监控度量不能为空',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="item.indexField"
                        @change="changeAlias($event, index)"
                        placeholder="请选择监控度量"
                      >
                        <el-option
                          v-for="item in metrics"
                          :key="item.value"
                          :label="
                            item.field.alias
                              ? item.field.alias
                              : item.displayName
                          "
                          :value="item.displayName"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :prop="'triggerRules.' + index + '.condition'"
                      :rules="{
                        required: true,
                        message: '请选择预警条件',
                        trigger: 'change',
                      }"
                    >
                      <el-select v-model="item.condition" placeholder="请选择">
                        <el-option
                          v-for="o in OperatorTypesLocale"
                          :key="o.value"
                          :label="o.label"
                          :value="o.value"
                          :disabled="
                            item.calu &&
                              (o.value == 'is null' || o.value == 'is not null')
                          "
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col
                    :span="6"
                    v-if="
                      item.calu &&
                        item.condition !== 'is null' &&
                        item.condition !== 'is not null'
                    "
                  >
                    <el-form-item
                      :prop="'triggerRules.' + index + '.caluValue'"
                      :rules="{
                        required: true,
                        message: '请选择比对值',
                        trigger: 'change',
                      }"
                    >
                      <el-select v-model="item.caluValue" placeholder="请选择">
                        <el-option
                          v-for="item in AggregatorLocal"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col
                    :span="8"
                    v-else-if="
                      !item.calu &&
                        item.condition !== 'is null' &&
                        item.condition !== 'is not null'
                    "
                  >
                    <el-input-number
                      v-model="item.warningValue"
                      controls-position="right"
                      :max="9999999"
                      @change="handleChange"
                    ></el-input-number>
                  </el-col>
                  <el-col :span="4">
                    <i class="el-icon-minus" @click="removeDomain(item)"></i>
                    <i class="el-icon-plus" @click="addDomain"></i>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="通知方式" prop="checkPass">
            <el-checkbox-group v-model="ruleForm.noticeType">
              <el-checkbox :label="1">手机短信</el-checkbox>
              <el-checkbox :label="2">邮件</el-checkbox>
              <el-checkbox disabled :label="0">站内信</el-checkbox>
              <el-checkbox :label="3" disabled>钉钉</el-checkbox>
              <el-checkbox :label="4" disabled>企业微信</el-checkbox>
              <el-checkbox :label="5" disabled>微信公众号</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="接收人" prop="age">
            <el-select
              v-model="ruleForm.receiver"
              disabled
              placeholder="默认为当前用户"
            >
              <el-option
                label="默认为当前用户"
                :value="$store.state.user.userId + ''"
                disabled
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通知时机" prop="age">
            <el-radio
              v-model="ruleForm.noticeTyle"
              :label="0"
            >
              数据更新时
            </el-radio
            >
            <el-radio
              v-model="ruleForm.noticeTyle"
              disabled
              :label="1"
            >
              定时
            </el-radio
            >
          </el-form-item>
          <el-form-item label="是否重复通知">
            <el-switch
              v-model="ruleForm.isRepeat"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSaveWarning">确 定</el-button>
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    warningVisible: {
      type: Boolean,
      default: false,
    },
    metrics: {
      type: Array,
      default: () => [],
    },
    currentWidget: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      warningList: [],
      ruleType: 1,

      AggregatorLocal: [
        {
          value: "avg",
          label: "平均数",
        },
        {
          value: "max",
          label: "最大值",
        },
        {
          value: "min",
          label: "最小值",
        },
      ],
      OperatorTypesLocale: [
        {
          value: "=",
          label: "等于",
        },
        {
          value: "!=",
          label: "不等于",
        },
        {
          value: ">",
          label: "大于",
        },
        {
          value: "<",
          label: "小于",
        },
        {
          value: ">=",
          label: "大于等于",
        },
        {
          value: "<=",
          label: "小于等于",
        },
        {
          value: "is null",
          label: "为空",
        },
        {
          value: "is not null",
          label: "不为空",
        },
        // {
        //   value: "qj",
        //   label: "区间",
        // },
      ],

      value: "",
      dialogVisible: false,
      rules: {
        warnName: [
          { required: true, message: "请输入预警名称", trigger: "blur" },
        ],
      },
      ruleForm: {
        triggerRules: [
          {
            calu: 0, // 动态阈值是否开启
            indexField: "", // --指标字段 （从图表指标字段中选择保存的）
            indexName: "", // --指标别名
            condition: "", // node_modules-- 预警条件（根据原型设计）
            warningValue: "", // --预警值 （范围 前包含后不包含）
            caluValue: "", // --动态阈值设置的类型（根据原型设计类型）
          },
        ],
        caluType: 0,
        createBy: "",
        createTime: "",
        dashboardId: 0,
        description: "",
        execSql: "",
        isAuth: 0,
        isRepeat: 1,
        warnName: "",
        noticeCron: "",
        noticeTyle: 0,
        noticeType: [ 0 ],
        receiver: "",
        sourceId: 0,
        triggerStatus: 1,
        isTrigger: 0,
        updateBy: "",
        updateTime: "",
        viewId: 0,
        widgetId: 0,
        xxid: 0,
      },
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    async init() {
      this.loading = true
      const res = await this.$httpBi.warning.getWarningByWidget({
        widgetId: this.currentWidget.widgetId,
        receiver: this.$store.state.user.userId,
      })
      this.warningList = res.data
      this.loading = false
    },
    // 新增预警
    addWarning() {
      Object.assign(this.$data.ruleForm, this.$options.data().ruleForm)
      this.dialogVisible = true
    },
    editWarning(warningInfo) {
      this.ruleForm = {
        ...warningInfo,
        triggerRules: JSON.parse(warningInfo.triggerRules),
        noticeType: JSON.parse(warningInfo.noticeType),
      }
      this.dialogVisible = true
    },
    addDomain() {
      this.ruleForm.triggerRules.push({
        calu: 0, // 动态阈值是否开启
        indexField: "", // --指标字段 （从图表指标字段中选择保存的）
        indexName: "", // --指标别名
        condition: "", // node_modules-- 预警条件（根据原型设计）
        warningValue: "", // --预警值 （范围 前包含后不包含）
        caluValue: "",
      })
    },

    async handleDel(id) {
      await this.$httpBi.warning.delWarning({ id })
      this.init()
    },
    async onSaveWarning() {
      this.$refs.ruleForm.validate(async(valid) => {
        if (valid) {
          this.ruleForm.triggerRules.forEach((val) => {
            if (val.condition === "is null" || val.condition === "is not null") {
              val.warningValue = ""
              val.caluValue = ""
            }
          })
          if (this.ruleForm.id) {
            await this.$httpBi.warning.updateWarning({
              ...this.ruleForm,
              triggerRules: JSON.stringify(this.ruleForm.triggerRules),
              noticeType: JSON.stringify(this.ruleForm.noticeType),
            })
            this.init()
            this.dialogVisible = false
          } else {
            await this.$httpBi.warning.addWarning({
              ...this.ruleForm,
              receiver: this.$store.state.user.userId,
              dashboardId: this.currentWidget.dashboardId,
              viewId: this.currentWidget.viewId,
              widgetId: this.currentWidget.widgetId,
              triggerRules: JSON.stringify(this.ruleForm.triggerRules),
              noticeType: JSON.stringify(this.ruleForm.noticeType),
            })
            this.init()
            this.dialogVisible = false
          }
        } else {
          return false
        }
      })
    },
    removeDomain(item) {
      var index = this.ruleForm.triggerRules.indexOf(item)
      if (index !== -1 && this.ruleForm.triggerRules.length > 1) {
        this.ruleForm.triggerRules.splice(index, 1)
      }
    },
    goRecord(id) {
      this.$router.push({
        name: "EarlywarningRecord",
        query: {
          id,
        },
      })
    },
    changeAlias(val, index) {
      const alias = this.metrics.find((metric) => metric.displayName === val)
        .field.alias
      if (alias) {
        this.$set(this.ruleForm.triggerRules, index, {
          ...this.ruleForm.triggerRules[index],
          indexName: alias,
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.ruleList {
  background-color: #61616155;
  padding: 20px;
  .formItem {
    margin-top: 10px;
    div {
      margin-right: 10px;
    }
    i {
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
.add {
  color: #409eff;
  padding: 10px;
  cursor: pointer;
}
</style>
