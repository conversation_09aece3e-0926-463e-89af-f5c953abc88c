<template>
  <el-dialog
    title="提示"
    :visible.sync="controlConfigVisible"
    width="30%"
    :before-close="handleClose"
    @close="handleCancel"
  >
    <span>这是控制器</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    controlConfigVisible: {
      type: Boolean,
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleCancel() {
      this.$emit("update:controlConfigVisible", false)
    },
  },
}
</script>

<style scoped lang="scss"></style>
