<template>
  <div>
    <el-table ref="filterTable" :data="changeRecordList" style="width: 100%">
      <el-table-column prop="changeTime" label="变更时间"></el-table-column>
      <el-table-column
        prop="comments"
        label="变更内容"
        show-overflow-tooltip
      ></el-table-column>
      <template v-if="parent.lxbm != 'yz'">
        <el-table-column
          prop="oldIndValue"
          label="变更前指标值"
        ></el-table-column>
        <el-table-column
          prop="newIndValue"
          label="变更后指标值"
        ></el-table-column>
      </template>

      <el-table-column prop="changedBy" label="变更人"></el-table-column>
      <el-table-column fixed="right" label="操作" width="170">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="showAttrDialog(row)">
            指标属性记录
          </el-button>
          <!-- <el-button type="text" size="small" @click="dialogVisible = true">
            版本回滚
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 指标属性记录弹窗 -->
    <el-dialog
      :visible.sync="dialogAttrVisible"
      width="600px"
      v-if="dialogAttrVisible"
      custom-class="indicator-attr-dialog"
      :show-close="false"
    >
      <template #title>
        <div class="dialog-header">
          <span class="title">指标属性记录</span>
          <i
            class="el-icon-close close-btn"
            @click="dialogAttrVisible = false"
          ></i>
        </div>
      </template>
      <div class="attr-content">
        <el-row class="attr-row">
          <el-col :span="6" class="attr-label">变更时间：</el-col>
          <el-col :span="16">{{ indicator.changeTime }}</el-col>
        </el-row>
        <el-row class="attr-row">
          <el-col :span="6" class="attr-label">指标名称：</el-col>
          <el-col :span="16">
            {{ indicator.zbmc || indicator.fieldNameModified }}
          </el-col>
        </el-row>
        <!-- 原子指标属性 -->
        <template v-if="parent.lxbm == 'yz'">
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">指标原数据列：</el-col>
            <el-col :span="16">{{ indicator.zbymc }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">可派生维度：</el-col>
            <el-col :span="16">
              {{
                indicator.pswd.length
                  ? indicator.pswd.map((item) => item.zdmc).join(",")
                  : "无"
              }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">所属指标域：</el-col>
            <el-col :span="16">
              {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">归属部门：</el-col>
            <el-col :span="16">{{ indicator.deptAllName }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">描述：</el-col>
            <el-col :span="16">{{ indicator.ms }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">标签：</el-col>
            <el-col :span="16">{{ indicator.bqs }}</el-col>
          </el-row>
        </template>
        <!-- 派生指标属性 -->
        <template v-if="parent.lxbm == 'ps'">
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">所属指标域：</el-col>
            <el-col :span="16">
              {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">归属部门：</el-col>
            <el-col :span="16">{{ indicator.deptAllName || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">描述：</el-col>
            <el-col :span="16">{{ indicator.ms || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">标签：</el-col>
            <el-col :span="16">{{ indicator.bqs || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">基础指标：</el-col>
            <el-col :span="16">
              {{ getLabel(yzzbList, indicator.atomIndCode, "zbmc", "indCode") }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">派生维度：</el-col>
            <el-col :span="16" v-if="indicator.xsc.length">
              <p v-for="item in indicator.xsc" :key="item.zdmc">
                {{ item.zdmc }}:

                <template v-if="mapLevelCodeValues[item.levelCode]">
                  <span v-for="val in item.wdzval" :key="val">
                    {{
                      getLabel(
                        mapLevelCodeValues[item.levelCode],
                        val,
                        "value",
                        "valueCode"
                      )
                    }}
                  </span>
                </template>
              </p>
            </el-col>
            <el-col :span="16" v-else>无</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">可扩展维度：</el-col>
            <el-col :span="16">
              {{
                indicator.extDim.length
                  ? indicator.extDim.map((item) => item.zdmc).join("、")
                  : "无"
              }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">计算方式：</el-col>
            <el-col :span="16">{{ getLabel(jsfsList, indicator.jsfs) }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">单位：</el-col>
            <el-col :span="16">{{ indicator.jldw || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">数据格式：</el-col>
            <el-col :span="16">
              {{ getLabel(sjgs, indicator.dataFormat) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">精度：</el-col>
            <el-col :span="16">{{ indicator.jd }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">四舍五入：</el-col>
            <el-col :span="16">{{ indicator.sswr ? "开启" : "关闭" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">时间范围：</el-col>
            <el-col :span="16">
              {{ getLabel(sjdwList, indicator.sjwd) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">计算周期：</el-col>
            <el-col :span="16">
              {{ getLabel(jszqList, indicator.jszq) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">0值预警：</el-col>
            <el-col :span="16">
              {{ getLabel(zeroList, indicator.zeroWarnTime) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">阈值：</el-col>
            <el-col :span="16">
              {{ indicator.tvmin }} - {{ indicator.tvmax }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">开启阈值外预警：</el-col>
            <el-col :span="16">
              {{ indicator.isWarnThreshold ? "开启" : "关闭" }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">数据过滤：</el-col>
            <el-col :span="16">{{ indicator.dataFilters || "无" }}</el-col>
          </el-row>
        </template>
        <!-- 衍生指标属性 -->
        <template v-if="parent.lxbm == 'ys'">
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">所属指标域：</el-col>
            <el-col :span="16">
              {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">归属部门：</el-col>
            <el-col :span="16">{{ indicator.deptAllName || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">描述：</el-col>
            <el-col :span="16">{{ indicator.ms || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">标签：</el-col>
            <el-col :span="16">{{ indicator.bq || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">计算表达式：</el-col>
            <el-col :span="16">
              {{ indicator.expression || "无" }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">可扩展维度：</el-col>
            <el-col :span="16">
              {{
                indicator.expandDims.length
                  ? indicator.expandDims.map((item) => item.dimName).join("、")
                  : "无"
              }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">单位：</el-col>
            <el-col :span="16">{{ indicator.jldw }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">数据格式：</el-col>
            <el-col :span="16">
              {{ getLabel(sjgs, indicator.dataFormat) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">精度：</el-col>
            <el-col :span="16">{{ indicator.jd }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">四舍五入：</el-col>
            <el-col :span="16">{{ indicator.sswr ? "开启" : "关闭" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">计算周期：</el-col>
            <el-col :span="16">
              {{ getLabel(jszqList, indicator.jszq) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">0值预警：</el-col>
            <el-col :span="16">
              {{ getLabel(zeroList, indicator.zeroWarnTime) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">阈值：</el-col>
            <el-col :span="16">
              {{ indicator.tvmin }} - {{ indicator.tvmax }}
            </el-col>
          </el-row>
          <!-- <el-row class="attr-row">
            <el-col :span="6" class="attr-label">开启阈值外预警</el-col>
            <el-col :span="16">
              {{ indicator.isWarnThreshold ? "开启" : "关闭" }}
            </el-col>
          </el-row> -->
        </template>
        <!-- sql指标属性 -->
        <template v-if="parent.lxbm == 'sq'">
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">所属指标域：</el-col>
            <el-col :span="16">
              {{ getTreeLabel(viewGroup, indicator.scopeId, "name", "id") }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">归属部门：</el-col>
            <el-col :span="16">{{ indicator.deptAllName || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">描述：</el-col>
            <el-col :span="16">{{ indicator.description || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">标签：</el-col>
            <el-col :span="16">
              {{
                (indicator.tagsName && indicator.tagsName.join("、")) || "无"
              }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">sql表达式：</el-col>
            <el-col :span="16">
              {{ indicator.sqlStatement || "无" }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">可扩展维度：</el-col>
            <el-col :span="16">
              {{
                indicator.dims.length
                  ? indicator.dims
                      .map((item) => item.fieldNameModified)
                      .join("、")
                  : "无"
              }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">单位：</el-col>
            <el-col :span="16">{{ indicator.unitName || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">数据格式：</el-col>
            <el-col :span="16">
              {{ getLabel(sjgs, indicator.dataFormat) }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">精度：</el-col>
            <el-col :span="16">{{ indicator.precision || "无" }}</el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">四舍五入：</el-col>
            <el-col :span="16">
              {{ indicator.rounding ? "开启" : "关闭" }}
            </el-col>
          </el-row>
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">计算周期：</el-col>
            <el-col :span="16">
              {{ getLabel(jszqList, indicator.period) }}
            </el-col>
          </el-row>
          <!-- <el-row class="attr-row">
            <el-col :span="6" class="attr-label">0值预警</el-col>
            <el-col :span="16">
              {{ getLabel(zeroList, indicator.zeroWarnTime) }}
            </el-col>
          </el-row> -->
          <el-row class="attr-row">
            <el-col :span="6" class="attr-label">阈值：</el-col>
            <el-col :span="16">
              {{ indicator.thresholdMin }} - {{ indicator.thresholdMax }}
            </el-col>
          </el-row>
          <!-- <el-row class="attr-row">
            <el-col :span="6" class="attr-label">开启阈值外预警</el-col>
            <el-col :span="16">
              {{ indicator.isZeroCluster ? "开启" : "关闭" }}
            </el-col>
          </el-row> -->
        </template>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="action-btn" @click="dialogAttrVisible = false">
            关闭
          </el-button>
          <el-button type="primary" class="action-btn" @click="handleRollback">
            版本回滚
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      width="600px"
      custom-class="rule-rollback-dialog"
      :show-close="false"
    >
      <!-- 顶部标题栏 -->
      <template #title>
        <div class="dialog-header">
          <span class="title">规则回滚</span>
          <i class="el-icon-close close-btn" @click="dialogVisible = false"></i>
        </div>
      </template>

      <!-- 主要内容区域 -->
      <div class="dialog-content">
        <!-- 影响范围区域 -->
        <section class="scope-section">
          <h3 class="section-title">影响范围</h3>
          <div class="info-group">
            <div class="info-item">
              <span class="label">指标：</span>
              <span class="value">
                {{
                  scopeList
                    .filter(
                      (item) =>
                        item.currentType !== "chart" &&
                        item.currentType !== "display"
                    )
                    .map((item) => item.currentName)
                    .join(";") || "无"
                }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">图表：</span>
              <span class="value">
                {{
                  scopeList
                    .filter((item) => item.currentType === "chart")
                    .map((item) => item.currentName)
                    .join(";") || "无"
                }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">驾驶舱：</span>
              <span class="value">
                {{
                  scopeList
                    .filter((item) => item.currentType === "display")
                    .map((item) => item.currentName)
                    .join(";") || "无"
                }}
              </span>
            </div>
          </div>
        </section>

        <!-- 规则变化区域 -->
        <section class="rule-section">
          <div class="section-header">
            <h3 class="section-title">规则变化</h3>
            <!-- <el-checkbox v-model="showDiffOnly">仅查看差异规则</el-checkbox> -->
          </div>

          <!-- 规则对比表格 -->
          <div class="compare-table">
            <div class="table-header">
              <div class="col"></div>
              <div class="col">当前规则</div>
              <div class="col">回滚后规则</div>
            </div>

            <!-- 原子指标 (yz) -->
            <div class="table-content" v-if="parent.lxbm == 'yz'">
              <div class="table-row" v-if="!currentInd.zbmc_status">
                <div class="col">指标名称：</div>
                <div class="col">{{ currentInd.zbmc }}</div>
                <div class="col">{{ indicator.zbmc }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.zbymc_status">
                <div class="col">指标原数据列：</div>
                <div class="col">{{ currentInd.zbymc }}</div>
                <div class="col">{{ indicator.zbymc }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.pswd_status">
                <div class="col">可派生维度：</div>
                <div class="col">
                  {{
                    currentInd.pswd.length
                      ? currentInd.pswd.map((item) => item.zdmc).join(",")
                      : "无"
                  }}
                </div>
                <div class="col">
                  {{
                    indicator.pswd.length
                      ? indicator.pswd.map((item) => item.zdmc).join(",")
                      : "无"
                  }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.sysjy_status">
                <div class="col">所属指标域：</div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, currentInd.sysjy, "name", "id") }}
                </div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.deptAllCode_status">
                <div class="col">归属部门：</div>
                <div class="col">{{ currentInd.deptAllName || "无" }}</div>
                <div class="col">{{ indicator.deptAllName || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.ms_status">
                <div class="col">描述：</div>
                <div class="col">
                  {{ currentInd.ms || "无" }}
                </div>
                <div class="col">
                  {{ indicator.ms || "无" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.bqs_status">
                <div class="col">标签：</div>
                <div class="col">
                  {{ currentInd.bqs || "无" }}
                </div>
                <div class="col">
                  {{ indicator.bqs || "无" }}
                </div>
              </div>
            </div>

            <!-- 派生指标 (ps) -->
            <div class="table-content" v-if="parent.lxbm == 'ps'">
              <div class="table-row" v-if="!currentInd.zbmc_status">
                <div class="col">指标名称：</div>
                <div class="col">{{ currentInd.zbmc }}</div>
                <div class="col">{{ indicator.zbmc }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.sysjy_status">
                <div class="col">所属指标域：</div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, currentInd.sysjy, "name", "id") }}
                </div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.deptAllCode_status">
                <div class="col">归属部门：</div>
                <div class="col">{{ currentInd.deptAllName || "无" }}</div>
                <div class="col">{{ indicator.deptAllName || "无" }}</div>
              </div>

              <div class="table-row" v-if="!currentInd.ms_status">
                <div class="col">描述：</div>
                <div class="col">
                  {{ currentInd.ms || "无" }}
                </div>
                <div class="col">
                  {{ indicator.ms || "无" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.bqs_status">
                <div class="col">标签：</div>
                <div class="col">
                  {{ currentInd.bqs || "无" }}
                </div>
                <div class="col">
                  {{ indicator.bqs || "无" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.atomIndCode_status">
                <div class="col">基础指标：</div>
                <div class="col">
                  {{
                    getLabel(
                      yzzbList,
                      currentInd.atomIndCode,
                      "zbmc",
                      "indCode"
                    )
                  }}
                </div>
                <div class="col">
                  {{
                    getLabel(yzzbList, indicator.atomIndCode, "zbmc", "indCode")
                  }}
                </div>
              </div>

              <div class="table-row" v-if="!currentInd.xsc_status">
                <div class="col">可派生维度：</div>
                <div class="col">
                  <template v-if="currentInd.xsc.length">
                    <p v-for="item in currentInd.xsc" :key="item.zdmc">
                      {{ item.zdmc }}:

                      <template v-if="mapLevelCodeValues[item.levelCode]">
                        <span v-for="val in item.wdzval" :key="val">
                          {{
                            getLabel(
                              mapLevelCodeValues[item.levelCode],
                              val,
                              "value",
                              "valueCode"
                            )
                          }}
                        </span>
                      </template>
                    </p>
                  </template>
                  <div v-else>无</div>
                </div>
                <div class="col">
                  <template v-if="indicator.xsc.length">
                    <p v-for="item in indicator.xsc" :key="item.zdmc">
                      {{ item.zdmc }}:

                      <template v-if="mapLevelCodeValues[item.levelCode]">
                        <span v-for="val in item.wdzval" :key="val">
                          {{
                            getLabel(
                              mapLevelCodeValues[item.levelCode],
                              val,
                              "value",
                              "valueCode"
                            )
                          }}
                        </span>
                      </template>
                    </p>
                  </template>
                  <div v-else>无</div>
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.extDim_status">
                <div class="col">可扩展维度：</div>
                <div class="col">
                  {{
                    currentInd.extDim.length
                      ? currentInd.extDim.map((item) => item.zdmc).join(",")
                      : "无"
                  }}
                </div>
                <div class="col">
                  {{
                    indicator.extDim.length
                      ? indicator.extDim.map((item) => item.zdmc).join(",")
                      : "无"
                  }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jsfs_status">
                <div class="col">计算方式：</div>
                <div class="col">
                  {{ getLabel(jsfsList, currentInd.jsfs) }}
                </div>
                <div class="col">
                  {{ getLabel(jsfsList, indicator.jsfs) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jldw_status">
                <div class="col">单位：</div>
                <div class="col">
                  {{ currentInd.jldw || "无" }}
                </div>
                <div class="col">
                  {{ indicator.jldw || "无" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.dataFormat_status">
                <div class="col">数据格式：</div>
                <div class="col">
                  {{ getLabel(sjgs, currentInd.dataFormat) }}
                </div>
                <div class="col">
                  {{ getLabel(sjgs, indicator.dataFormat) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jd_status">
                <div class="col">精度：</div>
                <div class="col">
                  {{ currentInd.jd }}
                </div>
                <div class="col">
                  {{ indicator.jd }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.sswr_status">
                <div class="col">精度：</div>
                <div class="col">
                  {{ currentInd.sswr ? "开启" : "关闭" }}
                </div>
                <div class="col">
                  {{ indicator.sswr ? "开启" : "关闭" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.sjwd_status">
                <div class="col">时间范围：</div>
                <div class="col">
                  {{ getLabel(sjdwList, currentInd.sjwd) }}
                </div>
                <div class="col">
                  {{ getLabel(sjdwList, indicator.sjwd) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jszq_status">
                <div class="col">计算周期：</div>
                <div class="col">
                  {{ getLabel(jszqList, currentInd.jszq) }}
                </div>
                <div class="col">
                  {{ getLabel(jszqList, indicator.jszq) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.zeroWarnTime_status">
                <div class="col">0值预警：</div>
                <div class="col">
                  {{ getLabel(zeroList, currentInd.zeroWarnTime) }}
                </div>
                <div class="col">
                  {{ getLabel(zeroList, indicator.zeroWarnTime) }}
                </div>
              </div>
              <div
                class="table-row"
                v-if="!currentInd.tvmin_status || !currentInd.tvmax_status"
              >
                <div class="col">阈值：</div>
                <div class="col">
                  {{ currentInd.tvmin }} - {{ currentInd.tvmax }}
                </div>
                <div class="col">
                  {{ indicator.tvmin }} - {{ indicator.tvmax }}
                </div>
              </div>

              <div class="table-row" v-if="!currentInd.isWarnThreshold_status">
                <div class="col">开启阈值外预警：</div>
                <div class="col">
                  {{ currentInd.isWarnThreshold ? "开启" : "关闭" }}
                </div>
                <div class="col">
                  {{ indicator.isWarnThreshold ? "开启" : "关闭" }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.dataFilters_status">
                <div class="col">数据过滤：</div>
                <div class="col">
                  {{ currentInd.dataFilters || "无" }}
                </div>
                <div class="col">
                  {{ indicator.dataFilters || "无" }}
                </div>
              </div>
            </div>

            <!-- filepath: e:\workbench\dds-ui-3.0.0\projects\dds-frontend-bi\src\views\search\app-indicators\indicator-detail\IndicatorChangeRecord.vue -->
            <!-- 衍生指标 (ys) -->
            <div class="table-content" v-if="parent.lxbm == 'ys'">
              <div class="table-row" v-if="!currentInd.zbmc_status">
                <div class="col">指标名称：</div>
                <div class="col">{{ currentInd.zbmc }}</div>
                <div class="col">{{ indicator.zbmc }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.sysjy_status">
                <div class="col">所属指标域：</div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, currentInd.sysjy, "name", "id") }}
                </div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, indicator.sysjy, "name", "id") }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.deptAllCode_status">
                <div class="col">归属部门：</div>
                <div class="col">{{ currentInd.deptAllName || "无" }}</div>
                <div class="col">{{ indicator.deptAllName || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.ms_status">
                <div class="col">描述：</div>
                <div class="col">{{ currentInd.ms || "无" }}</div>
                <div class="col">{{ indicator.ms || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.bq_status">
                <div class="col">标签：</div>
                <div class="col">{{ currentInd.bq || "无" }}</div>
                <div class="col">{{ indicator.bq || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.expression_status">
                <div class="col">计算表达式：</div>
                <div class="col">{{ currentInd.expression || "无" }}</div>
                <div class="col">{{ indicator.expression || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.expandDims_status">
                <div class="col">可扩展维度：</div>
                <div class="col">
                  {{
                    currentInd.expandDims.length
                      ? currentInd.expandDims
                          .map((item) => item.dimName)
                          .join("、")
                      : "无"
                  }}
                </div>
                <div class="col">
                  {{
                    indicator.expandDims.length
                      ? indicator.expandDims
                          .map((item) => item.dimName)
                          .join("、")
                      : "无"
                  }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jldw_status">
                <div class="col">单位：</div>
                <div class="col">{{ currentInd.jldw || "无" }}</div>
                <div class="col">{{ indicator.jldw || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.dataFormat_status">
                <div class="col">数据格式：</div>
                <div class="col">
                  {{ getLabel(sjgs, currentInd.dataFormat) }}
                </div>
                <div class="col">
                  {{ getLabel(sjgs, indicator.dataFormat) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.jd_status">
                <div class="col">精度：</div>
                <div class="col">{{ currentInd.jd || "无" }}</div>
                <div class="col">{{ indicator.jd || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.sswr_status">
                <div class="col">四舍五入：</div>
                <div class="col">{{ currentInd.sswr ? "开启" : "关闭" }}</div>
                <div class="col">{{ indicator.sswr ? "开启" : "关闭" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.jszq_status">
                <div class="col">计算周期：</div>
                <div class="col">{{ getLabel(jszqList, currentInd.jszq) }}</div>
                <div class="col">{{ getLabel(jszqList, indicator.jszq) }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.zeroWarnTime_status">
                <div class="col">0值预警：</div>
                <div class="col">
                  {{ getLabel(zeroList, currentInd.zeroWarnTime) }}
                </div>
                <div class="col">
                  {{ getLabel(zeroList, indicator.zeroWarnTime) }}
                </div>
              </div>
              <div
                class="table-row"
                v-if="!currentInd.tvmin_status || !currentInd.tvmax_status"
              >
                <div class="col">阈值：</div>
                <div class="col">
                  {{ currentInd.tvmin }} - {{ currentInd.tvmax }}
                </div>
                <div class="col">
                  {{ indicator.tvmin }} - {{ indicator.tvmax }}
                </div>
              </div>
            </div>

            <!-- SQL指标 (sq) -->
            <div class="table-content" v-if="parent.lxbm == 'sq'">
              <div
                class="table-row"
                v-if="!currentInd.fieldNameModified_status"
              >
                <div class="col">指标名称：</div>
                <div class="col">{{ currentInd.fieldNameModified }}</div>
                <div class="col">{{ indicator.fieldNameModified }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.scopeId_status">
                <div class="col">所属指标域：</div>
                <div class="col">
                  {{
                    getTreeLabel(viewGroup, currentInd.scopeId, "name", "id")
                  }}
                </div>
                <div class="col">
                  {{ getTreeLabel(viewGroup, indicator.scopeId, "name", "id") }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.deptAllCode_status">
                <div class="col">归属部门：</div>
                <div class="col">{{ currentInd.deptAllName || "无" }}</div>
                <div class="col">{{ indicator.deptAllName || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.description_status">
                <div class="col">描述：</div>
                <div class="col">{{ currentInd.description || "无" }}</div>
                <div class="col">{{ indicator.description || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.tagsName_status">
                <div class="col">标签：</div>
                <div class="col">
                  {{
                    (currentInd.tagsName && currentInd.tagsName.join("")) ||
                    "无"
                  }}
                </div>
                <div class="col">
                  {{
                    (indicator.tagsName && indicator.tagsName.join("")) || "无"
                  }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.sqlStatement_status">
                <div class="col">SQL表达式：</div>
                <div class="col">{{ currentInd.sqlStatement || "无" }}</div>
                <div class="col">{{ indicator.sqlStatement || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.dims_status">
                <div class="col">可扩展维度：</div>
                <div class="col">
                  {{
                    currentInd.dims.length
                      ? currentInd.dims
                          .map((item) => item.fieldNameModified)
                          .join("、")
                      : "无"
                  }}
                </div>
                <div class="col">
                  {{
                    indicator.dims.length
                      ? indicator.dims
                          .map((item) => item.fieldNameModified)
                          .join("、")
                      : "无"
                  }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.unitName_status">
                <div class="col">单位：</div>
                <div class="col">{{ currentInd.unitName || "无" }}</div>
                <div class="col">{{ indicator.unitName || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.dataFormat_status">
                <div class="col">数据格式：</div>
                <div class="col">
                  {{ getLabel(sjgs, currentInd.dataFormat) }}
                </div>
                <div class="col">
                  {{ getLabel(sjgs, indicator.dataFormat) }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.precision_status">
                <div class="col">精度：</div>
                <div class="col">{{ currentInd.precision || "无" }}</div>
                <div class="col">{{ indicator.precision || "无" }}</div>
              </div>
              <div class="table-row" v-if="!currentInd.rounding_status">
                <div class="col">四舍五入：</div>
                <div class="col">
                  {{ currentInd.rounding }}
                </div>
                <div class="col">
                  {{ indicator.rounding }}
                </div>
              </div>
              <div class="table-row" v-if="!currentInd.period_status">
                <div class="col">计算周期：</div>
                <div class="col">
                  {{ getLabel(jszqList, currentInd.period) }}
                </div>
                <div class="col">
                  {{ getLabel(jszqList, indicator.period) }}
                </div>
              </div>
              <div
                class="table-row"
                v-if="
                  !currentInd.thresholdMin_status ||
                  !currentInd.thresholdMax_status
                "
              >
                <div class="col">阈值：</div>
                <div class="col">
                  {{ currentInd.thresholdMin }} - {{ currentInd.thresholdMax }}
                </div>
                <div class="col">
                  {{ indicator.thresholdMin }} - {{ indicator.thresholdMax }}
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- 底部操作按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button class="action-btn" @click="dialogVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            class="action-btn"
            @click="handleRollbackSubmit"
          >
            确认回滚
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import options from "../../../indicator-anagement/mixins/options"
// import cloneDeep from "lodash/cloneDeep"

export default {
  components: {},
  mixins: [options],
  props: {},
  data() {
    return {
      dialogVisible: false,
      dialogAttrVisible: false,
      yzzbList: [],
      changeRecordList: [],
      indicator: {
        pswd: [],
        xsc: [],
        extDim: [],
        expandDims: [],
        dims: [],
      },
      currentInd: {
        pswd: [],
        xsc: [],
        extDim: [],
        expandDims: [],
        dims: [],
      }, // 当前指标
      currentCode: "",
      scopeList: [],
      sqlEditList: [],
      sqlDimList: [],
      mapLevelCodeValues: {},
    }
  },

  created() {
    this.getChangeRecord()
    this.getAllViewGroup()
    this.getYzList()
  },
  mounted() {},
  inject: ["parent"],
  watch: {},
  methods: {
    // 获取变更记录
    async getChangeRecord() {
      const { data } = await this.$httpBi.api.paramGet(
        "/IndUpdRec/getIndUpdRecordsByInd",
        {
          indCode: this.parent.indCode,
        }
      )
      this.changeRecordList = data
    },
    // 展示属性弹窗
    async showAttrDialog(row) {
      this.currentCode = row.historyCode
      const { data } = await this.$httpBi.api.paramGet(
        "/IndUpdRec/getIndHistoryByHistoryCode",
        {
          historyCode: row.historyCode,
        }
      )

      if (this.parent.lxbm === "sq") {
        let indObj = data.indJson.data.find((item) => item.tagType === "度量")
        this.indicator = this.indicator = {
          ...indObj,
          changeTime: data.changeTime,
          dims:
            data.indJson.data.filter((item) => item.tagType !== "度量") || [],
          scopeId: indObj.scopeId === 0 ? "0" : indObj.scopeId,
        }
        console.log(this.indicator, "this.indicator ")
        this.sqlEditList = data.indJson.data || []
      } else {
        this.indicator = {
          changeTime: data.changeTime,
          ...data.indJson,
          sysjy: data.indJson.sysjy === 0 ? "0" : data.indJson.sysjy,
        }
      }

      if (this.parent.lxbm === "ps") {
        this.indicator.xsc.forEach(async (item) => {
          this.getDimValues(item.levelCode)
        })
      }
      this.dialogAttrVisible = true
    },
    // 回滚
    async handleRollback() {
      this.ruleChange = []
      this.getRuleChange()
      this.getScopeList()
      this.dialogVisible = true
    },
    // 确定回滚
    async handleRollbackSubmit() {
      let saveUrl = ""
      let params = this.indicator
      // 原子
      if (this.parent.lxbm === "yz") {
        saveUrl = "/AtomIndicator/editAtomIndicator"
      }
      // 派生
      if (this.parent.lxbm === "ps") {
        saveUrl = "/DeriveIndicator/editDeriveIndicator"
      }
      // 衍生
      if (this.parent.lxbm === "ys") {
        saveUrl = "/compositeIndicator/save"
        params = [this.indicator]
      }
      // sql
      if (this.parent.lxbm === "sq") {
        saveUrl = "/SqlIndicator/saveEdit"
        params = this.sqlEditList.map((item) => ({
          ...item,
          groupName:'old',
          newSqlStatement:item.sqlStatementOrigin,
        }))
      }
      const { code } = await this.$httpBi.api.paramPost(saveUrl, {
        ind: params,
        optType: "rollback",
      })
      if (code === 200) {
        this.$message({
          message: "回滚成功",
          type: "success",
        })
        this.dialogVisible = false
      }
    },
    // 获取规则变化
    async getRuleChange() {
      const { data } = await this.$httpBi.api.paramGet(
        "/IndUpdRec/getIndRule",
        {
          historyCode: this.currentCode,
        }
      )
      if (this.parent.lxbm === "sq") {
        this.currentInd = {
          ...data.data.find((item) => item.tagType === "度量"),
          dims: data.data.filter((item) => item.tagType !== "度量") || [],
        }
      } else {
        this.currentInd = data
      }

      if (this.parent.lxbm === "ps") {
        this.currentInd.xsc.forEach(async (item) => {
          this.getDimValues(item.levelCode)
        })
      }
    },
    // 获取影响范围
    async getScopeList() {
      const { data } = await this.$httpBi.api.paramGet(
        "/IndUpdRec//getIndRollBackScope",
        {
          indCode: this.parent.indCode,
          indType: this.parent.lxbm,
        }
      )
      this.scopeList = data || []
    },

    // 获取原子指标
    async getYzList() {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: "",
      })
      this.yzzbList = data
    },
    // 通用转换函数
    getLabel(options, value, labelKey = "label", valueKey = "value") {
      if (
        value === "" ||
        value === undefined ||
        value === null ||
        !options ||
        !options.length
      ) {
        return "无"
      }
      const item = options.find((opt) => opt[valueKey] === value)
      if (item) {
        return item[labelKey]
      }
      return value
    },
    // 树形结构label转换使用getLabel函数封装
    getTreeLabel(
      options,
      value,
      labelKey = "label",
      valueKey = "value",
      childrenKey = "children"
    ) {
      const item = options.find((opt) => opt[valueKey] === value)
      if (item) {
        return item[labelKey]
      }
      if (options.length > 0) {
        for (let i = 0; i < options.length; i++) {
          const element = options[i]
          if (element[childrenKey] && element[childrenKey].length > 0) {
            const label = this.getTreeLabel(
              element[childrenKey],
              value,
              labelKey,
              valueKey,
              childrenKey
            )
            if (label) {
              return label
            }
          }
        }
      }
      return value
    },
    async getDimValues(levelCode) {
      if (this.mapLevelCodeValues[levelCode]) {
        return
      }
      const { data } = await this.$httpBi.api.paramPostQuery(
        "/DimManage/getDimValueByLevelCode",
        {
          levelCode: levelCode,
          dimId: "",
          indType: "",
          size: -1,
          page: 1,
        }
      )
      this.$set(this.mapLevelCodeValues, levelCode, data.list)
      console.log(this.mapLevelCodeValues, "this.mapLevelCodeValues")
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 20px;
}
.rule-rollback-dialog {
  background: #f5f5f5;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;

    .title {
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      color: #999;
      cursor: pointer;
      font-size: 18px;

      &:hover {
        color: #666;
      }
    }
  }

  .dialog-content {
    .section-title {
      height: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #222222;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      margin-bottom: 12px;
    }

    .scope-section {
      margin-bottom: 24px;
      .info-group {
        width: 100%;
        height: 120px;
        background: #f5f7fa;
        border-radius: 8px;
        padding: 16px;
        box-sizing: border-box;
      }
      .info-item {
        display: flex;
        margin-bottom: 20px;
        font-size: 13px;

        .label {
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
        }
      }
    }

    .rule-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .compare-table {
        // border: 1px solid #e8e8e8;
        border-radius: 8px;
        background: #f5f7fa;
        padding: 16px;
        box-sizing: border-box;

        // &::-webkit-scrollbar {
        //   width: 8px;
        //   height: 8px;
        // }
        // &::-webkit-scrollbar-track {
        //   background: #f5f7fa;
        // }
        // &::-webkit-scrollbar-thumb {
        //   background: #e8e8e8;
        //   border-radius: 4px;
        // }

        .table-header,
        .table-row {
          display: flex;
          padding: 12px;

          .col {
            flex: 1;
            color: #333;
            font-size: 13px;
            line-height: 16px;
            margin-right: 10px;

            // &:not(:last-child) {
            //   border-right: 1px solid #e8e8e8;
            // }
          }
        }
        .table-content {
          overflow: auto;
          height: 300px;
          //滚动条样式
          &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }
          &::-webkit-scrollbar-track {
            background: #f5f7fa;
          }
          &::-webkit-scrollbar-thumb {
            background: #e8e8e8;
            border-radius: 4px;
          }
        }

        // .diff {
        //   color: #f56c6c;
        //   background: #fef0f0;
        // }
      }
    }
  }

  .dialog-footer {
    padding: 16px 0 0;
    border-top: 1px solid #e8e8e8;
    text-align: right;

    .action-btn {
      margin-left: 12px;
      padding: 8px 20px;
      border-radius: 4px;
    }
  }
}
.indicator-attr-dialog {
  background: #fff;
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    .title {
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
    .close-btn {
      color: #999;
      cursor: pointer;
      font-size: 18px;
      &:hover {
        color: #666;
      }
    }
  }
  .attr-content {
    padding: 20px 24px 0 24px;
    .attr-row {
      margin-bottom: 14px;
      font-size: 14px;
      line-height: 18px;
      .attr-label {
        color: #666;
        font-weight: 500;
        text-align: right;
        padding-right: 8px;
      }
    }
  }
  .dialog-footer {
    padding: 16px 0 0;
    border-top: 1px solid #e8e8e8;
    text-align: right;
    .action-btn {
      margin-left: 12px;
      padding: 8px 20px;
      border-radius: 4px;
    }
  }
}
</style>
