<template>
  <div style="display: flex">
    <ul class="list">
      <li
        class="list-item-custom"
        draggable
        v-for="(item, index) in tables"
        :key="index"
        @dragstart="dragStart(item, $event)"
        @dragend="dragEnd"
      >
        {{ item.tableName }}
      </li>
    </ul>
    <div
      class="sql-view"
      @drop="drop"
      @dragover="dragover"
      ref="sqlView">
      <div class="table-links-wrapper">
        <table-wrapper
          @drop="drop"
          @dragover="dragover"
          root
          :dargging="dargging"
          :tree="tree"
          v-for="(item, index) in tree"
          :item="item"
          :index="index"
          :key="index"
        />
        <div
          v-if="!dragTables.length"
          class="empty-table-tips-wrapper"
          :class="{ dargging: dargging }"
        >
          <div class="empty-table-tips-wrapper">
            <div class="empty-table-tips">请从左侧拖拽数据表开始创建</div>
          </div>
        </div>
        <div class="table-link-help-wrapper" v-if="dragTables.length == 1">
          <div class="table-link-help-title">
            继续从左侧拖拽数据表进行表关联
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TableWrapper from "./table-wrapper"
export default {
  components: {
    TableWrapper,
  },
  props: {},
  data() {
    return {
      dragged: null,
      dargging: false,
      tree: [],
      links: [
        {
          source: null,
          target: "d175dda743",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "d175dda743",
          target: "dc17b42ade",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "d175dda743",
          target: "baf83cd782",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "d175dda743",
          target: "970ca16a83",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "970ca16a83",
          target: "d2f4d25633",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "baf83cd782",
          target: "c13641c34f",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "baf83cd782",
          target: "c13641c35f",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "baf83cd782",
          target: "c13641c36f",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
        {
          source: "970ca16a83",
          target: "c4a9f2a681",
          joinType: "LEFT_OUTER_JOIN",
          relation: "many-to-one",
          joinKeys: [],
        },
      ],
      tables: [
        {
          uniqueId: "d175dda743",
          caption: "ads_tb_slr_itm_olap_1d_test",
          fromPanel: false,
          tableName: "ads_tb_slr_itm_olap_1d_test",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: true,
          dsType: "mysql",
        },
        {
          uniqueId: "dc17b42ade",
          caption: "ads_tb_slr_src_flow_olap_1d_test",
          fromPanel: false,
          tableName: "ads_tb_slr_src_flow_olap_1d_test",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
        {
          uniqueId: "baf83cd782",
          caption: "ads_tb_slr_mbr_olap_1d_test",
          fromPanel: false,
          tableName: "ads_tb_slr_mbr_olap_1d_test",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
        {
          uniqueId: "970ca16a83",
          caption: "airbnb_listings",
          fromPanel: false,
          tableName: "airbnb_listings",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
        {
          uniqueId: "d2f4d25633",
          caption: "airbnb_calendar",
          fromPanel: false,
          tableName: "airbnb_calendar",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
        {
          uniqueId: "c13641c34f",
          caption: "aipl_shop_offline",
          fromPanel: false,
          tableName: "aipl_shop_offline",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
        {
          uniqueId: "c4a9f2a681",
          caption: "aipl_user_online",
          fromPanel: false,
          tableName: "aipl_user_online",
          datasourceId: "4a04a20d-f665-4318-85dc-e776042bdedc",
          factTable: false,
          dsType: "mysql",
        },
      ],
      dragTables: [],
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {
    dragTables: {
      handler(val, ) {
        this.tree = this.arrayToTree(val)
        console.log(this.tree, "tree")
      },
      deep: true,
    },
  },
  methods: {
    // 拖拽开始
    dragStart(data,) {
      const index = this.dragTables.findIndex(
        (item) => item.uniqueId === data.uniqueId
      )
      if (index > -1) {
        return this.$message.warning("当前数据表已经存在，请不要重复关联哦")
      }
      this.dragged = data
      this.dargging = true
      console.log(this.dragged, "拖拽开始")
    },
    // 拖拽结束
    dragEnd() {
      console.log("拖拽结束")
      if (this.dragged) {
        this.dragged = null
      }
      this.dargging = false
    },
    // 允许放下拖拽
    dragover(e) {
      console.log("容器范围内拖动")
      e.preventDefault()
    },
    // 放下事件
    drop(e, source) {
      console.log("容器放下事件", source)
      e.preventDefault()
      // 如果已经拖拽了根元素就不能再拖拽了
      if (this.dragTables.length && !source) {
        return
      }
      this.dragTables.push({
        ...this.dragged,
        source,
        target: this.dragged.uniqueId,
      })
      console.log(this.dragTables, "this.dragTables")
    },
    // 数组转树
    arrayToTree(
      items,
      idKey = "target",
      pidKey = "source",
      childKey = "children"
    ) {
      const result = [] // 存放结果集
      const itemMap = {} //
      for (const item of items) {
        itemMap[item[idKey]] = { ...item, [childKey]: [] }
      }
      console.log(itemMap, "itemMap")
      for (const item of items) {
        const id = item[idKey]
        const pid = item[pidKey]
        const treeItem = itemMap[id]

        if (!itemMap[pid]) {
          result.push(treeItem)
        } else {
          itemMap[pid][childKey].push(treeItem)
        }
      }
      return this.renderTree(result, 0)
    },
    renderTree(data, level) {
      return data.map((item) => {
        if (item.children) {
          this.renderTree(item.children, level + 1)
        }
        item.level = level
        return item
      })
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  width: 300px;
  padding: 50px;
  border: 1px solid #ccc;
  .list-item-custom {
    width: 200px;
    height: 30px;
    background-color: skyblue;
    margin: 10px 0;
    line-height: 30px;
    cursor: move;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.sql-view {
  margin-left: 20px;
  width: 1000px;
  height: 500px;
  padding: 40px;
  box-sizing: border-box;
  // border: 1px solid red;
  text-align: center;
  overflow: auto;
  .table-links-wrapper {
    position: relative;

    width: 100%;
    height: 100%;
    .table-wrapper {
      .table-link-tip {
        position: absolute;
        align-items: center;
        display: flex;
        justify-content: center;
        cursor: pointer;
        .table-link-line {
          position: absolute;
          z-index: 2;
          background: #c1c1c1;
        }
        .table-link-line.vertical {
          width: 1px;
          left: 18px;
        }
        .table-link-line.horizontal {
          right: 0;
          height: 1px;
        }
      }
      .table-info {
        height: 28px;
        width: 180px;
        background: #fff;
        position: absolute;
        color: #000;
        cursor: pointer;
        padding: 1px 1px 1px 0;
        line-height: 28px;
        border-left: 2px solid #2153d4;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .empty-table-tips-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      border: 1px solid transparent;
      color: rgba(0, 0, 0, 0.65);
      &.dargging {
        border: 1px dashed #c6c6c6;
        background-color: #f8f8f8;
      }
    }
    .table-link-help-wrapper {
      text-align: center;
      height: 100%;
      padding-top: 30px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
</style>
