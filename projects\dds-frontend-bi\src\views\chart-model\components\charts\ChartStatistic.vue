<template>
  <div class="card-container">
    <!-- 循环生成卡片 -->
    <div v-for="(value, index) in values" :key="index" class="card-item">
      <div class="card-title">标题{{ index + 1 }}</div>
      <div class="card-data">{{ value }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 接收父组件传入的数据
    dataList: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  computed: {
    // 提取数据中的值列表
    values () {
      if (this.dataList.length === 0) return []
      // 获取第一个对象的所有值
      return Object.values(this.dataList[0])
    }
  }
}
</script>

<style scoped lang="scss">
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px;
}

.card-item {
  width: 200px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f2f3f5;
}

.card-data {
  font-size: 14px;
  color: #4e5969;
  word-break: break-all;
}
</style>
