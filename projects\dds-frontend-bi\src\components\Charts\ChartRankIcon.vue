<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"

import { getTextWidth, toThousands } from "@/utils"

import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "value"
    },
    // Y轴字段
    yField: {
      type: String,
      default: "name"
    },
    // 系列名称
    seriesName: {
      type: String,
      default: ""
    },
    xAxisName: {
      type: String,
      default: ""
    },
    // 颜色
    color: {
      type: Array,
      default: () => ["#2361DB"]
    },
    // 柱状图宽
    barWidth: {
      type: Number,
      default: 12
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: "auto"
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: false
    },
    sortType: {
      type: String,
      default: "asc"
    },
    isDrill: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      maxWidth: 0,
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      let max = 0

      this.chartData.forEach(item => {
        max = Math.max(max, item[this.xField])

        this.maxWidth = Math.max(
          getTextWidth(item[this.yField].slice(0, 9), 12),
          this.maxWidth
        )
      })

      const series = []
      let dataset = null
      series.push(
        this.createSeriesObject({
          name: this.seriesName ?? this.yField,

          color: this.color[0]
        })
      )
      ;(dataset = [
        { dimensions: [this.yField, this.xField], source: this.chartData },
        {
          transform: {
            type: "sort",
            config: { dimension: this.xField, order: this.sortType }
          }
        }
      ]),
        this.chart.setOption({
          dataset,
          tooltip: {
            // trigger: "axis",

            // triggerOn: "click",
            trigger: this.isDrill ? "item" : "axis",

            axisPointer: {
              show: false,
              type: "shadow",
              label: { show: false, backgroundColor: "transparent" },
              shadowStyle: {
                color: "rgba(35,97,219,0.05)"
              }
            },
            formatter: params => {
              if (this.isDrill) {
                return `<div style="color:#fff;font-size:14px;font-weight: 500">点击查看详情</div>`
              }

              return `<div>
              <p class="tooltip-title">${params[0].seriesName}</p>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
                   params[0].color
                 }" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${toThousands(
                  params[0].value[
                    params[0].dimensionNames[params[0].encode.x[0]]
                  ]
                )}${this.unit}
                </span>
              </div>
            </div>`
            },
            className: this.isDrill
              ? "echarts-tooltip-drill"
              : "echarts-tooltip-diy"
          },

          grid: {
            right: 0,
            left: this.maxWidth + 32 + 24,
            bottom: this.bottom,
            top: 20
            // containLabel: true,
          },
          legend: {
            show: false
          },
          xAxis: {
            max: function () {
              return max * 1.2
            },
            position: "top",
            nameLocation: "end",
            type: "value",
            axisLabel: {
              show: false,
              fontSize: 12,
              color: "#000",
              fontWeight: "400",
              interval: 0,
              rotate: 0 // 倾斜角度
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },

          yAxis: [
            {
              type: "category",
              triggerEvent: true,
              axisLabel: {
                fontSize: 12,
                margin: this.maxWidth + 32 + 24,
                color: "#8294A2",
                align: "left",
                fontWeight: "400",
                formatter: (value, index) => {
                  let len = this.chartData.length
                  let name =
                    value.length > 7 ? value.substr(0, 7) + "..." : value
                  if (len - index - 1 > 2) {
                    return `{idx|${len - index}} {title|${name}}`
                  }
                  return `{idx${len - index - 1}|} {title|${name}}`
                },
                rich: {
                  idx0: {
                    width: 20,
                    height: 24,
                    fontSize: 12,
                    color: "#000",
                    align: "left",
                    backgroundColor: {
                      image: require("@/assets/images/NO1.png")
                    }
                  },
                  idx1: {
                    width: 20,
                    height: 24,
                    fontSize: 12,
                    color: "#000",
                    backgroundColor: {
                      image: require("@/assets/images/NO2.png")
                    }
                  },

                  idx2: {
                    width: 20,
                    height: 24,
                    fontSize: 12,
                    color: "#000",
                    backgroundColor: {
                      image: require("@/assets/images/NO3.png")
                    }
                  },
                  idx: {
                    width: 10,
                    height: 24,
                    fontSize: 14,
                    color: "#222",
                    padding: [0, 2, 0, 6],
                    fontFamily: "PingFangSC-Regular, PingFang SC"
                  },
                  title: {
                    width: 150,
                    fontSize: 14,
                    align: "left",
                    padding: [0, 0, 0, 13],

                    fontFamily: "PingFangSC-Regular, PingFang SC",
                    fontWeight: 400,
                    color: "#222222"
                  }
                }
              },
              splitLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            }
          ],
          graphic: [
            {
              type: "text",
              top: 5,
              right: 0,
              style: {
                text: this.xAxisName,
                fill: "#969799",
                stroke: "#969799",
                fontSize: 12,
                fontFamily: "PingFangSC-Regular, PingFang SC"
              }
            }
          ],
          series
        })

      // extension(this.chart, "yAxis")
    },
    // 创建系列对象
    createSeriesObject({ name, color, index = 1, yField = this.yField }) {
      return {
        type: "bar",
        barWidth: this.barWidth,
        name: name,
        itemStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },
        datasetIndex: index,
        label: {
          show: true,
          color: "#323233",
          fontSize: 12,
          position: "right",
          offset: [0, 0],
          formatter: params => {
            return (
              toThousands(
                params.value[params.dimensionNames[params.encode.x[0]]]
              ) + this.unit
            )
          }
        }
      }
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.x[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-drill {
  background: rgba(48, 49, 51, 1) !important;
}
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    min-width: 220px;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 9px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
