<template>
  <el-dialog
    :title="portalInfo.id ? '设置看板' : '新增看板'"
    :border="true"
    :visible.sync="dialogShow"
    width="50%"
    @close="handleCancel"
  >
    <el-form
      :model="portalInfo"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="主题" prop="theme">
        <el-select
          v-model="portalInfo.theme"
          placeholder="请选择主题"
          style="width: 100%"
        >
          <el-option
            v-for="item in themes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="portalInfo.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="portalInfo.description"
          placeholder="请输入描述"
        ></el-input>
      </el-form-item>
      <el-form-item label="权限设置" prop="permission">
        <el-radio-group v-model="portalInfo.permission">
          <el-radio
            :label="item.value"
            v-for="(item, key) in permissionList"
            :disabled="item.value === 2 && !portalInfo.id"
            :key="key"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
        <el-tooltip
          class="item"
          effect="dark"
          content="角色权限设置,需要新增保存后可编辑"
          placement="top"
        >
          <span style="margin-left: 5px" v-if="!portalInfo.id">
            <i class="el-icon-question"></i>
          </span>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        ref="role"
        class="is-required"
        :label="'角色'"
        prop="roles"
        v-if="form.permission === 2"
        :rules="[{ validator: rules.rolesRule }]"
      >
        <el-scrollbar class="block-view" style="height: 220px">
          <el-checkbox-group
            v-model="form.roles"
            @change="validateFormSlot('role')"
          >
            <div
              class="checkbox-item-block"
              v-for="item in roleList"
              :key="item.roleId"
            >
              <el-checkbox :label="item.roleId">
                <div class="checkbox-item">
                  <div>{{ item.roleTitle }}</div>
                  <div class="item-content">{{ item.remark }}</div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-scrollbar>
      </el-form-item>
      <!-- <el-form-item label="是否发布">
              <el-radio-group v-model="portalInfo.publish">
                <el-radio :label="true">发布</el-radio>
                <el-radio :label="false">编辑</el-radio>
              </el-radio-group>
            </el-form-item> -->
      <!-- <el-form-item label="显示水印">
                <el-switch
                  v-model="portalInfo.sy"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: "info",
      rules: {
        theme: [{ required: true, message: "请选择主题", trigger: "blur" }],
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        rolesRule: (rule, value, callback) => {
          if (this.form.roles.length === 0) callback(new Error("请选择角色"))
          else callback()
        }
      },
      themes: [],
      roleList: [],
      permissionList: [
        {
          label: "全不可见",
          value: 0
        },
        {
          label: "全部可见",
          value: 1
        },
        {
          label: "角色可见",
          value: 2
        }
      ]
    }
  },
  computed: {
    portalInfo() {
      return this.form
    }
  },
  created() {},
  mounted() {
    this.getAllThemes()
    this.getAllRole()
  },
  watch: {
    "portalInfo.id": {
      handler() {
        if (this.portalInfo.id) {
          this.dashboardRoles()
        }
      },
      deep: true
    }
  },
  methods: {
    async getAllRole() {
      const { data } = await Request.getAllRole()
      this.roleList = data
    },
    // 获取看板权限
    async dashboardRoles() {
      const { data } = await Request.dashboard.dashboardRoles({
        id: this.portalInfo.id
      })
      this.form.roles = data
      console.log(data)
    },
    // 初始化表格数据
    getAllThemes() {
      this.loading = true
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    handleCancel() {
      this.$emit("update:dialogShow", false)
    },

    // 表单插槽校验
    validateFormSlot(ref_key) {
      if (ref_key === "all") {
        Object.keys(this.$refs).map(
          item =>
            this.$refs[item] &&
            this.$refs[item].validate &&
            this.$refs[item].validate()
        )
      } else {
        this.$refs[ref_key].validate && this.$refs[ref_key].validate()
      }
    },
    // 提交保存
    async handleSave() {
      this.validateFormSlot("all")
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          this.$emit("onSave", this.portalInfo)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/.el-form {
  .realInfo-mask {
    position: relative;
    width: 100%;
    height: 32px;
    margin-top: -32px;
    background: transparent;
    cursor: pointer;
    &.disabled {
      cursor: not-allowed;
    }
  }
  .block-view {
    background: #f8f8f8;
    padding: 15px 30px;
    width: 90%;
    max-width: 900px;
    .title {
      font-weight: bolder;
      margin-bottom: 10px;
    }
    .el-radio-group {
      width: 100%;
      .el-radio {
        width: 33%;
        margin-right: 0;
        vertical-align: top;
        .el-radio__input {
          vertical-align: top;
        }
        .radio-item {
          width: 85%;
          font-size: 13px;
          display: inline-block;
          .item-content {
            color: #cccccc;
            margin: 10px 0;
          }
        }
      }
    }
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      .checkbox-item-block {
        width: 33%;
        height: 100px;
        .el-checkbox {
          background: white;
          width: 90%;
          height: 80px;
          padding: 20px;
          margin-left: 5%;
          margin-top: 10px;
          box-shadow: 0px 0px 10px 2px #f3eeee;
          .el-checkbox__input {
            vertical-align: top;
            margin-top: 3px;
          }
          .el-checkbox__label {
            width: 90%;
            vertical-align: top;
            .checkbox-item {
              font-size: 13px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              .item-content {
                margin: 10px 0;
                color: #cccccc;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
    &.el-scrollbar {
      padding: 0;
      .el-scrollbar__view {
        padding: 15px;
      }
      .el-scrollbar__bar.is-horizontal {
        display: none;
      }
    }
  }
  .is-error {
    .block-view {
      border: 1px solid #ff4949;
    }
  }
}
</style>
