<template>
  <div :style="{ height, width }" class="chart-wrap">
    <!-- 添加放大缩小按钮 -->
    <div class="zoom-controls">
      <div class="zoom-out" @click="zoomOut"></div>
      <div class="zoom-value">{{ (currentZoom * 100).toFixed(0) }}%</div>
      <div class="zoom-in" @click="zoomIn"></div>
    </div>
    <div id="myChat" ref="chartRef"></div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
import { formatValue,  } from "@/utils"

// 防抖
export default {
  components: {},
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    indicatorData:{
      type:Object,
      default:()=>{
        return {
          zbmc:""
        }
      }
    },

    // 颜色
    color: {
      type: Array,
      default: () => [
        "#5B8FF9",
        "#5AD8A7",
        "#6B84BD",
        "#FF9845",
        "#FCD473",
        "#FF99C3",
        "#FC7364",
        "#77D2E5"
      ]
    }
  },
  data() {
    return {
      chart: null,
      currentZoom: 1, // 当前缩放比率，初始为 1
      zoomStep: 0.1, // 缩放步长
      // 初始坐标轴范围
      xAxisRange: [-80, 80],
      yAxisRange: [-80, 80],
      initialXRange: [-80, 80], // 初始 X 轴范围
      initialYRange: [-80, 80] // 初始 Y 轴范围
    }
  },
  computed: {},
  watch: {
    chartData: {
      deep: true,
      handler() {
        console.log('出事阿虎')
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.chart.on("click", params => {
        console.log(params,'params')
        if (params.componentType === "series") {
          // 打开对话框
          this.$emit("openDialog",params.data)
        }
      })
      console.log('初始化图表')
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      // 计算节点位置

      const positiveNodes = this.calculatePositions(
        this.chartData.positive,
        25, // 正向指标基础半径
        150, // 起始角度（左上）
        30 // 结束角度（右上）
      )

      const negativeNodes = this.calculatePositions(
        this.chartData.negative,
        25, // 负向指标基础半径
        210, // 起始角度（左下）
        330 // 结束角度（右下）
      )
      // const positiveNodes = this.calculatePositions(
      //   this.chartData.positive,
      //   true
      // )
      // const negativeNodes = this.calculatePositions(
      //   this.chartData.negative,
      //   false
      // )
      console.log(negativeNodes, positiveNodes)
      // // 构建节点数据
      const data = [
        // 中心节点
        {
          name: this.indicatorData.zbmc,

          value: [0, 0],

          fixed: true,
          symbolSize: 108,
          category: 1,
          itemStyle: {
            color: "#fff",
            shadowColor: "rgba(14,49,161,0.12)",
            shadowBlur: 12
          },
          label: {
            show: true,
            position: "inside",
            fontSize: 16,
            color: "#222222",
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: "500"
          }
        },
        ...positiveNodes.map(node => ({
          ...node,
          itemStyle: {
            color: "#fff",
            borderColor: "#00CC88",
            shadowColor: "rgba(0,204,136,0.12)",
            shadowBlur: 12
          },

          // 大小与相关度正相关
          symbolSize: 64
        })),
        ...negativeNodes.map(node => ({
          ...node,
          itemStyle: {
            color: "#fff",
            borderColor: "#FF5256",
            shadowColor: "rgba(255,82,86,0.12)",
            shadowBlur: 12
          },
          // 大小与相关度正相关
          symbolSize: 64
        }))
      ]

      // // 构建连接数据
      const links = [
        ...positiveNodes.map(node => ({
          source: this.indicatorData.zbmc,
          target: node.name,
          value: node.value,
          lineStyle: {
            color: "rgba(0, 204, 136, 0.40)",
            width: 1 // 线宽反映相关强度
          }
        })),
        ...negativeNodes.map(node => ({
          source: this.indicatorData.zbmc,
          target: node.name,
          value: node.value,
          lineStyle: {
            color: "rgba(255, 82, 86, 0.40)",
            width: 1
          }
        }))
      ]
      // 获取图表容器的尺寸
      // const height = chartDom.offsetHeight

      this.chart.setOption({
        graphic: [
          {
            type: "text",
            top: 323,
            right: 40,
            style: {
              text: "强正向相关度",
              fill: "#00CC88",
              stroke: "#00CC88",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          },
          {
            type: "image",
            style: {
              image: require("@/assets/images/down1.png"),
              width: 8,
              height: 56
            },
            top: 256,
            right: 72
          },
          {
            type: "text",
            top: 359,
            right: 40,
            style: {
              text: "强负向相关度",
              fill: "#FF5256",
              stroke: "#FF5256",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          },
          {
            type: "image",
            style: {
              image: require("@/assets/images/up1.png"),
              width: 8,
              height: 56
            },
            top: 381,
            right: 72
          }
        ],
        xAxis: {
          show: true,
          type: "value",
          min: this.xAxisRange[0], // 使用动态范围
          max: this.xAxisRange[1], // 使用动态范围
          axisLine: {
            lineStyle: {
              type: "dashed", // 虚线
              color: "rgba(21, 99, 255, 0.40)" // 线条颜色
            }
          },
          splitLine: {
            show: false // 是否显示分隔线
          },
          splitArea: {
            show: false // 是否显示分隔区域。
          },
          axisTick: {
            show: false // 是否显示坐标轴刻度。
          }
        },
        yAxis: {
          show: false,
          type: "value",
          min: this.yAxisRange[0], // 使用动态范围
          max: this.yAxisRange[1] // 使用动态范围
        },
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
         tooltip: {
          trigger: "item",
          confine: true,
          className: "echarts-tooltip-diy",
          formatter: params => {
            return `<div class="content-panel">
        <p>
          <span style="background-color: ${
            params.data.itemStyle.borderColor
          }" class="tooltip-item-icon"></span>
          <span>${params.name}</span>
        </p>
        <span class="tooltip-value">
          ${formatValue(params.data.val??"")}
        </span>
      </div>`
          }
        },
        series: [
          {
            type: "graph",
            zlevel: 5,
            draggable: false,
            zoom: 1,
            coordinateSystem: "cartesian2d", // 使用二维的直角坐标系（也称笛卡尔坐标系）
            symbol: "circle",
            label: {
              show: true,
              position: "inside",
              fontSize: 12,
              color: "#222222"
            },
            data: data,
            links: links,
            /** 设置服务器件的连线样式*/
            lineStyle: {
              opacity: 1,
              color: "#53B5EA",
              width: 2
            }
            // 启用鼠标滚轮缩放
          }
        ]
      })
    },
    // 计算节点位置
    calculatePositions(nodes, baseRadius, startAngle, endAngle) {
      const total = nodes.length
      if (total === 0) return []

      // 检查节点是否重叠
      const isOverlap = (newPos, existingPositions, minDistance) => {
        for (const pos of existingPositions) {
          const dx = newPos[0] - pos[0]
          const dy = newPos[1] - pos[1]
          const distance = Math.sqrt(dx * dx + dy * dy)
          if (distance < minDistance) {
            return true
          }
        }
        return false
      }

      let currentRadius = baseRadius
      const positions = []

      for (const node of nodes) {
        console.log(node)
        let newPos
        let attempts = 0
        const maxAttempts = 100 // 最大尝试次数
        do {
          const randomAngle =
            Math.random() * (endAngle - startAngle) + startAngle
          const x = Math.max(
            -99,
            Math.min(
              99,
              currentRadius * Math.cos((randomAngle * Math.PI) / 180)
            )
          )
          let y = currentRadius * Math.sin((randomAngle * Math.PI) / 180)
          if (startAngle < 180) {
            // 正向节点，Y > 0
            y = Math.max(1, Math.min(99, y))
          } else {
            // 负向节点，Y < 0
            y = Math.max(-99, Math.min(-1, y))
          }
          newPos = [x, y]
          attempts++
          if (attempts > maxAttempts) {
            currentRadius += 20
            attempts = 0
          }
        } while (isOverlap(newPos, positions, 20)) // 最小间距设为 20
        positions.push(newPos)
      }

      return nodes.map((node, index) => ({
        ...node,
        val: node.value,
        value: positions[index]
      }))
    },

    // 高亮某个节点
    highlightNode(nodeId) {
      this.chart.dispatchAction({
        type: "downplay",
        seriesIndex: 0
      })

      setTimeout(() => {
        this.chart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: nodeId
        })
        console.log("高亮")
      }, 100)
    },
    // 将某个节点固定在中心
    locationNode(index) {
      setTimeout(() => {
        // 获取所有节点位置信息

        var positions = this.chart
          .getModel()
          .getSeriesByIndex(0)
          .getData()._itemLayouts

        this.chart.setOption({
          series: [
            {
              center: positions[index]
            }
          ]
        })
      }, 1000)
    },
    // 放大
    zoomIn() {
      const range = this.calculateNewRange(this.zoomStep)
      this.updateAxisRange(range)
      this.currentZoom *= 1 + this.zoomStep // 更新缩放比率
    },
    // 缩小
    zoomOut() {
      const range = this.calculateNewRange(-this.zoomStep)
      this.updateAxisRange(range)
      this.currentZoom /= 1 + this.zoomStep // 更新缩放比率
    },
    // 计算新的坐标轴范围
    calculateNewRange(step) {
      const xCenter = (this.xAxisRange[0] + this.xAxisRange[1]) / 2
      const yCenter = (this.yAxisRange[0] + this.yAxisRange[1]) / 2
      const xHalfRange =
        ((this.xAxisRange[1] - this.xAxisRange[0]) / 2) * (1 - step)
      const yHalfRange =
        ((this.yAxisRange[1] - this.yAxisRange[0]) / 2) * (1 - step)
      return {
        x: [xCenter - xHalfRange, xCenter + xHalfRange],
        y: [yCenter - yHalfRange, yCenter + yHalfRange]
      }
    },
    // 更新坐标轴范围
    updateAxisRange(range) {
      this.xAxisRange = range.x
      this.yAxisRange = range.y
      this.chart.setOption({
        xAxis: {
          min: this.xAxisRange[0],
          max: this.xAxisRange[1]
        },
        yAxis: {
          min: this.yAxisRange[0],
          max: this.yAxisRange[1]
        }
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  .zoom-controls {
    position: absolute;
    bottom: 40px;
    right: 0;
    z-index: 10;
    width: 120px;
    height: 36px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    box-sizing: border-box;
    .zoom-in {
      width: 16px;
      height: 16px;
      background: url("~@/assets/images/add.png") no-repeat center;
      background-size: cover;
      cursor: pointer;
    }
    .zoom-value {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #222222;
      line-height: 12px;
      text-align: right;
      font-style: normal;
    }
    .zoom-out {
      width: 16px;
      height: 16px;
      background: url("~@/assets/images/zoom-out.png") no-repeat center;
      background-size: cover;
      cursor: pointer;
    }
  }
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>

// 放大
