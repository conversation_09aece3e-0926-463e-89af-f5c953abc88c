import service from "../base"
import config from "../config"

/**
 * 数据源
 */
export default {
  getAll() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/getAll",
      method: "get"
    })
  },
  getOne(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/getOne",
      method: "get",
      params
    })
  },
  create(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/create",
      method: "post",
      data: params
    })
  },
  update(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/update",
      method: "put",
      data: params
    })
  },
  delete(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/delete",
      method: "DELETE",
      params
    })
  },
  test(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/test",
      method: "post",
      data: params
    })
  },
  syncMetaData(params) {
    return service({
      url: config.VUE_MODULE_DDS_MAIN + "/dataManageTable/syncMetaData",
      method: "post",
      data: params
    })
  }
}
