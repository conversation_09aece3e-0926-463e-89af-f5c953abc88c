<template>
  <a
    :style="styleColor"
    :href="styleColor.linkAdress"
    :target="styleColor.jumpMode"
  >{{ styleColor.text }}</a
  >
</template>

<script>
export default {
  name: "href-layer",
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {
    styleColor() {
      const {
        color,
        fontWeight,
        text,
        fontSize,
        letterSpacing,
        background,
        textAlign,
        linkAdress,
        jumpMode,
      } = this.layer.params

      return {
        color,
        fontWeight,
        text,
        fontSize: fontSize + "px" || "12px",
        letterSpacing: letterSpacing + "em",
        background,
        textAlign,
        linkAdress,
        jumpMode,
      }
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
a {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
