const CompressionPlugin = require("compression-webpack-plugin")
const MonacoWebpackPlugin = require("monaco-editor-webpack-plugin")
const IS_PROD = ["production", "prod"].includes(process.env.ENV)
const path = require("path")

const resolve = dir => path.join(__dirname, dir)
module.exports = {
  configureWebpack: {
    plugins: [
      new CompressionPlugin({
        cache: false,
        algorithm: "gzip", // 使用gzip压缩
        test: /\.js$|\.html$|\.css$/, // 匹配文件名
        filename: "[path][base].gz[query]", // 压缩后的文件名(保持原文件名，后缀加.gz)
        minRatio: 1, // 压缩率小于1才会压缩
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
      }),
      new MonacoWebpackPlugin()
    ],
    performance: {
      hints: "warning",
      // 入口起点的最大体积 整数类型（以字节为单位）
      maxEntrypointSize: 50000000,
      // 生成文件的最大体积 整数类型（以字节为单位 300k）
      maxAssetSize: 30000000,
      // 只给出 js 文件的性能提示
      assetFilter: function (assetFilename) {
        return assetFilename.endsWith(".js")
      }
    },
    output: {
      // library的值在所有子应用中需要唯一
      library: "ddsBi",
      libraryTarget: "umd"
    }
  },
  chainWebpack: config => {
    // config
    //   .plugin("webpack-bundle-analyzer")
    //   .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin)
    config.devServer.set("inline", false)
    config.devServer.set("hot", true)
    config.output.filename(`js/[name].js`)
    // set dependents ignore
    config.externals([
      "vue",
      "vue-router",
      "vuex",
      "axios",
      "element-ui",
      "vue-i18n",
      "js-cookie"
    ])
    config.plugins.delete("preload") // TODO: need test
    config.plugins.delete("prefetch") // TODO: need test

    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end()
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]"
      })
      .end()
    config.devServer.set("inline", false)
    config.devServer.set("hot", true)
    config.output.filename(`js/[name].js`)
    // set dependents ignore
    config.externals([
      "vue",
      "vue-router",
      "vuex",
      "axios",
      "element-ui",
      "vue-i18n",
      "js-cookie"
    ])
  },
  css: {
    extract: IS_PROD,
    sourceMap: false,
    loaderOptions: {
      scss: {
        prependData: `
                    @import "@/assets/css/mixin.scss";
                    @import "@/assets/css/variables.scss";
                     `
      },
      postcss: {
        plugins: [require("tailwindcss"), require("autoprefixer")]
      }
    }
  },
  filenameHashing: false,
  productionSourceMap: false
}
