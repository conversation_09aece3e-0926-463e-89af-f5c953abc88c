<template>
  <div class="panels" :class="[size, direction]">
    <div
      class="panel-item"
      :class="'col-' + panels.length + ' ' + theme"
      v-for="(item, index) in panels"
      :key="index"
    >
      <div class="panel-item-text">
        {{ item.title }}
        <el-tooltip v-if="item.tip" :content="item.tip" placement="top">
          <svg-icon icon-class="tip" class="panel-item-icon" />
        </el-tooltip>
      </div>
      <div class="panel-item-content" :class="{ isDrill: item.isDrill }">
        <el-tooltip
          class="item"
          effect="dark"
          content="点击查看详情"
          placement="top"
          v-if="item.isDrill"
        >
          <span @click="onCallback(item, index)">
            {{ item.content | formatNumber }}
            <span class="unit">
              {{ item.content | formatUnit }}{{ item.unit }}
            </span>
          </span>
        </el-tooltip>
        <span v-else>
          {{ item.content | formatNumber }}
          <span class="unit">
            {{ item.content | formatUnit }}{{ item.unit }}
          </span>
        </span>
      </div>
      <div class="panel-item-subTitle" v-if="item.subList">
        <div class="sub-item" v-for="(sub, index) in item.subList" :key="index">
          {{ sub.subTitle }}
          <span>
            {{ sub.subValue | formatNumber }}{{ sub.subValue | formatUnit
            }}{{ sub.subUnit }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatNumber } from "@/utils/index.js"
export default {
  components: {},
  filters: {
    formatNumber(n) {
      return formatNumber(n).value
    },
    formatUnit(n) {
      return formatNumber(n).unit
    }
  },
  props: {
    panels: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: 160
    },
    size: {
      type: String,
      default: "default"
    },
    theme: {
      type: String,
      default: ""
    },
    direction: {
      type: String,
      default: "horizontal" // horizontal   vertical
    }
  },
  methods: {
    onCallback(params, index) {
      this.$emit("onCallback", params, index)
    }
  }
}
</script>

<style scoped lang="scss">
.panels {
  width: 100%;
  display: flex;
  &.vertical {
    flex-direction: column;
    .panel-item {
      margin-left: 0;
      margin-top: 24px;
      &.col-1 {
        width: 100%;
      }
      &.col-2 {
        width: 100%;
      }
      &.col-3 {
        width: 100%;
      }
      &.col-4 {
        width: 100%;
      }
      &.col-5 {
        width: 100%;
      }
    }
  }
  .panel-item {
    width: 100%;
    background: #fff;
    padding: 24px;
    box-sizing: border-box;
    margin-left: 24px;
    height: 138px;
    // margin-bottom: 24px;
    &.col-1 {
      width: calc(100% / 2);
    }
    &.col-2 {
      width: calc(100% / 2);
    }
    &.col-3 {
      width: calc(100% / 3);
    }
    &.col-4 {
      width: calc(100% / 4);
    }
    &.col-5 {
      width: calc(100% / 5);
    }
    .panel-item-text {
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #323233;
      .panel-item-icon {
        width: 14px;
        height: 14px;
        margin-left: 8px;
        cursor: pointer;
      }
    }

    .panel-item-content {
      height: 32px;
      font-size: 32px;
      font-family: AlibabaSans102Ver2-Medium, AlibabaSans102Ver2;
      font-weight: 500;
      color: #323233;
      margin-top: 12px;
      &.isDrill {
        cursor: pointer;
        span {
          &:hover {
            color: #2361db;
            .unit {
              color: #2361db;
            }
          }
        }
      }
      .unit {
        height: 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        margin-left: 2px;
      }
    }

    .panel-item-subTitle {
      margin-top: 20px;
      display: flex;
      .sub-item {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #969799;
        margin-right: 16px;
        span {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #646566;
          margin-left: 4px;
        }
      }
    }

    &:first-child {
      margin-left: 0 !important;
      margin-top: 0 !important;
    }
    &.gray {
      background: rgba(21, 99, 255, 0.04);
      .panel-item-text {
        color: #646566;
      }
    }
  }
}
.panels.small {
  .panel-item {
    height: 110px;
    .panel-item-content {
      margin-top: 20px;
    }
    &.gray {
      background: rgba(21, 99, 255, 0.04);
      .panel-item-content {
        height: 28px;
        font-size: 28px;
        margin-top: 20px;
      }
      .panel-item-text {
        color: #646566;
      }
    }
  }
}
.panels.large {
  .panel-item {
    height: 160px;
    display: flex;
    flex-direction: column;
    .panel-item-content {
      font-size: 22px;
      line-height: 26px;
      margin-top: 20px;
    }
    .panel-item-subTitle {
      margin-top: 34px;
    }
  }
}
</style>
