<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
import { toThousands } from "@/utils/index.js"
export default {
  components: {},
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String || Array,
      default: []
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 系列字段
    seriesField: {
      type: String || null,
      default: null
    },
    // 维度名称
    seriesName: {
      type: String || Array || null,
      default: null
    },
    // 是否开启区域颜色
    isAreaStyle: {
      type: Boolean,
      default: true
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#0565e3",
        "#0EACCC",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 单位
    unit: {
      type: String,
      default: ""
    },
    // X轴展示数量  auto会自动折叠隐藏
    interval: {
      type: [String, Number],
      default: "auto"
    },
    // 是否曲线
    smooth: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    dimensions() {
      if (Array.isArray(this.yField)) {
        return [this.xField, ...this.yField]
      } else {
        return [this.xField, this.yField]
      }
    },
    // 判断是否为多系列
    isMultipleSeries() {
      return Array.isArray(this.yField) || this.seriesField !== null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      let dataset = null
      if (this.seriesField) {
        // 去重获取系列
        const uniqueSeriesNames =
          this.chartData.length &&
          Array.from(
            new Set(this.chartData.map(item => item[this.seriesField]))
          )
        const transforms = []
        uniqueSeriesNames &&
          uniqueSeriesNames.forEach((seriesName, index) => {
            transforms.push({
              transform: {
                type: "filter",
                config: { dimension: this.seriesField, value: seriesName }
              }
            })
            series.push(
              this.createSeriesObject({
                name: seriesName,
                index: index + 1,
                color: this.color[index]
              })
            )
          })

        dataset = [
          {
            source: this.chartData
          },
          ...(transforms || [])
        ]
      } else {
        if (Array.isArray(this.yField)) {
          this.yField.forEach((seriesName, index) => {
            series.push(
              this.createSeriesObject({
                name: (this.seriesName && this.seriesName[index]) ?? seriesName,
                color: this.color[index],
                yField: seriesName
              })
            )
          })
        } else {
          series.push(
            this.createSeriesObject({
              name: this.seriesName ?? this.yField,
              color: this.color[0]
            })
          )
        }

        dataset = {
          dimensions: this.dimensions,
          source: this.chartData
        }
      }
      this.chart.setOption({
        dataset,
        tooltip: {
          trigger: "axis",
          formatter: params => {
            if (!this.isMultipleSeries) {
              return `<div>
              <p class="tooltip-title">${params[0].seriesName}</p>
              <div class="content-panel">
                <p>
                 <span style="background-color: ${
                   params[0].color
                 }" class="tooltip-item-icon"></span>
                 <span>${params[0].name}</span>
                </p>
                <span class="tooltip-value">
                ${toThousands(
                  params[0].value[
                    params[0].dimensionNames[params[0].encode.y[0]]
                  ]
                )}${this.unit}
                </span>
              </div>
            </div>`
            }
            return `<div>
            <p class="tooltip-title">${params[0].name}</p>
            ${this.tooltipItemsHtmlString(params)}
          </div>`
          },
          className: "echarts-tooltip-diy"
        },
        grid: {
          top: 40,
          bottom: 0,
          left: 0,
          right: 0,
          containLabel: true
        },
        legend: {
          show: true,
          right: 0,
          top: 0,
          icon: "rect",
          itemWidth: 12,
          itemHeight: 2,
          itemGap: 20,
          orient: "horizontal",
          // color: this.color,
          textStyle: {
            color: "#646566",
            fontSize: 12
          }
        },
        graphic: [
          {
            type: "text",
            top: 5,
            left: 0,
            style: {
              text: this.yAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          }
        ],
        yAxis: {
          show: false,
          type: "value",
          max: 0.5,
          splitLine: {
            show: false,
            lineStyle: {
              color: "#EBEDF0",
              type: "dashed"
            }
          },
          axisLabel: {
            fontSize: 12,
            color: "#646566",
            fontWeight: "400"
            // formatter: metricAxisLabelFormatter,
          }
        },

        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: "#000"
            }
          },
          axisTick: {
            show: true,
            inside: true
          },
          // 分割线
          splitLine: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            lineHeight: 16,
            color: "#323233",
            fontWeight: "400",
            interval: this.interval,
            rotate: 0, // 倾斜角度
            formatter: this.isFormatterXAxis
              ? value => {
                  let startName = value.substring(0, 5)
                  let endName = value.substring(5)
                  if (endName.length > 5) {
                    return `${startName}\n${value.substring(5, 9)}...`
                  }
                  return `${startName}\n${endName}`
                }
              : value => value
          }
        },
        series
      })
      // 设置监听容器大小的改变，改变后重置图表
      // var elementResizeDetectorMaker = require('element-resize-detector');
      // var erd = elementResizeDetectorMaker();
      // var worldMapContainer = this.$el;

      // erd.listenTo(worldMapContainer, () => {
      //   that.$nextTick(() => {
      //     that.chart.resize();
      //   });
      // });
      // this.chart.off().on('click', (params) => {
      //   const p = {
      //     name: params.name,
      //     data: params.data,
      //     dataIndex: params.dataIndex,
      //     seriesIndex: params.seriesIndex,
      //     seriesName: params.seriesName,
      //   };
      //   console.log(p);
      //   that.$emit('barLineCharts', p);
      // });
    },
    // 创建系列对象
    createSeriesObject({
      name,
      color,
      index = 0,
      yField = this.yField,
      isAreaStyle = this.isAreaStyle
    }) {
      return {
        type: "line",
        name: name,
        itemStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },
        showSymbol: false,
        datasetIndex: index,
        smooth: this.smooth,
        markLine: {
          symbol: "none",
          lineStyle: {
            color: "#000",
            type: "solid"
          },
          data: [
            {
              name: "",
              xAxis: "μ",
              // symbol: "none",
              label: {
                // 文字标签
                // show: false
                formatter: "f(x)",
                fontSize: "16px",
                color: "#323233",
                // position: 'middle',
                distance: [10, 10] // 设置标签文字的样式
              }
            }
          ]
        },

        areaStyle: isAreaStyle
          ? {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: color + "1A" // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: color + "00" // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          : null
      }
    },
    tooltipItemsHtmlString(items) {
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.y[0]]])}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 12px;
    height: 2px;
  }
}
</style>
