<template>
  <div class="layer-core">
    <ChartLayer
      v-if="layer.type === 1"
      v-on="$listeners"
      :widget-id="layer.widgetId"
    />
    <component
      v-if="layer.subType === 2"
      @click.native="$emit('handleChartClick')"
      :layer="layer"
      :is="componentName"
      @input="handleInput"
    />
    <component
      @click.native="$emit('handleChartClick')"
      v-else
      :layer="layer"
      :is="componentName"
    />
  </div>
</template>

<script>
import { getLayerCode } from "../index.js"
import { mapGetters } from "vuex"
import _ from "lodash"
export default {
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentLayer: "currentLayer"
    }),
    // 获取组件名称
    componentName() {
      if (this.layer.type === 1) return
      return getLayerCode(this.layer.subType).component
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleInput: _.debounce(function (value) {
      this.$emit("setOptionsOnClick", this.currentLayer.subType, {
        text: value
      })

      // this.$store.dispatch("display/updateLayer", {
      //   layer: {
      //     ...this.currentLayer,
      //     params: {
      //       ...this.currentLayer.params,
      //       text: value
      //     }
      //   }
      // })
    }, 500)
  }
}
</script>

<style scoped lang="scss">
.layer-core {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
