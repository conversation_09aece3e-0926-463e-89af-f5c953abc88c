<template>
  <div class="chart"></div>
</template>

<script>
import { debounce } from "@/utils"
import * as echarts from "echarts"
import "echarts-liquidfill"
import "echarts-wordcloud"
import chartOptionGenerator from "../../../render/index"
// import ChinaData from "@/assets/json/map/China.json"
import chartlibs from "../../../config/index"
import { mapGetters } from "vuex"

// import purplPassion from "echarts/theme/purple-passion";
// 注册主题
let elementResizeDetectorMaker = require("element-resize-detector") // 导入
let erd = elementResizeDetectorMaker()
import axios from "axios"
export default {
  components: {},
  props: {
    widgetProps: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      instanceChart: null
    }
  },
  computed: {
    ...mapGetters({
      currentTheme: "currentTheme"
    })
  },
  created() {},
  mounted() {
    echarts.registerTheme("theme", this.currentTheme)
    this.instanceChart = echarts.init(this.$el, "theme")

    this.renderChart()
    erd.listenTo(
      this.$el,
      debounce(() => {
        console.log("使echarts尺寸重置")
        // 使echarts尺寸重置
        if (!this.instanceChart) return
        this.instanceChart.resize()
      })
    )
  },
  watch: {
    "widgetProps.data": {
      deep: true,
      handler() {
        this.renderChart()
      }
    },
    "widgetProps.references": {
      deep: true,
      handler() {
        this.renderChart()
      }
    },
    "widgetProps.chartStyles": {
      deep: true,
      handler() {
        this.renderChart()
      }
    },
    currentTheme: {
      deep: true,
      handler(val) {
        console.log(val, "val")
        this.renderChart()
      }
    }
  },
  methods: {
    renderChart() {
      const { selectedChartId, chartStyles } = this.widgetProps
      // if (renderType === "loading") {
      //   return;
      // }
      if (this.instanceChart) {
        this.instanceChart.clear()
        this.instanceChart = null
      }
      echarts.registerTheme("theme", this.currentTheme)
      if (chartStyles.spec.layerType === "map") {
        const mapJsonUrl = `/file/upms-service/file/map/${chartStyles.spec.areaCode}.json`
        // http://192.168.113.153/file/upms-service/file/map/<EMAIL>

        axios.get(mapJsonUrl).then(response => {
          echarts.registerMap(chartStyles.spec.areaCode, response.data)
          this.instanceChart = echarts.init(this.$el, "theme")
          console.log(this.instanceChart, "this.instanceChart")
          this.instanceChart.setOption(
            chartOptionGenerator(
              chartlibs.find(cl => cl.id === selectedChartId).name,
              this.widgetProps
            )
          )
          this.instanceChart.on("click", params => {
            this.$emit("handleChartClick", params, this.widgetProps)
          })
          this.instanceChart.resize()

          // this.jsonData = response.data
        })

        // const mapJson = require(mapJsonUrl)
      } else {
        this.instanceChart = echarts.init(this.$el, "theme")
        this.instanceChart.setOption(
          chartOptionGenerator(
            chartlibs.find(cl => cl.id === selectedChartId).name,
            this.widgetProps
          )
        )
        this.instanceChart.on("click", params => {
          this.$emit("handleChartClick", params, this.widgetProps)
        })
        this.instanceChart.resize()
      }
    },
    base64ToBlob(code) {
      let parts = code.split(";base64,")
      let contentType = parts[0].split(":")[1]
      let raw = window.atob(parts[1])
      let rawLength = raw.length

      let uInt8Array = new Uint8Array(rawLength)

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    saveAsImage(name) {
      let content = this.instanceChart.getDataURL({
        backgroundColor: "#fff",
        pixelRatio: 2
      })

      let aLink = document.createElement("a")
      let blob = this.base64ToBlob(content)

      let evt = document.createEvent("HTMLEvents")
      evt.initEvent("click", true, true)
      aLink.download = `${name}.png`
      aLink.href = URL.createObjectURL(blob)
      aLink.dispatchEvent(
        new MouseEvent("click", {
          bubbles: true,
          cancelable: true,
          view: window
        })
      )
    }
  }
}
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
</style>
