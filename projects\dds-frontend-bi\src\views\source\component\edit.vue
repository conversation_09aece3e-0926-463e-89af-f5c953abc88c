<template>
  <dt-single-page-view
    class="editPage"
    :inner-style="{ textAlign: 'left' }"
    :show="show"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <dt-header>
      <el-page-header @back="goBack" :content="id == '' ? '新增' : '修改'">
      </el-page-header>
    </dt-header>
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="80px"
      class="my_with_P"
    >
      <el-form-item label="数据库" prop="name">
        <el-input v-model="form.name" class="my_with"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择" class="my_with">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="连接url" prop="url">
        <el-link :underline="false">mysql、oracle、dm</el-link>
        <el-input
          type="textarea"
          :rows="4"
          v-model="form.url"
          placeholder="请输入jdbc连接url 例：********************************
达梦：jdbc:dm://127.0.0.1:5236?schema=demo
"
          class="my_with"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" class="my_with"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          class="my_with"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          type="textarea"
          v-model="form.description"
          class="my_with"
        ></el-input>
      </el-form-item>
      <el-form-item style="text-align: center">
        <el-button
          :type="testRes == false ? 'info' : 'primary'"
          @click="submitFrom('ruleForm')"
        >
          保存
        </el-button>
        <el-button
          :type="testRes == false ? 'info' : 'success'"
          @click="testForm()"
          :icon="testRes == false ? 'el-icon-close' : 'el-icon-check'"
        >
          点击测试
        </el-button>
      </el-form-item>
    </el-form>
  </dt-single-page-view>
</template>

<script>
import Request from '@/service'
export default {
  name: 'edit-page',
  props: {
    id: String
  },
  data() {
    return {
      show: false,
      loading: true,
      title: '新增',
      form: {},
      rules: {
        name: [
          { required: true, message: '请输入数据库名称', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        url: [
          { required: true, message: '请输入数据源连接url', trigger: 'blur' }
        ]
      },
      typeList: [
        {
          label: 'JDBC',
          value: 'jdbc'
        },
        {
          label: 'CSV文件',
          value: 'csv'
        }
      ],
      testRes: false
    }
  },
  created() {
    console.log(this.id)
    if (this.id) {
      console.log(this.id)
      this.initForm()
    }
    setTimeout(() => {
      this.show = true
      this.loading = false
    }, 0.5 * 1000)
  },
  methods: {
    goBack() {
      this.$emit('click', { com: 'list', opt: 'back', data: { id: '' } })
    },
    // 请求表单数据
    initForm() {
      Request.source
        .getOne({ id: this.id })
        .then(res => {
          let config = JSON.parse(res.data.config)
          this.form = {
            id: res.data.id,
            name: res.data.name,
            description: res.data.description,
            username: config.username,
            password: config.password,
            url: config.url,
            type: res.data.type
          }
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 表单组确认
    submitFrom(formName) {
      if (!this.testRes) {
        return
      }
      this.$refs[formName].validate(valid => {
        // 表单验证
        if (valid) {
          let source = {}
          source.id = this.form.id
          source.name = this.form.name
          source.description = this.form.description
          source.type = this.form.type ? this.form.type : 'jdbc'
          let config = {
            username: this.form.username,
            password: this.form.password,
            url: this.form.url
          }
          source.config = config
          // 修改
          if (this.id) {
            Request.source
              .update(source)
              .then(() => {
                this.$emit('click', {
                  com: 'list',
                  opt: 'back',
                  data: { id: '' }
                })
                this.$message.success('操作成功')
              })
              .catch(() => {
                this.$message.error('操作失败')
              })
          } else {
            // 新增
            Request.source
              .create(source)
              .then(() => {
                this.$emit('click', {
                  com: 'list',
                  opt: 'back',
                  data: { id: '' }
                })
                this.$message.success('操作成功')
              })
              .catch(() => {
                this.$message.error('操作失败')
              })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          return false
        }
      })
    },
    // 表单组重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    testForm() {
      this.$refs.ruleForm.validate(valid => {
        // 表单验证
        if (valid) {
          let test = {
            username: this.form.username,
            password: this.form.password,
            url: this.form.url
          }
          Request.source
            .test(test)
            .then(res => {
              this.testRes = res.data
              if (res.data) {
                this.$message.success('连接成功')
              } else {
                this.$message.error('连接失败')
              }
            })
            .catch(() => {})
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.my_with_P {
  width: 500px;
}
.my_with {
  width: 420px;
}
</style>
