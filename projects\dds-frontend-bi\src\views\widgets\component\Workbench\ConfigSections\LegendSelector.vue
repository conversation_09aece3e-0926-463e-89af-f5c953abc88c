<template>
  <div class="paneBlock">
    <h4>图例</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            v-model="legendForm.showLegend"
            @change="changeLegendStyle"
          >
            显示图例
          </el-checkbox>
        </el-col>
        <el-col span="4">图标</el-col>
        <el-col span="10">
          <el-select
            v-model="legendForm.icon"
            placeholder="请选择"
            size="mini"
            @change="changeLegendStyle"
          >
            <el-option
              v-for="item in CHART_LINES_SYMBOL_TYPE"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox
            v-model="legendForm.selectAll"
            @change="changeLegendStyle"
          >
            是否全选
          </el-checkbox>
        </el-col>
        <el-col span="4">朝向</el-col>
        <el-col span="10">
          <el-select
            v-model="legendForm.orient"
            placeholder="请选择"
            size="mini"
            @change="changeLegendStyle"
          >
            <el-option
              v-for="item in CHART_VISUALMAP_DIRECYTIONS"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-select
            v-model="legendForm.fontFamily"
            placeholder="请选择"
            @change="changeLegendStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_FAMILIES"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col span="10">
          <el-select
            v-model="legendForm.fontSize"
            placeholder="请选择"
            @change="changeLegendStyle"
            size="mini"
          >
            <el-option
              v-for="item in PIVOT_CHART_FONT_SIZES"
              :key="item.value"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-col>
        <!-- <el-col span="4">
          <el-color-picker
            @change="changeLegendStyle"
            v-model="legendForm.color"
          ></el-color-picker>
        </el-col> -->
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="14">图形宽度</el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            controls-position="right"
            placeholder=""
            v-model="legendForm.itemWidth"
            @change="changeLegendStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="14">图形高度</el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            controls-position="right"
            placeholder=""
            v-model="legendForm.itemHeight"
            @change="changeLegendStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row class="blockRow">
        <el-col span="24">左右边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0 10px"
            v-model="legendForm.right"
            :format-tooltip="(val) => val + '%'"
            @change="changeLegendStyle"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="100"
            controls-position="right"
            placeholder=""
            v-model="legendForm.right"
            @change="changeLegendStyle"
          ></el-input-number>
        </el-col>
      </el-row>
      <el-row class="blockRow" style="margin-bottom: 20px">
        <el-col span="24">上下边距</el-col>
        <el-col span="14">
          <el-slider
            style="width: 100%; margin: 0 10px"
            v-model="legendForm.top"
            @change="changeLegendStyle"
            :format-tooltip="(val) => val + '%'"
          ></el-slider>
        </el-col>
        <el-col span="10">
          <el-input-number
            min="0"
            max="100"
            controls-position="right"
            placeholder=""
            v-model="legendForm.top"
            @change="changeLegendStyle"
          ></el-input-number>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_CHART_FONT_SIZES,
  CHART_LEGEND_POSITIONS,
  CHART_VISUALMAP_DIRECYTIONS,
  CHART_LINES_SYMBOL_TYPE,
} from "@/globalConstants"
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      PIVOT_CHART_FONT_FAMILIES,
      CHART_LEGEND_POSITIONS,
      PIVOT_CHART_FONT_SIZES,
      CHART_VISUALMAP_DIRECYTIONS,
      CHART_LINES_SYMBOL_TYPE,
      legendForm: {},
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      this.legendForm = this._.cloneDeep(this.chartData.chartStyles.legend)
    },
    changeLegendStyle() {
      this.$emit("changeStyle", "legend", this.legendForm)
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
