import { decodeMetricName } from "../component/util.js"
const defaultTheme = require("@/assets/json/echartsThemes/default.project.json")
const defaultThemeColors = defaultTheme.theme.color
export default function(chartProps) {
  const { data, cols, metrics,  } = chartProps


  const title = cols[0].displayName
  const agg = metrics[0].agg

  const metricName = decodeMetricName(metrics[0].name)

  const tooltip = {
    // ...TOOLTIP_STYLE,
    // formatter(params) {
    //   const { name, value, color } = params;
    //   const tooltipLabels = [];
    //   if (color) {
    //     tooltipLabels.push(
    //       `<span style="background:${color};width:10px;height:10px;border-radius:50%;display:inline-block;margin-right: 5px;"></span>`
    //     );
    //   }
    //   tooltipLabels.push(name);
    //   if (data) {
    //     tooltipLabels.push(": ");
    //     tooltipLabels.push(getFormattedValue(value, metrics[0].format));
    //   }
    //   return tooltipLabels.join("");
    // },
  }
  const seriesData = data
    .filter((d) => !!d[title])
    .map((d) => ({
      name: d[title],
      value: d[`${agg}(${metricName})`],
    }))
  return {
    tooltip,
    series: [
      {
        type: "wordCloud",
        sizeRange: [ 12, 72 ],
        textStyle: {
          color: function() {
            return defaultThemeColors[parseInt(Math.random() * 13)]
          },
          emphasis: {
            shadowBlur: 10,
            shadowColor: "#333",
          },
        },
        rotationStep: 90,
        data: seriesData,
      },
    ],
  }
}
