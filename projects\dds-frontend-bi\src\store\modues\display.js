let display = {
  namespaced: true,
  state: {
    displayInfo: {}, // 大屏信息
    displaySlides: [], // 所有幻灯片信息
    currentSlide: {
      // 当前幻灯片
      config: {
        slideParams: {
          width: 1920,
          height: 1080,
          backgroundColor: "rgba(45, 86, 126, 1)",
          backgroundImage: null,
          autoSlideGlobal: true,
          autoPlay: true,
          autoSlide: "10",
          transitionGlobal: true,
          transitionStyleIn: "none",
          transitionStyleOut: "none",
          transitionSpeed: "default",
          scaleMode: "scaleWidth",
          avatar: undefined
        }
      },
      index: 0,
      displayId: 0
    },
    slideLayers: [], // 当前幻灯片的所有图层
    currentLayer: {}, // 当前图层
    widgets: [] // 当前图层中的chat
  },
  mutations: {
    // 保存大屏信息
    SET_DISPLAY_INFO(state, display) {
      state.displayInfo = display
    },
    // 保存幻灯片信息
    SET_DISPLAY_SLIDES(state, slides) {
      state.displaySlides = slides
    },
    // 更新幻灯片信息
    UPDATE_DISPLAY_SLIDES(state, slides) {
      state.displaySlides.find(s => s.id === slides.id).config = slides.config
    },
    // 保存当前幻灯片
    SET_DISPLAY_CUR_SLIDE(state, slide) {
      state.currentSlide = slide
    },
    // 保存当前幻灯片所有图层
    SET_SLIDELAYERS(state, layers) {
      if (layers.length) {
        state.slideLayers = []
        this._vm.$nextTick(() => {
          state.slideLayers = layers.map(layer => {
            return {
              ...layer,
              params: JSON.parse(layer.params || "{}")
            }
          })
        })
      } else {
        state.slideLayers = []
      }
    },
    // 保存当前图层中的widgets
    SET_WIDGETS(state, widgets) {
      // console.log(state, "state")
      // // 过滤重复
      // const w = widgets.filter(w => {
      //   return !state.widgets.find(widget => widget.id === w.id)
      // })
      state.widgets = widgets
    },
    // 保存当前图层
    SET_CURRENT_LAYER(state, layer) {
      state.currentLayer = layer
      state.slideLayers.find(l => l.id === layer.id).params = layer.params
    },
    // 更新图层index
    UPDATE_SLIDE_LAYER(state, layer) {
      state.slideLayers.find(l => l.id === layer.id).index = layer.index
    },
    // 删除图层
    DELETE_SLIDE_LAYER(state) {
      // 删除state.slideLayers
      const i = state.slideLayers.findIndex(l => l.id === state.currentLayer.id)
      state.slideLayers.splice(i, 1)
    }
  },

  actions: {
    // 获取大屏信息和幻灯片信息
    async getDisplaySlides({ commit }, { displayId, slideIndex = 0 }) {
      const { data } = await this._vm.$httpBi.display.getDisplaySlideList({
        displayId
      })
      const { slides, ...rest } = data
      // 将config转换为对象
      slides.forEach(slide => {
        slide.config = JSON.parse(slide.config || "{}")
      })
      rest.config = JSON.parse(rest.config || "{}")
      commit("SET_DISPLAY_SLIDES", slides || [])
      commit("SET_DISPLAY_INFO", rest)
      // 如果幻灯片为空，默认为最后一个幻灯片
      if (slideIndex === slides.length) {
        commit("SET_DISPLAY_CUR_SLIDE", slides[slideIndex - 1])
      } else {
        commit("SET_DISPLAY_CUR_SLIDE", slides[slideIndex])
      }
    },
    // 添加大屏幻灯片
    async addSlide({ state, dispatch }) {
      // 插入幻灯片
      const insertSlideIdx =
        state.displaySlides.findIndex(s => s.id === state.currentSlide.id) + 1
      const slide = {
        id: undefined,
        index: insertSlideIdx,
        displayId: state.displayInfo.id,
        config: {
          slideParams: {
            ...state.currentSlide.config.slideParams,
            avatar: undefined
          }
        }
      }
      // 后面的幻灯片
      const afterSlides = state.displaySlides.slice(insertSlideIdx) || []
      // 添加幻灯片
      await this._vm.$httpBi.display.addDisplaySlide({
        ...slide,
        config: JSON.stringify(slide.config)
      })
      // 更新后面的幻灯片的下标
      if (afterSlides.length) {
        const slides = afterSlides.map((s, idx) => ({
          ...s,
          index: insertSlideIdx + idx + 1
        }))
        dispatch("updateDisplaySlide", slides)
      }
      dispatch("getDisplaySlides", {
        displayId: state.displayInfo.id,
        slideIndex: insertSlideIdx
      })
    },
    // 更新幻灯片
    async updateDisplaySlide({ state }, slides) {
      const params = slides.map(s => {
        return {
          ...s,
          displayId: state.displayInfo.id,
          config: JSON.stringify(s.config)
        }
      })
      await this._vm.$httpBi.display.updDisplaySlides(params)
    },
    // 删除幻灯片
    async deleteDisplaySlide({ state, dispatch }, slide) {
      // 删除幻灯片
      await this._vm.$httpBi.display.delDisplaySlide({
        slideId: slide.id
      })
      const slideIndex = state.displaySlides.findIndex(s => s.id === slide.id)
      // 更新后面的幻灯片的下标
      const afterSlides = state.displaySlides.slice(slideIndex + 1) || []
      if (afterSlides.length) {
        const slides = afterSlides.map((s, idx) => ({
          ...s,
          index: slide.index - idx
        }))
        dispatch("updateDisplaySlide", slides)
      }
      dispatch("getDisplaySlides", {
        displayId: state.displayInfo.id,
        slideIndex
      })
    },
    // 新增图层
    addSlideLayers({ state, dispatch }, newLayers) {
      return new Promise(reslove => {
        this._vm.$httpBi.display
          .addDisplaySlideWidget({
            displayId: state.displayInfo.id,
            slideId: state.currentSlide.id,
            slideWidgetCreates: newLayers.map(layer => ({
              ...layer,
              params: JSON.stringify(layer.params)
            }))
          })
          .then(() => {
            dispatch("getSlideLayers")
            reslove()
          })
      })
    },
    // 获取当前幻灯片的所有图层
    async getSlideLayers({ state, commit }) {
      const { data } = await this._vm.$httpBi.display.getDisplaySlideWidgets({
        displayId: state.displayInfo.id,
        slideId: state.currentSlide.id
      })
      const { items, widgets } = data || {
        items: [],
        views: [],
        widgets: []
      }
      commit("SET_WIDGETS", widgets)
      commit("SET_SLIDELAYERS", items)
    },
    // 更新updateLayer
    async updateLayer({ commit }, { layer, isUpdateCurrentLayer = true }) {
      const layerParamsString = {
        ...layer,
        params: JSON.stringify(layer.params)
      }
      await this._vm.$httpBi.display.updSlidesWidget(layerParamsString)
      if (isUpdateCurrentLayer) {
        commit("SET_CURRENT_LAYER", layer)
      } else {
        commit("UPDATE_SLIDE_LAYER", layer)
      }
    },
    // 删除图层
    async deleteLayer({ state, commit }) {
      await this._vm.$httpBi.display.delSlidesWidget({
        relationId: state.currentLayer.id
      })
      commit("DELETE_SLIDE_LAYER")
    }
  },
  getters: {}
}
export default display
