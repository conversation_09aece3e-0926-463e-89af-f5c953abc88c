import service from "../base"
import config from "../config"

/**
 * 数据集
 */
export default {
  getGroupAndView() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getGroupAndView",
      method: "get"
    })
  },

  getAllViewGroup() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getAllViewGroup",
      method: "get"
    })
  },

  addViewGroup(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/addViewGroup",
      method: "post",
      data: params
    })
  },
  updViewGroup(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/updViewGroup",
      method: "post",
      data: params
    })
  },

  delViewGroup(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/delViewGroup",
      method: "get",
      params
    })
  },

  checkViewCode(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/checkViewCode",
      method: "get",
      params
    })
  },

  getPage(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getPage",
      method: "post",
      data: params
    })
  },

  getdistinctvalue(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getdistinctvalue",
      method: "post",
      data
    })
  },
  getAll() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getAll",
      method: "get"
    })
  },
  getOne(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getOne",
      method: "get",
      params
    })
  },
  getdata(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getdata",
      method: "post",
      data
    })
  },
  create(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/createView",
      method: "post",
      data: params
    })
  },
  update(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/updateView",
      method: "put",
      data: params
    })
  },
  delete(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/deleteView",
      method: "DELETE",
      params
    })
  },
  getSources() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/getAll",
      method: "get"
    })
  },
  getDatabases(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/databases",
      method: "get",
      params
    })
  },
  getTables(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/tables",
      method: "get",
      params
    })
  },
  getColumns(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/table/columns",
      method: "get",
      params
    })
  },
  getTableData(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/getTableData",
      method: "post",
      data
    })
  },
  runsql(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/executesql",
      method: "post",
      data: params
    })
  },

  getAllSource() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/source/getAll",
      method: "get"
    })
  },
  changeViewGroup(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/changeViewGroup",
      method: "post",
      data: params
    })
  },

  getDtRoles() {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getDtRoles",
      method: "get"
    })
  },
  // 获取sql
  getSql(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/view/getViewSql",
      method: "post",
      data
    })
  }
}
