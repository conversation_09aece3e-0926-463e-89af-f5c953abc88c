<template>
  <div class="wrapper" id="wrapper">
    <SketchRule
      :lang="lang"
      :thick="thick"
      :scale="scale"
      :width="width"
      :height="height"
      :start-x="startX"
      :start-y="startY"
      :shadow="shadow"
      :hor-line-arr="lines.h"
      :ver-line-arr="lines.v"
      :corner-active="true"
      @handleLine="handleLine"
      @onCornerClick="handleCornerClick"
    ></SketchRule>
    <div
      ref="screensRef"
      id="screens"
      @wheel="handleWheel"
      @scroll="handleScroll"
    >
      <div ref="containerRef" class="screen-container">
        <div id="canvas" :style="canvasStyle">
          <div
            class="screen-wrapper"
            ref="screenWrapper"
            :style="displayStyle"
            @mousedown="$emit('onMousedown')"
          >
            <slot></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from "vue"
import html2canvas from "html2canvas"
import SketchRule from "vue-sketch-ruler"
import axios from "axios"
import { mapGetters } from "vuex"
export default Vue.extend({
  components: {
    SketchRule
  },
  props: {
    screenWidth: {
      type: String,
      default: "1920"
    },
    screenHeight: {
      type: String,
      default: "1080"
    },
    backgroundImage: {
      type: String
    },
    scaleMode: {
      type: String,
      default: "scaleWidth"
    }
  },
  data() {
    return {
      scale: 0.9, // 658813476562495, //1,
      startX: 0,
      startY: 0,
      lines: {
        h: [],
        v: []
      },
      thick: 20,
      width: 780,
      height: 480,
      lang: "zh-CN", // 中英文
      isShowRuler: true, // 显示标尺
      isShowReferLine: true, // 显示参考线
      rectHeight: 0,
      rectWidth: 0,
      transform: "scale(1) translate(-50%, -50%)"
    }
  },
  computed: {
    shadow() {
      return {
        x: 0,
        y: 0,
        width: this.rectWidth,
        height: this.rectHeight
      }
    },
    canvasStyle() {
      return {
        width: this.rectWidth + "px",
        height: this.rectHeight + "px",
        transform: `scale(${this.scale})`
      }
    },
    ...mapGetters({
      currentSlide: "currentSlide"
    }),
    displayStyle() {
      const {
        width: slideWidth,
        height: slideHeight,
        backgroundColor,
        backgroundImage
      } = this.currentSlide.config.slideParams
      return {
        width: slideWidth + "px",
        height: slideHeight + "px",
        backgroundColor: backgroundColor,
        backgroundImage:
          process.env.NODE_ENV === "development"
            ? `url("http://192.168.113.153${backgroundImage}")`
            : `url("${window.location.origin}${backgroundImage}")`,
        backgroundSize: "cover",
        transform: this.transform
      }
    }
  },
  methods: {
    handleLine(lines) {
      this.lines = lines
    },
    handleCornerClick() {
      return
    },
    handleScroll() {
      const screensRect = document
        .querySelector("#screens")
        .getBoundingClientRect()
      const canvasRect = document
        .querySelector("#canvas")
        .getBoundingClientRect()

      // 标尺开始的刻度
      const startX =
        (screensRect.left + this.thick - canvasRect.left) / this.scale
      const startY =
        (screensRect.top + this.thick - canvasRect.top) / this.scale

      this.startX = startX >> 0
      this.startY = startY >> 0
    },
    // 控制缩放值
    handleWheel(e) {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault()
        const nextScale = parseFloat(
          Math.max(0.2, this.scale - e.deltaY / 500).toFixed(2)
        )
        this.scale = nextScale
      }
      this.$nextTick(() => {
        this.handleScroll()
      })
    },
    initSize() {
      const wrapperRect = document
        .querySelector("#wrapper")
        .getBoundingClientRect()
      const borderWidth = 1
      this.width = wrapperRect.width - this.thick - borderWidth
      this.height = wrapperRect.height - this.thick - borderWidth
      this.rectWidth = wrapperRect.width
      this.rectHeight = wrapperRect.height
      this.setScale()
    },
    // 获取放大缩小比例
    getScale() {
      const w = this.rectWidth / this.screenWidth
      const h = this.rectHeight / this.screenHeight
      return w < h ? w : h
    },
    // 设置比例
    setScale() {
      const scale = this.getScale()
      this.$emit("onScale", scale)
      this.transform = `scale(${scale})`
    },
    // 截图
    screenshot() {
      const content = this.$refs.screenWrapper
      html2canvas(content, {
        scale: 1,
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/png")
        let blob = this.dataURLtoFile(dataURL, "image/jpeg") // 将文件转换成file的格式，就可以使用file_url传递给服务端了
        let fileOfBlob = new File([blob], new Date() + ".jpg")
        let formData = new FormData()
        formData.append("file", fileOfBlob)
        axios
          .post("/file-api/upms-service/upload", formData, {
            headers: this.$utils.auth.getAdminheader()
          })
          .then(res => {
            console.log(
              "%cScreenWrapper.vue line:198 res",
              "color: #007acc;",
              res
            )
            const slide = {
              ...this.currentSlide,
              config: {
                slideParams: {
                  ...this.currentSlide.config.slideParams,
                  avatar: res.data.data.path
                }
              }
            }
            this.$store.dispatch("display/updateDisplaySlide", [slide])
            this.$store.commit("display/UPDATE_DISPLAY_SLIDES", slide)
          })

        // this.$httpBi.display.fileUploadPath(formData).then((res) => {
        //   const slide = {
        //     ...this.currentSlide,
        //     config: {
        //       slideParams: {
        //         ...this.currentSlide.config.slideParams,
        //         avatar: res.data.path,
        //       },
        //     },
        //   }
        //   this.$store.dispatch("display/updateDisplaySlide", [ slide ])
        // })
      })
    },
    // 将文件转换成file的格式，就可以使用file_url传递给服务端了
    dataURLtoFile(dataURI, type) {
      let binary = atob(dataURI.split(",")[1])
      let array = []
      for (let i = 0; i < binary.length; i++) {
        array.push(binary.charCodeAt(i))
      }
      return new Blob([new Uint8Array(array)], { type: type })
    }
  },
  mounted() {
    // 滚动居中
    setTimeout(() => {
      this.$refs.screensRef.scrollLeft =
        this.$refs.containerRef.getBoundingClientRect().width / 2 - 100 // 300 = #screens.width / 2
      this.initSize()
    }, 0)
  }
})
</script>
<style>
body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  overflow: hidden;
}

body * {
  box-sizing: border-box;
}

.wrapper {
  background-color: #f5f5f5;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  border: 1px solid #dadadc;
}

#screens {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.screen-container {
  position: absolute;
  width: 5000px;
  height: 3000px;
}

.scale-value {
  position: absolute;
  left: 0;
  bottom: 100%;
}

.button {
  position: absolute;
  left: 100px;
  bottom: 100%;
}

.button-ch {
  position: absolute;
  left: 200px;
  bottom: 100%;
}
.button-en {
  position: absolute;
  left: 230px;
  bottom: 100%;
}

#canvas {
  position: absolute;
  top: 80px;
  left: 50%;
  margin-left: -80px;
  transform-origin: 50% 0;
}
.screen-wrapper {
  position: relative;
  transform-origin: 0 0;
  box-sizing: border-box;
  color: #fff;
  left: 0%;
  top: 0%;
  transition: 0.3s;
}
</style>
