<template>
  <DT-Upload :on-change="changed" :value="joinUrl" />
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: String,
      default: ""
    }
  },
  data() {
    return {}
  },
  computed: {
    joinUrl() {
      if (process.env.NODE_ENV === "development") {
        return "http://***************" + this.value
      } else {
        return window.location.origin + this.value
      }
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    changed(val) {
      console.log(val.response)
      if (val.response) {
        console.log(val.response.data.path, "val")
        this.$emit("input", val.response.data.path)
        console.log(val.response.data.path, "val.response.data.path")
        this.$emit("change", val.response.data.path)
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
