<template>
  <div class="chart-gauge" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartGauge',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '250px'
    },
    // 数值配置
    value: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    min: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: '%'
    },
    precision: {
      type: Number,
      default: 1
    },
    // 仪表盘配置
    startAngle: {
      type: Number,
      default: 225
    },
    endAngle: {
      type: Number,
      default: -45
    },
    radius: {
      type: String,
      default: '75%'
    },
    // 样式配置
    color: {
      type: String,
      default: '#faad14'
    },
    colors: {
      type: Array,
      default: () => [
        [0.2, '#52c41a'],
        [0.8, '#faad14'],
        [1, '#f5222d']
      ]
    },
    // 刻度配置
    splitNumber: {
      type: Number,
      default: 10
    },
    showSplitLine: {
      type: Boolean,
      default: true
    },
    showAxisTick: {
      type: Boolean,
      default: true
    },
    showAxisLabel: {
      type: Boolean,
      default: true
    },
    // 指针配置
    pointerLength: {
      type: String,
      default: '80%'
    },
    pointerWidth: {
      type: Number,
      default: 6
    },
    // 显示配置
    showTitle: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    showDetail: {
      type: Boolean,
      default: true
    },
    // 格式化配置
    formatter: {
      type: Function,
      default: null
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      const option = {
        series: [
          {
            name: this.title || '仪表盘',
            type: 'gauge',
            min: this.min,
            max: this.max,
            startAngle: this.startAngle,
            endAngle: this.endAngle,
            radius: this.radius,
            splitNumber: this.splitNumber,
            data: [
              {
                value: this.value,
                name: this.title,
                title: {
                  show: this.showTitle,
                  offsetCenter: [0, '20%'],
                  textStyle: {
                    color: '#666666',
                    fontSize: 14
                  }
                },
                detail: {
                  show: this.showDetail,
                  offsetCenter: [0, '-20%'],
                  valueAnimation: this.animation,
                  formatter: (value) => {
                    if (this.formatter && typeof this.formatter === 'function') {
                      return this.formatter(value)
                    }
                    return `${Number(value).toFixed(this.precision)}${this.unit}`
                  },
                  textStyle: {
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#262626'
                  }
                }
              }
            ],
            // 仪表盘轴线
            axisLine: {
              lineStyle: {
                width: 10,
                color: this.colors.length > 0 ? this.colors : [[1, this.color]]
              }
            },
            // 分割线
            splitLine: {
              show: this.showSplitLine,
              distance: -10,
              length: 15,
              lineStyle: {
                color: '#ffffff',
                width: 2
              }
            },
            // 刻度
            axisTick: {
              show: this.showAxisTick,
              distance: -10,
              length: 8,
              lineStyle: {
                color: '#ffffff',
                width: 1
              }
            },
            // 轴标签
            axisLabel: {
              show: this.showAxisLabel,
              distance: 15,
              color: '#666666',
              fontSize: 12,
              formatter: (value) => {
                return Math.round(value)
              }
            },
            // 指针
            pointer: {
              length: this.pointerLength,
              width: this.pointerWidth,
              itemStyle: {
                color: '#262626'
              }
            },
            // 锚点
            anchor: {
              show: true,
              showAbove: true,
              size: 8,
              itemStyle: {
                color: '#262626'
              }
            },
            // 标题
            title: {
              show: false
            },
            // 详情
            detail: {
              show: false
            }
          }
        ],
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    value: {
      handler() {
        this.updateChart()
      }
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    updateValue(newValue) {
      this.chart.setOption({
        series: [{
          data: [{
            value: newValue,
            name: this.title
          }]
        }]
      })
    }
  }
}
</script>

<style scoped>
.chart-gauge {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
