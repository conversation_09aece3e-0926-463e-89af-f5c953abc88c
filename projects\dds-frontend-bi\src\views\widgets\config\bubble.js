
import ChartTypes from './ChartTypes'
const bubble = {
  id: ChartTypes.bubble,
  name: 'bubble',
  title: '气泡图',
  icon: 'chart_bubble',
  coordinate: 'other',
  rules: [ { dimension: 1, metric: 1 } ],
  dimetionAxis: 'col',
  data: [
    {
      title: '维度',
      type: 'category',
      name: "cols"

    },
    {
      title: '指标',
      type: 'value',
      name: "metrics"
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '颜色',
    //   type: 'category',
    //   name: "color"
    // },

  ],
  style: {
    spec: {

    }
  }
}

export default bubble
