<template>
  <div class="head">
    <div class="title">
      {{ title }}
      <span class="subtitle">{{ subtitle }}</span>
      <el-tooltip
        class="item"
        effect="dark"
        :content="content"
        popper-class="tool-tip"
        placement="top"
      >
        <svg-icon v-if="isShowTip" icon-class="tip" class="icon" />
      </el-tooltip>
    </div>
    <div>
      <svg-icon
        v-if="isShowDownloadIcon"
        icon-class="export1"
        class="icon"
        style="margin-bottom: 2px"
        @click="$emit('download')"
      />
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: ""
    },
    subtitle: {
      type: String,
      default: ""
    },
    isShowTip: {
      type: Boolean,
      default: false
    },
    isShowDownloadIcon: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ""
    },
    value: {
      type: String,
      default: ""
    },
    isShowSelect: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      default: "培养层次"
    },
    options: {
      type: Array,
      default: () => []
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: "nr",
          value: "bm"
        }
      }
    }
  },
  inject: ["parent"],
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    change() {
      this.$emit("input", this.value)
      this.$emit("change", this.value)
    }
  }
}
</script>

<style scoped lang="scss">
.head {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #222222;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.subtitle {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}

.icon {
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
}

::v-deep .el-form-item__label {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}

::v-deep .el-input--small .el-input__inner {
  width: 180px;
  height: 32px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #cbced1;
}
.custom {
  position: absolute;
  top: -7px;
  right: 0;
}
</style>
<style lang="scss">
.el-tooltip__popper.is-dark {
  max-width: 220px !important;
}
</style>
