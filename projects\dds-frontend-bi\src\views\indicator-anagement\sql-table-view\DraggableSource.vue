<template>
  <div>
    <el-input v-model="tableTitle" placeholder="请输入表名称"></el-input>
    <ul class="list">
      <li
        class="list-item-custom"
        draggable
        v-for="(item, index) in newTables"
        :key="index"
        @dragstart="dragStart(item, $event)"
        @dragend="dragEnd"
      >
        <i class="el-icon-s-grid"></i>

        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    tables: {
      type: Array,
      default: () => []
    },
    dragTables: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableTitle: ""
    }
  },
  computed: {
    newTables() {
      return this.tables.filter(item => {
        return item.name.indexOf(this.tableTitle) > -1
      })
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    // 拖拽开始
    dragStart(data, e) {
      console.log(data, "阿萨德")
      this.$emit("onDragStart", data, e)
    },
    // 拖拽结束
    dragEnd() {
      this.$emit("onDragEnd")
    }
  }
}
</script>

<style scoped lang="scss">
.list {
  width: 100%;
  height: calc(100vh - 360px);
  background: #fff;
  overflow: auto;

  .list-item-custom {
    cursor: pointer;
    font-size: 12px;
    width: 200px;
    height: 18px;
    margin: 10px 0;
    line-height: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .list-item-custom:hover {
    background-color: #ededed;
  }
}
</style>
