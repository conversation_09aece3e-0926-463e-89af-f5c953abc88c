<template>
  <DT-View
    :inner-style="{
      padding: 0,
      position: 'relative',
      height: '100%'
    }"
    :outer-style="{
      padding: isFull ? 0 : '20px'
    }"
    class="container-wrapper"
  >
    <div class="create-head">
      <div class="create-text">创建工具类算法</div>
      <div class="steps">
        <div class="step-item active">
          <i class="el-icon-edit"></i>
          Python
        </div>
        <!-- <div class="line" :class="{ active: activeId == 2 }"></div>
        <div class="step-item" :class="{ active: activeId == 2 }">
          <i class="el-icon-data-line"></i>
          指标与维度
        </div> -->
      </div>
      <el-button
        type="primary"
        style="margin-left: auto"
        @click="$router.push('/ddsBi/algorithmHistory')"

      >
        编辑历史
      </el-button>
    </div>
    <div class="editor-content" v-if="activeId === 1">
      <div class="left">
        <el-form label-width="75px" label-position="top">
          <el-form-item prop="sourceId" label="数据源:">
            <el-select
              @change="sourceDb"
              v-model="form.sourceId"
              placeholder="请选择数据源"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="item in sourcesList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库表:">
            <el-tree
              :props="defaultProps"
              class="tree-db"
              :load="loadNode"
              :data="dbs"
              lazy
            >
              <span class="custom-tree-node" slot-scope="{ node }">
                <span>
                  <i v-if="node.data.type == 'DB'" class="el-icon-coin" />
                  <i v-if="node.data.type == 'TABLE'" class="el-icon-s-grid" />
                  {{ node.label }}
                </span>
              </span>
            </el-tree>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="editor-pane">
          <MonacoEditor
            ref="MonacoEditor"
            :init-value.sync="form.sql"
            @mouseup="getSelectCode"
            :hint-data="hintData"
            height="100%"
            language="python"
          />
        </div>
        <div class="config-pane">
          <div class="config-content">
            <!-- 算法基本信息 -->
            <div class="form-section">
              <h3>
                算法属性
                <el-button type="primary">编辑</el-button>
              </h3>
              <div class="attrs">
                <div class="attr-item">
                  <div class="label">名称:</div>
                  <div class="value">test.py</div>
                </div>
                <div class="attr-item">
                  <div class="label">介绍:</div>
                  <div class="value">基于消费的学生消费类型聚类算法</div>
                </div>
              </div>
            </div>
            <div class="form-section">
              <h3>
                输入参数
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  circle
                ></el-button>
              </h3>
              <div class="params">
                <div class="params-item">
                  <div class="label">数据集</div>
                  <el-button type="text">编辑</el-button>
                </div>
                <div class="params-item">
                  <div class="label">聚类簇数</div>
                  <el-button type="text">编辑</el-button>
                </div>
                <!-- <div class="params-item">
                  <div class="label">聚类簇数</div>
                  <el-button type="text">编辑</el-button>
                </div> -->
              </div>
            </div>
            <div class="form-section">
              <h3>输出说明</h3>
              <div class="explain">
                <el-input
                  type="textarea"
                  :rows="4"
                  placeholder="请输入输出说明"
                  v-model="textarea"
                ></el-input>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-btn">
          <el-button @click="$router.go(-1)">取消</el-button>
          <el-button type="success" @click="dialogVisible = true">
            试计算
          </el-button>
          <el-button type="primary" @click="nextStep">保存</el-button>
        </div>
      </div>
    </div>

    <el-dialog
      title="试运行"
      :visible.sync="dialogVisible"
      width="750px"
      :before-close="handleDone"
    >
      <el-form
        v-if="dialogStep == 1"
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="请输入参数" style="text-align: right;">
          <el-button type="primary" >切换输入模式</el-button>
        </el-form-item>
        <el-form-item label="数据集">
          <el-input
            type="textarea"
            :rows="10"
            placeholder="请输入内容"
            v-model="ruleForm.textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="聚类蔟数">
          <el-input v-model.number="ruleForm.num"></el-input>
        </el-form-item>
      </el-form>
      <div class="inner-box" v-if="dialogStep == 2">
        <div class="icon"></div>
        <div class="right">
          <div class="text">运行中...</div>
          <!-- <div class="progress"></div> -->
          <el-progress
            :show-text="false"
            :percentage="uploadPercentage"
          ></el-progress>
        </div>
      </div>
      <div v-if="dialogStep == 3">
        <p style="margin-bottom: 20px;">试运行完成，执行时长：{{ executionTime }}s</p>
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="输出结果:">
            <!-- <CommonTable
              :page.sync="page"
              id="xh"
              :table-data="tableData"
              height="200px"
              :show-selection="false"
              :show-batch-tag="false"
              :table-columns.sync="tableColumns"
            ></CommonTable> -->
22126300204  112.10     0<br/>
22001000107   46.42     1<br/>
22001000110  118.19     0<br/>
22126300175  176.94     2<br/>
22126300221  107.00     0<br/>
22001001123  115.92     0<br/>
22126300158   70.62     1<br/>
22126300187  195.68     2<br/>
21126301531    5.27     1<br/>
22001000538   41.20     1<br/>
</el-form-item>
          <el-form-item label="print:">
            <el-input
              type="textarea"
              :rows="10"
              placeholder=""
              v-model="demo"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="dialogStep !== 2">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleRun" v-if="dialogStep == 1">下一步</el-button>

        <el-button type="primary" @click="handleDone" v-if="dialogStep == 3">完成</el-button>
      </span>
    </el-dialog>
  </DT-View>
</template>

<script>
import Request from "@/service"
// import encryptSql from "@/utils/encryptSql.js"
import { jsonToSheetXlsx } from "@/utils/Export2Excel"
import MonacoEditor from "@/components/MonacoEditor"
import { exportExcel } from "@/utils"
// import CommonTable from "@/components/CommonTable.vue"

export default {
  components: { MonacoEditor, },

  props: {},
  data() {
    var changeFieldNameModified = async (rule, value, callback) => {
      const arr = this.form.tableData1.filter(
        item => item.fieldNameModified === value
      )
      if (arr.length > 1) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
      const { data } = await Request.api.paramPost(
        "SqlIndicator/existByNameAndType",
        {
          name: value,
          type: arr[0].tagType,

          dimensions: this.form.tableData1
            .filter(e => e.tagType === "派生维度")
            .map(item => item.fieldNameModified)
        }
      )

      if (data) {
        callback(new Error("指标或维度名称已存在,请重新输入"))
      }
    }
    return {
      demo:"数据条数： 10\n聚类完成",
         tableColumns: [
        {
          label: "学号",
          prop: "val",
          visible: true,
          sortable: false
        },
        {
          label: "消费金额",
          prop: "val1",
          visible: true,
          sortable: false
        },
        {
          label: "聚类类别",
          prop: "val2",
          visible: true,
          sortable: false,
          formatter: val2 => {
            return val2
          }
        },
       
      ],
      tableData: [
        { val: 22126300204, val1: 112.1, val2: 0 },
        { val: 22001000107, val1: 46.42, val2: 1 },
        { val: 22001000110, val1: 118.19, val2: 0 },
        { val: 22126300175, val1: 176.94, val2: 2 },
        { val: 22126300221, val1: 107.0, val2: 0 },
        { val: 22001001123, val1: 115.92, val2: 0 },
        { val: 22126300158, val1: 70.62, val2: 1 },
        { val: 22126300187, val1: 195.68, val2: 2 },
        { val: 21126301531, val1: 5.27, val2: 1 },
        { val: 22001000538, val1: 41.2, val2: 1 }
      ],
      executionTime: "",
      uploadPercentage: 0,
      dialogStep: 1,
      ruleForm: {
        textarea: `   SELECT

        user_id,

        sum(txamt) txamt

    FROM

        ws_trade_summary

    WHERE

        txtype_id = '1'

        AND year_id =2024

    GROUP BY

        user_id`,
        num: 3
      },
      dialogVisible: false,
      textarea: `Dataframe：
| 样本id | 数据值 | cluster | 
| 样本id（学号）|用于聚类的原始数据值 | 聚类类别（0，1，2...）|`,
      isSelectSQL: false, // 是否选中sql
      activeId: 1,
      sourcesList: [
        {
          updateTime: null,
          id: 39,
          name: "112.60-dds_platform_dev",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 38,
          name: "ods",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 37,
          name: "演示数据源",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 30,
          name: "演示配置流程库",
          description: null,
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 18,
          name: "112.60-dds_platform_3.0.0",
          description: "112.60-dds_platform_3.0.0",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 6,
          name: "113.8库",
          description: "达芬奇",
          type: "jdbc"
        },
        {
          updateTime: null,
          id: 5,
          name: "112.60库/dds-platform",
          description: "112.60库dds-platform",
          type: "jdbc"
        },
        {
          updateTime: "2022-02-25 16:32:25",
          id: 2,
          name: "nbda",
          description: "oracleNbda数据库",
          type: "jdbc"
        },
        {
          updateTime: "2022-01-26 10:18:07",
          id: 1,
          name: "dds-platform",
          description: "数字桌面演示数据库",
          type: "jdbc"
        }
      ], // 数据源列表
      form: {
        sourceId: 27, // 数据源id
        sql: `import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from sklearn.cluster import KMeans
# 单维度聚类分析
def calculate(input_params):
    # 读取输入项
    sql_query=input_params['sql_query']
    k=input_params['k']
    engine = create_engine('mysql+pymysql://{user}:{password}@{host}:{port}/{database}'.format(**db_config))  # 在数据源配置中获取的数据源配置
    df=pd.read_sql(sql_query,engine)

    print("数据条数：",df.shape[0])

    # 进行聚类分析
    if len(df)>k:
        KMEANS = KMeans(n_clusters=k, init='k-means++', max_iter=30)
        dataset = np.array(df.iloc[:, 1]).reshape(-1, 1)
        clusters = KMEANS.fit_predict(dataset)
        df["cluster"] = clusters
        print("聚类完成")
        return df
    else:
        return "数据量不足，请重新输入k值或数据集"`,
        variables: [],
        tableData1: []
      },
      rules: {
        fieldNameModified: [
          {
            validator: changeFieldNameModified,
            trigger: "blur",
            required: true
          }
        ]
      },
      dbs: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      defaultProps: {
        label: "name",
        children: "zones",
        isLeaf: "leaf"
      },
      tables: [],
      columns: [],
      isFull: false, // 是否全屏
      copySql: "", // 备份sql
      sqlInfo: {}, // 保存的sql信息
      indCode: "",
      numericFields: [], // 数值字段

      code: '# 默认示例代码\nprint("Hello World")',
      cmOptions: {
        tabSize: 4,
        mode: "text/x-python",
        theme: "material-darker",
        lineNumbers: true,
        line: true,
        readOnly: false
      },
      algorithm: {
        name: "",
        description: ""
      },
      parameters: []
    }
  },
  computed: {
    hintData() {
      return [
        ...this.tables.map(item => item.name),
        ...this.columns.map(item => item.name)
      ]
    },
    isNewSql() {
      console.log(this.copySql, "this.copySql")
      console.log(this.form.sql, "this.form.sql")

      return this.indCode
    }
  },
  created() {
    this.indCode = this.$route.query.indCode
    if (this.indCode) {
      this.getSqlDetail()
    }
  },
  mounted() {
    this.initSource()
    this.sourceDb()
  },
  watch: {},
  methods: {
    handleDone() {
      this.dialogStep = 1
      this.dialogVisible = false // 初始化进度为 0
    },
    handleRun() {
      this.dialogStep = 2
      this.uploadPercentage = 0 // 初始化进度为 0
      this.startTime = Date.now() // 记录开始时间

      const interval = setInterval(() => {
        // 生成 1 到 10 之间的随机增量
        const increment = Math.floor(Math.random() * 10) + 1
        this.uploadPercentage = Math.min(this.uploadPercentage + increment, 100)

        if (this.uploadPercentage >= 100) {
          clearInterval(interval)
          this.dialogStep = 3
          const endTime = Date.now() // 记录结束时间
          this.executionTime = (endTime - this.startTime) / 1000 // 计算执行时间，单位为秒
          console.log(`执行用时: ${this.executionTime} 秒`)
        }
      }, 200) // 每 500 毫秒更新一次进度
    },
    // 获取sql详情
    async getSqlDetail() {
      const { data } = await Request.api.paramPostQuery(
        "/SqlIndicator/getSqlIndicatorByIndCode",
        {
          indCode: this.indCode
        }
      )
      this.form.sql = data.sqlStatement
      this.copySql = data.sqlStatement
      this.sqlInfo = data
      this.$nextTick(() => {
        this.$refs.MonacoEditor.setInitValue()
        this.handleRunSql()
      })
    },

    // 全部导出
    handleAllExport() {
      // 导出
      exportExcel("/api/dds-server-bi/SqlIndicator/exportAllData", {
        sql: this.form.sql
      })
    },
    async handleSave() {
      let params = this.form.tableData1.map(item => {
        return {
          ...item,
          unitName: item.unitName === "其他" ? item.diydw : item.unitName
        }
      })
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/SqlIndicator/save",
            params
          )
          this.$message.success(data)
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {}
          })
        }
      })
    },
    // 初始化数据源
    async initSource() {
      const { data } = await Request.view.getSources()
      this.sourcesList = data
    },
    // 数据源库
    async sourceDb() {
      this.dbs = []
      if (this.form.sourceId) {
        Request.view
          .getDatabases({ id: this.form.sourceId })
          .then(res => {
            for (let i = 0; i < res.data.length; i++) {
              this.dbs.push({ name: res.data[i], type: "DB" })
            }
          })
          .catch(() => {})
      }
      console.log(this.dbs)
    },
    // 树节点加载
    loadNode(node, resolve) {
      // 第一层库
      if (node.level === 0) {
        return resolve(this.dbs)
      }
      // 第二层表
      if (node.level === 1) {
        Request.view
          .getTables({ id: this.form.sourceId, dbName: node.data.name })
          .then(res => {
            this.tables = this.tables.concat(res.data.tables)
            return resolve(res.data.tables)
          })
          .catch(() => {})
      }
      if (node.level === 2) {
        Request.view
          .getColumns({
            id: this.form.sourceId,
            dbName: node.parent.data.name,
            tableName: node.data.name
          })
          .then(res => {
            this.columns = this.columns.concat(res.data.columns)
            return resolve(
              res.data.columns.map(item => ({
                ...item,
                leaf: true
              }))
            )
          })
          .catch(() => {})
      }
      // 第三层字段
      if (node.level >= 2) return resolve([])
    },
    getSelectCode() {
      if (this.$refs.MonacoEditor.getSelectionVal()) {
        this.isSelectSQL = true
      } else {
        this.isSelectSQL = false
      }
    },
    // 执行slq
    async handleRunSql() {},
    handleExportExcel(selection) {
      console.log(selection, "selection")

      jsonToSheetXlsx({
        data: selection,
        filename: "表数据" + new Date().getTime()
      })
    },
    // 下一步
    nextStep() {
      this.handleRunSql(() => {
        console.log("执行回到")
        this.activeId = 2
        if (this.indCode) {
          this.form.tableData1 = [this.sqlInfo]
        } else {
          this.form.tableData1 = this.tableColumns.map(item => ({
            fieldName: item.prop,
            fieldNameModified: item.prop,
            tagType: "指标",
            period: "1",
            scopeId: 0,
            precision: null,
            rounding: "否",
            thresholdMin: null,
            thresholdMax: null,
            unitName: null,
            description: "",
            tagsName: [],
            createdById: null,
            indCode: null,
            sqlStatementOrigin: this.form.sql
          }))
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
}
.inner-box {
  display: flex;
  align-items: center;
  padding: 24px;
  box-sizing: border-box;
  .icon {
    width: 56px;
    height: 56px;
    background: url("~@/assets/images/yjs.png") no-repeat;
    margin-right: 12px;
  }
  .text {
    text-align: left;
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #323233;
    line-height: 14px;
    margin-bottom: 12px;
  }
  .right {
    width: 500px;
  }
  height: 104px;
  background: #ffffff;
  border-radius: 8px;
}
.create-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  height: 80px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  .el-icon-back {
    font-size: 20px;
    padding-right: 16px;
    cursor: pointer;
  }
  .steps {
    width: 150px;
    height: 48px;
    background: #f5f7fa;
    border-radius: 6px;
    margin-left: 40px;
    display: flex;
    align-items: center;
    padding: 0 32px;
    .line {
      width: 168px;
      height: 1px;
      background: #cbced1;
      margin: 0 12px;
      &.active {
        background: #1563ff;
      }
    }
    .step-item {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #5c646e;
      &.active {
        color: #1563ff;
      }
      i {
        font-size: 19px;
        margin-right: 6px;
      }
    }
  }
}
.step-two {
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.editor-content {
  width: 100%;
  display: flex;
  min-height: calc(100% - 100px);
  padding-top: 20px;

  background-color: #f0f2f5;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;
    width: 320px;
    border-radius: 6px;
    margin-right: 16px;
    background: #fff;
    padding: 24px 20px 0 16px;
    ::v-deep .el-form {
      height: 100%;
      display: flex;
      flex-direction: column;
      .el-form-item:nth-child(2) {
        flex: 1 1 0;
        overflow: hidden;
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
          height: 6px;
        }
        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 6px;
          height: 2px;
          background-color: #cfd6e6;
        }
        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: transparent;
          border-radius: 6px;
        }
        .el-form-item__content {
          height: calc(100% - 45px);
        }
      }
    }
    .custom-tree-node {
      width: 100%;
    }
    .tree-db {
      height: 100%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
  }
  .right {
    position: relative;
    width: calc(100% - 340px);
    padding: 20px;
    min-height: calc(100vh - 230px);

    background: #fff;
    padding-bottom: 52px;
    display: flex;

    .editor-pane {
      width: calc(100% - 340px);
      padding: 20px;
      background: #2d2d2d;
    }

    .config-pane {
      flex: 0 0 320px;
      overflow-y: auto;
      margin-left: 20px;
    }

    .code-editor {
      height: 100%;
      margin-right: 20px;
    }

    .form-section {
      margin-bottom: 16px;
      padding-bottom: 16px;
      h3 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        background: #f5f6fa;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 4px 4px 0 0;
      }
      .attrs {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;
        .attr-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0;
          }
          .label {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: right;
            font-style: normal;
            margin-right: 8px;
          }
          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 18px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .params {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;
        .params-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0;
          }
          .label {
            height: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #222222;
            line-height: 14px;
            text-align: right;
            font-style: normal;
            margin-right: 8px;
          }
          .value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 18px;
            text-align: left;
            font-style: normal;
          }
        }
      }
      .explain {
        padding: 16px;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
        border: 1px solid #f5f6fa;
        border-top: none;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .footer-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 52px;
      padding-right: 24px;
      box-sizing: border-box;
      border-top: 1px solid #f0f0f0;
    }
  }
}
.step-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 52px;
  padding-right: 24px;
  box-sizing: border-box;
  border-top: 1px solid #f0f0f0;
}
</style>
<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.item-style) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}
#project_frame {
  .el-tree-node:not(.is-expanded)
    > .el-tree-node__content:has(> span.item-style) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }
  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}
#project_frame .el-table.sqlIndicator-table td,
#project_frame .el-table.sqlIndicator-table th {
  padding: 0;
}
</style>
