<template>
  <div class="wrapper">
    <div class="reveal">
      <div class="slides">
        <section v-for="(slide, index) in displaySlides" :key="index">
          <ScreenWrapper :slide="slide">
            <template v-if="slideLayers[currentSlide.id]">
              <PreviewLayer
                @click.native="handleClick(layer)"
                v-for="(layer, i) in slideLayers[currentSlide.id]"
                :key="i"
                :layer="layer"
              ></PreviewLayer>
            </template>
          </ScreenWrapper>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
import Reveal from "reveal.js"
import "reveal.js/dist/reveal.css"
import ScreenWrapper from "./ScreenWrapper"
import PreviewLayer from "./PreviewLayer.vue"
export default {
  components: { ScreenWrapper, PreviewLayer },
  props: {},
  data() {
    return {
      displaySlides: [],
      displayInfo: {},
      slideLayers: {},
      currentSlide: {}
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getDisplaySlides()
  },
  watch: {},
  methods: {
    handleClick(params){
  
      console.log(params,'params')
    },
    // 获取幻灯片列表
    async getDisplaySlides() {
      const { data } = await this.$httpBi.display.getDisplaySlideList({
        displayId: this.$route.query.displayId
      })
      const { slides, ...rest } = data
      // 将config转换为对象
      slides.forEach(slide => {
        slide.config = JSON.parse(slide.config || "{}")
      })
      rest.config = JSON.parse(rest.config || "{}")
      this.displaySlides = slides || []
      this.currentSlide = slides[0] || {}
      this.displayInfo = rest || {}
      this.initReveal()
      this.getSlideLayers()
    },
    // 获取幻灯片的图层
    async getSlideLayers() {
      const { data } = await this.$httpBi.display.getDisplaySlideWidgets({
        displayId: this.displayInfo.id,
        slideId: this.currentSlide.id
      })
      const { items, widgets } = data || {
        items: [],
        views: [],
        widgets: []
      }

      const layers = items.map(layer => {
        return {
          ...layer,
          params: JSON.parse(layer.params || "{}")
        }
      })
      this.$set(this.slideLayers, this.currentSlide.id, layers)
      this.$store.commit("display/SET_WIDGETS", widgets)
    },
    // 获取幻灯片切换
    slideChanged(event) {
      this.currentSlide = this.displaySlides[event.indexh]
      if (!this.slideLayers[this.currentSlide.id]) {
        this.getSlideLayers()
      }
    },
    // 初始化reveal
    initReveal() {
      const { autoPlay, autoSlide, transitionStyle, transitionSpeed } =
        this.displayInfo.config.displayParams
      Reveal.initialize({
        hash: false, // 将当前幻灯片编号添加到URL散列中，以便重新加载页面/复制URL将返回到相同的幻灯片
        history: false, // 将每个幻灯片更改推送到浏览器历史记录。这意味着 `hash: true`
        controls: true, // 显示演示文稿控制箭头
        controlsLayout: "none", // 确定控件出现的位置，“边缘”(edges)或“右下”(bottom-right)
        slideNumber: this.displaySlides.length > 1 ? "c/t" : false, // 显示当前幻灯片的页码
        controlsTutorial: false, // 通过提供提示来帮助用户学习控件，例如，当用户第一次遇到垂直幻灯片时通过弹跳向下箭头来帮助他们
        progress: false, // 显示演示进度条
        loop: true, // 循环演示
        width: "100%",
        height: "100%",
        margin: 0,
        minScale: 1,
        maxScale: 1,
        autoSlide: autoPlay ? autoSlide * 1000 : 0, // 自动前进到下一张幻灯片之间的毫秒数，设置为0时将禁用，可以通过使用幻灯片上的data-autoslide属性覆盖此值
        // 过渡风格 无/淡入/滑动/凸出/凹入/缩放
        transition: transitionStyle, // none/fade/slide/convex/concave/zoom
        // 整页幻灯片背景的过渡样式 无/淡入/滑动/凸出/凹入/缩放
        // backgroundTransition: 'zoom', // none/fade/slide/convex/concave/zoom
        // 过渡速度 默认/快速/慢
        transitionSpeed: transitionSpeed, // default/fast/slow
        viewDistance: 100 // 远离当前可见的幻灯片数量
      })
      Reveal.addEventListener("slidechanged", this.slideChanged)
    }
  },
  destroyed() {
    Reveal.removeEventListener("slidechanged", this.slideChanged)
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  z-index: 999;
  & .reveal .slides {
    text-align: left;
  }
}
.screen {
  width: 1920px;
  height: 1080px;
  background-color: skyblue;
}
.screen2 {
  width: 1920px;
  height: 1080px;
  background-color: pink;
}
.present {
  top: 0 !important;
}
</style>
