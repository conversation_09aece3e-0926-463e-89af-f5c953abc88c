.portal-main[theme="dark"] {
    --el-color-primary: #409eff;
    --theme-color: #232324;
    --theme-text-color: #C9CCD6;
    --theme-border-color: #333335;
    --theme-icon-color: #828285;
    --theme-bg-color: #17171A;
    --theme-content-color: #FFFFFF;
    --footer1-text-color: #A6A6AD;
    --footer2-text-color: #A6A6AD;
    //滚动条颜色
    --scrollbar-color: #434343;



    ::v-deep .el-row {
        background-color: var(--theme-color);
        margin-bottom: 0;
    }

    ::v-deep .el-form-item--small.el-form-item {
        margin-bottom: 16px;
    }
    ::v-deep .el-select-dropdown {
        border: none;
        background-color: #232324 !important;
    }

    ::v-deep .el-tree .custom-tree-node{
        padding: 0!important;   

    }
    ::v-deep .el-select-dropdown__item {
        font-size: 14px;
        font-weight: 400;
        color: rgba(233, 233, 238, 0.55);
    }

    ::v-deep .el-select-dropdown__item.selected {
        color: #ffffff;
    }

    ::v-deep .el-select-dropdown__item.hover,
    ::v-deep .el-select-dropdown__item:hover {
        color: #ffffff;
        background-color: #343664;
    }

    ::v-deep .el-select .el-input.is-focus .el-input__inner {
        border-color: #ffffff;
    }

    ::v-deep .el-popper[x-placement^="bottom"] {
        margin-top: 4px;
    }

    // 取消小三角
    ::v-deep .el-popper .popper__arrow,
    .el-popper .popper__arrow::after {
        border-style: none !important;
    }

    ::v-deep .el-popper[x-placement^="bottom"] .popper__arrow::after {
        border-bottom-color: transparent;
    }


    ::v-deep .el-input__inner {
        background-color: var(--theme-color);
        border: 1px solid #444563;
        color: #ffffff;
    }

    ::v-deep .el-input__inner:focus {
        border-color: var(--el-color-primary);
    }


    ::v-deep .el-radio-button__inner {
        border: none;
        background: var(--theme-color);
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #444563;
    }

    ::v-deep .el-radio-button.is-active .el-radio-button__inner {
        border: none;
        background: var(--el-color-primary);
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #444563;
    }


    // 表格-------------------------------------------------------------------------------------------------------
    ::v-deep .vxe-table--render-default .vxe-table--body-wrapper, .vxe-table--render-default .vxe-table--footer-wrapper{
        background-color: transparent !important;
    }
    ::v-deep .vxe-table--header-wrapper.body--wrapper,
    ::v-deep .el-table tr,
    ::v-deep .el-table__header {
        background-color: transparent !important;
        color: #a3a6ad;
    }

    ::v-deep .vxe-table--body {
        background-color: transparent;
        color: #a3a6ad;
    }

    ::v-deep .vxe-body--column {
        background-image: none !important;

    }
    ::v-deep .vxe-table th.is-leaf,
    ::v-deep .vxe-table td {
        border-bottom: 1px solid var(--theme-border-color) !important
    }
    ::v-deep .el-table th.is-leaf,
    ::v-deep .el-table td {
        border-bottom: 1px solid var(--theme-border-color) !important
    }

    ::v-deep .el-table::before {
        background-color: transparent !important;
    }

    ::v-deep .el-table,
    ::v-deep .el-table__expanded-cell {
        background-color: transparent !important;
        color: #a3a6ad;

    }

    ::v-deep .el-table th>.cell {
        color: #a3a6ad !important;

    }

    /* 表格内背景颜色 */
    ::v-deep .el-table th,
    ::v-deep .el-table tr,
    ::v-deep .el-table td {
        background-color: transparent !important;
        color: #a3a6ad;

    }


    // 滚动条的宽度
    ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
        background-color: #ccc;
    }


    ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb {
        background-color: #000;
        border-radius: 3px;
    }

    // 分页器-------------------------------------------------------------------------------------------------------

    ::v-deep .vxe-pager,
    ::v-deep .dt-pagination-container {
        background-color: var(--theme-color);
        color: var(--theme-text-color)
    }

    ::v-deep .vxe-pager--prev-btn,
    ::v-deep .vxe-pager--next-btn,
    ::v-deep .vxe-pager--num-btn,
    ::v-deep .vxe-pager--jump-next,
    ::v-deep .btn-prev,
    ::v-deep .btn-next,
    ::v-deep .number,
    ::v-deep .btn-quicknext {
        background-color: #303030;
        color: var(--theme-text-color)
    }

    // 树-------------------------------------------------------------------------------------------------------
    ::v-deep .el-tree {
        background-color: var(--theme-color);
    }

    ::v-deep .el-tree-node.is-current>.el-tree-node__content {
        i {
            color: #ffffff;
        }

        .el-tree-node__expand-icon {
            background-color: #2361DB !important;

        }

        .custom-tree-node,
        .el-tree-node__label {
            background-color: #2361DB !important;
            color: #ffffff !important;
            padding: 0!important;




        }
    }

    ::v-deep .el-tree-node>.el-tree-node__content {
        &:hover {
            background: none;

            >.el-checkbox {
                background-color: #2361DB !important;
            }

            .el-tree-node__expand-icon {
                background-color: #2361DB !important;
            }

            .custom-tree-node,
            .el-tree-node__label {
                background-color: #2361DB !important;
                padding: 0!important;
            }
        }
    }


    ::v-deep .el-tree-node.is-current>.el-tree-node__content .el-tree-node__labe,
    .el-tree-node.is-current>.el-tree-node__content .custom-tree-node {
        background-color: #2361DB !important;
        color: #ffffff !important;
    }
    ::v-deep .el-tree-node>.el-tree-node__content, ::v-deep .el-tree-node>.el-tree-node__content .custom-tree-node {
        height: 46px !important;
        display: flex;
        align-items: center;
        flex: 1;
        margin-bottom: 4px;

    }
    ::v-deep .el-tree .el-tree-node>.el-tree-node__content .el-tree-node__expand-icon{
        height: 46px !important;
        width: 46px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 4px;

    }
    ::v-deep .el-tree .el-tree-node>.el-tree-node__content .el-tree-node__expand-icon.is-leaf{
        height: 46px !important;
        margin-bottom: 4px;

        width: 0 !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 14px;
        margin-bottom: 4px;
        margin-left: 0!important;

    }
    ::v-deep .custom-tree-node {
        color: var(--theme-text-color) !important;

    }
    //菜单-------------------------------------------------------------------------------------------------------
    ::v-deep .el-menu{
        background-color: var(--theme-color);
        border-right:var(--theme-border-color)
    }
    
    ::v-deep .el-menu-item{
        height: 46px;
        line-height: 46px;
        background-color: var(--theme-color);
        color: var(--theme-text-color);
        margin-bottom: 4px;
        span{
            margin-left: 13px;
        }

    }
    ::v-deep .el-menu-item.is-active,
    ::v-deep .el-menu-item:hover{
        background-color: #2361DB;
        color: #ffffff;
    }

    // 弹窗-------------------------------------------------------------------------------------------------------

    ::v-deep .el-dialog {
        background-color: var(--theme-color);

        color: var(--theme-text-color);

    }

    ::v-deep .el-dialog__title {
        color: var(--theme-text-color);
    }

    ::v-deep .el-collapse-item__content,
    ::v-deep .el-collapse-item__header {
        background-color: var(--theme-color);
        color: var(--theme-text-color);
    }

    ::v-deep .el-collapse-item__wrap {
        border-bottom: 1px solid var(--theme-border-color);
    }

    ::v-deep .el-collapse {
        border: none;
    }


    //表单
    ::v-deep .el-form-item__label {
        color: var(--theme-text-color);
    }

    //步骤条
    ::v-deep .el-steps--simple {
        background-color: var(--theme-color);
        color: var(--theme-text-color);
    }

    ::v-deep .el-step__title.is-wait {
        color: #303133 !important;
    }

    ::v-deep .el-step__title.is-process {
        color: #fff;
    }

    //日期选择
    ::v-deep .el-range-editor--mini .el-range-input {
        background: var(--theme-color);
    }

    ::v-deep .el-range-editor--small .el-range-input,
    ::v-deep .el-date-editor .el-range-separator {
        background: var(--theme-color);
        color: var(--theme-text-color);
    }

    //标签
    ::v-deep .el-tag.el-tag--info {
        background: var(--theme-color);
        color: var(--theme-text-color);
    }
}
