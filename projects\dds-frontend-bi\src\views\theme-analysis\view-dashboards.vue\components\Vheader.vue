<template>
  <div class="panel-header">
    <span class="back-button" @click="goBack">
      <i class="el-icon-arrow-left" />
    </span>
    <div class="title" v-if="loading">
      <div class="name">
        <span>{{ name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Request from '@/service'
import { getUrlParams } from '@/utils'
export default {
  props: {},
  data() {
    return {
      loading: true,
      themes: []
    }
  },
  created() {
    this.getAllThemes()
  },
  methods: {
    // 初始化表格数据
    getAllThemes() {
      this.loading = true
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
          this.changeTheme()
        })
        .catch(() => {})
        .finally(() => {
          this.show = true
          this.loading = false
        })
    },
    goBack() {
      console.log(this.$router)
      const path = getUrlParams('redirect')
      window.location.replace(`${window.location.origin}${path}`)
      // this.$router.replace(path);
    },
    // 将query中的theme转换为中文
    changeTheme() {
      const name = this.$route.query.theme
      this.themes.forEach(item => {
        if (item.value === name) {
          this.name = item.label
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.panel-header {
  position: relative;
  background-color: var(--theme-color);
  height: 56px;
  @include flexbox();
  padding: 0 20px;
  z-index: 9;
  border-bottom: 1px solid var(--theme-border-color);
  color: var(--theme-text-color);

  .title {
    flex: 1;
    padding: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: flex-end;

    .name {
      flex-shrink: 0;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .desc {
      flex: 1;
      height: 56px;
      line-height: 56px;
      color: '#ccc';
      font-size: 12px;
      position: relative;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 10px;
    }
  }
}
.back-button {
  cursor: pointer;
}
</style>
