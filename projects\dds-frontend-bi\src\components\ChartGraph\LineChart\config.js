import ChartTypes from '../ChartTypes'
export default {
  id: ChartTypes.Line,
  name: "line",
  title: "折线图",
  icon: "chart_line",
  rules: [ { dimension: 1, metrics: [ 1, 9999 ] } ],
  data: [
    {
      title: "维度",
      type: "category",
      name: "cols",
    },
    {
      title: "指标",
      type: "value",
      name: "metrics",
    },
    {
      title: "颜色",
      type: "category",
      name: "color",
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
  ],
  style: {
    
  },
}
