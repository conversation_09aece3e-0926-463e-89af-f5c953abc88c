<template>
  <el-dialog
    title="下钻配置"
    :visible.sync="drillDownVisible"
    width="60%"
    @closed="$emit('update:drillDownVisible', false)"
  >
    <el-form
      :model="form"
      status-icon
      :rules="rules"
      ref="ruleForm"
      label-width="200px"
      class="ruleForm"
    >
      <el-form-item label="开启层级下钻">
        <el-switch
          v-model="form.isOpen"
          active-color="#13ce66"
          inactive-color="#ff4949"
        ></el-switch>
      </el-form-item>
      <template v-if="form.isOpen">
        <el-form-item label="下钻类型">
          <el-radio-group v-model="form.drillType" @change="changeDrillType">
            <el-radio :label="1">图表</el-radio>
            <el-radio :label="2">看板</el-radio>
            <el-radio :label="3">外链</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="下钻后展示的图表" v-if="form.drillType == 1">
          <el-select
            v-model="form.target"
            @change="handleTarget"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in widgets"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下钻后展示的看板" v-if="form.drillType == 2">
          <el-select
            v-model="form.target"
            @change="handleTarget"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in AllProtal"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="外链URL地址" v-if="form.drillType == 3">
          <el-input v-model="form.url" placeholder="请输入内容"></el-input>
        </el-form-item>

        <template v-if="selectedChartId == 1 && form.drillType !== 3">
          <el-form-item
            label="字段关联控制器"
            v-for="(item, index) in form.relation"
            :key="index"
            :prop="'relation.' + index + '.zd'"
            :rules="{
              required: true,
              message: '字段关联控制器不能为空',
              trigger: 'blur'
            }"
          >
            <el-select v-model="item.zd" placeholder="请选择关联字段" clearable>
              <el-option
                v-for="o in options"
                :key="o.displayName"
                :label="o.displayName"
                :value="o.displayName"
                :disabled="form.relation.some(r => r.zd == o.displayName)"
              ></el-option>
            </el-select>
            <el-select
              v-model="item.kzq"
              placeholder="请选择关联控制器"
              clearable
            >
              <el-option
                v-for="c in controls"
                :key="c.key"
                :label="c.name"
                :value="c.key"
                :disabled="form.relation.some(r => r.kzq == c.key)"
              ></el-option>
            </el-select>
            <i class="el-icon-minus" @click="removeDomain(item)"></i>
            <i class="el-icon-plus" @click="addDomain"></i>
          </el-form-item>
        </template>
        <template v-if="selectedChartId !== 1 && form.drillType !== 3">
          <el-form-item label="请选择要下钻X轴关联控制器">
            <el-select v-model="form.xAxis" placeholder="请选择" clearable>
              <el-option
                v-for="item in controls"
                :key="item.key"
                :label="item.name"
                :value="item.key"
                :disabled="form.series == item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请选择要下钻系列关联控制器">
            <el-select v-model="form.series" placeholder="请选择" clearable>
              <el-option
                v-for="item in controls"
                :key="item.key"
                :label="item.name"
                :value="item.key"
                :disabled="form.xAxis == item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="请选择要下钻指标关联控制器">
          <el-select v-model="form.valueName" placeholder="请选择">
            <el-option
              v-for="item in controls"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        </template>
        <template v-if="selectedChartId !== 1 && form.drillType == 3">
          <el-form-item label="请输入X轴参数名称">
            <el-input v-model="form.xAxis" x-axis="请输入参数名称"></el-input>
          </el-form-item>
          <el-form-item label="请输入系列参数名称">
            <el-input v-model="form.series" x-axis="请输入参数名称"></el-input>
          </el-form-item>
          <!-- <el-form-item label="请选择要下钻指标关联控制器">
          <el-select v-model="form.valueName" placeholder="请选择">
            <el-option
              v-for="item in controls"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        </template>

        <template v-if="selectedChartId == 1 && form.drillType == 3">
          <el-form-item
            label="字段参数名称"
            v-for="(item, index) in form.relation"
            :prop="'relation.' + index + '.zd'"
            :key="index"
            :rules="{
              required: true,
              message: '名称不能为空',
              trigger: 'blur'
            }"
          >
            <el-input
              style="width: 150px"
              v-model="item.kzq"
              x-axis="请输入参数名称"
            ></el-input>
            =
            <el-select v-model="item.zd" placeholder="请选择关联字段" clearable>
              <el-option
                v-for="o in options"
                :key="o.displayName"
                :label="o.displayName"
                :value="o.displayName"
                :disabled="form.relation.some(r => r.zd == o.displayName)"
              ></el-option>
            </el-select>

            <i class="el-icon-minus" @click="removeDomain(item)"></i>
            <i class="el-icon-plus" @click="addDomain"></i>
          </el-form-item>
        </template>
        <el-form-item label="默认展示方式" v-if="form.drillType !== 3">
          <el-select v-model="form.revealType" placeholder="请选择">
            <el-option
              v-for="item in revealOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:drillDownVisible', false)">
        取 消
      </el-button>
      <el-button type="primary" @click="handleEditSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from "vuex"
export default {
  components: {},
  props: {
    drillDownVisible: {
      type: Boolean,
      default: false
    },
    currentWidget: {
      type: Object
    },
    form: {
      type: Object
    }
  },
  data() {
    return {
      AllProtal: [],
      drillType: 1,
      controls: [],
      value: "",
      revealOptions: [
        {
          value: 1,
          label: "当前页弹窗展示"
        },
        {
          value: 2,
          label: "新窗口打开"
        }
      ]
    }
  },
  computed: {
    options() {
      if (this.currentWidget.config) {
        const config = JSON.parse(this.currentWidget.config)
        return [...config.cols, ...config.metrics, ...config.color]
      }
      return []
    },
    selectedChartId() {
      if (this.currentWidget.config) {
        const config = JSON.parse(this.currentWidget.config)
        return config.selectedChartId
      } else {
        return null
      }
    },
    ...mapState({
      widgets: state => state.widget.widgets
    })
  },
  created() {},
  mounted() {},
  watch: {
    "form.target": {
      handler(targetId) {
        if (targetId) {
          console.log(targetId, "targetId")
          // 图标控制器
          if (this.form.drillType === 1) {
            let widgetConfig = this.widgets.find(item => item.id === targetId)
            this.controls = widgetConfig.config.controls
          }
          // 看板控制器
          if (this.form.drillType === 2) {
            let widgetConfig = this.AllProtal.find(item => item.id === targetId)
            this.controls =
              widgetConfig && JSON.parse(widgetConfig.config).filters
          }
        }
      }
    }
  },
  methods: {
    changeDrillType() {
      this.form = {
        ...this.form,
        target: "",
        xAxis: "",
        series: "",
        valueName: "",
        revealType: 1,
        relation: [{ zd: "", kzq: "" }]
      }
    },
    handleEditSave() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$emit("handleEditSave", this.form)
        } else {
          return false
        }
      })
    },
    handleTarget() {
      this.form.xAxis = ""
      this.form.series = ""
    },
    addDomain() {
      this.form.relation.push({
        zd: "",
        kzq: "",
        key: Date.now()
      })
    },
    removeDomain(item) {
      var index = this.form.relation.indexOf(item)
      if (index !== -1 && this.form.relation.length > 1) {
        this.form.relation.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.ruleList {
  background-color: #61616155;
  padding: 20px;
}
.formItem {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  div {
    margin-right: 10px;
  }
  i {
    margin-right: 10px;
    cursor: pointer;
  }
}
.add {
  color: #409eff;
  padding: 10px;
  cursor: pointer;
}
</style>
