export function metricAxisLabelFormatter(value) {
  if (value >= Math.pow(10, 12)) {
    return `${precision(value / Math.pow(10, 12))}T`
  } else if (value >= Math.pow(10, 9) && value < Math.pow(10, 12)) {
    return `${precision(value / Math.pow(10, 9))}B`
  } else if (value >= Math.pow(10, 6) && value < Math.pow(10, 9)) {
    return `${precision(value / Math.pow(10, 6))}M`
  } else if (value >= Math.pow(10, 3) && value < Math.pow(10, 6)) {
    return `${precision(value / Math.pow(10, 3))}K`
  } else {
    return value
  }

  function precision(num) {
    return num >= 10 ? Math.floor(num) : num.toFixed(1)
  }
}


