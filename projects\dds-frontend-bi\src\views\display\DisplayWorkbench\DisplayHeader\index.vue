<template>
  <div class="panel-header">
    <span class="back-button" @click="goBack">
      <i class="el-icon-back" />
      <span>返回</span>
    </span>
    <el-divider direction="vertical"></el-divider>
    <div
      class="avatar"
      :style="{
        backgroundImage: displayInfo.avatar
          ? env === 'development'
            ? `url('http://***************${displayInfo.avatar}')`
            : `url('${origin}${displayInfo.avatar}')`
          : 'url()'
      }"
    ></div>
    <div class="title">{{ displayInfo.name }}</div>
    <div class="description">{{ displayInfo.description }}</div>
    <div className="display-toolbar">
      <el-button size="mini" plain icon="el-icon-plus" @click="addSlide">
        添加大屏页
      </el-button>
      <el-button
        plain
        size="mini"
        icon="el-icon-setting"
        @click="openDisplaySettingModal"
      >
        大屏设置
      </el-button>

      <el-button
        plain
        size="mini"
        @click="isAddWidgetVisible = true"
        icon="el-icon-s-data"
      >
        图表
      </el-button>
      <el-dropdown size="mini" trigger="hover" @command="handleCommand">
        <el-button size="mini" icon="el-icon-menu" style="margin-left: 10px">
          辅助图形
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in SecondaryGraphTypes"
            :key="item.id"
            :icon="item.icon"
            :command="{
              code: item.code,
              label: item.label
            }"
          >
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown size="mini" trigger="hover">
        <el-button
          size="mini"
          :icon="BorderImageLayerConfig.icon"
          style="margin-left: 10px"
        >
          {{ BorderImageLayerConfig.label }}
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <ul class="borderImgList">
            <li
              class="borderImgList-item"
              @click="addBorderImg(item)"
              v-for="item in borderImageList"
              :key="item.name"
            >
              <img :src="item.url" alt="" />
              <p>{{ item.name }}</p>
            </li>
          </ul>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        plain
        size="mini"
        style="margin-left: 10px"
        icon="el-icon-camera"
        @click="$emit('screenshot')"
      >
        截图
      </el-button>
      <el-button
        plain
        size="mini"
        style="margin-left: 10px"
        icon="el-icon-view"
        @click="previewDisplay"
      >
        预览
      </el-button>
      <el-button plain size="mini" icon="el-icon-share">分享</el-button>
    </div>
    <!-- 大屏设置modal -->
    <DisplaySettingModal :visible.sync="isDisplaySettingModalVisible" />
    <!-- 图表选择modal -->
    <WidgetSelectModal
      ref="Widgets"
      :add-widget-visible.sync="isAddWidgetVisible"
      @onSelectWidget="onSelectWidget"
    />
  </div>
</template>

<script>
import DisplaySettingModal from "./DisplaySettingModal.vue"
import WidgetSelectModal from "./WidgetSelectModal.vue"
import { mapGetters } from "vuex"
import { uuid } from "@/utils"
import { getDefaultLayerConfig } from "../LayerComponents/index"
import { ImageLayerConfig } from "../LayerComponents/ImageLayer/ImageLayerConfig"
import { TextLayerConfig } from "../LayerComponents/TextLayer/TextLayerConfig"
import { VideoLayerConfig } from "../LayerComponents/VideoLayer/VideoLayerConfig"
import { TimeLayerConfig } from "../LayerComponents/TimeLayer/TimeLayerConfig"
import { HrefLayerConfig } from "../LayerComponents/HrefLayer/HrefLayerConfig"
import { IframeLayerConfig } from "../LayerComponents/IframeLayer/IframeLayerConfig"
import { TabLayerConfig } from "../LayerComponents/TabLayer/TabLayerConfig"
import { BorderImageLayerConfig } from "../LayerComponents/BorderImageLayer/BorderImageLayerConfig"
export default {
  components: {
    DisplaySettingModal,
    WidgetSelectModal
  },
  props: {
    currentPortal: {
      type: Object
    }
  },
  data() {
    return {
      BorderImageLayerConfig,
      // 边框图片集合
      borderImageList: BorderImageLayerConfig.options.style[4].selectOptions,
      // 辅助图形
      SecondaryGraphTypes: [
        TextLayerConfig,
        ImageLayerConfig,
        VideoLayerConfig,
        TimeLayerConfig,
        HrefLayerConfig,
        IframeLayerConfig,
        TabLayerConfig
      ],
      isDisplaySettingModalVisible: false,
      isAddWidgetVisible: false
    }
  },
  computed: {
    ...mapGetters({
      displayInfo: "displayInfo",
      maxLayerIndex: "maxLayerIndex",
      currentSlide: "currentSlide"
    }),
    env() {
      return process.env.NODE_ENV
    },
    origin() {
      return window.location.origin
    }
  },
  created() {},
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 打开大屏设置
    openDisplaySettingModal() {
      this.isDisplaySettingModalVisible = true
    },
    // 添加大屏页
    addSlide() {
      this.$store.dispatch("display/addSlide")
    },
    // 图表选择确定
    onSelectWidget(selectedWidgets, pollingSetting) {
      const { id: slideId } = this.currentSlide
      const { polling, frequency } = pollingSetting
      const newLayers = selectedWidgets.map(({ id, name }, idx) => ({
        displaySlideId: slideId,
        index: this.maxLayerIndex + idx + 1,
        widgetId: id,
        name,
        type: 1,
        active: false,
        params: {
          ...getDefaultLayerConfig(1),
          polling,
          frequency
        }
      }))
      this.$store.dispatch("display/addSlideLayers", newLayers).then(() => {
        this.isAddWidgetVisible = false
        this.$refs.Widgets.initData()
      })
    },
    // 辅助图形
    handleCommand({ label, code }) {
      const { id: slideId } = this.currentSlide
      const newLayers = [
        {
          displaySlideId: slideId,
          index: this.maxLayerIndex + 1,
          name: `${label}_${uuid(5)}`,
          type: 2,
          subType: code,
          active: false,
          params: {
            ...getDefaultLayerConfig(code)
          }
        }
      ]
      this.$store.dispatch("display/addSlideLayers", newLayers)
    },
    // 添加边框图片
    addBorderImg(item) {
      const { id: slideId } = this.currentSlide
      const newLayers = [
        {
          displaySlideId: slideId,
          index: this.maxLayerIndex + 1,
          name: `${item.name}_${uuid(5)}`,
          type: 2,
          active: false,
          subType: BorderImageLayerConfig.code,
          params: {
            ...getDefaultLayerConfig(BorderImageLayerConfig.code),
            component: item.code
          }
        }
      ]
      this.$store.dispatch("display/addSlideLayers", newLayers)
    },
    // 预览
    previewDisplay() {
      // 另标签页打开
      window.open(
        `${window.location.origin}/ddsBi/displayPreview?isFullPage=true&displayId=${this.displayInfo.id}`
      )
    }
  }
}
</script>

<style scoped lang="scss">
.panel-header {
  position: relative;
  background-color: #fff;
  height: 60px;
  @include flexbox();
  padding: 0 20px;
  box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.1);
  z-index: 9;
  white-space: nowrap;
  overflow-x: auto;

  .title {
    padding: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 20px;
  }
  .description {
    flex: 1;
    text-align: left;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
}
.avatar {
  width: 60px;
  height: 50px;
  border-radius: 5px;
  background-size: cover;
}
.back-button {
  cursor: pointer;
}
.el-button-group {
  margin-left: 10px;
}
.borderImgList {
  padding: 20px;
  width: 390px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
  .borderImgList-item {
    cursor: pointer;
    img {
      width: 110px;
      height: 70px;
    }
    p {
      text-align: center;
    }
  }
  .borderImgList-item:hover > p {
    color: #409eff;
  }
}
</style>
