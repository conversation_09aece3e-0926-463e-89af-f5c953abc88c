<template>
  <!-- 编辑容器 -->
  <div class="display-container">
    <ScreenWrapper
      ref="ScreenWrapper"
      @onScale="setStep"
      @onMousedown="handleMouseDown"
    >
      <template v-for="(layer, index) in slideLayers">
        <avue-draggable
          :key="index"
          :step="step"
          :width="layer.params.width"
          :height="layer.params.height"
          :left="layer.params.left"
          :top="layer.params.top"
          :class="{ closeMask: layer.subType === 2 }"
          :index="index"
          :z-index="layer.index"
          ref="draggable"
          @focus="() => handleFocus(layer)"
          @move="handleMove"
          @blur="$event => handleBlur($event, layer)"
          @contextmenu.prevent.native="rightClick($event, layer.index)"
        >
          <LayerBox :layer="layer" v-on="$listeners" />
        </avue-draggable>
      </template>
    </ScreenWrapper>
    <ContentMenu
      :visible.sync="visibleContentMenu"
      :style-obj="styleObj"
      @handlelayer="handlelayer"
    />
    <!-- <MarkLine /> -->
  </div>
</template>

<script>
// import MarkLine from "./MarkLine"
import { mapGetters } from "vuex"
import ScreenWrapper from "./ScreenWrapper"
import LayerBox from "../LayerComponents/LayerBox/LayerBox.vue"
import ContentMenu from "./components/ContentMenu.vue"
export default {
  components: { ScreenWrapper, LayerBox, ContentMenu },
  props: {},
  data() {
    return {
      step: 1, // 拖拽步长
      // 右键菜单坐标
      styleObj: {
        left: 0,
        top: 0
      },
      visibleContentMenu: false, // 右键菜单是否显示
      rightClickIndex: 0 // 右键菜单点击的图层z-index
    }
  },
  computed: {
    ...mapGetters({
      currentSlide: "currentSlide",
      slideLayers: "slideLayers",
      currentLayerId: "currentLayerId",
      layersIndexRankList: "layersIndexRankList",
      maxLayerIndex: "maxLayerIndex",
      currentLayer: "currentLayer"
    }),
    deepClonelayersIndexRankList() {
      return this._.deepClone(this.layersIndexRankList)
    }
  },
  created() {},
  mounted() {},
  watch: {
    currentLayerId(val) {
      for (let i = 0; i < this.slideLayers.length; i++) {
        if (this.slideLayers[i].id === val) {
          this.$refs.draggable[i].setActive(true)
          this.handleFocus(this.slideLayers[i])
        } else {
          this.$refs.draggable[i].setActive(false)
        }
      }
    }
  },
  methods: {
    // 截图
    screenshot() {
      this.$refs.ScreenWrapper.screenshot()
    },

    // 右键菜单
    rightClick(event, index) {
      this.rightClickIndex = index
      const left = event.clientX
      const top = event.clientY
      if (left || top) {
        this.styleObj = {
          left: left + "px",
          top: top + "px",
          display: "block"
        }
      }
      this.visibleContentMenu = true
      return false
    },
    // 调用内部方法取消选中，false取消，true激活
    handleMouseDown() {
      this.$nextTick(() => {
        for (let i = 0; i < this.slideLayers.length; i++) {
          this.$refs.draggable[i].setActive(false)
        }
        console.log("screen")
        this.$emit("setOptionsOnClick", "screen")
      })
    },
    // 根据大屏缩放比例设置拖拽步长
    setStep(scale) {
      this.step = Number(100 / (scale * 100))
    },
    handleFocus(layer) {
      console.log(layer, "SET_CURRENT_LAYER")
      this.$store.commit("display/SET_CURRENT_LAYER", layer)
      if (layer.type === 1) {
        // 图表
        this.$emit("setOptionsOnClick", layer.type)
      } else {
        // 其他类型
        this.$emit("setOptionsOnClick", layer.subType)
      }
    },
    //  选中当前图层取消其他图层选中
    handleBlur({ index, left, top, width, height }, layer) {
      console.log(left, top, width, height)
      for (let i = 0; i < this.layersIndexRankList.length; i++) {
        if (this.layersIndexRankList[i].id === layer.id) {
          this.$refs.draggable[i].setActive(true)
        } else {
          this.$refs.draggable[i].setActive(false)
        }
      }
      if (layer.type === 1) {
        // 图表
        this.$emit("setOptionsOnClick", layer.type, {
          index,
          left,
          top,
          width,
          height
        })
      } else {
        // 其他类型
        this.$emit("setOptionsOnClick", layer.subType, {
          index,
          left,
          top,
          width,
          height
        })
      }
    },
    // 图表移动中
    handleMove(e, b, c) {
      console.log(e, b, c)
    },
    // 处理右键菜单事件
    handlelayer(eventName) {
      const i = this.layersIndexRankList.findIndex(
        item => item.index === this.rightClickIndex
      )
      switch (eventName) {
        // 上移一层
        case "moveupLayer":
          this.moveupLayer(i)
          break
        // 下移一层
        case "movedownLayer":
          this.movedownLayer(i)
          break
        // 置顶
        case "istopLayer":
          this.istopLayer(i)
          break
        // 置底
        case "setlowLayer":
          this.setlowLayer(i)
          break
        // 删除
        case "deletelayer":
          this.deleteLayer(i)
          break
        // 复制
        case "copylayer":
          this.copylayer(i)
          break
        default:
          break
      }
    },
    // 上移一层
    moveupLayer(i) {
      if (i !== 0) {
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i],
            index: this.layersIndexRankList[i - 1].index
          },
          isUpdateCurrentLayer: false
        })
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i - 1],
            index: this.layersIndexRankList[i].index
          },
          isUpdateCurrentLayer: false
        })
      }
    },
    // 下移一层
    movedownLayer(i) {
      if (i !== this.layersIndexRankList.length - 1) {
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i],
            index: this.layersIndexRankList[i + 1].index
          },
          isUpdateCurrentLayer: false
        })
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i + 1],
            index: this.layersIndexRankList[i].index
          },
          isUpdateCurrentLayer: false
        })
      }
    },
    // 置顶
    istopLayer(i) {
      if (i !== 0) {
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i],
            index: this.layersIndexRankList[0].index + 1
          },
          isUpdateCurrentLayer: false
        })
      }
    },
    // 置底
    setlowLayer(i) {
      if (i !== this.layersIndexRankList.length - 1) {
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.layersIndexRankList[i],
            index:
              this.layersIndexRankList[this.layersIndexRankList.length - 1]
                .index - 1
          },
          isUpdateCurrentLayer: false
        })
      }
    },
    // 删除
    deleteLayer(i) {
      this.$store.dispatch("display/deleteLayer", {
        layer: this.layersIndexRankList[i]
      })
    },
    // 复制
    copylayer() {
      const { id: slideId } = this.currentSlide
      const newLayers = [
        {
          ...this.currentLayer,
          displaySlideId: slideId,
          index: this.maxLayerIndex + 1,
          id: undefined
        }
      ]
      this.$store.dispatch("display/addSlideLayers", newLayers)
    }
  }
}
</script>

<style lang="scss">
.display-container {
  position: relative;
  width: 100%;
  flex: 1;
  height: 100%;
}

.avue-draggable {
  padding: 0 !important;
}
//文本框可以双击编辑
.avue-draggable.avue-draggable--active.closeMask {
  .avue-draggable__wrapper {
    .avue-draggable__mask {
      z-index: -1;
    }
  }
}
</style>
