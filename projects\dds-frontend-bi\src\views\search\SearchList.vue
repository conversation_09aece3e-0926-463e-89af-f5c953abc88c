<template>
  <DT-View>
    <div class="search-warp search-warp-new">
      <div class="icon"></div>
      <h1>综合查询</h1>
      <div class="search-input">
        <el-input
          placeholder="请输入关键词查询"
          v-model="searchContent"
          class="input-with-select"
          clearable
          @keyup.enter.native="handleSearch"
        ></el-input>
        <div class="btn" @click="handleSearch">查询</div>
      </div>
    </div>
    <div class="content">
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        v-loading="loading"
      >
        <el-tab-pane :label="`主题场景(${themeScenesData.length})`" name="1">
          <ThemeScenes
            :theme-scenes-data="themeScenesData"
            :search-content="searchContent"
          />
        </el-tab-pane>
        <el-tab-pane :label="`数据图表(${data.widget.length})`" name="2">
          <DataCharts
            :widget-list="data.widget"
            :search-content="searchContent"
          />
        </el-tab-pane>
        <el-tab-pane :label="`原子指标(${data.index.length})`" name="3">
          <Source :source-list="data.index" :search-content="searchContent" />
        </el-tab-pane>
        <el-tab-pane :label="`应用指标(${data.view.length})`" name="4">
          <ViewData :source-list="data.view" :search-content="searchContent" />
        </el-tab-pane>
        <el-tab-pane :label="`预警指标(${data.warning.length})`" name="5">
          <Warning
            :warning-list="data.warning"
            :search-content="searchContent"
          />
        </el-tab-pane>
        <!-- <el-tab-pane :label="`全文搜索(${data.overall.total})`" name="6">
          <ThemeScenes1
            :theme-scenes-data="data.overall"
            :search-content="searchContent"
            :page.sync="page"
            @changePage="changePage"
          />
        </el-tab-pane> -->
      </el-tabs>
      <div class="history">
        <div class="header">
          <span style="font-weight: 600; color: #323233">最近搜索</span>
          <div
            style="float: right; padding: 3px 0; cursor: pointer"
            @click="handleClean"
          >
            <i class="el-icon-delete"></i>
            清空
          </div>
        </div>
        <div class="list">
          <div
            v-for="(item, index) in history.slice(0, 5)"
            :key="index"
            class="list-item-custom"
            @click="handleHistory(item.label)"
          >
            <span class="number">{{ index + 1 }}</span>
            <span class="name" :title="item.label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </DT-View>
</template>

<script>
import ThemeScenes from "./ThemeScenes"
// import ThemeScenes1 from "./ThemeScenes/index1"

import DataCharts from "./DataCharts"
import Source from "./Source"
import ViewData from "./ViewData"
import Warning from "./Warning"
export default {
  components: {
    ThemeScenes,
    // ThemeScenes1,
    DataCharts,
    Source,
    ViewData,
    Warning
  },
  props: {},
  data() {
    return {
      loading: false,
      value: "",
      data: {
        warning: [],
        view: [],
        index: [],
        widget: [],
        display: [],
        dashboard: [],
        overall: {
          total: 0,
          list: []
        }
      },
      page: {
        currentPage: 1,
        pageSisze: 10
      },
      activeName: "1",
      // 搜索内容
      searchContent: "",
      // 历史搜索
      history: []
    }
  },
  computed: {
    // 主题场景数据
    themeScenesData() {
      return [...this.data.display, ...this.data.dashboard]
    }
  },
  created() {
    const { searchContent } = this.$route.params
    this.searchContent = searchContent || ""
    this.getHistory()
    if (this.searchContent) {
      this.handleSearch()
    }
  },
  mounted() {},
  watch: {},
  methods: {
    async handleSearch() {
      this.page.currentPage = 1
      this.changePage()
      // 判断是否是空格
    },
    // 搜索内容
    async changePage() {
      if (this.searchContent.trim().length) {
        this.loading = true
        const { data } = await this.$httpBi.search.globalSearch({
          inputText: this.searchContent,
          userId: 0,
          currentPage: this.page.currentPage,
          pageSize: this.page.pageSisze
        })
        this.data = {
          warning: data.warning || [],
          view: data.view || [],
          index: data.index || [],
          widget: data.widget || [],
          display: data.display || [],
          dashboard: data.dashboard || [],
          overall: data.overall || {
            total: 0,
            list: []
          }
        }
        this.loading = false
        this.getHistory()
      } else {
        this.$message.warning("请输入关键词查询")
      }
    },
    // 获取历史记录
    async getHistory() {
      const { data } = await this.$httpBi.search.globalSearchRecord()
      this.history = data
    },
    // 历史记录点击
    handleHistory(item) {
      this.searchContent = item
      this.handleSearch()
    },
    // 清除历史记录
    async handleClean() {
      await this.$httpBi.search.clearHistory()
      this.getHistory()
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    handleCurrentChange(event) {
      console.log(event)
    }
    // 文字高亮
  }
}
</script>

<style scoped lang="scss">
.search-warp {
  width: 576px;
  margin: 0 auto;
  margin-top: 6%;
  h1 {
    display: none;
  }
  .icon {
    width: 248px;
    height: 183px;
    margin: 0 auto;
    margin-bottom: 29px;
    background: url("~@/assets/imgs/bi/home-search.png") no-repeat;
  }
}
.search-warp-new {
  width: 60%;
  margin: 0;
  .icon {
    display: none;
  }
  h1 {
    display: block;
    height: 28px;
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #1d2129;
    margin-bottom: 16px;
  }
}
.content {
  position: relative;
  .history {
    position: absolute;
    right: 10%;
    top: 100px;
    width: 352px;
    height: 208px;
    background: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(78, 89, 105, 0.2);
    border-radius: 4px;
    .text {
      font-size: 14px;
    }

    .item {
      margin-bottom: 18px;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 352px;
      height: 40px;
      padding: 0 16px;
      box-sizing: border-box;
    }
    .list-item-custom {
      padding: 0 16px;
      height: 32px;
      font-size: 14px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .number {
        color: #4e5969;
      }
      .name {
        flex: 1;
        margin-left: 19px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #1463ff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .list-item-custom:nth-child(1) {
      .number {
        color: #eb2f3e;
      }
    }
    .list-item-custom:nth-child(2) {
      .number {
        color: #f57a00;
      }
    }
    .list-item-custom:nth-child(3) {
      .number {
        color: #f7902a;
      }
    }
  }
}
::v-deep .el-input__inner {
  height: 40px;
  background: #ffffff;
  border-radius: 4px 0px 0px 4px;
}
::v-deep .el-tabs__item {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #323233;
}
::v-deep .el-tabs__item.is-active {
  font-weight: 600;
  color: #1d2129;
}
.search-input {
  display: flex;
  align-items: center;
  .btn {
    width: 96px;
    height: 40px;
    background: #1463ff;
    border-radius: 0px 4px 4px 0px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    cursor: pointer;
    line-height: 40px;
    text-align: center;
  }
}
::v-deep .el-tabs__content {
  width: calc(90% - 376px);
}
</style>
