// 引入Mock.js

export default {
  "/send": () => ({
    code: 200,
    message: "OK",
    data: {
      index: [
        {
          lxbm: "yz", // 指标类型编码
          name: "ZGH", // 原子指标的字段
          id: 149,
          info: "tabmc[tabid]", // 使用这个字段显示数据源表名
          tabmc: "", // 数据源表名
          zbmc: "原子指标名称", // 原子指标名称
          sysjy: "教师域", // 所属指标域
          tabid: "ods_gxxs_bzksjbxx_x" // 数据源表
        }
      ],
      warning: [],
      view: [
        {
          sysjy: "测试域",
          code: 109,
          lxbm: "ps",
          wdcnt: 3,
          ttab: "DWD_ZB_PS_109",
          isSingle: 0,
          name: "2222",
          id: 109,
          childid: 159,
          glzb: null,
          zval: 0,
          info: ""
        },
        {
          sysjy: "测试域",
          code: 110,
          lxbm: "ps",
          wdcnt: null,
          ttab: "DWD_ZB_PS_110",
          isSingle: 1,
          name: "222222",
          id: 110,
          childid: 159,
          glzb: null,
          zval: 155772.0,
          info: ""
        }
      ],
      widget: [],
      dashboard: "",
      display: []
    }
  }),
  "/getIndicatorLatestData": () => ({
    code: 200,
    message: "OK",
    data: {
      id: 110,
      calctime: "10点40分",
      lxbm: "ps",
      zbmc: "222222",
      ms: "我是描述",
      bq: null,
      sysjy: "测试域",
      isSingle: 1,
      search: [],
      header: [],
      chart: [
        {
          wdx: "2024-03-01",
          wdz: 155772.0
        }
      ],
      data: {
        totalCount: 1,
        pageSize: 10,
        totalPage: 1,
        currentPage: 1,
        list: [
          {
            TSTAMP: "2024-03-01T18:57:56",
            YJZT: "0",
            LXBM: null,
            TVMAX: 0,
            JLDW: "人",
            TVMIN: 1,
            ZBID: 110,
            ZVAL: 155772.0
          }
        ]
      },
      glzb: [
        {
          id: 110,
          lxbm: "ps",
          zbmc: "222222"
        }
      ]
    }
    // data: {
    //   id: 109,

    //   calctime: "",

    //   lxbm: "ps",

    //   zbmc: "2222",

    //   ms: "",

    //   bq: null,

    //   sysjy: "测试域",

    //   isSingle: 0,

    //   search: [
    //     {
    //       id: 194,

    //       pid: 109,

    //       wdzd: "YXMC",

    //       zdmc: "院系名称",

    //       lxbm: "ps",

    //       ttab: "ods_gxxs_bzksjbxx_x",

    //       wdzval: [""],

    //       wd: [
    //         {
    //           bm: "",

    //           name: "全部"
    //         },

    //         {
    //           bm: "信息与智能科学技术学院",

    //           name: "信息与智能科学技术学院"
    //         },

    //         {
    //           bm: "公共管理与法学学院",

    //           name: "公共管理与法学学院"
    //         },

    //         {
    //           bm: "农学院",

    //           name: "农学院"
    //         },

    //         {
    //           bm: "动物医学院",

    //           name: "动物医学院"
    //         },

    //         {
    //           bm: "动物科学技术学院",

    //           name: "动物科学技术学院"
    //         },

    //         {
    //           bm: "化学与材料科学学院",

    //           name: "化学与材料科学学院"
    //         },

    //         {
    //           bm: "商学院",

    //           name: "商学院"
    //         },

    //         {
    //           bm: "园艺学院",

    //           name: "园艺学院"
    //         },

    //         {
    //           bm: "机电工程学院",

    //           name: "机电工程学院"
    //         },

    //         {
    //           bm: "植物保护学院",

    //           name: "植物保护学院"
    //         },

    //         {
    //           bm: "水产学院",

    //           name: "水产学院"
    //         },

    //         {
    //           bm: "水利与土木工程学院",

    //           name: "水利与土木工程学院"
    //         },

    //         {
    //           bm: "湖南机电职业技术学院",

    //           name: "湖南机电职业技术学院"
    //         },

    //         {
    //           bm: "湖南生物机电职业技术学院",

    //           name: "湖南生物机电职业技术学院"
    //         },

    //         {
    //           bm: "生物科学技术学院",

    //           name: "生物科学技术学院"
    //         },

    //         {
    //           bm: "经济学院",

    //           name: "经济学院"
    //         },

    //         {
    //           bm: "资源学院",

    //           name: "资源学院"
    //         },

    //         {
    //           bm: "资源环境学院",

    //           name: "资源环境学院"
    //         },

    //         {
    //           bm: "风景园林与艺术设计学院",

    //           name: "风景园林与艺术设计学院"
    //         },

    //         {
    //           bm: "马克思主义学院",

    //           name: "马克思主义学院"
    //         }
    //       ]
    //     },

    //     {
    //       id: 195,

    //       pid: 109,

    //       wdzd: "ZYMC",

    //       zdmc: "专业名称",

    //       lxbm: "ps",

    //       ttab: "ods_gxxs_bzksjbxx_x",

    //       wdzval: [""],

    //       wd: [
    //         {
    //           bm: "",

    //           name: "全部"
    //         },

    //         {
    //           bm: "公共事业管理",

    //           name: "公共事业管理"
    //         },

    //         {
    //           bm: "公共事业管理教育",

    //           name: "公共事业管理教育"
    //         },

    //         {
    //           bm: "农业机械化及其自动化教育",

    //           name: "农业机械化及其自动化教育"
    //         },

    //         {
    //           bm: "农林经济管理（旧）",

    //           name: "农林经济管理（旧）"
    //         },

    //         {
    //           bm: "动植物检疫教育",

    //           name: "动植物检疫教育"
    //         },

    //         {
    //           bm: "动植物检疫（旧）",

    //           name: "动植物检疫（旧）"
    //         },

    //         {
    //           bm: "动物科学（实验班）",

    //           name: "动物科学（实验班）"
    //         },

    //         {
    //           bm: "园林花卉",

    //           name: "园林花卉"
    //         },

    //         {
    //           bm: "园艺",

    //           name: "园艺"
    //         },

    //         {
    //           bm: "国际经济与贸易",

    //           name: "国际经济与贸易"
    //         },

    //         {
    //           bm: "国际经济与贸易（旧）",

    //           name: "国际经济与贸易（旧）"
    //         },

    //         {
    //           bm: "城镇建设",

    //           name: "城镇建设"
    //         },

    //         {
    //           bm: "工商管理（国际）",

    //           name: "工商管理（国际）"
    //         },

    //         {
    //           bm: "工商管理（旧）",

    //           name: "工商管理（旧）"
    //         },

    //         {
    //           bm: "市场营销教育",

    //           name: "市场营销教育"
    //         },

    //         {
    //           bm: "生态学",

    //           name: "生态学"
    //         },

    //         {
    //           bm: "生物工程（旧）",

    //           name: "生物工程（旧）"
    //         },

    //         {
    //           bm: "社会工作",

    //           name: "社会工作"
    //         },

    //         {
    //           bm: "计算机科学与技术",

    //           name: "计算机科学与技术"
    //         },

    //         {
    //           bm: "金融学（旧）",

    //           name: "金融学（旧）"
    //         }
    //       ]
    //     },

    //     {
    //       id: 196,

    //       pid: 109,

    //       wdzd: "XJZTMC",

    //       zdmc: "学籍状态",

    //       lxbm: "ps",

    //       ttab: "ods_gxxs_bzksjbxx_x",

    //       wdzval: [""],

    //       wd: [
    //         {
    //           bm: "",

    //           name: "全部"
    //         },

    //         {
    //           bm: "无学籍",

    //           name: "无学籍"
    //         },

    //         {
    //           bm: "有学籍",

    //           name: "有学籍"
    //         }
    //       ]
    //     }
    //   ],

    //   header: [
    //     {
    //       id: 194,

    //       lxbm: "ps",

    //       wdz: null,

    //       wdzd: "YXMC",

    //       zdmc: "院系名称"
    //     },

    //     {
    //       id: 195,

    //       lxbm: "ps",

    //       wdz: null,

    //       wdzd: "ZYMC",

    //       zdmc: "专业名称"
    //     },

    //     {
    //       id: 196,

    //       lxbm: "ps",

    //       wdz: null,

    //       wdzd: "XJZTMC",

    //       zdmc: "学籍状态"
    //     },
    //     {
    //       id: 196,

    //       lxbm: "ps",

    //       wdz: null,

    //       wdzd: "ZVAL",

    //       zdmc: "人数"
    //     }
    //   ],

    //   chart: [
    //     {
    //       wdx: "2024-02-28",

    //       wdz: 1002
    //     },

    //     {
    //       wdx: "2024-02-27",

    //       wdz: 1002
    //     }
    //   ],

    //   data: {
    //     totalCount: 290,

    //     pageSize: 10,

    //     totalPage: 29,

    //     currentPage: 1,

    //     list: [
    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "1",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "信息与智能科学技术学院",

    //         ZVAL: 368,

    //         ZYMC: "信息与计算科学"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "1",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "环境与生态学院",

    //         ZVAL: 191,

    //         ZYMC: "安全工程"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "1",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 0,

    //         JLDW: "人",

    //         TVMIN: 0,

    //         ZBID: 109,

    //         YXMC: "教育学院",

    //         ZVAL: 31,

    //         ZYMC: "园艺教育"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "0",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "风景园林与艺术设计学院",

    //         ZVAL: 213,

    //         ZYMC: "视觉传达设计"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "1",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "商学院",

    //         ZVAL: 1,

    //         ZYMC: "工商企业管理（new）"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "0",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "东方科技学院",

    //         ZVAL: 245,

    //         ZYMC: "电子商务(新)"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "0",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "农学院",

    //         ZVAL: 251,

    //         ZYMC: "烟草"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "0",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "园艺学院",

    //         ZVAL: 239,

    //         ZYMC: "中药资源与开发"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "1",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "东方科技学院",

    //         ZVAL: 333,

    //         ZYMC: "法学(新)"
    //       },

    //       {
    //         TSTAMP: "2024-02-28T13:20:06",

    //         YJZT: "0",

    //         LXBM: null,

    //         XJZTMC: "有学籍",

    //         TVMAX: 300,

    //         JLDW: "人",

    //         TVMIN: 200,

    //         ZBID: 109,

    //         YXMC: "生物科学技术学院",

    //         ZVAL: 289,

    //         ZYMC: "生物工程"
    //       }
    //     ]
    //   },

    //   glzb: [
    //     {
    //       id: 109,

    //       lxbm: "ps",

    //       zbmc: "2222"
    //     }
    //   ]
    // }
  }),
  "/getIndicatorTableColumn": () => ({
    code: 200,
    message: "OK",
    data: [
      { name: "ZGH", alias: "职工号" },
      { name: "XM", alias: "姓名" },
      { name: "XN", alias: "学年" },
      { name: "MXXMM", alias: "明细项目码" },
      { name: "XMJE", alias: "项目金额" },
      { name: "FGZNY", alias: "发工资年月" },
      { name: "BMDM", alias: "部门代码" },
      { name: "BMMC", alias: "部门名称" },
      { name: "RYLBDM", alias: "人员类别代码" },
      { name: "JCGZ", alias: "基础工资" },
      { name: "GWGZ", alias: "岗位工资" },
      { name: "QTGZ", alias: "其他工资" },
      { name: "YFS", alias: "应发数" },
      { name: "FZF", alias: "房租费" },
      { name: "SDF", alias: "水电费" },
      { name: "YLJ", alias: "养老金" },
      { name: "YB", alias: "医保" },
      { name: "GJJ", alias: "公积金" },
      { name: "SB", alias: "社保" },
      { name: "KK", alias: "扣款" },
      { name: "SDS", alias: "所得税" },
      { name: "SFS", alias: "实发数" },
      { name: "YHZH", alias: "银行账号" },
      { name: "GW", alias: "岗位" },
      { name: "PRXS", alias: "聘任形式" },
      { name: "SFZH", alias: "身份证号" },
      { name: "GWLXM", alias: "岗位类型码" },
      { name: "GWDJM", alias: "岗位等级码" },
      { name: "XB", alias: "性别" },
      { name: "QTSR", alias: "其他收入" },
      { name: "WYBT", alias: "物业补贴" },
      { name: "DSZNJL", alias: "独生子女奖励" },
      { name: "QT", alias: "其他" },
      { name: "JBTXF", alias: "基本（离）退休费" },
      { name: "QTBT", alias: "其他补贴" },
      { name: "GKK", alias: "共扣款" },
      { name: "TBSJ", alias: "同步时间" },
      { name: "JCJX", alias: "基础绩效" },
      { name: "ZYNJ", alias: "专业年金" },
      { name: "SBJ", alias: "失保金" },
      { name: "TSTAMP", alias: "时间戳" }
    ]
  }),
  "/getIndicatorTableData": () => ({
    code: 200,
    message: "OK",
    data: {
      totalCount: 4,
      pageSize: 10,
      totalPage: 1,
      currentPage: 1,
      list: [
        {
          GWLXM: null,
          DSZNJL: null,
          TSTAMP: null,
          GWGZ: null,
          GJJ: null,
          GWDJM: null,
          YB: null,
          MXXMM: null,
          BMDM: null,
          JCGZ: "7922.31",
          SB: null,
          ZYNJ: null,
          QTBT: null,
          SDF: null,
          YHZH: null,
          YLJ: null,
          PRXS: null,
          XMJE: null,
          SBJ: null,
          WYBT: null,
          KK: null,
          SFS: null,
          TBSJ: null,
          SDS: null,
          QT: null,
          QTSR: null,
          JCJX: null,
          GKK: null,
          YFS: null,
          SFZH: null,
          XB: null,
          QTGZ: null,
          GW: null,
          RYLBDM: null,
          FZF: null,
          XM: "李四",
          XN: "2021",
          FGZNY: null,
          JBTXF: null,
          ZGH: "10011",
          BMMC: "财务处"
        },
        {
          GWLXM: null,
          DSZNJL: null,
          TSTAMP: null,
          GWGZ: null,
          GJJ: null,
          GWDJM: null,
          YB: null,
          MXXMM: null,
          BMDM: null,
          JCGZ: "9872.11",
          SB: null,
          ZYNJ: null,
          QTBT: null,
          SDF: null,
          YHZH: null,
          YLJ: null,
          PRXS: null,
          XMJE: null,
          SBJ: null,
          WYBT: null,
          KK: null,
          SFS: null,
          TBSJ: null,
          SDS: null,
          QT: null,
          QTSR: null,
          JCJX: null,
          GKK: null,
          YFS: null,
          SFZH: null,
          XB: null,
          QTGZ: null,
          GW: null,
          RYLBDM: null,
          FZF: null,
          XM: "李四",
          XN: "2020",
          FGZNY: null,
          JBTXF: null,
          ZGH: "10011",
          BMMC: "财务处"
        },
        {
          GWLXM: null,
          DSZNJL: null,
          TSTAMP: null,
          GWGZ: null,
          GJJ: null,
          GWDJM: null,
          YB: null,
          MXXMM: null,
          BMDM: null,
          JCGZ: "7832.33",
          SB: null,
          ZYNJ: null,
          QTBT: null,
          SDF: null,
          YHZH: null,
          YLJ: null,
          PRXS: null,
          XMJE: null,
          SBJ: null,
          WYBT: null,
          KK: null,
          SFS: null,
          TBSJ: null,
          SDS: null,
          QT: null,
          QTSR: null,
          JCJX: null,
          GKK: null,
          YFS: null,
          SFZH: null,
          XB: null,
          QTGZ: null,
          GW: null,
          RYLBDM: null,
          FZF: null,
          XM: "张三",
          XN: "2021",
          FGZNY: null,
          JBTXF: null,
          ZGH: "10039",
          BMMC: "农学院"
        },
        {
          GWLXM: null,
          DSZNJL: null,
          TSTAMP: null,
          GWGZ: null,
          GJJ: null,
          GWDJM: null,
          YB: null,
          MXXMM: null,
          BMDM: null,
          JCGZ: "9833.22",
          SB: null,
          ZYNJ: null,
          QTBT: null,
          SDF: null,
          YHZH: null,
          YLJ: null,
          PRXS: null,
          XMJE: null,
          SBJ: null,
          WYBT: null,
          KK: null,
          SFS: null,
          TBSJ: null,
          SDS: null,
          QT: null,
          QTSR: null,
          JCJX: null,
          GKK: null,
          YFS: null,
          SFZH: null,
          XB: null,
          QTGZ: null,
          GW: null,
          RYLBDM: null,
          FZF: null,
          XM: "张三",
          XN: "2020",
          FGZNY: null,
          JBTXF: null,
          ZGH: "10039",
          BMMC: "农学院"
        }
      ]
    }
  })
}
