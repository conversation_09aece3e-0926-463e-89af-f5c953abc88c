<template>
  <div>
    <el-table :data="authTableData" ref="table">
      <el-table-column prop="roleName" label="角色" width="150">
      </el-table-column>
      <el-table-column label="权限变量值设置">
        <el-table-column
          v-for="(item, key) in authorVariable"
          :key="key"
        >
          <template slot="header">
            <el-checkbox
              :indeterminate="
                allCheckedCheckboxStatus &&
                  allCheckedCheckboxStatus[item.name] > 0 &&
                  allCheckedCheckboxStatus[item.name] != authTableData.length
              "
              :value="
                allCheckedCheckboxStatus &&
                  allCheckedCheckboxStatus[item.name] === authTableData.length
              "
              @change="viewRoleChangeAll(item.name, $event)"
            >
              {{ item.alias }}
            </el-checkbox>
          </template>
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.rowAuthConverted[item.name].enable">
            </el-checkbox>
            <template v-if="scope.row.rowAuthConverted[item.name].enable">
              <template
                v-if="
                  scope.row.rowAuthConverted[item.name].variable.valueType !=
                    'boolean'
                "
              >
                <el-tag
                  :key="tag"
                  v-for="tag in scope.row.rowAuthConverted[item.name].values"
                  closable
                  :disable-transitions="false"
                  @close="
                    handleClose(
                      scope.row.rowAuthConverted[item.name].values,
                      tag
                    )
                  "
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  class="input-new-tag"
                  v-if="
                    tempRoleId == scope.row.roleId + item.name &&
                      (scope.row.rowAuthConverted[item.name].variable
                        .valueType === 'string' ||
                        scope.row.rowAuthConverted[item.name].variable
                          .valueType === 'sql')
                  "
                  v-model="inputValue"
                  ref="saveTagInput"
                  size="small"
                  @keyup.enter.native="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                  @blur="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                >
                </el-input>
                <el-input-number
                  class="input-new-tag"
                  v-if="
                    tempRoleId == scope.row.roleId + item.name &&
                      scope.row.rowAuthConverted[item.name].variable.valueType ===
                      'number'
                  "
                  v-model="inputValue"
                  ref="saveTagInput"
                  size="small"
                  @keyup.enter.native="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                  @blur="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                ></el-input-number>
                <el-date-picker
                  class="input-new-tag"
                  type="date"
                  placeholder="选择日期"
                  v-if="
                    tempRoleId == scope.row.roleId + item.name &&
                      scope.row.rowAuthConverted[item.name].variable.valueType ===
                      'date'
                  "
                  v-model="inputValue"
                  ref="saveTagInput"
                  size="small"
                  value-format="yyyy-MM-dd"
                  @keyup.enter.native="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                  @blur="
                    handleInputConfirm(
                      scope.row.rowAuthConverted[item.name].values
                    )
                  "
                >
                </el-date-picker>
                <el-button
                  v-else
                  class="button-new-tag"
                  size="small"
                  @click="showInput(scope.row.roleId, item.name)"
                >
                  添加
                </el-button
                >
              </template>
              <template v-else>
                <el-switch
                  active-color="#13ce66"
                  inactive-color="#5f6368"
                  v-model="scope.row.rowAuthConverted[item.name].values[0]"
                >
                </el-switch>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="可见字段">
        <template slot-scope="scope">
          <el-button
            type="success"
            size="mini"
            v-if="scope.row.columnAuth.length == 0"
            @click="toggleSelection(scope.row)"
          >
            全部可见
          </el-button
          >
          <el-button
            type="primary"
            size="mini"
            v-if="scope.row.columnAuth.length != 0"
            @click="toggleSelection(scope.row)"
          >
            部分可见
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="勾选可见字段"
      v-show="dialogVisible"
      :show-close="true"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="360px"
      style="text-align: center"
    >
      <el-table
        ref="multipleTable"
        :data="allColumns"
        @selection-change="selectionChangeHandle"
        tooltip-effect="dark"
      >
        <el-table-column type="selection">
          <template slot="header" > 全选 </template>
        </el-table-column>
        <el-table-column label="全选" prop="name" width="250px">
        </el-table-column>
      </el-table>
      <el-button @click="saveChange()">保存</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </el-dialog>
  </div>
</template>

<script>
import Request from "@/service"
export default {
  name: "auth-pane",
  props: {
    allColumns: {
      type: Array,
      default: () => [],
    },
    authorVariable: {
      type: Array,
      default: () => [],
    },
    roles: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tempRoleId: "",
      inputValue: "",
      roleList: [],
      allVisible: true,
      dialogVisible: false,
      authTableData: [],
      dataListSelections: [],
      editingRole: 1,
      authDatasourceMap: null,
    }
  },
  computed: {
    allCheckedCheckboxStatus() {
      return this.authTableData.reduce((pre, next) => {
        for (let key in next.rowAuthConverted) {
          if (next.rowAuthConverted[key].enable) {
            if (pre[key]) {
              pre[key]++
            } else {
              this.$set(pre, key, 1)
            }
          }
        }
        return pre
      }, {})
    },
  },
  watch: {
    authTableData: {
      deep: true,
      handler() {
        this.changeRoles()
      },
    },
  },
  created() {
    this.getDtRoles()
  },
  methods: {
    // 查询dt 角色
    getDtRoles() {
      Request.view
        .getDtRoles()
        .then((res) => {
          this.roleList = res.data
          this.getAuthDatasource(
            this.roleList,
            this.authorVariable,
            this.roles
          )
        })
        .catch(() => {})
    },
    getAuthDatasource(roles, varibles, viewRoles) {
      if (!Array.isArray(roles)) {
        return []
      }
      const authDatasourceMap = new Map()
      const authDatasource = roles.map((role) => {
        const { roleId, roleName, roleTitle: roleDesc } = role
        const viewRole = viewRoles.find((v) => v.roleId === roleId)
        const columnAuth = viewRole ? viewRole.columnAuth : []

        const rowAuthConverted = varibles.reduce((obj, variable) => {
          const { name: variableName,  } = variable
          const authIdx = viewRole?.rowAuth
            ? viewRole.rowAuth.findIndex((auth) => auth.name === variableName)
            : -1
          obj[variableName] = {
            name: variableName,
            values: [],
            enable: false,
            variable,
          }
          if (authIdx >= 0) {
            const { enable, values } = viewRole.rowAuth[authIdx]
            obj[variableName] = {
              ...obj[variableName],
              enable,
              values,
            }
          }
          return obj
        }, {})
        const authDatasourceItem = {
          roleId,
          roleName,
          roleDesc,
          columnAuth,
          rowAuthConverted,
        }
        authDatasourceMap.set(roleId, authDatasourceItem)
        return authDatasourceItem
      })
      this.authDatasourceMap = authDatasourceMap
      this.authTableData = authDatasource
    },
    viewRoleChangeAll(checkedVariableName, checked) {
      this.authTableData.forEach(({ rowAuthConverted }) => {
        rowAuthConverted[checkedVariableName].enable = checked
      })
    },
    // 权限字段
    toggleSelection(row) {
      this.editingRole = row.roleId
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection()
        if (row.columnAuth.length === 0) {
          this.$refs.multipleTable.toggleAllSelection()
        } else {
          this.allColumns.forEach((col) => {
            if (row.columnAuth.indexOf(col.name) < 0) {
              this.$refs.multipleTable.toggleRowSelection(col)
            }
          })
        }
      })
    },
    saveChange() {
      let newArr = this.allColumns
        .filter((v) =>
          this.dataListSelections.every((val) => val.name !== v.name)
        )
        .map((item) => item.name)
      const authIdx = this.authTableData.findIndex(
        (item) => item.roleId === this.editingRole
      )
      this.authTableData[authIdx].columnAuth = newArr
      this.dialogVisible = false
    },
    changeRoles() {
      const roles = this.authTableData.map((item) => ({
        columnAuth: item.columnAuth,
        roleId: item.roleId,
        item,
        rowAuth: Object.values(item.rowAuthConverted),
      }))
      this.$emit("update:roles", roles)
    },
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    handleClose(arr, tag) {
      arr.splice(arr.indexOf(tag), 1)
    },

    showInput(roleId, name) {
      this.tempRoleId = roleId + name
      // this.$nextTick((_) => {
      //   this.$refs.saveTagInput.$refs.input.focus();
      // });
    },

    handleInputConfirm(arr) {
      let inputValue = this.inputValue
      if (inputValue) {
        arr.push(inputValue)
      }
      this.tempRoleId = ""
      this.inputValue = ""
    },
  },
}
</script>
