<template>
  <div class="edit-wrap">
    <editor
      :value="content"
      @input="handleInput"
      @init="editorInit"
      :theme="aceConfig.selectTheme"
      :lang="aceConfig.selectLang"
      :options="aceConfig.options"
      width="100%"
      height="100%"
    />
  </div>
</template>

<script>
// 编辑器主题
const themes = ["xcode", "eclipse", "monokai", "cobalt"]
// 编辑器语言
const langs = ["java", "c_cpp", "javascript", "golang"]
// tabs
const tabs = [2, 4, 8]
// 字体大小
const fontSizes = [14, 15, 16, 17, 18, 19, 20, 21, 22]
// 编辑器选项
const options = {
  tabSize: 4, // tab默认大小
  showPrintMargin: 0, // 去除编辑器里的竖线
  fontSize: 14, // 字体大小
  highlightActiveLine: true, // 高亮配置
  enableBasicAutocompletion: true, // 启用基本自动完成
  enableSnippets: true, // 启用代码段
  enableLiveAutocompletion: true // 启用实时自动完成
}
export default {
  name: "code-edit",
  components: {
    editor: require("vue2-ace-editor")
  },
  model: {
    prop: "content",
    event: "change"
  },
  props: {
    content: String
  },
  data() {
    return {
      aceConfig: {
        langs,
        themes,
        tabs,
        fontSizes,
        options,
        selectTheme: "monokai",
        selectLang: "javascript",
        readOnly: false
      }
    }
  },
  methods: {
    handleInput(e) {
      this.$emit("change", e)
    },
    // 代码块初始化
    editorInit() {
      require("brace/ext/language_tools") // language extension prerequsite...
      require(`brace/mode/${this.aceConfig.selectLang}`) // 语言
      require(`brace/theme/${this.aceConfig.selectTheme}`) // 主题
    }
  }
}
</script>
<style>
.edit-wrap {
  height: 100%;
}
</style>
