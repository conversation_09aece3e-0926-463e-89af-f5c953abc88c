<template>
  <div class="step-bar">
    <!-- 步骤条 -->
    <div v-for="(step, index) in steps" :key="index" class="step">
      <div
        class="step-header"
        :class="{ active: index === currentStep, success: index < currentStep }"
      >
        <div v-if="index < currentStep" class="icon-success"></div>
        <div class="step-circle" v-else>
          <span>{{ index + 1 }}</span>
        </div>
        <div class="step-title">{{ step }}</div>
      </div>
      <!-- 步骤线 -->
      <div v-if="index < steps.length - 1" class="step-line"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "step-bar",
  props: {
    steps: {
      type: Array,
      default: () => ["步骤1", "步骤2", "步骤3"]
    },
    currentStep: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  methods: {
    nextStep() {
      if (this.currentStep < this.steps.length - 1) {
        this.currentStep++
      }
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    setCurrentStep(stepIndex) {
      if (stepIndex >= 0 && stepIndex < this.steps.length) {
        this.currentStep = stepIndex
      }
    }
  }
}
</script>

<style lang="scss">
.step-bar {
  width: 568px;
  margin: 0 auto;
  display: flex;
}

.step {
  flex: 1;
  display: flex;
  &:last-child {
    flex-basis: auto !important;
    flex-shrink: 0;
    flex-grow: 0;
  }
}

.step-header {
  width: 26px;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.active {
    .step-circle {
      background: #1563ff;
      border: 2px solid #1563ff;
      color: #fff;
    }
    .step-title {
      color: #1563ff;
    }
  }
  &.success {
    .step-title {
      color: #2f3338;
    }
  }
  .icon-success {
    width: 26px;
    height: 26px;
    background: url("~@/assets/images/step-success.png") no-repeat center;
  }
  .step-circle {
    width: 26px;
    height: 26px;
    border: 2px solid #c0c4cb;
    background: transparent;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ccc;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
  }
  .step-title {
    margin-top: 12px;
    color: #adb0b8;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    white-space: nowrap;
  }
}

.step-line {
  flex: 1;
  height: 1px;
  background-color: #dcdfe6;
  margin: 13px 16px 0;
}
</style>
