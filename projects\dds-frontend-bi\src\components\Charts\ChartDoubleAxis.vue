<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"

import { toThousands } from "@/utils/index.js"
import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // 左Y轴单位
    leftYAxisName: {
      type: String,
      default: ""
    },
    // 右Y轴单位
    rightYAxisName: {
      type: String,
      default: ""
    },
    // 系列配置
    seriesOptions: {
      type: Array,
      default: () => []
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 展示数量
    showNum: {
      type: Number || String,
      default: "auto"
    },
    // 滚动轴
    showDataZoom: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      let dataset = null

      this.seriesOptions.forEach(item => {
        series.push(
          this.createSeriesObject({
            type: item.type,
            name: item.name,
            color: item.color,
            yField: item.yField,
            yAxisIndex: item.yAxisIndex,
            isAreaStyle: item.isAreaStyle,
            isStack: item.isStack,
            barWidth: item.barWidth
          })
        )
      })

      dataset = {
        source: this.chartData
      }

      this.chart.setOption({
        dataset,
        tooltip: {
          trigger: "axis",
          formatter: params => {
            return `<div>
              <p class="tooltip-title">${params[0].name}</p>
              ${this.tooltipItemsHtmlString(params)}

            </div>`
          },
          className: "echarts-tooltip-diy"
        },
        dataZoom:
          this.showDataZoom && this.chartData.length > this.showNum
            ? [
                // 给x轴设置滚动条
                {
                  type: "slider",
                  show: true,
                  xAxisIndex: 0,
                  zoomLock: true,
                  height: 6,
                  bottom: 0,

                  showDetail: false,
                  start: 0,
                  endValue: this.showNum - 1,

                  showDataShadow: false,
                  fillerColor: "#DBDBDB", // 滑块的颜色

                  backgroundColor: "transparent", // 滑块轨道的颜色
                  borderColor: "transparent", // 滑块轨道边框的颜色
                  moveHandleIcon: "none",

                  zoomOnMouseWheel: false,
                  brushSelect: false,

                  handleIcon:
                    "M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z",
                  handleSize: "100%",
                  handleStyle: {
                    color: "#DBDBDB",
                    borderColor: "transparent"
                  }
                },
                // 下面这个属性是里面拖到
                {
                  type: "inside",
                  show: true,
                  xAxisIndex: 0,
                  zoomOnMouseWheel: false,
                  moveOnMouseMove: true,
                  moveOnMouseWheel: true
                }
              ]
            : [{ show: false }],
        // dataZoom: {
        //   show: true,
        //   realtime: true,
        //   type: 'inside',
        //   minValueSpan: this.end,
        //   maxValueSpan: this.end,
        //   start: 0,
        //   end: this.end,
        // },
        grid: {
          top: 40,
          bottom: this.showDataZoom ? 12 : 0,
          left: 0,
          right: 0,
          containLabel: true
        },
        legend: {
          show: true,
          left: "center",
          top: 0,
          itemGap: 20,
          itemWidth: 12,
          itemHeight: 8,

          orient: "horizontal",
          // color: this.color,
          textStyle: {
            color: "#646566",
            fontSize: 12
          },
          data: [
            ...this.seriesOptions.map(item => {
              return {
                name: item.name,
                icon:
                  item.type === "bar"
                    ? "path://M0,0H8V8H0Z"
                    : "path://M0,0H12V2H0Z"
              }
            })
          ]
        },
        graphic: [
          {
            type: "text",
            top: 5,
            left: 0,
            style: {
              text: this.leftYAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          },
          {
            type: "text",
            top: 5,
            right: 0,
            style: {
              text: this.rightYAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            splitLine: {
              lineStyle: {
                color: "#EBEDF0",
                type: "dashed"
              }
            },
            axisLabel: {
              fontSize: 12,
              color: "#646566",
              fontWeight: "400"
              // formatter: metricAxisLabelFormatter,
            }
          },
          {
            type: "value",
            splitLine: {
              show: false,
              lineStyle: {
                color: "#EBEDF0",
                type: "dashed"
              }
            },
            axisLabel: {
              fontSize: 12,
              color: "#646566",
              fontWeight: "400"
              // formatter: metricAxisLabelFormatter,
            }
          }
        ],

        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#EBEDF0"
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            lineHeight: 16,
            color: "#323233",
            fontWeight: "400",
            interval: 0,
            rotate: 0, // 倾斜角度
            formatter: this.isFormatterXAxis
              ? value => {
                  let startName = value.substring(0, 5)
                  let endName = value.substring(5)
                  if (endName.length > 5) {
                    return `${startName}\n${value.substring(5, 9)}...`
                  }
                  return `${startName}\n${endName}`
                }
              : value => value
          }
        },
        series
      })
    },
    // 创建系列对象
    createSeriesObject({
      type,
      name,
      color,
      yField,
      yAxisIndex,
      isAreaStyle,
      isStack,
      barWidth
    }) {
      return {
        type,
        name,
        yAxisIndex,
        barWidth: barWidth ? barWidth : 16,
        stack: isStack ? "all" : null,
        itemStyle: {
          color: color
        },
        encode: {
          x: this.xField,
          y: yField
        },

        datasetIndex: 0,
        areaStyle: isAreaStyle
          ? {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: color + "1A" // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: color + "00" // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          : null
      }
    },
    tooltipItemsHtmlString(items) {
      return items
        .map(
          el => `<div class="content-panel">
        <p >


          <span style="background-color: ${el.color}"     ${
            this.seriesOptions[el.seriesIndex].type === "bar"
              ? `class="tooltip-item-bar-icon"`
              : `class="tooltip-item-line-icon"`
          }


          ></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value[el.dimensionNames[el.encode.y[0]]])}${
            this.seriesOptions[el.seriesIndex].unit
          }
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-line-icon {
    display: inline-block;
    margin-right: 8px;
    width: 12px;
    height: 2px;
  }
  .tooltip-item-bar-icon {
    display: inline-block;
    margin-right: 12px;
    width: 8px;
    height: 8px;
  }
}
</style>
