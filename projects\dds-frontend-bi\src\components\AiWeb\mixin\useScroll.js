export default {
  methods: {
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.scrollRef) {
          this.$refs.scrollRef.scrollTop = this.$refs.scrollRef.scrollHeight
        }
      })
    },
    scrollToTop() {
      this.$nextTick(() => {
        if (this.$refs.scrollRef) {
          this.$refs.scrollRef.scrollTop = 0
        }
      })
    },
    scrollToBottomIfAtBottom() {
      this.$nextTick(() => {
        if (this.$refs.scrollRef) {
          const threshold = 50 // 阈值，表示滚动条到底部的距离阈值
          const distanceToBottom =
            this.$refs.scrollRef.scrollHeight -
            this.$refs.scrollRef.scrollTop -
            this.$refs.scrollRef.clientHeight
          if (distanceToBottom <= threshold)
            this.$refs.scrollRef.scrollTop = this.$refs.scrollRef.scrollHeight
        }
      })
    }
  }
}
