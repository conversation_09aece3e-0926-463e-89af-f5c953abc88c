<template>
  <div>
    <template v-if="widgetList.length">
      <p class="result">{{ widgetList.length }}条相关结果</p>
      <div class="list">
        <div
          class="list-item-custom"
          v-for="(item, i) in widgetList.slice(
            (currentPage - 1) * 10,
            currentPage * 10
          )"
          :key="i"
          @click="handleGo(item)"
        >
          <div
            class="title"
            v-html="brightenKeyword(item.name, searchContent)"
          ></div>
          <div
            class="desc"
            v-html="brightenKeyword(item.info, searchContent)"
          ></div>
          <!-- <div class="type">
            {{ item.router ? "大屏驾驶舱" : "仪表盘看板" }}
          </div> -->
        </div>
      </div>
      <div class="page">
        <el-pagination
          v-if="widgetList.length > 10"
          small
          layout="prev, pager, next"
          :total="widgetList.length"
          :page-size="10"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </template>
    <Empty v-else />
  </div>
</template>

<script>
import Request from "@/service"
import { brightenKeyword } from "@/utils"
import Empty from "../Empty"
export default {
  components: { Empty },
  props: {
    widgetList: {
      type: Array,
      default: () => []
    },
    searchContent: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      listData: [],
      currentPage: 1,
      themes: []
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getAllThemes()
  },
  watch: {},
  methods: {
    brightenKeyword,
    handleGo(item) {
      if (item.theme === "no") return this.$message.warning("暂无应用主题")
      this.themes.filter(theme => {
        if (theme.value === item.theme) {
          window.open(
            `/ddsBi/viewDashboards/Portalview/${item.dashboardId}?isFullPage=true&theme=ykt&HighLight=true&widgetId=${item.widgetId}&redirect=/ddsBi/${theme.router}?theme=${item.theme}`,
            "_blank"
          )
        }
      })
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 初始化表格数据
    getAllThemes() {
      this.loading = true
      Request.dashboard
        .getAllThemes()
        .then(res => {
          this.themes = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.result {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
  margin-bottom: 16px;
}
.list {
  .list-item-custom {
    margin-bottom: 24px;
    .title {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 28px;
      color: #323233;
      cursor: pointer;
    }
    .desc {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #86909c;
      line-height: 22px;
      margin: 8px 0;
    }
    .type {
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
      line-height: 20px;
    }
  }
}
.page {
  display: flex;
}
</style>
