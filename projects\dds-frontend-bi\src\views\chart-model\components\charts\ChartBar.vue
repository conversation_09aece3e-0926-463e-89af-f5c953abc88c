<template>
  <div class="chart-bar" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartBar',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    xField: {
      type: String,
      default: 'value'
    },
    yField: {
      type: String,
      default: 'name'
    },
    seriesName: {
      type: String,
      default: '数据'
    },
    // 样式配置
    color: {
      type: [String, Array],
      default: '#52c41a'
    },
    colors: {
      type: Array,
      default: () => ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1']
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      // 判断使用哪种数据格式
      const useNewFormat = this.categories.length > 0 && this.series.length > 0

      let yData, seriesData, legendData

      if (useNewFormat) {
        // 新格式：categories + series
        yData = this.categories
        seriesData = this.series
        legendData = this.series.map(item => item.name)
      } else {
        // 旧格式：chartData + xField + yField
        yData = this.chartData.map(item => item[this.yField])
        const xData = this.chartData.map(item => item[this.xField])
        seriesData = [{
          name: this.seriesName,
          data: xData
        }]
        legendData = [this.seriesName]
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: this.showTooltip,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          show: this.showLegend && legendData.length > 1,
          data: legendData,
          top: 10,
          right: 20
        },
        grid: {
          show: this.showGrid,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
          borderColor: '#e6e6e6'
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: yData,
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {
            lineStyle: {
              color: '#d9d9d9'
            }
          },
          axisLabel: {
            color: '#666666',
            fontSize: 12
          }
        },
        series: seriesData.map((seriesItem, seriesIndex) => ({
          name: seriesItem.name,
          type: 'bar',
          data: seriesItem.data,
          itemStyle: {
            color: useNewFormat ?
              this.colors[seriesIndex % this.colors.length] :
              (Array.isArray(this.color) ?
                (params) => this.colors[params.dataIndex % this.colors.length] :
                this.color),
            borderRadius: [0, 4, 4, 0]
          },
          label: {
            show: this.showLabel,
            position: 'right',
            color: '#666666',
            fontSize: 12
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          animationDelay: (idx) => idx * 100 + seriesIndex * 200
        })),
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-bar {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
