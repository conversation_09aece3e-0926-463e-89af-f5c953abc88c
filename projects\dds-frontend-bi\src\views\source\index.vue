<template>
  <div>
    <list ref="list" v-show="'list' === com" @click="child_click" />
    <edit v-if="'edit' === com" @click="child_click" :id="editId" />
    <info v-if="'info' === com" @click="child_click" :id="editId" />
  </div>
</template>

<script>
import edit from './component/edit'
import list from './component/list'
import info from './component/info'

export default {
  name: 'index',
  components: { edit, list, info },
  data() {
    return {
      editId: '',
      com: 'list',
      name: ''
    }
  },
  methods: {
    child_click(val) {
      // this.show = false;
      // {com:'组件',opt:'操作',data:数据}
      console.log('99999999')
      this.com = val.com
      if (val.opt === 'list' || val.opt === 'back') {
        this.$refs.list.initData()
      } else {
        this.editId = val.data.id
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
