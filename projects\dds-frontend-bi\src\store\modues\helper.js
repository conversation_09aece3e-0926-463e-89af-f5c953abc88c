import { ss } from "@/utils/storage"

const LOCAL_NAME = "chatStorage"

export function defaultState() {
  const uuid = Date.now()
  return {
    active: uuid,
    history: [{ uuid, title: "New Chat", isEdit: false }],
    chat: [{ uuid, data: [] }],
    network: true
  }
}

export function getLocalState() {
  const localState = ss.get(LOCAL_NAME)
  if (localState && localState.network === undefined) {
    localState.network = true
  }
  return localState ?? defaultState()
}

export function setLocalState(state) {
  ss.set(LOCAL_NAME, state)
}
