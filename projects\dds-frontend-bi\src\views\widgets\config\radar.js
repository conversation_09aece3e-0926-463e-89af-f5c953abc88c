import ChartTypes from './ChartTypes'
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
} from '@/globalConstants'

const radar = {
  id: ChartTypes.Radar,
  name: 'radar',
  title: '雷达图',
  icon: 'chart_radar',
  coordinate: 'cartesian',
  rules: [ { dimension: 1, metric: [ 1, 9999 ] }, { dimension: 0, metric: [ 3, 9999 ] } ],
  dimetionAxis: 'col',
  data: [
    {
      title: '维度',
      type: 'category',
      name: "cols"

    },
    {
      title: '指标',
      type: 'value',
      name: "metrics"
    },
    {
      title: '筛选',
      type: 'all',
      name: "filters"
    }
    // {
    //   title: '颜色',
    //   type: 'category',
    //   name: "color"
    // },

  ],
  style: {
    label: {
      showLabel: true,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: '12',
      labelColor: PIVOT_DEFAULT_FONT_COLOR,
      labelParts: [ 'indicatorName', 'indicatorValue' ]
    },
    legend: {
      showLegend: true,
      legendPosition: 'right',
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: '12',
      color: PIVOT_DEFAULT_FONT_COLOR
    },
    radar: {
      shape: 'polygon', // 'polygon' | 'circle'
      axisName: {
        show: true,
        fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
        fontSize: '12',
        color: PIVOT_DEFAULT_FONT_COLOR
      },
      axisNameGap: 15,
      splitNumber: 5
    },
    spec: {}
    // toolbox: {
    //   showToolbox: false
    // }
  }
}

export default radar
