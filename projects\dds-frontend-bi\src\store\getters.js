import _ from "lodash"
const getters = {
  isCollapse: state => state.isCollapse,
  dimensions: state => state.chart.dimensions,
  widgets: state => state.widget.widgets,
  layerActive: state => {
    const currentLayer = state.display.slideLayers.find(item => item.active)
    if (currentLayer) {
      const layer = _.cloneDeep(currentLayer)
      return layer
    }
    return null
  },
  AllMetric: state => [...state.chart.metric, ...state.chart.secondaryMetrics],
  mergeData: state => state.chart.mergeData,

  currentTheme: state => state.settings.themeObj[state.settings.theme],
  screenStyle: state => {
    return {
      width: state.display.currentSlide.config.slideParams.width + "px",
      height: state.display.currentSlide.config.slideParams.height + "px",
      background: `url(${state.display.currentSlide.config.slideParams.backgroundImage}) no-repeat center `,
      backgroundSize: "cover",
      transform: "scale(1) translate(1.6%, 2.9%)",
      backgroundColor:
        state.display.currentSlide.config.slideParams.backgroundColor
    }
  },
  selectCurrentLayers: state =>
    state.display.currentDisplay ? state.display.slideLayers : [],

  makeSelectCurrentLayersMaxIndex: (state, getters) =>
    getters.makeSelectCurrentLayerList.length
      ? getters.makeSelectCurrentLayerList[0].index
      : 0,

  // ###########################################
  displayInfo: state => state.display.displayInfo,
  displaySlides: state => state.display.displaySlides,
  currentSlide: state => state.display.currentSlide,
  currentSlideId: state => state.display.currentSlide.id,
  slideLayers: state => state.display.slideLayers,
  currentLayer: state => state.display.currentLayer,
  currentLayerId: state => state.display.currentLayer.id,
  layersIndexRankList: (state, getters) =>
    getters.slideLayers
      ? getters.slideLayers.sort((l1, l2) => l2.index - l1.index)
      : [],
  maxLayerIndex: (state, getters) =>
    getters.layersIndexRankList.length
      ? getters.layersIndexRankList[0].index
      : 0,
  currentLayerWidget: (state, getters) =>
    getters.widgets.find(item => item.id === getters.currentLayer.widgetId)
}
export default getters
