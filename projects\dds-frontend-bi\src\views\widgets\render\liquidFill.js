export default function(chartData) {
  const { data,  metrics, } = chartData
  const key = `${metrics[0].agg}(${metrics[0].displayName})`
  const value = data[0][key]/100
  console.log('%cliquidFill.js line:5 value', 'color: #26bfa5;', data[0],key)
  return {
    series: [ {
      type: 'liquidFill',
      radius: '80%',
      data: [ value ],
      color: 'rgba(67,209,100,1)',
      label: {
        normal: {
          color: '#fff',
          insideColor: 'transparent',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            fontFamily: 'Microsoft YaHei'
          }
        }
      },
      outline: {
        show: true,
        borderDistance: 5,
        itemStyle: {
          borderColor: 'rgba(67,209,100,1)',
          borderWidth: 2
        }
      },
      backgroundStyle: {
        color: 'rgba(67,209,100,.3)'
      }
    } ]
   
  }
}
