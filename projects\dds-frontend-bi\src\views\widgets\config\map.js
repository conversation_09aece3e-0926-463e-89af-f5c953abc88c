import ChartTypes from "./ChartTypes"
import {
  PIVOT_CHART_FONT_FAMILIES,
  PIVOT_DEFAULT_FONT_COLOR,
  CHART_LABEL_POSITIONS,
  CHART_VISUALMAP_POSITIONS,
  CHART_LEGEND_POSITIONS,
  CHART_LINES_SYMBOL_TYPE
} from "@/globalConstants"
export default {
  id: ChartTypes.Map,
  name: "map",
  title: "地图",
  icon: "map",
  coordinate: "cartesian",
  rules: [{ dimension: 1, metric: 1 }],
  dimetionAxis: "col",
  data: [
    {
      title: "维度",
      type: "category",
      name: "cols"
    },
    {
      title: "指标",
      type: "value",
      name: "metrics"
    },
    {
      title: "筛选",
      type: "all",
      name: "filters"
    }
  ],
  style: {
    label: {
      showLabel: false,
      labelPosition: CHART_LABEL_POSITIONS[0].value,
      labelFontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      labelFontSize: "12",
      labelColor: PIVOT_DEFAULT_FONT_COLOR
    },
    visualMap: {
      showVisualMap: true,
      visualMapPosition: CHART_VISUALMAP_POSITIONS[0].value,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: "12",
      visualMapDirection: "vertical",
      visualMapWidth: 20,
      visualMapHeight: 150,
      fontColor: PIVOT_DEFAULT_FONT_COLOR,
      startColor: "#E7F4FF",
      endColor: "#1E41A3"
    },
    legend: {
      showLegend: true,
      legendPosition: CHART_LEGEND_POSITIONS[0].value,
      selectAll: true,
      fontFamily: PIVOT_CHART_FONT_FAMILIES[0].value,
      fontSize: "12",
      color: PIVOT_DEFAULT_FONT_COLOR
    },
    spec: {
      layerType: "map",
      roam: false,
      symbolType: CHART_LINES_SYMBOL_TYPE[0].value,
      linesSpeed: "10",
      isShowBorder: true,
      borderColor: "#000000",
      areaCode: "100000",
      areaColor: "#f0f2f5",
      isHighlight: true,
      highlightLabelColor: "#222222",
      highlightareaColor: "#AFD1FD",
      highlightborderColor: "#5B8FF9"
    }
  }
}
