<template>
  <el-select
    size="mini"
    multiple
    filterable
    :disabled="disabled"
    v-model="selectedArr"
    :loading="mulSelectLoading"
    :collapse-tags="collapseTags"
    placeholder="请选择"
    :value-key="valueKey"
    @change="changeSelect"
    @remove-tag="removeTag"
  >
    <el-option
      label="全部"
      value="全部"
      @click.native="selectAll"
      v-if="selectOptions.length"
    ></el-option>
    <el-option
      v-for="item in selectOptions"
      :key="item[props.value]"
      :label="item[props.label]"
      :value="valueKey ? item : item[props.value]"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: "MultipleSelect",
  props: {
    // 选项
    selectOptions: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 已选中选项
    value: {
      type: Array,
      default() {
        return []
      }
    },
    valueKey: {
      type: String,
      default: ""
    },
    props: {
      type: Object,
      default() {
        return {
          label: "label",
          value: "value"
        }
      }
    },
    mulSelectLoading: {
      type: <PERSON><PERSON>an,
      default: false
    },
    collapseTags: {
      type: <PERSON>olean,
      default: true
    },
   
  },
  data() {
    return {
      selectedArr: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedArr = newVal
        if (
          !this.selectedArr.includes("全部") &&
          this.selectedArr.length &&
          this.selectedArr.length === this.selectOptions.length
        ) {
          this.selectedArr.unshift("全部")
        }
      },
      immediate: true
    }
  },
  methods: {
    selectAll() {
      if (this.selectedArr.length < this.selectOptions.length) {
        this.selectedArr = []
        this.selectOptions.map(item => {
          this.selectedArr.push(this.valueKey ? item : item[this.props.value])
        })
        this.selectedArr.unshift("全部")
      } else {
        // 取消全选
        this.selectedArr = []
      }
      this.$emit("input", this.selectedArr)
      this.$emit("change", this.selectedArr)

      this.$emit("update:updateMultipleSelect", this.selectedArr)
    },
    changeSelect(val) {
      console.log(val, "/////////////////////")
      if (!val.includes("全部") && val.length === this.selectOptions.length) {
        this.selectedArr.unshift("全部")
      } else if (
        val.includes("全部") &&
        val.length - 1 < this.selectOptions.length
      ) {
        console.log(this.selectedArr, "this.selectedArr")

        this.selectedArr = this.selectedArr.filter(item => {
          return item !== "全部"
        })
      }
      this.$emit("input", this.selectedArr)
      this.$emit("change", this.selectedArr)

      this.$emit("update:updateMultipleSelect", this.selectedArr)
    },
    removeTag(val) {
      if (val === "全部") {
        this.selectedArr = []
        this.$emit("input", this.selectedArr)
        this.$emit("change", this.selectedArr)
        this.$emit("update:updateMultipleSelect", this.selectedArr)
      }
    }
  }
}
</script>
