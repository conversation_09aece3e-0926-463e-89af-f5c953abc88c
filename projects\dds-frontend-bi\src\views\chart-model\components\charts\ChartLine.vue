<template>
  <div class="chart-line" :style="{ width, height }">
    <div ref="chartContainer" class="chart-container"></div>
    <ChartEmpty v-if="!categories.length||!series.length" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import ChartEmpty from "@/components/Charts/ChartEmpty.vue"

export default {
  name: 'ChartLine',
  components: {
    ChartEmpty
  },
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 新格式数据支持
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    // 字段配置
    xField: {
      type: String,
      default: 'name'
    },
    yField: {
      type: String,
      default: 'value'
    },
    seriesName: {
      type: String,
      default: '数据'
    },
    // 样式配置
    color: {
      type: [String, Array],
      default: '#722ed1'
    },
    colors: {
      type: Array,
      default: () => ['#722ed1', '#1890ff', '#52c41a', '#faad14', '#f5222d']
    },
    // 线条配置
    smooth: {
      type: Boolean,
      default: false
    },
    showPoint: {
      type: Boolean,
      default: true
    },
    pointSize: {
      type: Number,
      default: 4
    },
    lineWidth: {
      type: Number,
      default: 2
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: false
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 区域填充
    showArea: {
      type: Boolean,
      default: false
    },
    areaOpacity: {
      type: Number,
      default: 0.3
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    // 自定义配置
    customOption: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    chartOption() {
      // 判断使用哪种数据格式
      const useNewFormat = this.categories.length > 0 && this.series.length > 0

      let xData, seriesData, legendData

      if (useNewFormat) {
        // 新格式：categories + series
        xData = this.categories
        seriesData = this.series
        legendData = this.series.map(item => item.name)
      } else {
        // 旧格式：chartData + xField + yField
        xData = this.chartData.map(item => item[this.xField])
        const yData = this.chartData.map(item => item[this.yField])
        seriesData = [{
          name: this.seriesName,
          data: yData
        }]
        legendData = [this.seriesName]
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: this.showTooltip,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          show: this.showLegend && legendData.length > 1,
          data: legendData,
          top: 10,
          right: 20
        },
        grid: {
          show: this.showGrid,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
          borderColor: '#e6e6e6'
        },
        xAxis: {
          type: 'category',
          data: xData,
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#d9d9d9'
            }
          },
          axisLabel: {
            color: '#666666',
            fontSize: 12
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        series: seriesData.map((seriesItem, seriesIndex) => ({
          name: seriesItem.name,
          type: 'line',
          data: seriesItem.data,
          smooth: this.smooth,
          symbol: this.showPoint ? 'circle' : 'none',
          symbolSize: this.pointSize,
          lineStyle: {
            color: useNewFormat ?
              this.colors[seriesIndex % this.colors.length] :
              (Array.isArray(this.color) ? this.color[0] : this.color),
            width: this.lineWidth
          },
          itemStyle: {
            color: useNewFormat ?
              this.colors[seriesIndex % this.colors.length] :
              (Array.isArray(this.color) ? this.color[0] : this.color)
          },
          areaStyle: this.showArea ? {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: useNewFormat ?
                  this.hexToRgba(this.colors[seriesIndex % this.colors.length], this.areaOpacity) :
                  (Array.isArray(this.color) ?
                    this.hexToRgba(this.color[0], this.areaOpacity) :
                    this.hexToRgba(this.color, this.areaOpacity))
              }, {
                offset: 1,
                color: useNewFormat ?
                  this.hexToRgba(this.colors[seriesIndex % this.colors.length], 0) :
                  (Array.isArray(this.color) ?
                    this.hexToRgba(this.color[0], 0) :
                    this.hexToRgba(this.color, 0))
              }]
            }
          } : null,
          label: {
            show: this.showLabel,
            position: 'top',
            color: '#666666',
            fontSize: 12
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          animationDelay: (idx) => idx * 50 + seriesIndex * 200
        })),
        animation: this.animation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 合并自定义配置
      return this.mergeOption(option, this.customOption)
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartOption: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 绑定点击事件
      this.chart.on('click', (params) => {
        this.$emit('chart-click', params)
      })
      
      // 绑定双击事件
      this.chart.on('dblclick', (params) => {
        this.$emit('chart-dblclick', params)
      })
      
      // 绑定鼠标悬停事件
      this.chart.on('mouseover', (params) => {
        this.$emit('chart-mouseover', params)
      })
      
      // 绑定鼠标离开事件
      this.chart.on('mouseout', (params) => {
        this.$emit('chart-mouseout', params)
      })
    },
    updateChart() {
      if (!this.chart) return
      
      this.chart.setOption(this.chartOption, true)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    hexToRgba(hex, alpha) {
      const r = parseInt(hex.slice(1, 3), 16)
      const g = parseInt(hex.slice(3, 5), 16)
      const b = parseInt(hex.slice(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    },
    mergeOption(target, source) {
      if (!source || typeof source !== 'object') return target
      
      const result = { ...target }
      
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeOption(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      })
      
      return result
    },
    // 公共方法
    getChart() {
      return this.chart
    },
    getOption() {
      return this.chart ? this.chart.getOption() : null
    },
    getDataURL(opts) {
      return this.chart ? this.chart.getDataURL(opts) : null
    },
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }
}
</script>

<style scoped>
.chart-line {
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
