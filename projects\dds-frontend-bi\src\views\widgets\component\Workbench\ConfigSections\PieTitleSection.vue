<template>
  <div class="paneBlock">
    <h4>标题</h4>
    <div class="blockBody">
      <el-row
        gutter="8"
        type="flex"
        align="middle"
        class="blockRow">
        <el-col span="10">
          <el-checkbox v-model="titleForm.show" @change="changeTitleStyle">
            显示标题
          </el-checkbox>
        </el-col>
      </el-row>
      <template v-if="titleForm.show">
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="24">
            <el-input
              v-model="titleForm.subtext"
              size="mini"
              placeholder="请输入副标题"
              clearable
              @blur="changeTitleStyle"
              @input="inputOnInput($event)"
            />
          </el-col>
        </el-row>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="8">标题字体</el-col>
          <el-col span="10">
            <el-select
              placeholder="请选择"
              @change="changeTitleStyle"
              v-model="titleForm.textStyle.fontSize"
              size="mini"
            >
              <el-option
                v-for="item in PIVOT_CHART_FONT_SIZES"
                :key="item.value"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row
          gutter="8"
          type="flex"
          align="middle"
          class="blockRow">
          <el-col span="8">子标题字体</el-col>
          <el-col span="10">
            <el-select
              placeholder="请选择"
              @change="changeTitleStyle"
              v-model="titleForm.subtextStyle.fontSize"
              size="mini"
            >
              <el-option
                v-for="item in PIVOT_CHART_FONT_SIZES"
                :key="item.value"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <div class="blockBody" style="margin-bottom: 20px">
          <el-row class="blockRow">
            <el-col span="24">左右边距</el-col>
            <el-col span="14">
              <el-slider
                style="width: 100%"
                v-model="titleForm.x"
                :format-tooltip="(val) => val + '%'"
                @change="changeTitleStyle"
              ></el-slider>
            </el-col>
            <el-col span="10">
              <el-input-number
                min="0"
                max="100"
                controls-position="right"
                placeholder=""
                v-model="titleForm.x"
                @change="changeTitleStyle"
              ></el-input-number>
            </el-col>
          </el-row>
          <el-row class="blockRow">
            <el-col span="24">上下边距</el-col>
            <el-col span="14">
              <el-slider
                style="width: 100%"
                v-model="titleForm.y"
                @change="changeTitleStyle"
                :format-tooltip="(val) => val + '%'"
              ></el-slider>
            </el-col>
            <el-col span="10">
              <el-input-number
                min="0"
                max="100"
                controls-position="right"
                placeholder=""
                v-model="titleForm.y"
                @change="changeTitleStyle"
              ></el-input-number>
            </el-col>
          </el-row>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { PIVOT_CHART_FONT_SIZES } from "@/globalConstants"
export default {
  name: "legend-selector",
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      titleForm: {},
      PIVOT_CHART_FONT_SIZES,
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,

      handler: function() {
        this.init()
      },
    },
  },
  mounted() {},
  methods: {
    init() {
      if (this.chartData.chartStyles.title) {
        this.titleForm = this._.cloneDeep(this.chartData.chartStyles.title)
      }
    },
    changeTitleStyle() {
      this.$emit("changeStyle", "title", this.titleForm)
    },
    inputOnInput: function() {
      this.$forceUpdate()
    },
  },
}
</script>

<style scoped lang="scss">
@import "../Workbench.scss";
</style>
