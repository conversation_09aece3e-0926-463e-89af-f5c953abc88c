<template>
  <el-dialog
    title="新增widget"
    :visible.sync="addWidgetVisible"
    width="680px"
    @close="closeDialog"
    :before-close="handleClose"
  >
    <el-steps :active="active">
      <el-step title="Widget"></el-step>
      <el-step title="数据更新"></el-step>
      <el-step title="完成"></el-step>
    </el-steps>
    <div v-if="active == 0" style="width:100%">
      <el-table
        :data="data"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          prop="name"
          show-overflow-tooltip
          label="名称"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="description"
          show-overflow-tooltip
          label="描述"
          align="center"
        >
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <dt-pagination
        :hidden="pagination.total == 0"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :page-sizes="[6, 20, 50, 100]"
        :current-page="pagination.currentPage"
        :pager-count="3"
        @sizeChange="sizeChange"
        @currentChange="currentChange"
      />
    </div>
    <div class="dataUpdate" v-else>
      <el-form label-width="120px" :model="params" :rules="rules">
        <el-form-item label="数据刷新模式">
          <el-select v-model="params.polling" placeholder="请选择">
            <el-option label="手动刷新" :value="false"> </el-option>
            <el-option label="定时刷新" :value="true"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时长" v-if="params.polling" prop="frequency">
          <el-input-number
            v-model="params.frequency"
            :min="1"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        :disabled="isDisabledNext"
        v-if="active == 0"
        @click="active = 1"
      >下一步</el-button
      >
      <el-button v-if="active !== 0" @click="active = 0">上一步</el-button>
      <el-button
        v-if="active !== 0"
        type="primary"
        @click="saveDashboardItem"
      >保 存
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {
    addWidgetVisible: {
      type: Boolean,
    },
    currentDashborad: {
      type: Object,
      default: () => {},
    },
    dashboardId: {
      type: Number,
    },
  },
  data() {
    return {
      active: 0,
      pagination: {
        total: 1,
        pageSize: 6,
        currentPage: 1,
      },
      data: [],
      multipleSelection: [],
      params: {
        frequency: 60,
        polling: false,
      },
      rules: {
        frequency: [
          { required: true, message: "时长不能为空", trigger: "blur" },
        ],
      },
    }
  },
  computed: {
    isDisabledNext() {
      return this.multipleSelection.length ? false : true
    },
  },
  created() {
    this.getWidgetList()
  },
  mounted() {},
  watch: {},
  methods: {
    // 保存
    async saveDashboardItem() {
      const yArr = [
        ...this.currentDashborad?.relations.map((item) => item.y + item.height),
        0,
      ]
      const maxY = Math.max(...yArr)
      const secondMaxY =
        maxY === 0 ? 0 : Math.max(...yArr.filter((y) => y !== maxY))

      let maxX = 0
      if (maxY) {
        const maxYItems = this.currentDashborad.relations.filter(
          (item) => item.y + item.height === maxY
        )
        maxX = Math.max(...maxYItems.map((item) => item.x + item.width))
      }
      const params = this.multipleSelection.map((el, index) => {
        const xAxisTemp = index % 2 !== 0 ? 6 : 0
        const yAxisTemp =
          index % 2 === 0
            ? secondMaxY + 6 * Math.floor(index / 2)
            : maxY + 6 * Math.floor(index / 2)
        let xAxis
        let yAxis
        if (maxX > 0 && maxX <= 6) {
          xAxis = index % 2 === 0 ? 6 : 0
          yAxis = yAxisTemp
        } else if (maxX === 0) {
          xAxis = xAxisTemp
          yAxis = yAxisTemp
        } else if (maxX > 6) {
          xAxis = xAxisTemp
          yAxis = maxY + 6 * Math.floor(index / 2)
        }
        const item = {
          alias: "",
          config: "",
          widgetId: el.id,
          dashboardId: this.dashboardId,
          height: 6,
          width: 6,
          x: xAxis,
          y: yAxis,
          roleIds: [],
          ...this.params,
        }
        return item
      })

      const { code } = await Request.dashboard.createMemDashboardWidget(
        params,
        this.dashboardId
      )
      if (code === 200) {
        this.$message.success("新增成功")
        this.active = 0
        this.closeDialog()
        this.$emit("onAddSuccess")
      }
    },
    // 选中
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取列表
    async getWidgetList() {
      this.loading = true
      const {
        data: { list, totalCount, currentPage },
      } = await Request.widget.getPage({
        name: "",
        currentPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
      })
      this.data = list
      this.pagination.total = totalCount
      this.pagination.currentPage = currentPage
      this.loading = false
    },

    sizeChange(event) {
      this.pagination.pageSize = event.pageSize
      this.getWidgetList()
    },
    currentChange(event) {
      this.pagination.currentPage = event.currentPage
      this.getWidgetList()
    },
    closeDialog() {
      this.$emit("update:addWidgetVisible", false)
    },
  },
}
</script>

<style scoped lang="scss">
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}
</style>
