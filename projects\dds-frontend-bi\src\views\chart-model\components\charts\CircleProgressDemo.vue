<template>
  <div class="circle-progress-demo">
    <h2>环形进度条演示</h2>
    <p class="description">只需要三个参数：label（文字）、value（数字）、data（进度）</p>
    
    <div class="demo-container">
      <!-- 主要演示 -->
      <div class="main-demo">
        <h3>主要演示</h3>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>人数增涨</h4>
            <ChartCircleProgress
              label="人数增涨"
              :value="50"
              :data="25"
              :animation="true"
              width="100%"
              height="200px"
            />
            <p class="demo-code">
              label: "人数增涨"<br>
              value: 50<br>
              data: 25
            </p>
          </div>
          
          <div class="demo-item">
            <h4>销售完成率</h4>
            <ChartCircleProgress
              label="销售完成率"
              :value="85"
              :data="75"
              :animation="true"
              width="100%"
              height="200px"
            />
            <p class="demo-code">
              label: "销售完成率"<br>
              value: 85<br>
              data: 75
            </p>
          </div>
          
          <div class="demo-item">
            <h4>任务进度</h4>
            <ChartCircleProgress
              label="任务进度"
              :value="120"
              :data="90"
              :animation="true"
              width="100%"
              height="200px"
            />
            <p class="demo-code">
              label: "任务进度"<br>
              value: 120<br>
              data: 90
            </p>
          </div>
        </div>
      </div>
      
      <!-- 动态演示 -->
      <div class="dynamic-demo">
        <h3>动态更新演示</h3>
        <div class="controls">
          <el-button type="primary" @click="setDemo1">设置演示1</el-button>
          <el-button type="success" @click="setDemo2">设置演示2</el-button>
          <el-button type="warning" @click="randomUpdate">随机更新</el-button>
          <el-button type="info" @click="resetDemo">重置</el-button>
        </div>
        
        <div class="dynamic-display">
          <ChartCircleProgress
            :label="dynamicLabel"
            :value="dynamicValue"
            :data="dynamicData"
            :animation="true"
            width="100%"
            height="220px"
          />
          <div class="current-values">
            <p><strong>当前参数：</strong></p>
            <p>label: "{{ dynamicLabel }}"</p>
            <p>value: {{ dynamicValue }}</p>
            <p>data: {{ dynamicData }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChartCircleProgress from './ChartCircleProgress.vue'

export default {
  name: 'CircleProgressDemo',
  components: {
    ChartCircleProgress
  },
  data() {
    return {
      dynamicLabel: '人数增涨',
      dynamicValue: 50,
      dynamicData: 25
    }
  },
  methods: {
    setDemo1() {
      this.dynamicLabel = '销售业绩'
      this.dynamicValue = 88
      this.dynamicData = 65
    },
    setDemo2() {
      this.dynamicLabel = '用户满意度'
      this.dynamicValue = 95
      this.dynamicData = 80
    },
    randomUpdate() {
      this.dynamicLabel = '随机指标'
      this.dynamicValue = Math.floor(Math.random() * 200)
      this.dynamicData = Math.floor(Math.random() * 100)
    },
    resetDemo() {
      this.dynamicLabel = '人数增涨'
      this.dynamicValue = 50
      this.dynamicData = 25
    }
  }
}
</script>

<style scoped>
.circle-progress-demo {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

h2 {
  text-align: center;
  margin-bottom: 10px;
  font-size: 32px;
  color: #333;
}

.description {
  text-align: center;
  margin-bottom: 40px;
  font-size: 16px;
  color: #666;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

.main-demo, .dynamic-demo {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

h3 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
  color: #333;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.demo-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background: #fafafa;
}

.demo-item h4 {
  margin-bottom: 20px;
  color: #52c41a;
  font-size: 18px;
}

.demo-code {
  margin-top: 15px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  text-align: left;
}

.controls {
  text-align: center;
  margin-bottom: 30px;
}

.controls .el-button {
  margin: 5px 10px;
}

.dynamic-display {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  align-items: center;
}

.current-values {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #52c41a;
}

.current-values p {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .circle-progress-demo {
    padding: 20px;
  }
  
  h2 {
    font-size: 24px;
  }
  
  .main-demo, .dynamic-demo {
    padding: 20px;
  }
  
  .demo-grid {
    grid-template-columns: 1fr;
  }
  
  .dynamic-display {
    grid-template-columns: 1fr;
  }
  
  .controls .el-button {
    margin: 5px;
    font-size: 12px;
  }
}
</style>
