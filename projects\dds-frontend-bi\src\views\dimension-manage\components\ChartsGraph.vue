<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
// 防抖
export default {
  components: {},
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },

    // 颜色
    color: {
      type: Array,
      default: () => [
        "#5B8FF9",
        "#5AD8A7",
        "#6B84BD",
        "#FF9845",
        "#FCD473",
        "#FF99C3",
        "#FC7364",
        "#77D2E5"
      ]
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    // 渲染图表
    renderChart() {
      if (!this.chartData.nodes || this.chartData.nodes.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      this.chart.setOption({
        backgroundColor: "#fff",
        tooltip: {},
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        series: [
          {
            type: "graph",
            layout: "force",
            force: {
              repulsion: 200,
              edgeLength: 120,
              layoutAnimation: true
            },
            left: "0",
            top: "0",
            symbolSize: 70,
            nodeScaleRatio: 1, // 图标大小是否随鼠标滚动而变
            roam: true, // 缩放
            // draggable: true, // 节点是否可以拖拽
            edgeSymbol: ["none", "arrow"], // 线2头标记
            label: {
              show: true,
              formatter: function (params) {
                // 控制文字长度，超出部分省略
                return params.data.dataName.length > 6
                  ? params.data.dataName.slice(0, 6) + "..."
                  : params.data.dataName
              }
            },
            edgeLabel: {
              show: true,
              textStyle: {
                fontSize: 12,
                color: "#000"
              },
              formatter(x) {
                console.log(x, "///////)")
                return x.data.name
              }
            },
            symbolKeepAspect: false,
            focusNodeAdjacency: false, // 指定的节点以及其所有邻接节点高亮
            itemStyle: {
              borderColor: "red",
              borderWidth: 0,
              // shadowColor: "#29ACFC",
              color: "#29ACFC"
              // curveness: 0.08
            },

            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0.15,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#007aff" // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#007aff" // 100% 处的颜色
                  }
                ],
                globalCoord: false
              }
            },

            data: this.chartData.nodes,
            links: this.chartData.links,
            width: "25%",
            height: "25%"
          },
          {
            width: "25%",
            height: "25%",
            type: "graph",
            layout: "force",
            cursor: "pointer",
            nodeScaleRatio: 1, // 图标大小是否随鼠标滚动而变
            geoIndex: 1,
            roam: true, // 缩放
            // draggable: true, // 节点是否可以拖拽
            force: {
              repulsion: 200,
              edgeLength: 100
            },
            symbolSize: 70,

            label: {
              normal: {
                show: true
              }
            },

            data: [
              { name: "污水", sum: 208, size: 50 },
              { name: "碳排放", sum: 80, size: 60 },
              { name: "钢铁1", sum: 108, size: 70 },
              { name: "碳排放1", sum: 80, size: 80 },
              { name: "钢铁2", sum: 108, size: 90 }
            ]
          }
        ]
      })
    },
    // 高亮某个节点
    highlightNode(nodeId) {
      this.chart.dispatchAction({
        type: "downplay",
        seriesIndex: 0
      })

      setTimeout(() => {
        this.chart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: nodeId
        })
        console.log("高亮")
      }, 100)
    },
    // 将某个节点固定在中心
    locationNode(index) {
      setTimeout(() => {
        // 获取所有节点位置信息

        var positions = this.chart
          .getModel()
          .getSeriesByIndex(0)
          .getData()._itemLayouts

        this.chart.setOption({
          series: [
            {
              center: positions[index]
            }
          ]
        })
      }, 1000)
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return false
    }
    this.chart.dispose()
    this.chart = null
  }
}
</script>

<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    min-width: 220px;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    display: flex;
    align-items: center;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    margin-left: 15px;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
  }
}
</style>
