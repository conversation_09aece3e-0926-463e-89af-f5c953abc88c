const geo<PERSON><PERSON><PERSON><PERSON><PERSON> = {
  台湾: [ 121.5135, 25.0308 ],
  黑龙江: [ 127.9688, 45.368 ],
  内蒙古: [ 110.3467, 41.4899 ],
  吉林: [ 125.8154, 44.2584 ],
  北京: [ 116.4551, 40.2539 ],
  辽宁: [ 123.1238, 42.1216 ],
  河北: [ 114.4995, 38.1006 ],
  天津: [ 117.4219, 39.4189 ],
  山西: [ 112.3352, 37.9413 ],
  陕西: [ 109.1162, 34.2004 ],
  甘肃: [ 103.5901, 36.3043 ],
  宁夏: [ 106.3586, 38.1775 ],
  青海: [ 101.4038, 36.8207 ],
  新疆: [ 87.9236, 43.5883 ],
  西藏: [ 91.11, 29.97 ],
  四川: [ 103.9526, 30.7617 ],
  重庆: [ 108.384366, 30.439702 ],
  山东: [ 117.1582, 36.8701 ],
  河南: [ 113.4668, 34.6234 ],
  江苏: [ 118.8062, 31.9208 ],
  安徽: [ 117.29, 32.0581 ],
  湖北: [ 114.3896, 30.6628 ],
  浙江: [ 119.5313, 29.8773 ],
  福建: [ 119.4543, 25.9222 ],
  江西: [ 116.0046, 28.6633 ],
  湖南: [ 113.0823, 28.2568 ],
  贵州: [ 106.6992, 26.7682 ],
  云南: [ 102.9199, 25.4663 ],
  广东: [ 113.12244, 23.009505 ],
  广西: [ 108.479, 23.1152 ],
  海南: [ 110.3893, 19.8516 ],
  上海: [ 121.4648, 31.2891 ],
  香港: [ 114.173355, 22.320048 ],
  澳门: [ 113.54909, 22.198951 ],
}
export default function(chartData, layerType) {
  const {
    metrics,
    cols,
    data,
    chartStyles: { label, spec, visualMap },
  } = chartData

  // 获取data数组对象中value属性最大的值
  let maxValue = 0
  data.forEach((item) => {
    if (item[`${metrics[0].agg}(${metrics[0].displayName})`] > maxValue) {
      maxValue = item[`${metrics[0].agg}(${metrics[0].displayName})`]
    }
  })
  maxValue = Math.floor(maxValue)
  const sizeRate = getSizeRate(0, maxValue)

  return {
    geo: {
      type: "map",
      roam: spec.roam,
      map: "China",
      itemStyle: {
        areaColor: "#edecf3",
        borderWidth: spec.isShowBorder ? 1 : 0, // 设置外层边框
      },
      label: {
        show: label.showLabel,
        // color: label.labelColor,
        fontFamily: label.labelFontFamily,
        fontSize: label.labelFontSize,
        position: label.labelPosition,
      },
    },
    series:
    {
      name: " ",
      type: layerType,
      coordinateSystem: "geo",
      data: convertData(data, metrics, sizeRate, cols),

    },

    visualMap: {
      show: visualMap.showVisualMap,
      orient: visualMap.visualMapDirection,
      min: 0,
      max: maxValue,
      calculable: true,
      ...getPosition(visualMap.visualMapPosition),
      // inRange: {
      //   color: [visualMap.startColor, visualMap.endColor],
      // },
      itemWidth: visualMap.visualMapWidth,
      itemHeight: visualMap.visualMapHeight,
      textStyle: {
        // color: visualMap.fontColor,
        fontFamily: visualMap.fontFamily,
        fontSize: visualMap.fontSize,
      },
    },
    // tooltip: {
    //   ...TOOLTIP_STYLE,
    //   trigger: "item",
    //   formatter: (params) => {
    //     const { name, value, color } = params;
    //     const tooltipLabels = [];
    //     if (color) {
    //       let circle = `<span  style="background:${params.color};width:10px;height:10px;border-radius:50%;display:inline-block;margin-right: 5px;"></span>`;
    //       tooltipLabels.push(circle);
    //     }
    //     tooltipLabels.push(name);
    //     tooltipLabels.push(value[2]);
    //     return tooltipLabels.join("");
    //   },
    // },
  }
}

function convertData(data, metrics, sizeRate, cols) {
  var res = []
  for (var i = 0; i < data.length; i++) {
    const formatName = getProvinceName(data[i][cols[0].displayName])
    var geoCoord = geoCoordMap[formatName]
    if (geoCoord) {
      const value = data[i][`${metrics[0].agg}(${metrics[0].displayName})`]
      console.log(data, 'data')
      console.log(value, 'value')
      res.push({
        name: formatName,
        value: geoCoord.concat(value),
        symbolSize: getSymbolSize(sizeRate, value) / 2,
      })
    }
  }
  return res
}
function getPosition(position) {
  let positionValue
  switch (position) {
  case "leftBottom":
    positionValue = {
      left: "left",
      top: "bottom",
    }
    break
  case "leftTop":
    positionValue = {
      left: "left",
      top: "top",
    }
    break
  case "rightTop":
    positionValue = {
      left: "right",
      top: "top",
    }
    break
  case "rightBottom":
    positionValue = {
      left: "right",
      top: "bottom",
    }
    break
  }
  return positionValue
}
function getSizeRate(min, max) {
  return Math.max(min / 10, max / 100)
}
function getSymbolSize(sizeRate, size) {
  return sizeRate ? Math.ceil(size / sizeRate) : size
}
const provinceSuffix = [
  "省",
  "市",
  "维吾尔自治区",
  "回族自治区",
  "壮族自治区",
  "特别行政区",
  "自治区",
]
function getProvinceName(name) {
  provinceSuffix.forEach((ps) => {
    if (name.includes(ps)) {
      name = name.replace(ps, "")
    }
  })
  return name
}
