
const chartTypeList = [
  {
    name: '表格',
    icon: 'chart_table',
    type: 'table',
    matchRule: {
      desc: '任意维度和数值',
      isUsable() {
        return true
      }
    },
    componentName: 'table',

  },
  {
    name: '折线图',
    icon: 'chart_line',
    type: 'line',
    matchRule: {
      desc: `1个维度<br/>1或多个数值`,
      isUsable(dimensions, calculs) {
        return dimensions.length === 1  && (calculs.length >= 1)
      }
    },
    componentName: 'line',
  },
  {
    name: '柱状图',
    icon: 'chart_bar',
    type: 'bar',
    matchRule: {
      desc: `1个维度<br/>1或多个数值`,
      isUsable(dimensions, calculs) {
        return dimensions.length === 1  && (calculs.length >= 1)

      }
    },
    componentName: 'BarChart',
  },
  {
    name: '饼图',
    icon: 'chart_pie',
    type: 'pie',
    matchRule: {
      desc: `<span>0到多个维度,1个指标</span><br/>0个维度,2个到多个指标`,
      isUsable(dimensions, calculs) {
        return (dimensions.length > 0 && calculs.length === 1) || (dimensions.length === 0 && calculs.length >= 2)
      }
    },
    componentName: 'PieChart',
  },
  {
    name: '指标卡',
    icon: 'scorecard',
    type: 'scorecard',
    matchRule: {
      desc: `0个维度<br/>1个指标到3个指标`,
      isUsable(dimensions, calculs) {
        return dimensions.length === 0 &&[ 1,2,3 ].includes(calculs.length) 
      }
    },
    componentName: 'scorecard',
  },
  {
    name: '双Y轴',
    icon: 'doubleYAxis',
    type: 'doubleYAxis',
    matchRule: {
      desc: `1个维度<br/>2个指标或多个指标`,
      isUsable(dimensions, calculs) {
        return dimensions.length === 1 && calculs.length >= 2
      }
    },
    componentName: 'scorecard',
  },
  {
    name: '地图',
    icon: 'map',
    type: 'map',
    matchRule: {
      desc: `0个维度到多个维度<br/>1个指标`,
      isUsable(dimensions, calculs) {
        return dimensions.length >= 0 && calculs.length === 1
      }
    },
    componentName: 'map',
  },
  // {
  //   name: '雷达图',
  //   icon: 'radar',
  //   type: 'radar',
  //   matchRule: {
  //     desc: '0个维度1个数值;0个维度多个数值',
  //     isUsable(dimensions, calculs) {
  //       return false
  //     }
  //   },
  //   componentName: 'radar',
  // },


]

export default chartTypeList
