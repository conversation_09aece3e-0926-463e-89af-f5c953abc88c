<template>
  <el-dialog
    title="参考线配置"
    :visible.sync="ReferenceVisible"
    width="70%"
    :before-close="handleClose"
    @close="onCancel"
    top="0"
  >
    <div class="configPanel">
      <div class="listContainer">
        <div class="title">
          <h2>参考线列表</h2>
          <i class="el-icon-plus" @click="addReference" />
        </div>
        <div class="listClass">
          <div
            class="reference-item"
            v-for="item in editingReferences"
            :class="[selected.key == item.key ? 'editing' : '']"
            :key="item.key"
            @click="selectReference(item)"
          >
            <template v-if="currentEditingReferenceNameKey == item.key">
              <el-input size="mini" v-model="item.name" autofocus />
              <i class="el-icon-check" @click.stop="editFinish"></i>
              <i class="el-icon-close" @click.stop="handleCancel(item)"></i>
            </template>
            <template v-else>
              <span class="name">{{ item.name }}</span>
              <span>
                <i
                  class="el-icon-edit"
                  @click.stop="handleEdit(item)"
                  style="margin-right: 5px"
                ></i>
                <i
                  class="el-icon-delete"
                  @click.stop="deleteReference(item.key)"
                ></i>
              </span>
            </template>
          </div>
        </div>
      </div>
      <div class="form" v-if="selected">
        <el-radio-group :value="selected.type" @input="changeReferenceType">
          <el-radio-button label="line">参考线</el-radio-button>
          <el-radio-button label="band">参考区间</el-radio-button>
        </el-radio-group>
        <el-form
          ref="form"
          :model="selected.data"
          label-width="80px"
          label-position="top"
        >
          <LineForm
            :selected="selected"
            v-bind="$attrs"
            v-if="selected.type == 'line'"
          />
          <BandForm v-else :selected="selected" v-bind="$attrs" />
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onCancel">取 消</el-button>
      <el-button type="primary" @click="onSave">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getDefaultReferenceLineData,
  getDefaultReferenceBandData,
} from "./util"
import { uuid } from "@/utils"
import LineForm from "./component/LineForm.vue"
import BandForm from "./component/BandForm.vue"
export default {
  components: {
    LineForm,
    BandForm,
  },
  props: {
    references: {
      type: Array,
    },
    ReferenceVisible: {
      type: Boolean,
    },
  },
  data() {
    return {
      currentEditingReferenceNameKey: "",
      initialName: "",
      editingReferences: [],
      selected: null,
      editing: false,
    }
  },
  methods: {
    onCancel() {
      this.$emit("update:ReferenceVisible", false)
    },
    onSave() {
      if (!this.editingReferences) {
        this.$emit("update:references", [])
        this.onCancel()
      } else {
        this.validateForm((mergedReferences) => {
          this.$emit("update:references", mergedReferences)
          this.onCancel()
        })
      }
    },
    // 编辑name
    handleEdit(references) {
      this.currentEditingReferenceNameKey = references.key
      this.initialName = references.name
    },
    // 取消name
    handleCancel(references) {
      references.name = this.initialName
      this.currentEditingReferenceNameKey = ""
    },
    // 编辑完成
    editFinish() {
      this.currentEditingReferenceNameKey = ""
    },
    // 选择参考类型
    changeReferenceType(referenceType) {
      const data =
        referenceType === "line"
          ? getDefaultReferenceLineData()
          : getDefaultReferenceBandData()
      this.selected = {
        ...this.selected,
        ...data,
      }
      this.selected.type = referenceType
    },
    // 验证是否选择指标
    validateForm(callback) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const mergedReferences = this.editingReferences.map((ref) =>
            ref.key === this.selected.key
              ? {
                ...this.selected,
              }
              : ref
          )
          callback(mergedReferences)
        } else {
          return false
        }
      })
    },
    // 添加参考线
    addReference() {
      const reference = this.getDefaultReference()
      if (this.selected) {
        this.validateForm((mergedReferences) => {
          (this.editingReferences = [ ...mergedReferences, reference ]),
          (this.selected = reference)
        })
      } else {
        this.editingReferences = [ reference ]
        this.selected = reference
      }
    },
    // 获取默认参考线配置
    getDefaultReference() {
      return {
        key: uuid(8, 16),
        name: "新建参考线",
        type: "line",
        ...getDefaultReferenceLineData(),
      }
    },

    // 选择参考线
    selectReference(item) {
      this.selected = item
    },
    // 删除
    deleteReference(key) {
      let reselected = null
      if (this.editingReferences.length > 1) {
        if (key === this.selected.key) {
          const delIndex = this.editingReferences.findIndex(
            (ref) => ref.key === key
          )
          reselected =
            delIndex === this.editingReferences.length - 1
              ? this.editingReferences[delIndex - 1]
              : this.editingReferences[delIndex + 1]
        } else {
          reselected = this.selected
        }
      }
      this.editingReferences = this.editingReferences.filter(
        (ref) => ref.key !== key
      )
      this.selected = reselected
    },
  },
}
</script>

<style scoped lang="scss">
.configPanel {
  display: flex;
  height: 480px;
  .listContainer {
    width: 200px;
    background-color: #f7f7f7;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      padding: 0 8px;
      h2 {
        font-size: 1em;
        height: 3em;
        line-height: 3em;
      }
    }
    .reference-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px 0 16px;
      height: 32px;
      cursor: pointer;
      i:hover {
        color: #000;
      }
    }
    & .editing {
      background: rgba(27, 152, 224, 0.15);
      border-right: 3px solid #1b98e0;
    }
    .reference-item:hover .name {
      color: #1b98e0;
    }
  }
  .form {
    flex: 1;
    padding: 16px;
  }
}

::v-deep .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
}
::v-deep .el-dialog__body {
  padding: 0px;
}

::v-deep .el-input--mini .el-input__inner {
  height: 24px !important;
  line-height: 24px !important;
}
::v-deep .el-form-item__label {
  padding: 10px 0 0 0;
  color: rgba(89, 89, 89, 0.65);
  font-size: 0.8571em;
  font-weight: bold;
  height: 42px;
}
::v-deep .el-row {
  margin-bottom: 0;
}
::v-deep .el-form-item {
  margin-bottom: 0;
}
::v-deep .el-form-item__content {
  height: 32px;
}
::v-deep .el-input-number--small {
  width: 100%;
}
</style>
