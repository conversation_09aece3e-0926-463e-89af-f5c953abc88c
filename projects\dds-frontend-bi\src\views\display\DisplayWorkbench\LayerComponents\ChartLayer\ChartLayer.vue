<template>
  <Widgets
    ref="WidgetsRefs"
    v-if="chartData.name"
    :widget-props="chartData"
    @onPageChange="onPageChange"
    v-on="$listeners"
    :loading="loading"
  />
</template>

<script>
import Widgets from "@/views/widgets/component/Widget"
import { widgetParamsFormat } from "@/views/widgets/component/util"
import { mapState } from "vuex"
export default {
  components: {
    Widgets
  },
  props: {
    widgetId: {
      type: Number
    }
  },
  data() {
    return {
      chartData: {},
      updatedPagination: {
        pageNo: 0,
        pageSize: 0
      },
      page: {
        pageNo: 0,
        pageSize: 0
      },
      loading: false
    }
  },
  computed: {
    ...mapState({
      widgets: state => state.display.widgets
    })
  },
  created() {},
  mounted() {},
  watch: {
    widgetId: {
      immediate: true,
      handler() {
        let widgetConfig = this.widgets.find(item => item.id === this.widgetId)
        if (widgetConfig) {
          this.chartData = JSON.parse(widgetConfig.config)
          this.item = widgetConfig
          this.getData()
        }
      }
    }
  },
  methods: {
    onPageChange(page) {
      this.updatedPagination.pageNo = page.currentPage
      this.updatedPagination.pageSize = page.pageSize
      this.getData()
      // this.page = page;
    },
    getData() {
      const filters = [] // 字段
      const params = [] // 变量
      const [lf, lp] = this.getControlsPrams(this.chartData.controls, "local")
      filters.push(...lf)
      params.push(...lp)
      let parameter = {
        ...widgetParamsFormat(
          this.chartData,
          this.item.viewId,
          this.updatedPagination
        ),
        filters,
        params
      }
      this.onLoadData(parameter)
      clearInterval(this.pollingTimer)
      if (this.item.polling) {
        this.pollingTimer = setInterval(() => {
          this.onLoadData(parameter)
        }, Number(this.item.frequency) * 1000)
      }
    },
    onLoadData(parameter) {
      this.loading = true
      this.$httpBi.view.getdata(parameter).then(res => {
        this.chartData.data = res.data.resultList
        if (this.item.name === "table") {
          this.item.pagination = {
            pageNo: this.chartData.data.pageNo,
            pageSize: this.chartData.data.pageSize,
            totalCount: this.chartData.data.totalCount,
            withPaging: this.widgetProps.chartStyles.table.withPaging
          }
        }
        this.loading = false
      })
    },
    getControlsPrams(controls, ControlType) {
      let filters = []
      let params = []
      controls.length &&
        controls.forEach(item => {
          let name = item.relatedViews[this.item.viewId].fields[0]
          let fieldType = item.relatedViews[this.item.viewId].fieldType
          let model = JSON.parse(this.chartData.model)
          if (item.defaultValue !== "" && item.defaultValue) {
            if (fieldType === "column") {
              let sqlType = model[name].sqlType
              if (["numberRange", "dateRange", "slider"].includes(item.type)) {
                filters.push(
                  JSON.stringify({
                    value: item.defaultValue[0],
                    name,
                    operator: ">=",
                    sqlType: sqlType,
                    type: "filter"
                  }),
                  JSON.stringify({
                    value: item.defaultValue[1],
                    name,
                    operator: "<=",
                    sqlType: sqlType,
                    type: "filter"
                  })
                )
              } else {
                filters.push(
                  JSON.stringify({
                    value: item.defaultValue,
                    name,
                    operator: item.operator ?? "=",
                    sqlType: sqlType,
                    type: "filter"
                  })
                )
              }
            } else {
              params.push({
                value: item.defaultValue,
                name
              })
            }
          }
          // 如果下拉选择控制器就请求 options   如果已经存在不需要二次请求
          if (
            ["select", "radio"].includes(item.type) &&
            !this.selectOptions[item.key] &&
            ControlType === "local"
          ) {
            this.getOptions(name, item)
          }
        })
      return [filters, params]
    }
  }
}
</script>

<style scoped lang="scss"></style>
