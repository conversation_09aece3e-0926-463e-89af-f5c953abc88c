<template>
  <div v-show="isShow" :style="style">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "grid-item",
  components: {},
  props: {
    offset: {
      type: Number,
      default: 0
    },
    span: {
      type: Number,
      default: 1
    },
    suffix: {
      type: Boolean,
      default: false
    },
    xs: {
      type: [Object, undefined],
      default: undefined
    },
    sm: {
      type: [Object, undefined],
      default: undefined
    },
    md: {
      type: [Object, undefined],
      default: undefined
    },
    lg: {
      type: [Object, undefined],
      default: undefined
    },
    xl: {
      type: [Object, undefined],
      default: undefined
    },
    index: {
      type: String
    }
  },
  inject: ["parentInstance"],
  data() {
    return {
      isShow: true
    }
  },
  computed: {
    gap() {
      return this.parentInstance.gapValue
    },
    breakPoint() {
      return this.parentInstance.breakPoint
    },
    shouldHiddenIndex() {
      return this.parentInstance.hiddenIndex
    },
    cols() {
      return this.parentInstance.gridCols
    },
    style() {
      let span = this[this.breakPoint] ? this[this.breakPoint].span : this.span
      let offset = this[this.breakPoint]
        ? this[this.breakPoint].offset !== undefined
          ? this[this.breakPoint].offset
          : this.offset
        : this.offset

      if (this.suffix) {
        return {
          gridColumnStart: this.cols - span - offset + 1,
          gridColumnEnd: `span ${span + offset}`,
          marginLeft:
            offset !== 0
              ? `calc(((100% + ${this.gap}px) / ${span + offset}) * ${offset})`
              : "unset"
        }
      } else {
        const gridSpan = span + offset > this.cols ? this.cols : span + offset
        return {
          gridColumn: `span ${gridSpan}/span ${gridSpan}`,
          marginLeft:
            offset !== 0
              ? `calc(((100% + ${this.gap}px) / ${span + offset}) * ${offset})`
              : "unset"
        }
      }
    }
  },
  created() {},
  mounted() {},
  watch: {
    shouldHiddenIndex: {
      handler: function (newVal) {
        this.handleWatchCallback([newVal, this.breakPoint])
      },
      deep: true,
      immediate: true
    },
    breakPoint: {
      handler: function (newVal) {
        this.handleWatchCallback([this.shouldHiddenIndex, newVal])
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleWatchCallback([shouldHiddenIndex]) {
      if (this.index) {
        this.isShow = !(
          shouldHiddenIndex !== -1 &&
          parseInt(this.index) >= Number(shouldHiddenIndex)
        )
      }
    }
  }
}
</script>

<style scoped lang=""></style>
