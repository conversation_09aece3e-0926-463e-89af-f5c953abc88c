<template>
  <div class="diaplay-workbench">
    <!-- 大屏头部 -->
    <DisplayHeader @screenshot="screenshot" />
    <div class="display-wrapper">
      <el-tabs type="border-card" stretch>
        <el-tab-pane label="幻灯片"><SlideThumbnailList /></el-tab-pane>
        <el-tab-pane label="图层"><LayerList /></el-tab-pane>
      </el-tabs>
      <!-- 幻灯片编辑器 -->
      <div class="display-layout">
        <!-- 大屏内容区域 -->
        <SlideEditor @setOptionsOnClick="setOptionsOnClick" ref="slideEditor" />
        <!-- 右侧属性设置列表  -->
        <Setting
          :style-options="widgetOptions.style"
          :event-options="widgetOptions.event"
          :form-data="formData"
          :cur-code="curCode"
          @onChanged="widgetValueChanged"
        />
      </div>
    </div>
  </div>
</template>

<script>
import DisplayHeader from "./DisplayHeader/index.vue"
import SlideThumbnailList from "./SlideThumbnailList/index.vue"
import LayerList from "./LayerList/index.vue"
import SlideEditor from "./SlideEditor/index.vue"
import Setting from "./Setting/index.vue"
import { getLayerCode } from "./LayerComponents/index"
import { mapGetters } from "vuex"
export default {
  components: {
    DisplayHeader,
    SlideThumbnailList,
    LayerList,
    SlideEditor,
    Setting
  },
  props: {},
  data() {
    return {
      curCode: "screen", // 当前激活组件code
      // 当前激活组件右侧配置
      widgetOptions: {
        style: [], // 样式配置
        event: [] // 事件配置
      },
      formData: {} // 配置属性值
    }
  },
  computed: {
    ...mapGetters({
      currentSlide: "currentSlide",
      currentSlideId: "currentSlideId",
      currentLayer: "currentLayer"
    })
  },
  created() {
    this.getDisplayData()
  },
  mounted() {},
  watch: {
    currentSlide: {
      handler(val) {
        console.log("%cindex.vue line:67 object", "color: #007acc;", val)
        this.formData = val.config.slideParams
        this.widgetOptions = getLayerCode("screen").options
      },
      immediate: true
    },
    currentSlideId: {
      handler(val) {
        if (val) {
          this.$store.dispatch("display/getSlideLayers")
        }
      },
      immediate: true
    }
  },
  methods: {
    // 截图
    screenshot() {
      this.$refs.slideEditor.screenshot()
    },
    // 获取大屏数据
    getDisplayData() {
      this.$store.dispatch("display/getDisplaySlides", {
        displayId: this.$route.query.displayId
      })
    },
    // 设置当前激活组件右侧配置属性
    setOptionsOnClick(code, style = null) {
      console.log(code)
      // 是否是初始化
      this.curCode = code
      this.widgetOptions = getLayerCode(code).options
      console.log(this.widgetOptions, this.widgetOptions)
      // 如果是大屏加载当前幻灯片配置style
      if (code === "screen") {
        this.formData = this.currentSlide.config.slideParams
      } else {
        this.formData = {
          ...this.currentLayer.params,
          ...style
        }
      }
      // 图层拖拽缩放结束
      if (style) {
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.currentLayer,
            params: {
              ...this.currentLayer.params,
              ...style
            }
          }
        })
      }
    },
    // 组件属性值改变
    widgetValueChanged(val) {
      console.log(val)
      // 大屏幻灯片配置
      if (this.curCode === "screen") {
        const slide = {
          ...this.currentSlide,
          config: {
            slideParams: val
          }
        }
        this.$store.commit("display/SET_DISPLAY_CUR_SLIDE", slide)
        this.$store.dispatch("display/updateDisplaySlide", [slide])
      } else {
        // 图层配置
        this.$store.dispatch("display/updateLayer", {
          layer: {
            ...this.currentLayer,
            params: val
          }
        })
      }
    }
  },
  destroyed() {}
}
</script>

<style scoped lang="scss">
.diaplay-workbench {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .display-wrapper {
    width: 100%;
    display: flex;
    height: calc(100% - 60px);
    .display-layout {
      width: calc(100% - 165px);
      display: flex;
    }
  }
}
::v-deep .el-tabs--border-card > .el-tabs__content {
  height: calc(100% - 40px);
  overflow: auto;
  box-sizing: border-box;
  padding: 0;
  padding-top: 5px;
}
</style>
