<template>
  <div class="view-main">
    <div class="grid-head" v-if="dashboardId">
      <div class="title">
        <i
          v-if="!isEdit"
          :class="
            $store.state.dashboard.isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'
          "
          @click="$store.commit('dashboard/TOGGLE_COLLAPSE')"
        />
        {{ currentDashborad.name }}
      </div>
      <el-row class="row-btn">
        <el-popover placement="bottom" title="主题色" trigger="hover">
          <RightPanel />
          <svg-icon slot="reference" class="icon" icon-class="_theme" />
        </el-popover>

        <el-tooltip content="新增" placement="top" v-if="isEdit">
          <svg-icon
            class="icon"
            icon-class="add"
            @click="addWidgetVisible = true"
          />
        </el-tooltip>

        <!-- <el-tooltip content="搜索" placement="top">
              <el-button icon="el-icon-search" type="primary"></el-button>
            </el-tooltip>
            <el-tooltip content="分享" placement="top">
              <el-button icon="el-icon-share" type="primary"></el-button>
            </el-tooltip>
            <el-tooltip content="下载" placement="top">
              <el-button icon="el-icon-download" type="primary"></el-button>
            </el-tooltip> -->
        <el-tooltip
          content="控制器"
          placement="top"
          v-if="
            currentDashborad.relations &&
              currentDashborad.relations.length &&
              isEdit
          "
        >
          <svg-icon
            @click="controlConfigVisible = true"
            class="icon"
            icon-class="controller"
          />
        </el-tooltip>
        <el-tooltip content="导出" placement="top">
          <span class="el-dropdown-link">
            <svg-icon
              class="icon"
              @click="downloadVisible = true"
              icon-class="download"
            />
          </span>
        </el-tooltip>

        <el-tooltip content="分享" placement="top">
          <svg-icon
            @click="shareVisible = true"
            class="icon"
            icon-class="share"
          />
        </el-tooltip>
      </el-row>
    </div>
    <div class="GolobalPanel" v-if="filters && filters.length">
      <GolobalPanelComponent
        ref="GolobalPanel"
        :controls.sync="filters"
        :query-mode="queryMode"
        :select-options="globalSelectOptions"
        @change="getFormValuesRelatedItems"
        @reset="handleResetGlobalFilters"
        type="golobal"
      />
    </div>

    <div class="dashboardList">
      <Dashboard
        ref="Dashboards"
        :global-controls="filters"
        :original-filters="originalFilters"
        :query-mode="queryMode"
        :widgets="widgets"
        :is-edit="isEdit"
        :form-values-related-items="FormValuesRelatedItems"
        :form-values-related-items-all="FormValuesRelatedItemsAll"
        :current-dashborad.sync="currentDashborad"
        @onReload="getWidgetsConfig"
        @onSetThumbnail="setThumbnail"
      />
    </div>
    <!-- 新增widget -->
    <add-widget
      :add-widget-visible.sync="addWidgetVisible"
      :dashboard-id="dashboardId"
      :current-dashborad="currentDashborad"
      @onAddSuccess="getWidgetsConfig"
    />
    <!-- 控制器弹窗 -->
    <global-control-config
      :id="portalId"
      :widget-list="widgetList"
      :query-mode="queryMode"
      :global-filters-config="filters"
      @confirm="handleConfirmGlobal"
      :current-dashborad="currentDashborad"
      :control-config-visible.sync="controlConfigVisible"
    />
    <ShareDialog
      :share-visible.sync="shareVisible"
      v-if="shareVisible"
      :share-id="dashboardId"
    />
    <DownloadDialog
      :download-visible.sync="downloadVisible"
      :export-title="currentDashborad.name"
      @handleExport="handleExport"
    />
  </div>
</template>

<script>
import Dashboard from "../dashboard/index.vue"
import ControlPanelComponent from "@/components/Control/Control"
import globalControlConfig from "@/components/Control/global"
import DownloadDialog from "./component/DownloadDialog"
import Request from "@/service"
import RightPanel from "./component/RightPanel"
import ShareDialog from "./component/ShareDialog"
import AddWidget from "./component/AddWidget.vue"
import { mapState } from "vuex"
export default {
  components: {
    Dashboard,
    RightPanel,
    GolobalPanelComponent: ControlPanelComponent,
    ShareDialog,
    AddWidget,
    globalControlConfig,
    DownloadDialog,
  },
  props: {},
  data() {
    return {
      downloadVisible: false, // 导出弹窗
      shareVisible: false, // 分享弹窗
      dashboardId: null,
      addWidgetVisible: false, // 新增
      controlConfigVisible: false, // 控制器
      widgetList: [],
      portalId: "",
      filters: [],
      queryMode: [],
      globalSelectOptions: [],
      originalFilters: [],
      FormValuesRelatedItems: [],
      FormValuesRelatedItemsAll: [],
      currentDashborad: {},
    }
  },
  computed: {
    ...mapState({
      widgets: (state) => state.widget.widgets,
    }),
  },
  created() {
    console.log("this.$parent", this.$parent);
    (this.isEdit = this.$route.meta.isEdit),
    (this.portalId = this.$route.query.id ?? null)
  },
  mounted() {},
  watch: {
    "$route.params.id": {
      handler(val) {
        this.dashboardId = val
        this.getWidgetsConfig()
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleResetGlobalFilters() {
      this.filters = this._.cloneDeep(this.originalFilters)
    },
    getFormValuesRelatedItems(formValues) {
      console.log(formValues, "formValues")
      console.log(this.filters, "this.filters")
      this.FormValuesRelatedItems = Object.keys(formValues).reduce(
        (items, key) => {
          const control = this.filters.find((c) => c.key === key)
          const { relatedItems } = control
          const checkedItems = Object.entries(relatedItems)
            .filter(([ , config ]) => config.checked)
            .map(([ itemId ]) => itemId)
          console.log('%cPortalview.vue line:202 object', 'color: #007acc;', checkedItems)
          return Array.from(new Set([ ...items, ...checkedItems ]))
        },
        []
      )
      console.log(this.FormValuesRelatedItems, "this.FormValuesRelatedItems")
    },
    // 获取dashboard
    async getWidgetsConfig() {
      this.filters = []
      const { data, code } = await Request.dashboard.widgets({
        dashboardId: this.dashboardId,
      })
      if (code === 200) {
        this.currentDashborad = data
        this.filters = (data.config && JSON.parse(data.config).filters) || []

        // 备份原始数据 为了重置
        this.originalFilters = this._.cloneDeep(this.filters)
        this.queryMode =
          (data.config && JSON.parse(data.config).queryMode) || 0
        this.getSelectOptions()
        this.getCurrentDashboradRelations()
        if (data.watermark) {
          this.$store.commit("watermark/SET_WATERMARK")
        } else {
          this.$store.commit("watermark/OUT_WATERMARK")
        }
      }
    },
    getSelectOptions() {
      const selectOptions = {}
      this.filters.length &&
        this.filters.forEach((filter) => {
          Object.entries(filter.relatedItems).forEach(async([ , v ]) => {
            if ([ "select", "radio" ].includes(filter.type)) {
              if (filter.optionType === "auto" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: filter.relatedViews[v.viewId].fields,
                  viewId: v.viewId,
                }
                const { data } = await this.$httpBi.view.getdistinctvalue(param)
                this.$set(selectOptions, filter.key, [ ...data ])
              } else if (filter.optionType === "manual" && v.checked) {
                let param = {
                  cache: false,
                  expired: 0,
                  columns: [ filter.valueField, filter.textField ],
                  viewId: filter.valueViewId,
                }
                // 如果加载过一次手动数据  不需要加载第二次
                if (!selectOptions[filter.key]) {
                  this.$set(selectOptions, filter.key, [])
                  const { data } = await this.$httpBi.view.getdistinctvalue(
                    param
                  )
                  this.$set(selectOptions, filter.key, [ ...data ])
                }
              } else if (
                filter.optionType === "custom" &&
                !selectOptions[filter.key]
              ) {
                this.$set(selectOptions, filter.key, [ ...filter.customOptions ])
              }
            }
          })
        })

      this.globalSelectOptions = selectOptions
    },
    getCurrentDashboradRelations() {
      this.widgetList = []
      let obj = {}
      const deepCloneWidgets = JSON.parse(
        JSON.stringify(this.currentDashborad.widgets)
      )
      deepCloneWidgets.forEach((item) => (obj[item.id] = item))
      this.currentDashborad.relations.forEach((item) => {
        obj[item.widgetId].widgetId = item.widgetId
        obj[item.widgetId].id = item.id
        this.widgetList.push(obj[item.widgetId])
      })
    },
    // 下载
    handleExport({ exportType, title }) {
      if (exportType === "IMG") {
        this.$refs.Dashboards.exportImg(title)
      }
      if (exportType === "PDF") {
        this.$refs.Dashboards.exportPDF(title)
      }
      if (exportType === "EXCEL") {
        this.$refs.Dashboards.exportExcel(title)
      }
      this.downloadVisible = false
    },
    // 获取全局控制器配置
    async handleConfirmGlobal(params, queryMode) {
      this.queryMode = queryMode
      this.filters = params
      this.originalFilters = this._.cloneDeep(this.filters)

      this.controlConfigVisible = false
      this.setControlGetRelatedItems(params)
      this.currentDashborad.config = JSON.stringify({
        filters: params,
        queryMode,
      })
      this.getSelectOptions()
      await this.$httpBi.dashboard.updateDashboards([ this.currentDashborad ])
    },
    setControlGetRelatedItems(filters) {
      this.FormValuesRelatedItems = filters.reduce((filter, nextFilter) => {
        const checkedItems = Object.entries(nextFilter.relatedItems)
          .filter(([ , config ]) => config.checked)
          .map(([ itemId ]) => itemId)
        return Array.from(new Set([ ...filter, ...checkedItems ]))
      }, [])
    },
    updateThumbnail() {
      this.$refs.Dashboards.setThumbnail()
    },
  },
  beforeDestroy() {},
}
</script>

<style scoped lang="scss">
.view-main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: var(--theme-bg-color);

  .grid-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 15px 18px 16px;
    height: 56px;
    background-color: var(--theme-color);
    color: var(--theme-text-color);
    font-size: 14px;
    box-sizing: border-box;
    font-family: PingFangSC-Regular, PingFang SC;
  }

  .row-btn {
    margin: 0;

    & > * {
      margin-left: 24px;
    }
  }

  .GolobalPanel {
    margin: 16px 20px 0 16px;
    padding: 16px 16px 0 16px;
    background-color: var(--theme-color);
  }

  .dashboardList {
    box-sizing: border-box;
    padding-right: 0px;
    // flex: 1;
    overflow: auto;
  }
}

::-webkit-scrollbar {
  height: 4px;
  width: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
  background-color: var(--theme-color);
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-thumb {
  width: 4px;
  min-height: 15px;
  background: var(--scrollbar-color) !important;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5) !important;
}


.el-icon-s-unfold:hover,
.el-icon-s-fold:hover {
    color: #1890ff;
    cursor: pointer;
  }

::v-deep .el-scrollbar__bar.is-horizontal{
  display:none
}
</style>
