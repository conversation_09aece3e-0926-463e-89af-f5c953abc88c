<template>
  <div>
    <!-- 头部 -->
    <DT-Header :content="title" @back="$emit('handleBack')" />
    <!-- 表单 -->
    <DT-Form v-model="formData" :render="formRender" @confirm="handleConfirm">
      <template v-slot:image>
        <DT-Upload
          v-model="formData.thumbnail"
          :on-change="handleChange"
        />
      </template>
    </DT-Form>
  </div>
</template>

<script>
import Request from "@/service"
export default {
  name: "param-form-view",
  props: {
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 表单数据（编辑）
    data: {
      type: Object,
      default: () => {}
    },
    // 服务名称（筛选条件）
    themes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var changeCode = async (rule, value, callback) => {
      const { data } = await Request.dashboard.getDashboardPagesByCode({
        code: value
      })
      if (data && this.title === "新增") {
        callback(new Error("code编码已存在,请重新输入"))
      } else {
        callback()
      }
    }
    return {
      // 表单数据
      formData: {
        config: "{}"
      },
      // 表单渲染配置
      formRender: [
        // 服务名称
        {
          label: "主题",
          type: "select",
          key: "theme",
          option: this.themes,
          props: {
            placeholder: "请选择主题"
          },
          rules: [
            {
              required: true,
              message: "主题不能为空"
            }
          ]
        },
        // 组名称
        {
          label: "编码",
          type: "input",
          key: "code",
          props: {
            placeholder: "请输入编码",
            disabled: this.title === "编辑" ? true : false
          },
          rules: [
            { validator: changeCode, trigger: "blur" },
            {
              required: true,
              message: "编码不能为空"
            }
          ]
        },
        // 键名称
        {
          label: "名称",
          type: "input",
          key: "name",
          props: {
            placeholder: "请输入名称"
          },
          rules: [
            {
              required: true,
              message: "名称不能为空"
            }
          ]
        },
        // 键值
        {
          label: "路由",
          type: "input",
          key: "router",
          props: {
            placeholder: "请输入路由"
          },
          rules: [
            {
              required: true,
              message: "路由不能为空"
            }
          ]
        },
        {
          label: "缩略图",
          type: "slot",
          key: "thumbnail",
          slotName: "image"
        },
        // 备注
        {
          label: "配置",
          type: "input",
          key: "config",
          props: {
            type: "textarea"
          }
        }
      ]
    }
  },
  created() {
    // “编辑”时回显数据
    if (Object.keys(this.data).length > 0) {
      this.formData = { ...this.data }
    }
  },
  methods: {
    handleChange(file) {
      const reader = new FileReader()
      reader.onload = e => {
        // 将文件转换为base64格式
        this.formData.thumbnail = e.target.result
      }
      reader.readAsDataURL(file.raw)
    },
    // 提交
    handleConfirm(form) {
      let fn =
          this.title === "编辑"
            ? Request.dashboard.updateDashboardPage
            : Request.dashboard.addDashboardPage,
        param = {
          ...this.formData,
          ...form,
          dashbordType: 1
        }
      this.$dt_loading.show()
      fn(param)
        .then(() => {
          this.$dt_loading.hide()
          this.$message.success(this.data.id ? "更新成功" : "新增成功")
          this.$emit("handleSuccess")
          this.$emit("handleBack")
        })
        .catch(() => this.$dt_loading.hide())
    }
  }
}
</script>
