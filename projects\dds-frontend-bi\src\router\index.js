import VueRouter from "vue-router"
import Request from "@/service"
import Store from "../store"
import Watermark from "@/utils/watermark"
import { prefix } from "./prefix"
import ThemeAnalysis from "@/views/theme-analysis"

// 路由数据
let routes = []

// 路由数据配置
const routePathArr = require.context("./config", true, /\.js$/)
routePathArr
  .keys()
  .forEach((path) => (routes = [...routes, ...routePathArr(path).default]))

// 注册路由
const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
})
// 动态添加主题路由
function getTheme() {
  return new Promise((resolve, reject) => {
    Request.dashboard
      .getAllThemes()
      .then((res) => {
        var themes = res.data.map((item) => {
          return {
            name: "themeAnalysis" + item.value.toLocaleUpperCase(),
            component: ThemeAnalysis,
            path: prefix + "/themeAnalysis" + item.value.toLocaleUpperCase(),
            meta: {
              title: item.label,
            },
          }
        })
        router.addRoutes(themes)
        resolve() // 异步操作完成，调用resolve()方法
      })
      .catch((error) => {
        reject(error) // 异步操作发生错误，调用reject()方法
      })
  })
}

let isRoutesRegistered = false
// 路由守卫
router.beforeEach(async (to, from, next) => {
  document.title = to.meta.title || "数字桌面"
  if (to.path === "/ddsBi/share") {
    next()
  } else {
    if (to.path.indexOf(process.env.VUE_APP_BASE_NAME) !== -1) {
      const user = Store.state && Store.state.user
      !user && (await Store.dispatch("getUserInfo"))
    }
    const watermarkPage = ["displayEditor", "PortalDetail"]
    if (watermarkPage.includes(from.name)) {
      Watermark.out()
    }
    if (!isRoutesRegistered) {
      getTheme().then(() => {
        isRoutesRegistered = true
        next(to.fullPath)
      })
    } else {
      next()
    }
  }
})

export default router
