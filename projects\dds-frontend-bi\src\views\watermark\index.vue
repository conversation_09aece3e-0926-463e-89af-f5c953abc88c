<template>
  <dt-single-page-view>
    <el-row>
      <el-checkbox
        v-model="form.portal"
      >
        允许每个看板可单独打开或关闭水印
      </el-checkbox>
      <el-tooltip
        class="item"
        effect="dark"
        content="允许每个看板可单独打开或关闭水印：开启该功能，每个看板的配置处出现 是否显示水印 的配置项；关闭该功能，每个看板的配置处隐藏 是否显示水印 的配置项；此项默认勾选"
        placement="top-start"
      >
        <i class="el-icon-info"></i>
      </el-tooltip>
    </el-row>
    <el-row>
      <el-checkbox
        v-model="form.display"
      >
        允许每个大屏可单独打开或关闭水印
      </el-checkbox>
      <el-tooltip
        class="item"
        effect="dark"
        content="允许每个大屏可单独打开或关闭水印：开启该功能，每个大屏的配置处出现 是否显示水印 的配置项；关闭该功能，每个看板的配置处隐藏 是否显示水印 的配置项；此项默认勾选"
        placement="top-start"
      >
        <i class="el-icon-info"></i>
      </el-tooltip>
    </el-row>
    <el-form ref="ruleForm" :model="form" label-width="92px">
      <el-form-item label="文本内容" prop="resource">
        <el-radio-group v-model="form.contentType">
          <el-radio :label="1">自定义值</el-radio>
          <el-radio :label="2">默认字段表达式</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="desc" v-if="form.contentType == 1">
        <el-input
          style="width: 500px"
          v-model="form.customizeText"
          type="textarea"
          maxlength="10"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="" prop="desc" v-else>
        <div class="expression">
          <div class="left">
            <div class="title">字段表达式</div>
            <div class="content">{{ fieldExpression }}</div>
          </div>
          <div class="right">
            <div class="title">点击应用变量</div>
            <div class="content">
              <el-tag
                style="margin-bottom: 10px; cursor: pointer"
                v-for="item in form.options"
                :key="item.value"
                :type="item.enable ? '' : 'info'"
                effect="dark"
                @click="item.enable = !item.enable"
              >
                {{ item.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveConfig()">确定</el-button>
        <el-button>重置</el-button>
      </el-form-item>
    </el-form>
  </dt-single-page-view>
</template>

<script>
import Request from "@/service"

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        portal: true,
        display: true,
        contentType: 1, // 1 自定义 2表达式
        customizeText: "",
        options: [
          { name: "用户名", value: "username", enable: false },
          { name: "当前时间", value: "date", enable: false },
        ],
      },
    }
  },
  computed: {
    fieldExpression() {
      return this.form.options
        .map((item) => {
          if (item.enable) {
            return "#" + item.name + "#"
          } else {
            return ""
          }
        })
        .join("")
    },
  },
  created() {
    this.getConfig()
  },
  mounted() {},
  watch: {},
  methods: {
    getConfig() {
      Request.watermark
        .get()
        .then((res) => {
          this.form = JSON.parse(res.data)
        })
        .catch(() => {})
    },
    saveConfig() {
      Request.watermark
        .upd({ config: JSON.stringify(this.form) })
        .then(() => {
          this.$message.success("操作成功")
          this.$store.dispatch("watermark/getText")
        })
        .catch(() => {
          this.$message.error("操作失败")
        })
    },
  },
}
</script>

<style scoped lang="scss">
.expression {
  display: flex;
  border: 1px solid black;
  width: 600px;
  height: 300px;
  justify-content: space-between;
  padding: 15px;
  box-sizing: border-box;
  background-color: #f2f2f2;

  .left {
    flex: 2;
    display: flex;
    flex-direction: column;

    .content {
      flex: 1;
      background-color: #fff;
    }
  }

  .right {
    flex: 1;
    display: flex;
    margin-left: 20px;
    flex-direction: column;

    .content {
      flex: 1;
      background-color: #fff;
      display: flex;
      padding: 15px 30px;
      box-sizing: border-box;
      flex-direction: column;
    }
  }
}
</style>
