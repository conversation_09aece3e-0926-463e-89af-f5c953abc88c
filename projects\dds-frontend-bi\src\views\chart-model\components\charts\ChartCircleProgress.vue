<template>
  <div class="chart-circle-progress" :style="{ width, height }">
    <div class="progress-container">
      <svg :width="svgSize" :height="svgSize" class="progress-svg">
        <!-- 背景圆环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="radius"
          stroke="#f0f0f0"
          stroke-width="8"
          fill="none"
          class="progress-bg"
        />

        <!-- 进度圆环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="radius"
          stroke="#52c41a"
          stroke-width="8"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashoffset"
          stroke-linecap="round"
          fill="none"
          class="progress-bar"
          :class="{ animate: animation }"
        />
      </svg>

      <!-- 中心内容 -->
      <div class="progress-content" :style="contentStyle">
        <div class="progress-value">
          {{ value }}
        </div>
        <div v-if="label" class="progress-label">
          {{ label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChartCircleProgress",
  props: {
    // 基础属性
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "200px"
    },
    // 三个核心参数
    label: {
      type: String,
      default: ""
    },
    value: {
      type: Number,
      default: 0
    },
    data: {
      type: Number,
      default: 0
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentData: 0,
      animationTimer: null
    }
  },
  computed: {
    svgSize() {
      return 120 // 固定尺寸
    },
    center() {
      return 60 // 固定中心点
    },
    radius() {
      return 52 // 固定半径
    },
    circumference() {
      return 2 * Math.PI * this.radius
    },
    percentage() {
      return Math.min(Math.max(this.currentData, 0), 100)
    },
    strokeDashoffset() {
      return this.circumference - (this.percentage / 100) * this.circumference
    },
    contentStyle() {
      return {
        width: "120px",
        height: "120px"
      }
    }
  },
  watch: {
    data: {
      handler(newVal) {
        if (this.animation) {
          this.animateValue(newVal)
        } else {
          this.currentData = newVal
        }
      },
      immediate: true
    }
  },
  beforeUnmount() {
    if (this.animationTimer) {
      cancelAnimationFrame(this.animationTimer)
    }
  },
  methods: {
    animateValue(targetValue) {
      if (this.animationTimer) {
        cancelAnimationFrame(this.animationTimer)
      }

      const startValue = this.currentData
      const endValue = Number(targetValue)
      const duration = 1000 // 固定动画时长
      const startTime = Date.now()

      const animate = () => {
        const now = Date.now()
        const progress = Math.min((now - startTime) / duration, 1)

        // 使用缓动函数
        const easeProgress = this.easeOutCubic(progress)
        this.currentData = startValue + (endValue - startValue) * easeProgress

        if (progress < 1) {
          this.animationTimer = requestAnimationFrame(animate)
        } else {
          this.currentData = endValue
          this.animationTimer = null
        }
      }

      this.animationTimer = requestAnimationFrame(animate)
    },
    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3)
    },
    // 公共方法
    updateValue(newValue) {
      if (this.animation) {
        this.animateValue(newValue)
      } else {
        this.currentData = newValue
      }
    },
    getValue() {
      return this.currentData
    },
    getPercentage() {
      return this.percentage
    },
    reset() {
      this.currentData = 0
    }
  }
}
</script>

<style scoped>
.chart-circle-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-container {
  position: relative;
  display: inline-block;
}

.progress-svg {
  transform: rotate(-90deg);
  overflow: visible;
}

.progress-bg {
  opacity: 0.3;
}

/* .progress-bar {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-bar.animate {
  animation: progressAnimation 2s ease-out;
} */

.progress-content {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  pointer-events: none;
}

.progress-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  line-height: 1;
}

.progress-label {
  font-size: 14px;
  color: #666666;
  margin-top: 4px;
}

.progress-description {
  font-size: 12px;
  color: #999999;
  margin-top: 2px;
}

/* 动画效果 */
@keyframes progressAnimation {
  0% {
    stroke-dashoffset: 377; /* 2 * Math.PI * 60 (假设半径为60) */
  }
  100% {
    stroke-dashoffset: var(--final-offset);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-value {
    font-size: 20px;
  }

  .progress-label {
    font-size: 12px;
  }

  .progress-description {
    font-size: 10px;
  }
}

/* 状态颜色 */
.progress-bar.success {
  stroke: #52c41a;
}

.progress-bar.warning {
  stroke: #faad14;
}

.progress-bar.error {
  stroke: #f5222d;
}

.progress-bar.info {
  stroke: #1890ff;
}

/* 发光效果 */
.progress-bar.glow {
  filter: drop-shadow(0 0 6px currentColor);
}

/* 脉冲效果 */
.progress-bar.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
