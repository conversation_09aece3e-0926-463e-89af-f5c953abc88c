<template>
  <div :style="{ height, width }" class="chart-wrap">
    <div id="myChat" ref="chartRef"></div>
    <ChartEmpty v-if="!chart" />
  </div>
</template>

<script>
import * as echarts from "echarts"
import resize from "@/mixins/chartResize"
import { toThousands } from "@/utils/index.js"
import ChartEmpty from "./ChartEmpty.vue"
export default {
  components: { ChartEmpty },
  mixins: [resize],
  props: {
    // 图表宽度
    width: {
      type: String,
      default: "100%"
    },
    // 图表高度
    height: {
      type: String,
      default: "100%"
    },
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // X轴字段
    xField: {
      type: String,
      default: "name"
    },
    // Y轴字段
    yField: {
      type: String || Array,
      default: []
    },
    // Y轴单位
    yAxisName: {
      type: String,
      default: ""
    },
    // 维度名称
    seriesName: {
      type: String || Array || null,
      default: null
    },
    // 是否格式化X轴label
    isFormatterXAxis: {
      type: Boolean,
      default: false
    },
    // 颜色
    color: {
      type: Array,
      default: () => [
        "#2361DB",
        "#0EACCC",
        "#1DB35B",
        "#FFC508",
        "#FF742E",
        "#F5427E",
        "#AA51D6",
        "#77D2E5"
      ]
    },
    // 柱状图宽
    barWidth: {
      type: Number,
      default: 16
    },
    // 单位
    unit: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {},
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart()
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef)
      }
      this.renderChart()
    },
    renderChart() {
      if (!this.chartData || this.chartData.length === 0) {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
          return
        }
      }
      const series = []
      const legendData = []
      this.seriesName.forEach((item, index) => {
        series.push({
          name: item,
          type: "bar",
          barWidth: 16, // 设置柱状图宽度
          xAxisIndex: index,
          yAxisIndex: index,
          itemStyle: {
            color: this.color[index]
          },
          label: {
            show: index ? true : false,
            position: "left",
            color: "#fff",
            fontSize: 12,
            formatter: "{b}"
          },
          data: this.chartData.map(item => item[this.xField[index]])
        })
        legendData.push({
          name: item,
          icon: "rect",
          itemStyle: {
            color: this.color[index]
          }
        })
      })
      console.log(series, "22222222222222222222222222222222")

      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: { show: false, backgroundColor: "transparent" },
            shadowStyle: {
              color: "rgba(35,97,219,0.05)"
            }
          },
          formatter: params => {
            console.log(params)
            return `<div>
            <p class="tooltip-title">${params[0].name}</p>
            ${this.tooltipItemsHtmlString(params)}
          </div>`
          },
          className: "echarts-tooltip-diy"
        },
        title: {},
        legend: {
          show: true,
          right: 0,
          top: 0,
          icon: "rect",
          itemWidth: 6,
          itemHeight: 6,
          itemGap: 20,
          orient: "horizontal",
          // color: this.color,
          textStyle: {
            color: "#646566",
            fontSize: 12
          },
          data: legendData
        },
        xAxis: [
          {
            type: "value",
            inverse: true,
            interval: 1,
            gridIndex: 0,
            splitLine: {
              lineStyle: {
                color: "#EDEFF0",
                type: "dashed"
              }
            },
            axisLabel: {
              fontSize: 12,
              color: "#999",
              fontWeight: "400",
              interval: 0,
              rotate: 0, // 倾斜角度

              formatter: name => {
                return name.length > 6 ? name.substr(0, 6) + "..." : name
              }
            }
          },
          {
            type: "value",
            gridIndex: 1,
            interval: 1,

            splitLine: {
              lineStyle: {
                color: "#EDEFF0",
                type: "dashed"
              }
            }
          },
          {
            type: "value",
            gridIndex: 2,
            interval: 1,

            splitLine: {
              show: false
            },

            axisTick: {
              show: false
            },

            axisLabel: {
              fontSize: 12,
              color: "#999",
              fontWeight: "400",
              interval: 0,
              rotate: 0, // 倾斜角度

              formatter: name => {
                return name.length > 6 ? name.substr(0, 6) + "..." : name
              }
            }
          }
        ],
        graphic: [
          {
            type: "text",
            top: 5,
            left: 0,
            style: {
              text: this.yAxisName,
              fill: "#969799",
              stroke: "#969799",
              fontSize: 12,
              fontFamily: "PingFangSC-Regular, PingFang SC"
            }
          }
        ],
        grid: [
          {
            left: "5%",
            right: "53%",
            top: 40,
            bottom: 0,
            containLabel: true
          },
          {
            left: "47%",
            right: "5%",
            top: 40,
            bottom: 0,
            containLabel: true
          },
          {
            left: "0%",
            right: "0%",
            top: 0,
            bottom: "0"
          }
        ],
        yAxis: [
          {
            type: "category",
            gridIndex: 0,
            inverse: true,

            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false,
              align: "right",
              fontSize: 12,
              color: "#8294A2",
              fontWeight: "400"
            },
            data: this.chartData.map(item => item[this.yField])
          },
          {
            type: "category",
            gridIndex: 1,
            inverse: true,

            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false,

              fontSize: 12,
              color: "#8294A2",
              fontWeight: "400",
              z: 6
            },
            data: this.chartData.map(item => item[this.yField])
          },
          {
            type: "category",
            gridIndex: 2,
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            inverse: true
          }
        ],
        series: series
      })
    },
    tooltipItemsHtmlString(items) {
      console.log(items)
      return items
        .map(
          el => `<div class="content-panel">
        <p >
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${toThousands(el.value)}${this.unit}
        </span>
      </div>`
        )
        .join("")
    }
  },
  beforeDestroy() {
    if (!this.Chart) {
      return false
    }
    this.Chart.dispose()
    this.Chart = null
  }
}
</script>
<style scoped lang="scss">
.chart-wrap {
  position: relative;
  #myChat {
    width: 100%;
    height: 100%;
  }
}
</style>
