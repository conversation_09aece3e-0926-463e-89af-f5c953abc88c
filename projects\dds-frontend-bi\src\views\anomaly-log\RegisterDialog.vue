<template>
  <el-dialog
    title="登记处理结果"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    append-to-body
    width="750px"
    :close-on-click-modal="false"
  >
    <el-form
      :model="ruleForm"
      status-icon
      :rules="rules"
      ref="ruleForm"
      label-width="120px"
      class="ruleForm"
    >
      <el-form-item label="登记对象:" prop="djdx">
        {{ ruleForm.title }}
      </el-form-item>
      <el-form-item label="异常时间:" prop="djdx">
        {{ ruleForm.alertTime }}
      </el-form-item>
      <el-form-item label="处理状态:" prop="processStatus">
        <el-select
          v-model="ruleForm.processStatus"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="异常原因:" prop="alertReason">
        <el-select
          v-model="ruleForm.alertReason"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="item in reasonOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input
          type="textarea"
          :rows="5"
          placeholder="请输入内容"
          v-model="ruleForm.remark"
        ></el-input>
      </el-form-item>
      <el-form-item label="附件:">
        <DT-Upload v-model="ruleForm.attachment" :on-change="handleChange" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="onSaveWarning">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
export default {
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      ruleForm: {},
      // 处理状态选项
      statusOptions: [
        {
          label: "暂未处理",
          value: "暂未处理"
        },
        {
          label: "已修复",
          value: "已修复"
        },
        {
          label: "无需处理",
          value: "无需处理"
        },
        {
          label: "数据链路正常后自动恢复",
          value: "数据链路正常后自动恢复"
        },
        {
          label: "排查中，问题尚不明确",
          value: "排查中，问题尚不明确"
        }
      ],
      // 异常原因选项
      reasonOptions: [
        {
          label: "计算规则错误",
          value: "计算规则错误"
        },
        {
          label: "数据源数据缺失",
          value: "数据源数据缺失"
        },
        {
          label: "指标正常波动",
          value: "指标正常波动"
        },
        {
          label: "服务器磁盘已满",
          value: "服务器磁盘已满"
        },
        {
          label: "服务器故障",
          value: "服务器故障"
        },
        {
          label: "停电",
          value: "停电"
        },
        {
          label: "服务器重启",
          value: "服务器重启 "
        }
      ],
      rules: {
        processStatus: [
          { required: true, message: "请选择处理状态", trigger: "blur" }
        ],
        alertReason: [
          { required: true, message: "请选择异常原因", trigger: "blur" }
        ]
      },
      ids: []
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    async open(list) {
      console.log(list, "list")
      this.dialogVisible = true
      this.ids = list.map(item => item.id)
      this.ruleForm = {
        title:
          list[0].appLocation +
          list[0].indName +
          (list.length > 1 ? `等${list.length}项异常记录` : ""),
        alertTime: list[0].alertTime,
        processStatus: "",
        alertReason: "",
        remark: "",
        attachment: ""
      }
    },
    handleChange() {
      console.log(this.ruleForm)
    },
    onSaveWarning() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          await Request.api.paramPost("zeroWarn/saveRegistration", {
            ids: this.ids,
            processStatus: this.ruleForm.processStatus,
            alertReason: this.ruleForm.alertReason,
            remark: this.ruleForm.remark,
            attachment: this.ruleForm.attachment
          })
          this.$message({
            message: "保存成功",
            type: "success"
          })
          this.dialogVisible = false
          this.$emit("onLoad")
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
