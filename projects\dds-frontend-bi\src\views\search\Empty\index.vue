<template>
  <div class="wrap"> 
    <p>没有搜索到相关的结果</p>
    <div class="empty-img"></div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.wrap{
  position: relative;
}
p {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
}
.empty-img {
     width: 160px;
    height: 160px;
    background: url(http://127.0.0.1:13582/img/search.png) no-repeat;
    background-size: cover;
    margin: 0 auto;
}
</style>
