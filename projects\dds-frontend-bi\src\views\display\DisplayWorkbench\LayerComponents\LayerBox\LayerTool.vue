<template>
  <div class="display-slide-layer-tools">
    <el-tooltip class="item" effect="dark" content="编辑" placement="top">
      <i class="el-icon-edit" @click="handleEdit"></i>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    id: {
      type: Number
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleEdit() {
      this.$router.push(`/ddsBi/Workbench?isFullPage=true&id=${this.id}`)
    }
  }
}
</script>

<style scoped lang="scss">
.display-slide-layer-tools {
  position: absolute;
  top: -25px;
  right: 0;
  cursor: pointer;
  z-index: 9999;
  font-size: 24px;
  display: none;
}
</style>
