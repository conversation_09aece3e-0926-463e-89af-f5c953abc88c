<template>
  <Fold name="大屏驾驶舱">
    <div class="content">
      <CardItem
        :type="1"
        v-for="(item, index) in displays"
        :info="item"
        :key="index"
      />
    </div>
  </Fold>
</template>

<script>
import Fold from "./Fold.vue"
import CardItem from "./CardItem.vue"
export default {
  components: { CardItem, Fold },
  props: {
    displays: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {
    console.log(this.displays, "theme")
  },
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.content {
  background-color: #fff;
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(357px, 1fr));
  width: 100%;
  box-sizing: border-box;
  margin-top: 8px;
}
</style>
