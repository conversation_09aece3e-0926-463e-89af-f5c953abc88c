<template>
  <div class="display-slide-layer-tooltips" v-if="isActive">
    x:{{ positionX }}px,y:{{ positionY }}px
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    isActive: {
      type: Boolean,
      default: false,
    },
    positionX: { type: Number, default: 0 },
    positionY: { type: Number, default: 0 },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.display-slide-layer-tooltips {
  position: absolute;
  top: -40px;
  left: 0;
  z-index: 999998;
  font-size: 24px;
  color: #fff;
  background-color: #000;
  padding: 0 8px;
  border-radius: 4px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
