import Vue from 'vue'
import VueI18n from 'vue-i18n'

// 国际化数据
const messages = {}

// 国际化数据处理方法
const setMessage = (parent, pathArr, index, langData) => {
  let _pathStr = pathArr[index]
  if (_pathStr.indexOf('.js') !== -1) {
    parent[_pathStr.replace('.js', '')] = langData
  } else {
    if (!parent[_pathStr]) {
      parent[_pathStr] = {}
    }
    setMessage(parent[_pathStr], pathArr, index + 1, langData)
  }
}

// 国际化语言数据配置 - 基础通用
const lang_base = require.context("../../../../common/lang", true, /\.js$/)
lang_base.keys().forEach(path => {
  const match = path.split('/')
  match && setMessage(messages, match, 1, lang_base(path).default)
})

// 国际化语言数据配置 - 项目自身
const lang_self = require.context("./lang", true, /\.js$/)
lang_self.keys().forEach(path => {
  const match = path.split('/')
  match && setMessage(messages, match, 1, lang_self(path).default)
})

// 输出
export default new VueI18n({
  locale: Vue.prototype.$utils.language.get(messages),
  messages
})