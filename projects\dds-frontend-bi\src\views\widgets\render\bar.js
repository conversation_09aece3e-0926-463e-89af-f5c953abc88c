import { DEFAULT_SPLITER } from "@/globalConstants"
import { decodeMetricName, getChartTooltipLabel } from "../component/util.js"
import {
  getDimetionAxisOption,
  getMetricAxisOption,
  getLabelOption,
  getLegendOption,
  makeGrouped,
  getGroupedXaxis,
  getCartesianChartReferenceOptions
} from "./util"

export default function (chartProps) {
  const { data, cols, chartStyles, color, metrics, references } = chartProps

  // const metrics = getCartesianChartMetrics(chartProps.metrics)
  const { xAxis, yAxis, splitLine, label, legend, bar, grid } = chartStyles
  const {
    showVerticalLine,
    verticalLineColor,
    verticalLineSize,
    verticalLineStyle,
    showHorizontalLine,
    horizontalLineColor,
    horizontalLineSize,
    horizontalLineStyle
  } = splitLine

  const labelOption = {
    label: getLabelOption("bar", label, metrics)
  }
  const xAxisColumnName = cols[0].displayName
  let xAxisData = []
  let grouped = {}
  let percentGrouped = {}
  const referenceOptions = getCartesianChartReferenceOptions(
    references,
    "bar",
    metrics,
    data,
    bar.barChart
  )

  if (color.length) {
    xAxisData = getGroupedXaxis(data, xAxisColumnName, metrics)
    grouped = makeGrouped(
      data,
      color.map(c => c.displayName),
      xAxisColumnName,
      metrics,
      xAxisData
    )
    const configValue = color[0].values
    const configKeys = []
    Object.entries(configValue).forEach(([, v]) => {
      configKeys.push(v.name)
    })
    percentGrouped = makeGrouped(
      data,
      cols.map(c => c.displayName),
      color[0].displayName,
      metrics,
      configKeys
    )
  } else {
    xAxisData = data.map(d => d[xAxisColumnName] || "")
  }
  const series = []
  const seriesData = []
  const sumArr = []
  metrics.forEach((m, i) => {
    if (color.length) {
      Object.entries(percentGrouped).forEach(([, v]) => {
        sumArr.push(getColorDataSum(v, metrics))
      })
      const groupedEntries = Object.entries(grouped)
      groupedEntries.forEach(([k, v], gIndex) => {
        const serieObj = {
          id: `${m.name}${DEFAULT_SPLITER}${DEFAULT_SPLITER}${k}`,
          name: `${k}${
            metrics.length > 1
              ? m.field.alias
                ? ` ${m.field.alias}`
                : `${m.displayName}`
              : ""
          }`,
          type: "bar",
          sampling: "average",
          data: v.map((g, index) => {
            return bar.percentage
              ? (
                  (g[`${m.agg}(${m.displayName})`] / sumArr[index]) *
                  100
                ).toFixed(2)
              : g[`${m.agg}(${m.displayName})`]
          }),
          // color: color[0].values[gIndex]?.color,
          stack: bar.stack ? "total" : "",
          itemStyle: {
            borderWidth: bar.border.width,
            borderRadius: bar.border.radius,
            borderColor: bar.border.color,
            borderType: bar.border.type
          },
          barWidth: bar.width,
          barCategoryGap: bar.gap,
          ...labelOption,
          ...(gIndex === groupedEntries.length - 1 &&
            i === metrics.length - 1 &&
            referenceOptions)
        }
        series.push(serieObj)
        seriesData.push(grouped[k])
      })
    } else {
      const serieObj = {
        id: m.name,

        type: "bar",
        itemStyle: {
          borderWidth: bar.border.width,
          borderRadius: bar.border.radius,
          borderType: bar.border.type
        },
        name: m.field.alias ?? m.displayName,
        stack: bar.stack ? "total" : "",
        barWidth: bar.width,
        barCategoryGap: bar.gap,
        // color: m.color ?? defaultThemeColors[i],
        ...labelOption,
        data: data.map((g, index) => {
          return bar.percentage
            ? ((g[`${m.agg}(${m.displayName})`] / sumArr[index]) * 100).toFixed(
                2
              )
            : g[`${m.agg}(${m.displayName})`]
        }),
        ...(i === metrics.length - 1 && referenceOptions)
      }
      series.push(serieObj)
      seriesData.push([...data])
    }
  })
  const seriesNames = series.map(s => s.name)

  const xAxisSplitLineConfig = {
    showLine: showVerticalLine,
    lineColor: verticalLineColor,
    lineSize: verticalLineSize,
    lineStyle: verticalLineStyle
  }

  const yAxisSplitLineConfig = {
    showLine: showHorizontalLine,
    lineColor: horizontalLineColor,
    lineSize: horizontalLineSize,
    lineStyle: horizontalLineStyle
  }
  let xAxisConfig = getDimetionAxisOption(
    xAxis,
    xAxisSplitLineConfig,
    xAxisData
  )
  let yAxisConfig = getMetricAxisOption(
    yAxis,
    yAxisSplitLineConfig,
    metrics.map(m => decodeMetricName(m.name)).join(` / `),
    "x",
    bar.percentage
  )
  const options = {
    xAxis: bar.barChart ? yAxisConfig : xAxisConfig,
    yAxis: bar.barChart ? xAxisConfig : yAxisConfig,
    series,
    legend: getLegendOption(legend, seriesNames),
    // grid: bar.showDataZoom
    //   ? {
    //     ...getGridPositions(
    //       legend,
    //       seriesNames,
    //       "",
    //       false,
    //       yAxis,
    //       xAxis,
    //       xAxisData
    //     ),
    //     bottom: 90,
    //   }
    //   : getGridPositions(
    //     legend,
    //     seriesNames,
    //     "",
    //     false,
    //     yAxis,
    //     xAxis,
    //     xAxisData
    //   ),
    grid,
    tooltip: {
      // trigger: "axis",
      confine: true,
      // ...TOOLTIP_STYLE,
      formatter: getChartTooltipLabel("bar", seriesData, {
        cols,
        metrics,
        color
      })
    },
    dataZoom: [
      {
        show: bar.showDataZoom,
        endValue: bar.endValue && bar.showDataZoom ? bar.endValue : null // 初始化滚动条
        // backgroundColor: "#F0F2F5",
        // type: "slider",
        // handleIcon:
        //   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAxFJREFUSEu1lV9IU1Ecx3/n7o9ru4TzBjLxz2Z3GivHWGLsQdIehIQZQkNkBfXay4ReLLEIs/kSbC+91kNDZAONgYFQLnwYyRhT15R5cxNlQ9hlEnftj7obx1yozWi13cdzf+d8fn/O+X4RnP2hxcVFlUwmGyAIohshpEEIXcDhPM8neJ4P5fP5+VQqNdXR0RHBy8WOQkUW0cLCgoqiqBdisfg2SZICmUwGVVVVIBQKD8P39/chm81CKpUCjuMOcrmci2XZx52dnb+BTgOIQCBglkqlr2pqaki5XA4EQfyhSIB8Pg/JZBJYluXS6fQDnU7nAIB8YdNxgGB5efkhSZITdXV1CGdcyocrisViPMdxw1qt9iUAHOD9BQDh9/vvVFdXv2loaECFVpQCKLRua2uL393dvafX69/iSjAAud1uurW11d/Y2EiWmvnpJHAlm5ubXDgc1huNRgYDRCsrKw6FQmGiKKrUpIvGsywL8Xjc2dbWZkYul+uSTqcLqlQqwVkD9Xq9YDAYThxWbK0QgAcfiUQOAoHAFeTz+Z7U19c/q62tPTN7j8cDXV1dJ/4XWzsesLOzA9vb209RMBj80NTUdIMkybICOI7Ds/iIQqFQjKZphUgkKitgb28PGIaJo7W1tWxLS4sYoWKP+ifzX1rE8zyEw+Fc5QEVb9HS0pKnubn5eiWGvLGx8Qkr53O1Wj1SiWu6vr4+jqxW61WTyfS5Eg/N6XRew1dH7vP5XiuVylvllIpoNPquvb39PgZIR0dHL5vN5nmlUikrh9hFo9GUw+HoHhsb+3IodgBAzszM3NVqtbZyyHUwGLT09fVhueYO5RoAsLucn5ubs9A0/eh/DIdhGGtPT48dAL4BQPaX4QCABEOmp6cHNBrNOEVRslIsM5FIpFZXV0f6+/unjg7PFAynoEGCo0rIoaEh9eDg4DBFUTf/xvRZln0/OTk5YbPZ1nFbcOanLbMAwQ4vBoBzePgWi+Vib2+vkaIog0QioYVCofzIGpOZTIZhWdY7OzvrttvtXwHgOwCkASB33PR/AK8MqDBvjq1bAAAAAElFTkSuQmCC",
        // moveHandleSize: 0,
        // showDataShadow: false,
        // fillerColor: "#CED4D9",
        // handleStyle: {
        //   color: "#63659F",
        // },
        // height: 18,
        // xAxisIndex: [0],
        // startValue: 0,
        // bottom: 0,
        // borderColor: "transparent",
        // brushSelect: false,
        // textStyle: {
        //   color: "rgba(0, 0, 0, 0.65)",
        //   fontWeight: "400",
        //   fontSize: 12,
        //   fontFamily: "PingFangSC-Regular, PingFang SC",
        //   lineHeight: 18,
        // },
      }
    ]
  }

  return options
}

export function getColorDataSum(data, metrics) {
  let maSum = 0
  const dataSum = data.map(d => {
    let metricArr = 0
    metrics.forEach(m => {
      const metricName = d[`${m.agg}(${m.displayName})`]
      metricArr += metricName
    })
    return metricArr
  })
  dataSum.forEach(mr => {
    maSum += mr
  })
  return maSum
}
