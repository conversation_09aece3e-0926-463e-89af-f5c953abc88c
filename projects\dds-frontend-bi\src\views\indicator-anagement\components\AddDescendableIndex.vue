<template>
  <DT-View
    :inner-style="{
      padding: 0
    }"
  >
    <div class="title">创建衍生指标</div>
    <div class="content">
      <!-- 指标域 -->
      <div class="pointer-field">
        <div class="tabs">
          <div
            class="tab-item"
            :class="{ active: dataType === index }"
            v-for="(tab, index) in tabs"
            :key="index"
            @click="dataType = index"
          >
            {{ tab }}
          </div>
        </div>
        <el-input
          style="width: 204px; margin: 0 12px"
          placeholder="请输入指标名称"
          v-model="filterText"
          prefix-icon="el-icon-search"
        ></el-input>
        <el-tree
          class="model-tree"
          ref="treeModel"
          draggable
          :data="dataType ? tabTreeData : treeData"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-drag-start="handleTreeDragStart"
          @node-drag-end="handleTreeDragEnd"
        ></el-tree>
      </div>
      <!--拖拽容器  -->
      <div class="drag-wrap" id="droppable">
        <!-- 新增按钮 -->
        <div class="add-btn">
          <el-button
            type="primary"
            :disabled="derivatives.length == 3"
            @click="handleAdd"
            icon="el-icon-plus"
          >
            新增指标
          </el-button>
        </div>
        <!-- 删除区域 -->
        <div
          class="deletes-region drag"
          v-show="isDragging"
          @dragover.prevent
          @drop="handleDeleteDragEnd"
        >
          <div class="icon"></div>
          <div class="text">删除区</div>
        </div>

        <!-- 拖拽区域 -->
        <div v-for="(item, i) in derivatives" :key="i">
          <div class="thumbnail">
            <div class="number">
              {{ i + 1 }}
            </div>
            <div class="steps">
              <div class="text">缩略图：</div>
              <div
                class="step-item"
                :class="{
                  active: item.tempIndex === -1
                }"
              ></div>

              <div class="symbol">=</div>
              <div
                class="indicators"
                v-for="(e, index) in item.indicators"
                :key="index"
              >
                <div
                  class="step-item"
                  :class="{
                    active: index === item.tempIndex
                  }"
                ></div>
                <div
                  class="dot"
                  v-if="index !== item.indicators.length - 1"
                  :class="{
                    isArithmetic: e.operator
                  }"
                ></div>
              </div>
            </div>
          </div>
          <div
            class="drag-region"
            :class="{ active: i === currentIndex }"
            @click="currentIndex = i"
          >
            <!-- <div class="no" style="margin-right: 24px">
           
          </div> -->

            <input
              type="text"
              class="result"
              :class="{ active: item.tempIndex === -1 }"
              v-model="item.zbmc"
              placeholder="请命名"
            />
            <div class="equal-sign"></div>
            <!-- 指标区域 -->
            <div
              class="indicator-region"
              :class="{
                dragging: isDragging
              }"
              @drop="handleDrop($event, 0, i)"
              @dragover.prevent
              v-if="!item.indicators.length"
            >
              <div class="icon"></div>

              请将指标拖到此处
            </div>
            <!-- 已拖拽指标 -->
            <div class="indicators" v-else>
              <template>
                <div
                  v-for="(idr, index) in item.indicators"
                  :key="index"
                  class="list-item-custom"
                  @dragend="handleDragEnd"
                >
                  <!-- <BracketItem
                  v-if="idr.direction == 'left'"
                  @handleDrop="handleDrop"
                  :type="idr.direction"
                  :index="index"
                  :is-dragging="isDragging"
                /> -->
                  <DragIndicatorItem
                    @handleDrop="handleDrop"
                    v-if="idr.type === 1"
                    :index="index"
                    :temp-index="item.tempIndex"
                    :parent-index="i"
                    :item-prop="idr"
                    @handleDragStart="handleDragStart"
                    @addWd="addWd"
                    :prev-item-prop="item.indicators[index - 1]"
                    :next-item-prop="item.indicators[index + 1]"
                    :is-last-indicator="lastIndicatorIndex(i) === index"
                    :is-dragging="isDragging"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 底部区域-->
        <div class="tools">
          <!-- 特殊计算符号 -->
          <div class="special-symbol">
            <div class="text">特殊计算符</div>
            <div
              class="bracket"
              :class="{
                draging: bracketDraging,
                dragstart: bracketDragstart
              }"
            >
              <div
                class="left"
                draggable="true"
                @dragstart="bracketDragStart($event, 'left')"
                @drag="bracketDraging = true"
                @dragend="handleDragEnd"
              >
                (
              </div>
              <div
                class="right"
                draggable="true"
                @drag="bracketDraging = true"
                @dragstart="bracketDragStart($event, 'right')"
                @dragend="handleDragEnd"
              >
                )
              </div>
            </div>
          </div>
          <!-- . -->
          <div class="btns">
            <el-button @click="$router.go(-1)">取消</el-button>
            <el-button @click="getCalculateResult">试计算</el-button>
            <el-button type="primary" @click="handleSave">保存</el-button>
          </div>
        </div>
      </div>
      <!-- 基础属性 -->
      <div class="base-property">
        <div class="base-title">
          基础属性
          <!-- {{ derivatives[currentIndex].zbmc }} -->
        </div>
        <el-form
          ref="form"
          :model="derivatives[currentIndex]"
          :rules="rules"
          label-width="80px"
          class="form"
          label-position="top"
        >
          <el-form-item label="计算周期" prop="jszq">
            <el-select
              v-model="derivatives[currentIndex].jszq"
              placeholder="请选择计算周期"
            >
              <el-option
                v-for="(item, index) in jszqList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="精度" prop="jd">
            <div style="display: flex; justify-content: space-between">
              <el-input
                style="width: 112px"
                v-model="derivatives[currentIndex].jd"
              ></el-input>
              <el-checkbox
                v-model="derivatives[currentIndex].sswr"
                true-label="1"
                false-label="0"
              >
                四舍五入
              </el-checkbox>
            </div>
          </el-form-item>
          <el-form-item label="组织域">
            <avue-input-tree
              default-expand-all
              v-model="derivatives[currentIndex].sysjy"
              :props="{
                label: 'name',
                value: 'id'
              }"
              placeholder="请选择内容"
              :dic="viewGroup"
            ></avue-input-tree>
          </el-form-item>
          <el-form-item label="数仓分层" prop="sjfc">
            <el-select
              v-model="derivatives[currentIndex].sjfc"
              placeholder="请选择数仓分层"
            >
              <el-option
                v-for="(item, index) in sjfcList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设置阈值">
            <div style="display: flex; justify-content: space-between">
              <el-input
                v-model="derivatives[currentIndex].zxz"
                placeholder="最小值"
                class="yz-input"
                style="width: 87px"
              ></el-input>
              -
              <el-input
                v-model="derivatives[currentIndex].zdz"
                placeholder="最大值"
                class="yz-input"
                style="width: 87px"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="单位" prop="dw">
            <div style="display: flex">
              <el-select
                v-model="derivatives[currentIndex].dw"
                placeholder="请选择单位"
                :style="{
                  width:
                    derivatives[currentIndex].dw === '其他' ? '87px' : '100%'
                }"
                class="myselect"
              >
                <el-option
                  v-for="(item, index) in dwList"
                  :key="index"
                  :label="item.name"
                  :value="item.bm"
                ></el-option>
              </el-select>
              <el-input
                v-if="derivatives[currentIndex].dw === '其他'"
                v-model="derivatives[currentIndex].customUnit"
                style="width: 90px; margin-left: 6px"
                placeholder="单位"
                class="yz-input"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="derivatives[currentIndex].ms"></el-input>
          </el-form-item>

          <el-form-item label="标签" prop="bq">
            <el-select
              v-model="derivatives[currentIndex].bq"
              filterable
              remote
              multiple
              allow-create
              default-first-option
              @change="changeTag"
              @remove-tag="removeTag"
              :remote-method="remoteMethod"
              placeholder="请创建或者选择标签"
            >
              <el-option
                v-for="item in formatLabels"
                :key="item.bqmc"
                :label="item.bqmc"
                :value="item.bqmc"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-dialog title="试计算结果" :visible.sync="dialogTableVisible">
      <el-table :data="tableData.list">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          v-for="(item, index) in tableData.head"
          :key="index"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </DT-View>
</template>

<script>
import DragIndicatorItem from "./DragIndicatorItem"
// import BracketItem from "./BracketItem"
import options from "../mixins/options"
export default {
  components: { DragIndicatorItem },
  mixins: [options],
  props: {},
  computed: {
    // 当前派生维度
    currentDerive() {
      if (!this.yzzbList.length) return []
      return (
        this.yzzbList[0] || {
          pswd: []
        }
      )
    },
    formatLabels() {
      return this.labels.filter(
        item => item.bqmc.includes(this.newTag) || !this.newTag
      )
    },
    // 排除特殊字符的最后指标的下标

    treeData() {
      const copiedArr = this.data.map(obj => ({ ...obj })) // 创建数组的副本
      copiedArr.forEach(obj => {
        this.replaceFieldRecursive(obj)
      })
      console.log(this.data, "this.data")
      return copiedArr
    },
    // 指标集合去除特殊字符
    cleanIndicator() {
      return this.indicators.filter(item => item.type !== 1)
    }
  },
  data() {
    return {
      // 选中衍生指标的INDEX
      currentIndex: 0,
      yzzbList: [],
      currentZb: {},
      pswdOptionsMap: {},

      xsc: [],
      dataType: 1, // 0按指标域 1 按标签
      tabs: ["按指标域展示", "按标签展示"],
      labels: [],
      newTag: "", // 新建标签
      tempTag: "", // 临时存储标签
      tableData: {
        head: [],
        list: []
      },
      dialogTableVisible: false,
      isIndicatorsDrag: false, // 是否是指标内的拖拽
      source_node: "",
      isDragging: false, // 是否是拖拽中
      indicators: [],
      currentParentIndex: 0,
      derivatives: [
        {
          zbmc: "", // 指标名称
          zblx: "衍生指标", // 指标类型
          lxbm: "ys", // 类型编码
          sysjy: 0, // 组织
          jszq: "", // 计算周期
          jd: 0, // 精度
          sswr: "", // 四舍五入
          ms: "", // 描述
          bq: [], // 标签
          sjfc: 5,
          tempIndex: -1,
          selfDimension: {},
          indicators: []
        }
      ],
      form: {},
      rules: {
        zbmc: [
          {
            required: true,
            message: "请输入指标名称",
            trigger: "change"
          },
          { max: 20, message: "最大为20个字符", trigger: "change" }
        ],
        atomid: {
          required: true,
          message: "请选择原子指标",
          trigger: "change"
        },
        jsfs: {
          required: true,
          message: "请选择计算方式",
          trigger: "change"
        },
        jszq: {
          required: true,
          message: "请选择计算周期",
          trigger: "change"
        },
        jd: { required: true, message: "请输入精度", trigger: "change" }
      },
      filterText: "",
      defaultProps: {
        children: "children",
        label: "name"
      },
      currentId: "",
      commonDimensionList: [],
      dimensionOptions: {},
      data: [],
      viewGroup: [{ id: 0, name: "根目录", children: [] }], // 数据域分组
      tabTreeData: [
        {
          code: 91,
          children: [
            {
              lxbm: "yz",
              name: "图书总数",
              ms: "统计学生人数时用这个指标",
              cjr: "admin",
              id: 177,
              zblx: "原子指标",
              type: 1,
              parentId: 91,
              bq: "校情分析指标"
            },
            {
              lxbm: "yz",
              name: "折合在校生数",
              ms: "",
              cjr: "admin",
              id: 207,
              zblx: "原子指标",
              type: 1,
              parentId: 91,
              bq: ""
            },
            {
              lxbm: "yz",
              name: "占地面积",
              ms: "",
              cjr: "admin",
              id: 209,
              zblx: "原子指标",
              type: 1,
              parentId: 91,
              bq: ""
            },
            {
              lxbm: "yz",
              name: "全日制在校生",
              ms: "",
              cjr: "admin",
              id: 210,
              zblx: "原子指标",
              type: 1,
              parentId: 91,
              bq: ""
            },
            {
              lxbm: "yz",
              name: "折合教师数 ",
              ms: "",
              cjr: "admin",
              id: 212,
              zblx: "原子指标",
              type: 1,
              parentId: 91,
              bq: ""
            }
          ],
          name: "基本办学条件检测指标",
          description: "指标库测试域，用于连接指标库创建的指标到图表的创建",
          id: 91,
          type: 0,
          usedCount: null,
          parentId: 0
        }
      ],
      bracketDraging: false,
      bracketDragstart: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeModel.filter(val)
    },
    cleanIndicator(val) {
      const ids = val.filter(item => item.id)
      this.getCommonDimension(ids)
    }
  },
  created() {
    this.getYzList()
    this.getLabelSelectList()
    this.getAllViewGroup()
    this.getAllIndicatorList()
  },
  mounted() {},

  methods: {
    // 选择派生维度
    async change(id, index) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionValueById({
          id: id,
          lxbm: "yz"
        })
      this.pswdOptionsMap[id] = data
      console.log(this.pswdOptionsMap)
      const item = { ...this.currentDerive.pswd.find(item => item.id === id) }
      delete item.id
      this.$set(this.xsc, index, {
        ...item,
        adid: id,
        wdzval: [""]
      })
      console.log(this.xsc)
    },

    // 添加派生维度
    addDerive() {
      if (this.xsc.length >= this.currentDerive.pswd.length) {
        return this.$message({
          message: "暂无派生维度",
          type: "warning"
        })
      }
      this.xsc.push({
        adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
        atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzd: "", // getAtomIndicatorList接口返回的zddm字段
        zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
        wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
        wdzval: [] // 用户选择的维度值
      })
    },
    // 删除派生维度
    removeDerive(item) {
      var index = this.xsc.indexOf(item)
      if (index !== -1 && this.xsc.length > 1) {
        this.xsc.splice(index, 1)
      }
    },
    // 获取原子指标
    async getYzList() {
      const { data } = await this.$httpBi.indicatorAnagement.getYzList({
        zbmc: ""
      })
      this.yzzbList = data
    },
    handleSaveWd() {
      this.currentZb = {
        ...this.currentZb,
        wd: this.xsc
      }
      let indicators = this.derivatives[this.currentZb.parentIndex].indicators

      this.$set(indicators, this.currentZb.index, this.currentZb)
    },
    addWd(item, index, parentIndex) {
      this.$set(this.derivatives[parentIndex], "tempIndex", index)

      this.xsc = [
        {
          adid: "", // getAtomIndicatorList接口返回的pswd中的项目id字段
          atomid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          tabid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          wdid: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          wdbm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          wdzd: "", // getAtomIndicatorList接口返回的zddm字段
          zdmc: "", // getAtomIndicatorList接口返回的zbmc字段
          wdlx: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          sjgs: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          gldm: "", // getAtomIndicatorList接口返回的pswd中的相同字段
          wdzval: [] // 用户选择的维度值
        }
      ]
      this.currentZb = {
        ...item,
        index,
        parentIndex
      }
    },
    // 新增衍生指标
    handleAdd() {
      // 最多新增三个
      if (this.derivatives.length >= 3) {
        return this.$message({
          message: "最多新增三个衍生指标",
          type: "warning"
        })
      }

      this.derivatives.push({
        zbmc: "", // 指标名称
        zblx: "衍生指标", // 指标类型
        lxbm: "ys", // 类型编码
        sysjy: 0, // 组织
        jszq: "", // 计算周期
        jd: 0, // 精度
        sswr: "", // 四舍五入
        ms: "", // 描述
        bq: [], // 标签
        sjfc: 5,
        tempIndex: -1,
        selfDimension: {},
        indicators: []
      })
    },
    // 获取最后一个
    lastIndicatorIndex(parentIndex) {
      let indicators = this.derivatives[parentIndex].indicators

      let i = 0
      indicators.forEach((item, index) => {
        if (item.type === 1) {
          i = index
        }
      })
      return i
    },
    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    // 获取指标列表
    async getAllIndicatorList() {
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllIndicatorList({
          zbmc: "",
          zblx: ""
        })
      this.data = data
    },
    // 式计算
    async getCalculateResult() {
      this.dialogTableVisible = true
      // const params = {
      //   ...this.form,
      //   indicators: this.indicators,
      //   commonDimensionList: this.commonDimensionList.filter(
      //     item => item.checked
      //   )
      // }
      // const { data } = await this.$httpBi.indicatorAnagement.getCalculateResult(
      //   params
      // )
      this.tableData.head = [
        {
          prop: "xh",
          label: "序号"
        },
        {
          prop: "zbmc",
          label: "指标名称"
        },
        {
          prop: "sz",
          label: "数值"
        }
      ]
      this.tableData.list = [
        {
          xh: "1",
          zbmc: "生均图书",
          sz: "110"
        },
        {
          xh: "2",
          zbmc: "生均占地面积",
          sz: "50"
        },
        {
          xh: "3",
          zbmc: "生师比",
          sz: "17.32"
        }
      ]
    },
    // 保存
    handleSave() {
      const zbmcs = this.derivatives.filter(item => item.zbmc !== "")
      if (zbmcs.length !== this.derivatives.length) {
        return this.$message({
          message: "请命名指标名称",
          type: "warning"
        })
      }
      this.$refs.form.validate(async valid => {
        if (valid) {
          await this.$httpBi.indicatorAnagement.saveTempCompositeIndicator()
          this.$message.success("保存成功")
          this.$router.push({
            path: "/ddsBi/indicatorAnagement",
            query: {}
          })
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    // 获取公共维度
    async getCommonDimension(ids) {
      const { data } = await this.$httpBi.indicatorAnagement.getCommonDimension(
        {
          ids
        }
      )
      this.commonDimensionList = data.map(item => ({
        ...item,
        checked: true,
        values: []
      }))

      data.forEach(item => {
        this.$set(this.dimensionOptions, item.bm, [])
        this.getDimensionOptions(item.bm)
      })

      console.log(this.commonDimensionList, "this.commonDimensionList")
    },
    // 获取维度选项
    async getDimensionOptions(bm) {
      const { data } =
        await this.$httpBi.indicatorAnagement.getDimensionOptions({
          bm
        })
      this.$set(this.dimensionOptions, bm, data)
      console.log(this.dimensionOptions)
    },
    // 获取独立维度
    async getDimension(id) {
      this.currentId = id
      this.$set(this.form.selfDimension, id, [])
      console.log(id)
      const { data } = await this.$httpBi.indicatorAnagement.getDimension({
        id
      })
      console.log(data, this.commonDimensionList)
      let selfDimensionList = data.filter(
        item => !this.commonDimensionList.some(d => d.bm === item.bm)
      )
      selfDimensionList = selfDimensionList.map(item => ({
        ...item,
        values: []
      }))
      selfDimensionList.forEach(item => {
        this.$set(this.dimensionOptions, item.bm, [])
        this.getDimensionOptions(item.bm)
      })
      this.$set(this.form.selfDimension, id, selfDimensionList)
    },

    handleDragStart(e, item, parentIndex, index) {
      this.currentParentIndex = parentIndex

      setTimeout(() => {
        this.isDragging = true
      }, 0)
      // 指标内拖拽
      this.isIndicatorsDrag = true
      console.log(e)
      // 在拖拽开始时设置拖拽节点的数据
      e.dataTransfer.setData("tempData", JSON.stringify(item))
      // 设置拖动效果
      e.dataTransfer.effectAllowed = "move"

      this.$set(this.derivatives[parentIndex], "tempIndex", index)
    },
    handleDragEnd() {
      this.bracketDraging = false
      this.bracketDragstart = false
      this.isDragging = false
      this.isIndicatorsDrag = false
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // tree节点能否被拖拽
    allowDrag(draggingNode) {
      if (draggingNode.data.type === 1) {
        return draggingNode
      }
    },
    handleTreeDragEnd() {
      this.isDragging = false
      this.isIndicatorsDrag = false
    },
    // tree停止拖拽时节点可放置的位置
    allowDrop() {
      return false
    },
    // 特殊符号拖拽
    bracketDragStart(event, direction) {
      this.isDragging = true
      this.bracketDragstart = true

      const item = {
        direction,
        id: Date.now(),
        type: 2
      }
      event.dataTransfer.setData("text/plain", JSON.stringify(item))
    },

    // tree拖拽开始
    handleTreeDragStart(node, event) {
      if (node.data.type === 1) {
        this.isDragging = true
        // 在拖拽开始时设置拖拽节点的数据
        event.dataTransfer.setData(
          "text/plain",
          JSON.stringify({
            ...node.data,
            wd: []
          })
        )
      } else {
        return false
      }
    },
    // 拖拽结束
    handleDrop(event, index, parentIndex) {
      console.log(index, "parentIndex")

      this.$set(this.derivatives[parentIndex], "tempIndex", index)
      let indicators = this.derivatives[parentIndex].indicators
      this.isDragging = false
      event.preventDefault()
      // 指标内拖拽
      if (this.isIndicatorsDrag) {
        const data = JSON.parse(event.dataTransfer.getData("tempData"))
        const id = data.id
        // 删除当前指标
        indicators = indicators.filter(item => item.id !== id)
        // 再插入指标
        indicators.splice(index, 0, data)
      } else {
        const data = JSON.parse(event.dataTransfer.getData("text/plain"))
        // 如果时从tree拖拽直接插入
        indicators.splice(index, 0, data)
      }
      this.derivatives[parentIndex].indicators = indicators
      console.log(this.derivatives, "this.derivatives")
    },
    // 拖拽删除指标
    handleDeleteDragEnd(e) {
      console.log(e, "EE")
      const data = JSON.parse(e.dataTransfer.getData("tempData"))
      const id = data.id
      let indicators = this.derivatives[this.currentParentIndex].indicators

      this.derivatives[this.currentParentIndex].tempIndex = --this.derivatives[
        this.currentParentIndex
      ].tempIndex
      // 删除指标
      indicators = indicators.filter(item => item.id !== id)
      this.derivatives[this.currentParentIndex].indicators = indicators

      // delete this.form.selfDimension[id]
      this.isDragging = false
    },
    replaceFieldRecursive(obj) {
      for (let key in obj) {
        if (key === "zbmc") {
          obj.name = obj.zbmc
          delete obj.zbmc
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
          this.replaceFieldRecursive(obj[key])
        }
      }
    },
    // 获取所有标签
    async getLabelSelectList() {
      const { data } = await this.$httpBi.indicatorAnagement.getIndicatorTags(
        ""
      )
      this.labels = data
      const tagObj = data.find(item => item.bqmc === this.tempTag)
      if (tagObj) {
        this.$nextTick(() => {
          this.value.splice(
            this.value.findIndex(e => e === this.tempTag),
            1,
            tagObj.bqcode
          )
        })
      }
    },
    changeTag(val) {
      console.log(val)
      const isNewLabel = this.labels.every(item =>
        val.every(element => {
          return item.bqcode === element
        })
      )
      console.log(isNewLabel, "isNewLabel")
      if (!isNewLabel) {
        this.addLabel()
      }
    },
    removeTag() {
      this.newTag = null
    },
    remoteMethod(tagName) {
      if (tagName) {
        this.newTag = tagName
      } else {
        this.newTag = null
      }
      console.log(!this.newTag)
    },
    // 添加新标签
    async addLabel() {
      console.log(this.newTag)
      if (!this.newTag) return
      await this.$httpBi.indicatorAnagement.addIndicatorTags([this.newTag])
      this.tempTag = this.newTag
      this.newTag = null
      this.getLabelSelectList()
    }
  }
}
</script>

<style scoped lang="scss">
.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 56px;
  width: 100%;
  height: 56px;
  padding-left: 20px;
  box-sizing: border-box;
  box-shadow: inset 0px -1px 0px 0px #ebedf0;
}
.content {
  display: flex;
  padding: 20px;

  box-sizing: border-box;
  height: calc(100vh - 195px);
  min-height: 640px;

  .pointer-field {
    flex: 0 0 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 5px 0px 0px 5px;
    border: 1px solid #e4e7ed;
    // width: 208px;
    // padding-right: 24px;
    // box-sizing: border-box;
    // height: 100%;
    // border-right: 1px solid #edeff0;
    // overflow: auto;
    .title {
      width: 224px;
      height: 70px;
      border-radius: 5px 0px 0px 0px;
      box-shadow: inset 0px -1px 0px 0px #ebedf0;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      line-height: 70px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 12px;
      box-sizing: border-box;
    }
    .model-tree {
      padding: 0 12px;
      margin-top: 12px;
      box-sizing: border-box;
      height: calc(100% - 120px);
      overflow: auto;
      ::v-deep .el-tree-node__label {
        width: 100%;
      }
      // ::v-deep .el-tree-node.is-current.is-focusable {
      //
      //   .el-tree-node__label {
      //     background: #f4f7ff;
      //     box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
      //       0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      //     border-radius: 4px;
      //     border: 1px solid #1563ff;
      //     color: #1563ff;
      //   } }

      //滚动条样式
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 6px;
        height: 2px;
        background-color: #cfd6e6;
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: transparent;
        border-radius: 6px;
      }
    }
    .tabs {
      display: flex;
      padding: 0 12px;
      box-sizing: border-box;
      margin: 16px 0;

      .tab-item {
        width: 100px;
        height: 30px;
        border-radius: 15px;
        border: 1px solid #dcdfe6;
        cursor: pointer;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        line-height: 30px;
        text-align: center;
        &:first-child {
          margin-right: 4px;
        }
        &.active {
          background: rgba(21, 99, 255, 0.1);
          color: #1563ff;
          font-weight: 500;
        }
      }
    }
    //滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #cbced1;
    }
    &::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: #ffffff;
    }
  }
  .drag-wrap {
    flex: 1;
    overflow: hidden;

    height: 100%;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    position: relative;
    padding-top: 150px;
    box-sizing: border-box;

    .add-btn {
      position: absolute;
      left: 16px;
      top: 24px;
    }
    .deletes-region {
      position: absolute;
      right: 16px;
      top: 24px;
      width: 164px;
      height: 64px;
      background: rgba(255, 82, 86, 0.12);
      border-radius: 6px;

      border: 1px dashed rgba(255, 82, 86, 0.8);
      // border-spacing: 20px; /* 边框间距 */
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 11px;
      box-sizing: border-box;
      .icon {
        width: 14px;
        height: 14px;
        background: url("~@/assets/images/del.png") no-repeat center;
        background-size: cover;
      }
      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ff5256;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        margin-top: 11px;
      }
    }
    .thumbnail {
      position: relative;
      height: 28px;
      z-index: 99;

      .number {
        position: absolute;
        left: 20px;
        width: 38px;
        height: 28px;
        padding-left: 13px;
        box-sizing: border-box;
        background: url("~@/assets/images/number.png") no-repeat center;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        line-height: 24px;
        z-index: 9;
      }
      .steps {
        position: absolute;
        top: 6px;
        left: 38px;
        height: 16px;
        background: #e6eefa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding-right: 12px;

        .text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #5c646e;
          line-height: 10px;
          text-align: right;
          padding-left: 26px;
          line-height: 16px;
          height: 16px;

          font-style: normal;
        }
        .step-item {
          width: 32px;
          height: 4px;
          background: #ceddf5;
          border-radius: 2px;
          &.active {
            background: #1563ff;
          }
        }
        .symbol {
          margin: 0 6px;
        }
        .indicators {
          display: flex;
          .dot {
            width: 4px;
            height: 4px;
            background: #ff800e;
            border-radius: 1px;
            opacity: 0.5;
            margin: 0 6px;
            &.isArithmetic {
              background: #00cc88;
            }
          }
        }
      }
    }
    .drag-region {
      width: 100%;
      position: relative;
      // padding-top: 56px;
      padding-left: 20px;
      height: 72px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      overflow-x: auto;
      margin-bottom: 38px;

      &.active {
        background-color: #e6eefa;
      }
      //滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #cbced1;
      }
      &::-webkit-scrollbar-track {
        border-radius: 2px;
        background-color: #ffffff;
      }
      .result {
        flex: 0 0 144px;
        height: 56px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #c8cbd1;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 10px;
        box-sizing: border-box;

        &.active {
          border: 1px solid #1563ff;
        }
        &::placeholder {
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #979da6;
        }
      }
      .equal-sign {
        min-width: 16px;
        height: 16px;
        margin: 0 12px;
        background: url("~@/assets/images/equal-sign.png") no-repeat;
      }
      .indicators {
        display: flex;
        align-items: center;
      }
      .indicator-region {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 160px;
        height: 56px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px dashed #d7d9db;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #979da6;
        text-align: center;
        line-height: 56px;
        &.dragging {
          background: #e5f9f3;
          border: 1px dashed #00cc88;
          color: #060607;
          .icon {
            width: 21px;
            height: 20px;
            background: url("~@/assets/images/drag-enter.png") no-repeat center;
            background-size: cover;
            margin-right: 4px;
          }
        }
      }
    }
    .tools {
      position: absolute;
      width: 100%;
      bottom: 0;
      padding: 0 16px 24px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .special-symbol {
        display: flex;
        flex-direction: column;
        .text {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-bottom: 12px;
        }
        .bracket {
          display: flex;
          align-items: center;
          width: 224px;
          height: 40px;
          background: #e6eefa;
          border-radius: 4px;

          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #2f3338;
          text-align: center;
          padding-left: 10px;
          box-sizing: border-box;
          &.dragstart {
            .left {
              box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
                0px 6px 6px -4px rgba(0, 42, 128, 0.12);
              border: 1px solid #1563ff;
              color: #1563ff;
            }
            .right {
              box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
                0px 6px 6px -4px rgba(0, 42, 128, 0.12);
              border: 1px solid #1563ff;
              color: #1563ff;
            }
          }
          &.draging {
            .left {
              box-shadow: none;
              border: 1px solid #c8cbd1;
              color: #2f3338;
            }
            .right {
              box-shadow: none;
              border: 1px solid #c8cbd1;
              color: #2f3338;
            }
          }
          .left {
            width: 20px;
            height: 24px;
            line-height: 20px;
            background: #ffffff;
            border-radius: 3px;
            border: 1px solid #c8cbd1;
            margin-right: 12px;
          }
          .right {
            width: 20px;
            height: 24px;
            line-height: 20px;

            background: #ffffff;
            border-radius: 3px;
            border: 1px solid #c8cbd1;
          }
        }
      }
      .btns {
        margin-top: 32px;
        margin-left: auto;
      }
    }
  }
  .base-property {
    flex: 0 0 224px;
    height: 100%;
    background: #ffffff;
    border-radius: 0px 5px 5px 0px;
    border: 1px solid #e4e7ed;
    .base-title {
      display: flex;
      align-items: center;
      height: 40px;
      border-bottom: 1px solid #e4e7ed;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2f3338;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 12px;
      box-sizing: border-box;
    }
    .form {
      width: 100%;
      margin-bottom: 10px;
      padding: 20px 12px;
      box-sizing: border-box;
      ::v-deep .el-form-item--small .el-form-item__label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        line-height: 14px;
        padding: 0 0 8px 0;
      }
      ::v-deep .el-checkbox:last-of-type {
        margin-right: 0px;
      }
      ::v-deep .el-checkbox__label {
        padding-left: 6px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2f3338;
      }
    }
    // ::v-deep .el-input--small .el-input__inner {
    //   width: 160px;

    //   font-size: 14px;
    // }

    // ::v-deep .yz-input.el-input--small .el-input__inner {
    //   width: 100%;

    //   font-size: 14px;
    // }
  }
}
::v-deep .jd.el-input--small .el-input__inner {
  width: 90px !important;

  font-size: 14px;
}
::v-deep .el-drawer__body {
  position: relative;
}
.footer-btn {
  position: absolute;
  right: 24px;
  bottom: 0;
  border-top: 1px solid #f0f0f0;
  height: 52px;
  box-sizing: border-box;
  width: 480px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
::v-deep .el-row {
  margin-bottom: 0px;
}
</style>

<style lang="scss">
.drag-element {
  /* 禁止文本选择 */
  user-select: none;
  /* 禁用默认拖拽效果 */
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content
  .el-tree-node__expand-icon {
  background-color: transparent;
}
#project_frame
  .model-tree
  .el-tree-node.is-current
  > .el-tree-node__content:has(> span.is-leaf) {
  position: relative;
  background: #f4f7ff !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
    0px 6px 6px -4px rgba(0, 42, 128, 0.12);
  border-radius: 4px;
  border: 1px solid #1563ff;
  cursor: move;

  &::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 9px;
    width: 12px;
    height: 10px;
    background: url("~@/assets/images/tree-icon.png") no-repeat center;
  }
  .el-tree-node__label {
    background: transparent !important;
  }
}

#project_frame .model-tree .el-tree-node {
  border: 1px solid transparent !important;
}
#project_frame {
  .el-tree-node:not(.is-expanded) > .el-tree-node__content:has(> span.is-leaf) {
    &:hover {
      position: relative;
      background: #f4f7ff !important;
      box-shadow: 0px 2px 8px 0px rgba(0, 42, 128, 0.1),
        0px 6px 6px -4px rgba(0, 42, 128, 0.12);
      border-radius: 4px;
      border: 1px solid #1563ff;
      cursor: move;
      &::after {
        content: "";
        position: absolute;
        right: 10px;
        top: 9px;
        width: 12px;
        height: 10px;
        background: url("~@/assets/images/tree-icon.png") no-repeat center;
      }

      .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.dragging > .el-tree-node__content {
    opacity: 0.2;
  }
  .el-tree-node > .el-tree-node__content {
    &:hover {
      background: #f5f7fa !important;

      > .el-checkbox {
        background-color: transparent !important;
      }

      .el-tree-node__expand-icon {
        background-color: transparent !important;

        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }

      .custom-tree-node,
      .el-tree-node__label {
        background-color: transparent !important;

        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background: #f5f7fa;

    > .el-checkbox {
      background-color: transparent !important;
    }

    .el-tree-node__expand-icon {
      background-color: transparent !important;

      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .custom-tree-node,
    .el-tree-node__label {
      background-color: transparent !important;

      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }
  }
}
</style>
