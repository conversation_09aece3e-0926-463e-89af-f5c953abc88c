<template>
  <div>
    <template v-if="warningList.length">
      <p class="result">{{ warningList.length }}条相关结果</p>
      <div class="list">
        <div
          class="list-item-custom"
          v-for="(item, index) in warningList.slice(
            (currentPage - 1) * 10,
            currentPage * 10
          )"
          :key="index"
          @click="handleDetail(item.id, item.name)"
        >
          <div
            class="title"
            v-html="brightenKeyword(item.name, searchContent)"
          ></div>
          <div
            class="desc"
            v-html="brightenKeyword(item.info, searchContent)"
          ></div>
          <!-- <div class="type">
            {{ item.router ? "大屏驾驶舱" : "仪表盘看板" }}
          </div> -->
        </div>
      </div>
      <div class="page">
        <el-pagination
          v-if="warningList.length > 10"
          small
          layout="prev, pager, next"
          :total="warningList.length"
          :page-size="10"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </template>
    <Empty v-else />
    <!-- <record v-if="dialogVisible" :dialogVisible.sync="dialogVisible" :id="id" /> -->
  </div>
</template>

<script>
import { brightenKeyword } from "@/utils"
import Empty from "../Empty"
export default {
  components: { Empty },
  props: {
    warningList: {
      type: Array,
      default: () => []
    },
    searchContent: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      listData: [],
      currentPage: 1,
      dialogVisible: false,
      // 预警id
      id: ""
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    brightenKeyword,
    // 查看详情
    handleDetail(id, name) {
      // 另标签打开
      const routerUrl = this.$router.resolve({
        path: "/ddsBi/searchDetail?isFullPage=true",
        query: {
          id,
          name,
          type: "5"
        }
      })
      window.open(routerUrl.href, "_blank")
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped lang="scss">
.result {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
  margin-bottom: 16px;
}
.list {
  .list-item-custom {
    margin-bottom: 24px;
    .title {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 28px;
      color: #323233;
      cursor: pointer;
    }
    .desc {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #86909c;
      line-height: 22px;
      margin: 8px 0;
    }
    .type {
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
      line-height: 20px;
    }
  }
}
.page {
  display: flex;
}
</style>
