<template>
  <div class="preview-layer-tool">
    <el-popover
      placement="top-start"
      width="200"
      trigger="hover"
      :content="layer.params.annotation"
    >
      <i slot="reference" class="el-icon-info"></i>
    </el-popover>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style scoped lang="scss">
.preview-layer-tool {
  position: absolute;
  top: -15px;
  right: 0;
  cursor: pointer;
}
</style>
