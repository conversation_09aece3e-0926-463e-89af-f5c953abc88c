export const ChartLayerConfig = {
  code: 1,
  type: "chart",
  tabName: "图表",
  label: "图表",
  icon: "iconziyuan",
  component: "ChartLayer",
  options: {
    // 配置
    style: [
      {
        type: "el-input-number",
        label: "左边距",
        name: "left",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "上边距",
        name: "top",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-input-number",
        label: "宽度",
        name: "width",
        required: false,
        placeholder: "",
        value: 460
      },
      {
        type: "el-input-number",
        label: "高度",
        name: "height",
        required: false,
        placeholder: "",
        value: 250
      },
      {
        type: "el-input-color",
        label: "背景颜色",
        name: "backgroundColor",
        required: false,
        placeholder: "",
        value: "rgba(255,255,255,0)"
      },
      {
        type: "el-input-color",
        label: "边框颜色",
        name: "borderColor",
        required: false,
        placeholder: "",
        value: "rgba(255,255,255,0)"
      },
      {
        type: "el-slider",
        label: "边框宽度",
        name: "borderWidth",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-slider",
        label: "圆角",
        name: "borderRadius",
        required: false,
        placeholder: "",
        value: 0
      },
      {
        type: "el-select",
        label: "边框样式",
        name: "borderStyle",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "solid", name: "实线" },
          { code: "dashed", name: "虚线" },
          { code: "dotted", name: "点线" },
          { code: "double", name: "双框" }
        ],
        value: "solid"
      },
      {
        type: "el-select",
        label: "数据刷新",
        name: "dataRefresh",
        required: false,
        placeholder: "",
        selectOptions: [
          { code: "false", name: "手动刷新" },
          { code: "true", name: "定时刷新" }
        ],
        value: "false"
      },
      {
        type: "el-input-number",
        label: "时长（秒）",
        name: "frequency",
        required: false,
        placeholder: "",
        value: 60,
        condition: {
          name: "dataRefresh",
          value: "true"
        }
      },
      {
        type: "el-input-textarea",
        label: "注释内容",
        name: "annotation",
        required: false,
        placeholder: "",
        value: ""
      }
    ],
    // 事件
    event: [
      {
        type: "el-switch",
        label: "开启事件",
        name: "isOpen",
        required: false,
        placeholder: "",
        value: false
      },
      {
        type: "el-radio-group",
        label: "下钻类型",
        name: "drillType",
        require: false,
        placeholder: "",
        selectValue: true,
        selectOptions: [
          {
            code: 1,
            name: "图表"
          },
          {
            code: 3,
            name: '外链'
          }
        ],
        value: 3
      },
      {
        type: "el-select-chart",
        label: "联动对象",
        name: "target",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: null
      },
      {
        type: "controls",
        label: "参数传递",
        name: "xAxis",
        relactiveDom: "drillType",
        relactiveDomValue: [1],
        value: ""
      },
      {
        type: "el-input-textarea",
        label: "外链地址",
        name: "url",
        relactiveDom: "drillType",
        relactiveDomValue: [3],
        value: "https://www.baidu.com/"
      },
      {
        type: "el-select",
        label: "打开方式",
        name: "revealType",
        relactiveDom: "drillType",
        relactiveDomValue: [1, 2],
        required: false,
        placeholder: "",
        selectOptions: [
          { code: 1, name: "当前页弹窗展示" },
          { code: 2, name: "新窗口打开" }
        ],
        value: 2
      }
    ]
  }
}
