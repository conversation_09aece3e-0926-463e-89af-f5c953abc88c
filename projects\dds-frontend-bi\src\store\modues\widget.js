let widget = {
  namespaced: true,
  state: {
    widgets: JSON.parse(window.localStorage.getItem("widgets")) || [],
  },
  mutations: {
    SET_WIDGETS(state, widgets) {
      const formedWidgets = widgets.map((widget) => {
        const parsedConfig = JSON.parse(widget.config)
        return {
          ...widget,
          config: parsedConfig,
        }
      })
      window.localStorage.setItem("widgets", JSON.stringify(formedWidgets))
      state.widgets = formedWidgets
    },
  },

  actions: {},
  getters: {},
}
export default widget
