// 复合指标
import service from "../base"
import config from "../config"

// const config = {
//   VUE_MODULE_DDS_BI: "/dds-server-bi-zqz/"
// }
export default {
  // 获取指标域展示列表
  getIndicatorGroupTree(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/indicatorGroupTree",
      method: "get",
      params: data
    })
  },
  // 获取标签展示列表
  getIndicatorTagTree(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/indicatorTagTree",
      method: "get",
      params: data
    })
  },
  // 获取指标详情
  getCompositeIndicatorInfo(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/detail",
      method: "get",
      params: data
    })
  },
  // 新增-编辑复合指标
  addEditCompositeIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/save",
      method: "post",
      data
    })
  },
  // 删除复合指标
  deleteCompositeIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/remove",
      method: "get",
      params: data
    })
  },
  // 复合指标试计算
  tryToCaluCompositeIndicator(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/tryExecute",
      method: "post",
      data
    })
  },
  // 获取指标维度列表
  getSelectDimension(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/selectDimension",
      method: "get",
      params: data
    })
  },
  // 获取维度值
  getSelectDimensionValue(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "compositeIndicator/selectDimensionValue",
      method: "get",
      params: data
    })
  },
  // 获取扩展维度
  getSelectExtendDimension(data) {
    return service({
      url:
        config.VUE_MODULE_DDS_BI + "compositeIndicator/selectExpandDimension",
      method: "post",
      data
    })
  }
}
