<template>
  <DT-View
    :inner-style="{
      position: 'relative'
    }"
  >
    <div class="search-warp">
      <div class="icon"></div>
      <h1>综合查询</h1>
      <div class="search-input">
        <el-input
          placeholder="请输入关键词查询"
          v-model="searchContent"
          class="input-with-select"
          clearable
          @keyup.enter.native="handleSearch"
        ></el-input>
        <div class="btn" @click="handleSearch">查询</div>
      </div>
    </div>
    <el-dialog
      title="提示"
      top="10vh"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="787px"
      :show-close="false"
    >
      <template #title>
        <div class="dialog-head">
          <div class="dialog-left">
            <img
              src="../../assets/images/ai/mf.gif"
              alt=""
              srcset=""
              class="dialog-mf"
              @mouseenter="reloadGif"
            />
            <div class="dialog-title"></div>
          </div>
          <div class="dialog-close" @click="dialogVisible = false"></div>
        </div>
      </template>
      <AiWeb />
    </el-dialog>
    <div
      v-if="$checkPermission(['ai:entry'])"
      @click="dialogVisible = true"
      class="ai-entry"
    ></div>
  </DT-View>
</template>

<script>
import AiWeb from "@/components/AiWeb"
export default {
  components: { AiWeb },
  data() {
    return {
      searchContent: "",
      dialogVisible: false
    }
  },
  computed: {
    username() {
      return this.$store.state.user.username
    }
  },
  methods: {
    reloadGif() {
      let img = document.querySelector(".dialog-mf")
      const imgSrc = require("../../assets/images/ai/mf.gif")
      img.src = imgSrc + "?date=" + new Date().getTime()
    },
    async handleSearch() {
      // 判断是否是空格
      if (this.searchContent.trim().length) {
        this.$router.push({
          name: "SearchList",
          params: {
            searchContent: this.searchContent
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.ai-entry {
  position: absolute;
  right: 24px;
  bottom: 40px;
  width: 200px;
  height: 150px;
  background: url("~@/assets/images/ai-home.png") no-repeat center;
  cursor: pointer;
  background-size: cover;
}
.search-warp {
  width: 576px;
  margin: 0 auto;
  margin-top: 6%;
  h1 {
    display: none;
  }
  .icon {
    width: 248px;
    height: 183px;
    margin: 0 auto;
    margin-bottom: 29px;
    background: url("~@/assets/imgs/bi/home-search.png") no-repeat;
  }
}
::v-deep .el-input__inner {
  height: 40px;
  background: #ffffff;
  border-radius: 4px 0px 0px 4px;
}
::v-deep .el-tabs__item {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #323233;
}
::v-deep .el-tabs__item.is-active {
  font-weight: 600;
  color: #1d2129;
}
.search-input {
  display: flex;
  align-items: center;
  .btn {
    width: 96px;
    height: 40px;
    background: #1463ff;
    border-radius: 0px 4px 4px 0px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    cursor: pointer;
    line-height: 40px;
    text-align: center;
  }
}
::v-deep .el-tabs__content {
  width: calc(90% - 376px);
}
::v-deep .el-dialog {
  width: 787px;
  height: 78vh;
  background: linear-gradient(180deg, #c7ebff, #d6ddff);
  // background: linear-gradient( 180deg, #43C0FF 0%, #788CFF 100%), #FFFFFF;
  border-radius: 10px;
  // border: 3px solid;
  // border-image: linear-gradient(180deg, rgba(67, 192, 255, 1), rgba(120, 140, 255, 1));
}
::v-deep .el-dialog__header {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0 20px 20px;
}
.dialog-head {
  display: flex;
  height: 74px;
  width: 100%;
  align-items: center;
  padding: 15px;
  box-sizing: border-box;
  .dialog-left {
    display: flex;

    height: 74px;
    padding: 15px 0 0 18px;
    box-sizing: border-box;

    .dialog-mf {
      width: 28px;
      height: 28px;
      background-size: cover;
      transition: all 0.3s;
    }
    .dialog-title {
      position: relative;
      width: 223px;
      height: 49px;
      margin-top: 4px;
      margin-left: 6px;
      background: url("~@/assets/images/ai/title.png") no-repeat;
    }
  }

  .dialog-close {
    position: absolute;
    width: 15px;
    height: 15px;
    background: url("~@/assets/images/ai/close.png") no-repeat;
    background-size: cover;
    right: 29px;
    top: 20px;
    cursor: pointer;
  }
}
</style>
