import Vuex from "vuex"
import Request from "../service"
import chart from "./modues/chart"
import widget from "./modues/widget"
import getters from "./getters"
import display from "./modues/display"
import watermark from "./modues/watermark"
import settings from "./modues/settings"
import dashboard from "./modues/dashboard"
export default new Vuex.Store({
  state: {
    user: null,
    // 菜单是否展开展示
    isCollapse: false
  },
  mutations: {
    SET_USER: (state, user) => {
      state.user = user
    },
    CLEAR_USER: state => {
      state.user = null
    },
    CHANGE_ISCOLLAPSE(state, isCollapse) {
      state.isCollapse = isCollapse
    }
  },
  actions: {
    getUserInfo({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        Request.getUserInfo()
          .then(response => {
            const { data } = response

            if (!data) {
              reject("获取用户信息失败.")
              return
            }
            commit("SET_USER", data)
            dispatch("watermark/getText")
            resolve(data)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    clearUser({ commit }) {
      commit("CLEAR_USER")
    },
    // 切换菜单展示
    changeIsCollapse: ({ commit }, isCollapse) => {
      commit("CHANGE_ISCOLLAPSE", isCollapse)
    }
  },
  modules: {
    chart,
    widget,
    display,
    watermark,
    settings,
    dashboard
  },
  getters
})
