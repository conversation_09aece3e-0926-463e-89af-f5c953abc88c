<template>
  <div class="dashboard-warp">
    <div class="modulue-wrap">
      <SectionTitle
        title="常模指标清单"
        style="margin-bottom: 24px"
      ></SectionTitle>
      <SearchForm
        @search="handleSearch"
        :columns="columns"
        :is-card="false"
        :search-param.sync="form"
        style="margin-bottom: 24px"
      />
      <CommonTable
        :page.sync="page"
        :table-data="tableData"
        :loading="loading"
        :show-selection="false"
        :table-columns="tableColumns"
        @handleExport="handleExportExcel"
        @onload="getData"
        max-height="685px"
        id="studentNum"
      >
        <template #actionSlot="{ row }">
          <el-button
            type="text"
            style="margin-right: 10px"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </CommonTable>
    </div>
  </div>
</template>

<script>
import SectionTitle from "@/components/SectionTitle.vue"
import SearchForm from "@/components/SearchForm/index.vue"

import CommonTable from "@/components/CommonTable.vue"

export default {
  components: {
    SearchForm,
    SectionTitle,
    CommonTable
  },
  props: {},
  data() {
    return {
      loading: false, // 卡片loading
      form: {
        zbmc: "", // 指标名称',
        zbnormativetypeid: "" // '常模类型',
      },

      columns: [
        {
          label: "指标名称",
          prop: "zbmc",
          search: {
            el: "input"
          }
        },
        {
          label: "常模类型",
          prop: "zbnormativetypeid",
          enum: this.$httpBi.indicatorAnagement.getAllNormativeType,
          fieldNames: {
            label: "name",
            value: "id"
          },
          search: {
            el: "select"
          }
        }
      ],
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      tableData: [],
      tableColumns: [
        {
          label: "指标名称",
          prop: "zbmc",
          visible: true,
          sortable: false
        },
        {
          label: "当前值",
          prop: "currentvalue",
          visible: true,
          sortable: false
        },
        {
          label: "一流大学建设高校常模",
          prop: "typeonevalue",
          visible: true,
          sortable: false
        },
        {
          label: "“双一流”建设高校常模",
          prop: "typetwovalue",
          visible: true,
          sortable: false
        },

        {
          label: "拥有一级学科博士点高校常模",
          prop: "typethreevalue",
          visible: true,
          sortable: false
        },

        {
          label: "世界一流大学常模",
          prop: "typefourvalue",
          visible: true,
          sortable: false
        },
        {
          label: "全国常模",
          prop: "typefivevalue",
          visible: true,
          sortable: false
        },
        {
          label: "操作",
          prop: "action",
          visible: true,
          sortable: false,
          slot: true
        }
      ]
    }
  },
  computed: {},
  created() {
    this.getData()
  },
  mounted() {},
  watch: {},
  methods: {
    async getData() {
      this.loading = true
      // 获取常模下拉选择
      const { data } =
        await this.$httpBi.indicatorAnagement.getNormativeTypePageList({
          ...this.form,
          pageSize: this.page.pageSize,
          currentPage: this.page.currentPage
        })
      this.tableData = data.list || []
      this.page.total = data.totalCopunt || 0
      this.loading = false
    },
    // 查询
    handleSearch() {
      this.page.currentPage = 1
      this.getData()
    },
    // 删除
    handleDelete(row) {
      this.$confirm(
        "该指标配置过的所有常模类型及数值都会被删除, 你确认要继续吗？",
        "提示",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(async () => {
          await this.$httpBi.indicatorAnagement.delNormativeTypeByZb({
            zbid: row.zbid,
            zblxm: row.zblxm
          })
          this.$message({
            type: "success",
            message: "删除成功"
          })
          this.getData()
        })
        .catch(() => {})
    }
  }
}
</script>

<style scoped lang="scss">
.dashboard-warp {
  padding: 24px;
  background-color: #f0f2f5;
}
.modulue-wrap {
  width: 100%;
  padding: 24px;
  background: #fff;
  height: calc(100vh - 135px);
  .pane-chart {
    padding-bottom: 40px;
  }
  .bar-chart {
    position: relative;
  }
  .section-content {
    position: relative;
    height: 395px;
    background: #fff;
    padding-top: 24px;
    padding-bottom: 16px;
    box-sizing: border-box;
    justify-content: flex-start;
  }
  .unit {
    position: absolute;
    top: 27px;
    left: 0;
    width: 100%;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #969799;
  }
}
.search-box {
  position: absolute;
  top: 0px;
  right: 0px;
  background: #fff;
  height: 30px;
  line-height: 30px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  z-index: 99;
  .select-item {
    margin-left: 24px;
    display: flex;
    align-items: center;
    height: 30px;
    .tab-box {
      display: flex;
      align-items: center;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
      height: 30px;
      font-size: 14px;
      overflow: hidden;
      .tab {
        padding: 0 10px;
        border-right: 1px solid #e5e5e5;
        cursor: pointer;
        height: 30px;

        &:last-child {
          border: none;
        }
        &.active {
          background: #2361dbc7;
          color: #fff;
        }
      }
    }
    .name {
      font-size: 16px;
      padding-right: 10px;
    }
  }
  ::v-deep .el-input__inner {
    width: 124px;
    height: 32px;
  }
}

.rlue {
  height: 40px;
  background: rgba(91, 143, 249, 0.06);
  display: flex;
  align-items: center;
  padding-left: 16px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #222222;
  span {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #5b8ff9;
    cursor: pointer;
  }
}
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}
::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
